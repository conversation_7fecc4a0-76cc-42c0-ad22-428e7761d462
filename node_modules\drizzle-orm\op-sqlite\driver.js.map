{"version": 3, "sources": ["../../src/op-sqlite/driver.ts"], "sourcesContent": ["import type { OPSQLiteConnection, QueryResult } from '@op-engineering/op-sqlite';\nimport { entityKind } from '~/entity.ts';\nimport { DefaultLogger } from '~/logger.ts';\nimport {\n\tcreateTableRelationsHelpers,\n\textractTablesRelationalConfig,\n\ttype RelationalSchemaConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { BaseSQLiteDatabase } from '~/sqlite-core/db.ts';\nimport { SQLiteAsyncDialect } from '~/sqlite-core/dialect.ts';\nimport type { DrizzleConfig } from '~/utils.ts';\nimport { OPSQLiteSession } from './session.ts';\n\nexport class OPSQLiteDatabase<\n\tTSchema extends Record<string, unknown> = Record<string, never>,\n> extends BaseSQLiteDatabase<'async', QueryResult, TSchema> {\n\tstatic override readonly [entityKind]: string = 'OPSQLiteDatabase';\n}\n\nexport function drizzle<TSchema extends Record<string, unknown> = Record<string, never>>(\n\tclient: OPSQLiteConnection,\n\tconfig: DrizzleConfig<TSchema> = {},\n): OPSQLiteDatabase<TSchema> & {\n\t$client: OPSQLiteConnection;\n} {\n\tconst dialect = new SQLiteAsyncDialect({ casing: config.casing });\n\tlet logger;\n\tif (config.logger === true) {\n\t\tlogger = new DefaultLogger();\n\t} else if (config.logger !== false) {\n\t\tlogger = config.logger;\n\t}\n\n\tlet schema: RelationalSchemaConfig<TablesRelationalConfig> | undefined;\n\tif (config.schema) {\n\t\tconst tablesConfig = extractTablesRelationalConfig(\n\t\t\tconfig.schema,\n\t\t\tcreateTableRelationsHelpers,\n\t\t);\n\t\tschema = {\n\t\t\tfullSchema: config.schema,\n\t\t\tschema: tablesConfig.tables,\n\t\t\ttableNamesMap: tablesConfig.tableNamesMap,\n\t\t};\n\t}\n\n\tconst session = new OPSQLiteSession(client, dialect, schema, { logger, cache: config.cache });\n\tconst db = new OPSQLiteDatabase('async', dialect, session, schema) as OPSQLiteDatabase<TSchema>;\n\t(<any> db).$client = client;\n\t(<any> db).$cache = config.cache;\n\tif ((<any> db).$cache) {\n\t\t(<any> db).$cache['invalidate'] = config.cache?.onMutate;\n\t}\n\n\treturn db as any;\n}\n"], "mappings": "AACA,SAAS,kBAAkB;AAC3B,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,OAGM;AACP,SAAS,0BAA0B;AACnC,SAAS,0BAA0B;AAEnC,SAAS,uBAAuB;AAEzB,MAAM,yBAEH,mBAAkD;AAAA,EAC3D,QAA0B,UAAU,IAAY;AACjD;AAEO,SAAS,QACf,QACA,SAAiC,CAAC,GAGjC;AACD,QAAM,UAAU,IAAI,mBAAmB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAChE,MAAI;AACJ,MAAI,OAAO,WAAW,MAAM;AAC3B,aAAS,IAAI,cAAc;AAAA,EAC5B,WAAW,OAAO,WAAW,OAAO;AACnC,aAAS,OAAO;AAAA,EACjB;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ;AAClB,UAAM,eAAe;AAAA,MACpB,OAAO;AAAA,MACP;AAAA,IACD;AACA,aAAS;AAAA,MACR,YAAY,OAAO;AAAA,MACnB,QAAQ,aAAa;AAAA,MACrB,eAAe,aAAa;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,gBAAgB,QAAQ,SAAS,QAAQ,EAAE,QAAQ,OAAO,OAAO,MAAM,CAAC;AAC5F,QAAM,KAAK,IAAI,iBAAiB,SAAS,SAAS,SAAS,MAAM;AACjE,EAAO,GAAI,UAAU;AACrB,EAAO,GAAI,SAAS,OAAO;AAC3B,MAAW,GAAI,QAAQ;AACtB,IAAO,GAAI,OAAO,YAAY,IAAI,OAAO,OAAO;AAAA,EACjD;AAEA,SAAO;AACR;", "names": []}