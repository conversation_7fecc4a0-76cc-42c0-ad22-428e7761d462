{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 5}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 5}], "isBlockCoverage": true}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 9}], "isBlockCoverage": true}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5133, "endOffset": 5235, "count": 132}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5283, "endOffset": 5385, "count": 44}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5438, "endOffset": 5540, "count": 44}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 22}], "isBlockCoverage": true}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 0}], "isBlockCoverage": false}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 27}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/__tests__/TaskInput.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 51716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 51716, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1253, "endOffset": 22766, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1296, "endOffset": 1350, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1399, "endOffset": 4177, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1473, "endOffset": 2250, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2319, "endOffset": 3108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3190, "endOffset": 3645, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3727, "endOffset": 4171, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4234, "endOffset": 6377, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4308, "endOffset": 4904, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4972, "endOffset": 5554, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5618, "endOffset": 6371, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6432, "endOffset": 12261, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6510, "endOffset": 7774, "count": 1}, {"startOffset": 7768, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7368, "endOffset": 7766, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7851, "endOffset": 9722, "count": 1}, {"startOffset": 9716, "endOffset": 9721, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9293, "endOffset": 9714, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9803, "endOffset": 11240, "count": 1}, {"startOffset": 11234, "endOffset": 11239, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11010, "endOffset": 11232, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11320, "endOffset": 12255, "count": 1}, {"startOffset": 12249, "endOffset": 12254, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12124, "endOffset": 12247, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12316, "endOffset": 15682, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12393, "endOffset": 12980, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13062, "endOffset": 13830, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13901, "endOffset": 15009, "count": 1}, {"startOffset": 15003, "endOffset": 15008, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14766, "endOffset": 15001, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 15091, "endOffset": 15676, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15736, "endOffset": 18201, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15820, "endOffset": 16845, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15918, "endOffset": 16020, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16918, "endOffset": 18195, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17016, "endOffset": 17118, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18255, "endOffset": 20403, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18343, "endOffset": 19296, "count": 1}, {"startOffset": 19290, "endOffset": 19295, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 19161, "endOffset": 19288, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 19375, "endOffset": 20397, "count": 1}, {"startOffset": 20322, "endOffset": 20396, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20193, "endOffset": 20320, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 20463, "endOffset": 22762, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 20551, "endOffset": 21166, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 21234, "endOffset": 22032, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 22102, "endOffset": 22756, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1566", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/TaskInput.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19661, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19661, "count": 1}], "isBlockCoverage": true}, {"functionName": "TaskInput", "ranges": [{"startOffset": 1436, "endOffset": 10063, "count": 22}, {"startOffset": 3491, "endOffset": 9115, "count": 0}, {"startOffset": 9158, "endOffset": 9168, "count": 0}, {"startOffset": 9169, "endOffset": 9177, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleSubmit", "ranges": [{"startOffset": 1872, "endOffset": 2295, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCancel", "ranges": [{"startOffset": 2320, "endOffset": 2466, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleKeyDown", "ranges": [{"startOffset": 2492, "endOffset": 2649, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 2788, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "onChange", "ranges": [{"startOffset": 3862, "endOffset": 3895, "count": 0}], "isBlockCoverage": false}, {"functionName": "onChange", "ranges": [{"startOffset": 7290, "endOffset": 7323, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10161, "endOffset": 10186, "count": 22}], "isBlockCoverage": true}]}, {"scriptId": "1571", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/button.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1951, "endOffset": 2495, "count": 22}, {"startOffset": 2044, "endOffset": 2072, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2625, "endOffset": 2647, "count": 22}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2753, "endOffset": 2783, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1580", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 550, "count": 22}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 641, "endOffset": 659, "count": 22}], "isBlockCoverage": true}, {"functionName": "formatDate", "ranges": [{"startOffset": 663, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1280, "endOffset": 1306, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDueDate", "ranges": [{"startOffset": 1310, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1984, "endOffset": 2013, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTaskOverdue", "ranges": [{"startOffset": 2017, "endOffset": 2113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2244, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 2248, "endOffset": 2454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2551, "endOffset": 2575, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 2579, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2908, "endOffset": 2932, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateOrderIndex", "ranges": [{"startOffset": 2936, "endOffset": 3207, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3314, "endOffset": 3348, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1582", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/input.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 569, "endOffset": 1384, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1511, "endOffset": 1532, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1583", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/select.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 996, "endOffset": 2293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2429, "endOffset": 3149, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3301, "endOffset": 4025, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4172, "endOffset": 6521, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6648, "endOffset": 7047, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7169, "endOffset": 8924, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9049, "endOffset": 9438, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9611, "endOffset": 9633, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9736, "endOffset": 9763, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9866, "endOffset": 9893, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9998, "endOffset": 10027, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10132, "endOffset": 10161, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10264, "endOffset": 10291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10393, "endOffset": 10419, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10526, "endOffset": 10557, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10669, "endOffset": 10705, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10819, "endOffset": 10857, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1651", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/hooks/useTasks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "useTasks", "ranges": [{"startOffset": 612, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 884, "endOffset": 908, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskStats", "ranges": [{"startOffset": 912, "endOffset": 1097, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1198, "endOffset": 1226, "count": 0}], "isBlockCoverage": false}, {"functionName": "useCreateTask", "ranges": [{"startOffset": 1230, "endOffset": 1870, "count": 22}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 1380, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSuccess", "ranges": [{"startOffset": 1451, "endOffset": 1743, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 1758, "endOffset": 1862, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1972, "endOffset": 2001, "count": 22}], "isBlockCoverage": true}, {"functionName": "useUpdateTask", "ranges": [{"startOffset": 2005, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2836, "endOffset": 2865, "count": 0}], "isBlockCoverage": false}, {"functionName": "useDeleteTask", "ranges": [{"startOffset": 2869, "endOffset": 3527, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3629, "endOffset": 3658, "count": 0}], "isBlockCoverage": false}, {"functionName": "useReorderTasks", "ranges": [{"startOffset": 3662, "endOffset": 4873, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4977, "endOffset": 5008, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1696", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 309, "endOffset": 328, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAll", "ranges": [{"startOffset": 360, "endOffset": 383, "count": 0}], "isBlockCoverage": false}, {"functionName": "create", "ranges": [{"startOffset": 395, "endOffset": 428, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 440, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 493, "endOffset": 520, "count": 0}], "isBlockCoverage": false}, {"functionName": "reorder", "ranges": [{"startOffset": 533, "endOffset": 565, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStats", "ranges": [{"startOffset": 579, "endOffset": 604, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 703, "endOffset": 726, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVersion", "ranges": [{"startOffset": 761, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "quit", "ranges": [{"startOffset": 797, "endOffset": 817, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 915, "endOffset": 937, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 970, "endOffset": 1000, "count": 22}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 1009, "endOffset": 1053, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1156, "endOffset": 1183, "count": 22}], "isBlockCoverage": true}]}, {"scriptId": "1697", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/queryClient.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "retry<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 597, "endOffset": 653, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 964, "endOffset": 991, "count": 0}], "isBlockCoverage": false}, {"functionName": "settings", "ranges": [{"startOffset": 1081, "endOffset": 1128, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1264, "endOffset": 1290, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1698", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/stores/uiStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10606, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10606, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.create.__vite_ssr_import_1__.devtools.name", "ranges": [{"startOffset": 535, "endOffset": 1411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setT<PERSON><PERSON><PERSON>er", "ranges": [{"startOffset": 613, "endOffset": 652, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSidebarOpen", "ranges": [{"startOffset": 716, "endOffset": 752, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleSidebar", "ranges": [{"startOffset": 775, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTheme", "ranges": [{"startOffset": 887, "endOffset": 912, "count": 0}], "isBlockCoverage": false}, {"functionName": "setIsAddingTask", "ranges": [{"startOffset": 981, "endOffset": 1022, "count": 0}], "isBlockCoverage": false}, {"functionName": "setEditingTaskId", "ranges": [{"startOffset": 1094, "endOffset": 1128, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSearch<PERSON>uery", "ranges": [{"startOffset": 1190, "endOffset": 1228, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSortBy", "ranges": [{"startOffset": 1285, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "setViewMode", "ranges": [{"startOffset": 1371, "endOffset": 1404, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1553, "endOffset": 1579, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskFilter", "ranges": [{"startOffset": 1605, "endOffset": 1650, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1753, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSetTaskFilter", "ranges": [{"startOffset": 1811, "endOffset": 1859, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1965, "endOffset": 1997, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSidebar", "ranges": [{"startOffset": 2020, "endOffset": 2146, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2246, "endOffset": 2272, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTheme", "ranges": [{"startOffset": 2293, "endOffset": 2376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2474, "endOffset": 2498, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskInput", "ranges": [{"startOffset": 2523, "endOffset": 2626, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2540, "endOffset": 2625, "count": 22}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2728, "endOffset": 2756, "count": 22}], "isBlockCoverage": true}, {"functionName": "useTaskEditing", "ranges": [{"startOffset": 2783, "endOffset": 2890, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2994, "endOffset": 3024, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSearch", "ranges": [{"startOffset": 3046, "endOffset": 3141, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3240, "endOffset": 3265, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSort", "ranges": [{"startOffset": 3285, "endOffset": 3372, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3469, "endOffset": 3492, "count": 0}], "isBlockCoverage": false}, {"functionName": "useViewMode", "ranges": [{"startOffset": 3516, "endOffset": 3603, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3704, "endOffset": 3731, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1708", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/test-utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTestQueryClient", "ranges": [{"startOffset": 1029, "endOffset": 1433, "count": 44}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1543, "endOffset": 1580, "count": 0}], "isBlockCoverage": false}, {"functionName": "TestWrapper", "ranges": [{"startOffset": 1584, "endOffset": 2468, "count": 23}, {"startOffset": 1663, "endOffset": 1689, "count": 22}], "isBlockCoverage": true}, {"functionName": "renderWithProviders", "ranges": [{"startOffset": 2469, "endOffset": 3001, "count": 22}], "isBlockCoverage": true}, {"functionName": "Wrapper", "ranges": [{"startOffset": 2589, "endOffset": 2845, "count": 38}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3109, "endOffset": 3144, "count": 22}], "isBlockCoverage": true}, {"functionName": "waitF<PERSON><PERSON><PERSON><PERSON><PERSON>oSettle", "ranges": [{"startOffset": 3148, "endOffset": 3624, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3733, "endOffset": 3769, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 3802, "endOffset": 3971, "count": 0}], "isBlockCoverage": false}, {"functionName": "type", "ranges": [{"startOffset": 3981, "endOffset": 4186, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 4197, "endOffset": 4394, "count": 0}], "isBlockCoverage": false}, {"functionName": "keyDown", "ranges": [{"startOffset": 4407, "endOffset": 4592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4693, "endOffset": 4718, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeInDocument", "ranges": [{"startOffset": 4761, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveTextContent", "ranges": [{"startOffset": 4843, "endOffset": 4912, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveValue", "ranges": [{"startOffset": 4929, "endOffset": 4994, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeDisabled", "ranges": [{"startOffset": 5012, "endOffset": 5066, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeEnabled", "ranges": [{"startOffset": 5083, "endOffset": 5136, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5238, "endOffset": 5264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5639, "endOffset": 5679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5778, "endOffset": 5813, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1709", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/contexts/ThemeContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "useSystemTheme", "ranges": [{"startOffset": 885, "endOffset": 1405, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1008, "endOffset": 1375, "count": 22}, {"startOffset": 1221, "endOffset": 1229, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleChange", "ranges": [{"startOffset": 1115, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1310, "endOffset": 1370, "count": 22}], "isBlockCoverage": true}, {"functionName": "applyThemeToDOM", "ranges": [{"startOffset": 1431, "endOffset": 1994, "count": 0}], "isBlockCoverage": false}, {"functionName": "ThemeProvider", "ranges": [{"startOffset": 2018, "endOffset": 7343, "count": 23}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2483, "endOffset": 2652, "count": 22}, {"startOffset": 2595, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2741, "endOffset": 3140, "count": 22}, {"startOffset": 2997, "endOffset": 3082, "count": 0}, {"startOffset": 3083, "endOffset": 3099, "count": 0}, {"startOffset": 3106, "endOffset": 3139, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3030, "endOffset": 3061, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3287, "endOffset": 3358, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3445, "endOffset": 3710, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3769, "endOffset": 3866, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3944, "endOffset": 4214, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4313, "endOffset": 4419, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4499, "endOffset": 4708, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4797, "endOffset": 4877, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4946, "endOffset": 5022, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5063, "endOffset": 5597, "count": 22}], "isBlockCoverage": true}, {"functionName": "loadConfig", "ranges": [{"startOffset": 5094, "endOffset": 5574, "count": 22}, {"startOffset": 5262, "endOffset": 5364, "count": 0}, {"startOffset": 5373, "endOffset": 5521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5638, "endOffset": 5709, "count": 22}, {"startOffset": 5666, "endOffset": 5705, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5785, "endOffset": 6731, "count": 22}, {"startOffset": 5835, "endOffset": 6730, "count": 0}], "isBlockCoverage": true}, {"functionName": "checkAutoSwitch", "ranges": [{"startOffset": 5864, "endOffset": 6605, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6697, "endOffset": 6726, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7446, "endOffset": 7475, "count": 22}], "isBlockCoverage": true}, {"functionName": "useTheme", "ranges": [{"startOffset": 7496, "endOffset": 7690, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7788, "endOffset": 7812, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7917, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1710", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1399, "endOffset": 1432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1854, "endOffset": 1881, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2496, "endOffset": 2529, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7696, "endOffset": 7727, "count": 22}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8083, "endOffset": 8119, "count": 22}], "isBlockCoverage": true}, {"functionName": "getThemeById", "ranges": [{"startOffset": 8144, "endOffset": 8214, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8184, "endOffset": 8210, "count": 22}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8316, "endOffset": 8344, "count": 22}], "isBlockCoverage": true}, {"functionName": "getThemesByMode", "ranges": [{"startOffset": 8372, "endOffset": 8450, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8416, "endOffset": 8446, "count": 132}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8555, "endOffset": 8586, "count": 22}], "isBlockCoverage": true}, {"functionName": "isValidThemeId", "ranges": [{"startOffset": 8613, "endOffset": 8683, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8787, "endOffset": 8817, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1711", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/toast.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToastProvider", "ranges": [{"startOffset": 857, "endOffset": 1996, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1015, "endOffset": 1317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1380, "endOffset": 1451, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2098, "endOffset": 2127, "count": 22}], "isBlockCoverage": true}, {"functionName": "useToast", "ranges": [{"startOffset": 2131, "endOffset": 2329, "count": 22}, {"startOffset": 2235, "endOffset": 2309, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2426, "endOffset": 2450, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastContainer", "ranges": [{"startOffset": 2454, "endOffset": 3062, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2670, "endOffset": 2902, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastItem", "ranges": [{"startOffset": 3063, "endOffset": 7280, "count": 0}], "isBlockCoverage": false}, {"functionName": "useToastActions", "ranges": [{"startOffset": 7281, "endOffset": 8394, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8498, "endOffset": 8529, "count": 0}], "isBlockCoverage": false}]}]}