{"version": 3, "sources": ["../../../src/singlestore-core/columns/float.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\n\nexport type SingleStoreFloatBuilderInitial<TName extends string> = SingleStoreFloatBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreFloat';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreFloatBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreFloat'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreFloatConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreFloatBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreFloatConfig | undefined) {\n\t\tsuper(name, 'number', 'SingleStoreFloat');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreFloat<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreFloat<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreFloat<T extends ColumnBaseConfig<'number', 'SingleStoreFloat'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreFloatConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreFloat';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `float(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'float';\n\t\t} else {\n\t\t\ttype += `float(${this.precision},0)`;\n\t\t}\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport interface SingleStoreFloatConfig {\n\tprecision?: number;\n\tscale?: number;\n\tunsigned?: boolean;\n}\n\nexport function float(): SingleStoreFloatBuilderInitial<''>;\nexport function float(\n\tconfig?: SingleStoreFloatConfig,\n): SingleStoreFloatBuilderInitial<''>;\nexport function float<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreFloatConfig,\n): SingleStoreFloatBuilderInitial<TName>;\nexport function float(a?: string | SingleStoreFloatConfig, b?: SingleStoreFloatConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreFloatConfig>(a, b);\n\treturn new SingleStoreFloatBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,8BAA8B;AACvC,SAAS,2CAA2C,0CAA0C;AAYvF,MAAM,gCACJ,0CACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA4C;AACxE,UAAM,MAAM,UAAU,kBAAkB;AACxC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OACoD;AACpD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,yBACJ,mCACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAErD,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,SAAS,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAC9C,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,SAAS,KAAK,SAAS;AAAA,IAChC;AACA,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAgBO,SAAS,MAAM,GAAqC,GAA4B;AACtF,QAAM,EAAE,MAAM,OAAO,IAAI,uBAA+C,GAAG,CAAC;AAC5E,SAAO,IAAI,wBAAwB,MAAM,MAAM;AAChD;", "names": []}