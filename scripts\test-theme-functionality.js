const fs = require('fs')
const path = require('path')

console.log('🎨 主题切换功能测试')
console.log('='.repeat(40))

// 检查关键文件是否存在
const criticalFiles = [
  'src/shared/types/theme.ts',
  'src/renderer/contexts/ThemeContext.tsx',
  'src/renderer/components/layout/ModernLayout.tsx',
  'src/renderer/App.tsx',
  'src/renderer/globals.css'
]

console.log('📁 检查关键文件:')
let allFilesOk = true
criticalFiles.forEach(file => {
  const exists = fs.existsSync(file)
  console.log(`${exists ? '✅' : '❌'} ${file}`)
  if (!exists) allFilesOk = false
})

if (!allFilesOk) {
  console.log('\n❌ 部分关键文件缺失')
  process.exit(1)
}

// 检查 ModernLayout 中的主题切换实现
console.log('\n🔍 检查 ModernLayout 主题切换实现:')
const modernLayoutContent = fs.readFileSync('src/renderer/components/layout/ModernLayout.tsx', 'utf8')

const checks = [
  {
    name: '导入 ThemeContext useTheme',
    pattern: /import.*useTheme.*from.*ThemeContext/,
    required: true
  },
  {
    name: '使用 currentTheme 和 toggleMode',
    pattern: /const.*{.*currentTheme.*toggleMode.*}.*=.*useTheme/,
    required: true
  },
  {
    name: '主题切换按钮点击事件',
    pattern: /onClick={toggleMode}/,
    required: true
  },
  {
    name: '根据主题模式显示图标',
    pattern: /currentTheme\.mode.*===.*'dark'.*\?.*Sun.*:.*Moon/,
    required: true
  },
  {
    name: '主题切换按钮提示文本',
    pattern: /title=.*当前主题.*currentTheme\.name/,
    required: true
  }
]

checks.forEach(check => {
  const found = check.pattern.test(modernLayoutContent)
  const status = found ? '✅' : '❌'
  console.log(`${status} ${check.name}`)
  
  if (check.required && !found) {
    allFilesOk = false
  }
})

// 检查 App.tsx 中的 ThemeProvider
console.log('\n🔍 检查 App.tsx ThemeProvider 集成:')
const appContent = fs.readFileSync('src/renderer/App.tsx', 'utf8')

const appChecks = [
  {
    name: '导入 ThemeProvider',
    pattern: /import.*ThemeProvider.*from.*ThemeContext/,
    required: true
  },
  {
    name: 'ThemeProvider 包装应用',
    pattern: /<ThemeProvider>/,
    required: true
  },
  {
    name: '主题过渡动画类',
    pattern: /theme-transition/,
    required: true
  }
]

appChecks.forEach(check => {
  const found = check.pattern.test(appContent)
  const status = found ? '✅' : '❌'
  console.log(`${status} ${check.name}`)
  
  if (check.required && !found) {
    allFilesOk = false
  }
})

// 检查 CSS 主题过渡动画
console.log('\n🔍 检查 CSS 主题过渡动画:')
const cssContent = fs.readFileSync('src/renderer/globals.css', 'utf8')

const cssChecks = [
  {
    name: '主题过渡持续时间变量',
    pattern: /--theme-transition-duration/,
    required: true
  },
  {
    name: '主题过渡动画类',
    pattern: /\.theme-transition/,
    required: true
  },
  {
    name: '根元素过渡动画',
    pattern: /:root.*{[\s\S]*transition:/,
    required: true
  }
]

cssChecks.forEach(check => {
  const found = check.pattern.test(cssContent)
  const status = found ? '✅' : '❌'
  console.log(`${status} ${check.name}`)
  
  if (check.required && !found) {
    allFilesOk = false
  }
})

// 检查主题类型定义
console.log('\n🔍 检查主题类型定义:')
const themeTypesContent = fs.readFileSync('src/shared/types/theme.ts', 'utf8')

const typeChecks = [
  {
    name: 'ThemeMode 类型定义',
    pattern: /export type ThemeMode.*=.*'light'.*\|.*'dark'.*\|.*'system'/,
    required: true
  },
  {
    name: 'BUILT_IN_THEMES 导出',
    pattern: /export const BUILT_IN_THEMES/,
    required: true
  },
  {
    name: '包含浅色和深色默认主题',
    pattern: /'light-default'[\s\S]*'dark-default'/,
    required: true
  }
]

typeChecks.forEach(check => {
  const found = check.pattern.test(themeTypesContent)
  const status = found ? '✅' : '❌'
  console.log(`${status} ${check.name}`)
  
  if (check.required && !found) {
    allFilesOk = false
  }
})

// 检查 ThemeContext 实现
console.log('\n🔍 检查 ThemeContext 实现:')
const themeContextContent = fs.readFileSync('src/renderer/contexts/ThemeContext.tsx', 'utf8')

const contextChecks = [
  {
    name: 'toggleMode 函数实现',
    pattern: /const toggleMode.*=.*useCallback/,
    required: true
  },
  {
    name: '系统主题检测',
    pattern: /useSystemTheme/,
    required: true
  },
  {
    name: '主题应用到 DOM',
    pattern: /applyThemeToDOM/,
    required: true
  },
  {
    name: '设置持久化',
    pattern: /settingsAPI\.set.*theme_config/,
    required: true
  }
]

contextChecks.forEach(check => {
  const found = check.pattern.test(themeContextContent)
  const status = found ? '✅' : '❌'
  console.log(`${status} ${check.name}`)
  
  if (check.required && !found) {
    allFilesOk = false
  }
})

console.log('\n' + '='.repeat(40))
if (allFilesOk) {
  console.log('🎉 主题切换功能实现检查通过！')
  console.log('\n📋 功能特性:')
  console.log('✅ 明暗主题切换')
  console.log('✅ 系统主题跟随')
  console.log('✅ 主题状态持久化')
  console.log('✅ 平滑过渡动画')
  console.log('✅ 视觉反馈（图标切换）')
  console.log('✅ 工具提示显示当前主题')
  
  console.log('\n🔧 使用说明:')
  console.log('• 点击左侧边栏的太阳/月亮图标切换主题')
  console.log('• 支持三种模式：浅色 → 深色 → 系统跟随')
  console.log('• 主题选择会自动保存并在重启后保持')
  console.log('• 切换时有平滑的过渡动画效果')
} else {
  console.log('❌ 主题切换功能实现存在问题，请检查上述失败项')
  process.exit(1)
}
