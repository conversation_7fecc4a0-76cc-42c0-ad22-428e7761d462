import React, { useState, useEffect } from 'react'
import { X, Calendar, Star, Flag } from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { useUpdateTask } from '../../hooks/useTasks'
import { useToastActions } from '../ui/toast'
import type { Task, TaskPriority } from '../../../shared/types/task'

interface TaskEditDialogProps {
  task: Task | null
  isOpen: boolean
  onClose: () => void
}

export function TaskEditDialog({ task, isOpen, onClose }: TaskEditDialogProps) {
  const [content, setContent] = useState('')
  const [description, setDescription] = useState('')
  const [priority, setPriority] = useState<TaskPriority>(2)
  const [dueDate, setDueDate] = useState('')
  const [isImportant, setIsImportant] = useState(false)
  
  const updateTask = useUpdateTask()
  const { showSuccess, showError } = useToastActions()

  // 当任务变化时更新表单
  useEffect(() => {
    if (task) {
      setContent(task.content)
      setDescription(task.description || '')
      setPriority(task.priority)
      setDueDate(task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : '')
      setIsImportant(task.isImportant || false)
    }
  }, [task])

  const handleSave = async () => {
    if (!task || !content.trim()) return

    try {
      await updateTask.mutateAsync({
        id: task.id,
        input: {
          content: content.trim(),
          description: description.trim() || undefined,
          priority,
          dueDate: dueDate ? new Date(dueDate).getTime() : null,
          isImportant
        }
      })
      
      showSuccess('任务已更新', '任务信息已成功保存')
      onClose()
    } catch (error) {
      console.error('Failed to update task:', error)
      showError('更新失败', '无法保存任务信息')
    }
  }

  const handleClose = () => {
    onClose()
  }

  const getPriorityLabel = (priority: TaskPriority) => {
    switch (priority) {
      case 1: return '低'
      case 2: return '中'
      case 3: return '高'
      case 4: return '紧急'
      default: return '中'
    }
  }

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case 1: return 'text-blue-600'
      case 2: return 'text-green-600'
      case 3: return 'text-orange-600'
      case 4: return 'text-red-600'
      default: return 'text-green-600'
    }
  }

  if (!task) return null

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>编辑任务</span>
            {task.parentTaskId && (
              <span className="text-sm text-muted-foreground bg-muted px-2 py-1 rounded">
                子任务
              </span>
            )}
          </DialogTitle>
          <DialogDescription>
            编辑任务的详细信息，包括标题、描述、优先级和截止日期等。
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* 任务标题 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">任务标题</label>
            <Input
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="输入任务标题..."
              className="w-full"
            />
          </div>

          {/* 任务描述 */}
          <div className="space-y-2">
            <label className="text-sm font-medium">描述</label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="添加任务描述..."
              className="w-full min-h-[80px] resize-none"
            />
          </div>

          {/* 优先级和重要性 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <Flag className="h-4 w-4" />
                优先级
              </label>
              <Select value={priority.toString()} onValueChange={(value) => setPriority(Number(value) as TaskPriority)}>
                <SelectTrigger>
                  <SelectValue>
                    <span className={getPriorityColor(priority)}>
                      {getPriorityLabel(priority)}
                    </span>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">
                    <span className="text-blue-600">低</span>
                  </SelectItem>
                  <SelectItem value="2">
                    <span className="text-green-600">中</span>
                  </SelectItem>
                  <SelectItem value="3">
                    <span className="text-orange-600">高</span>
                  </SelectItem>
                  <SelectItem value="4">
                    <span className="text-red-600">紧急</span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium flex items-center gap-1">
                <Star className="h-4 w-4" />
                重要性
              </label>
              <Button
                variant={isImportant ? "default" : "outline"}
                onClick={() => setIsImportant(!isImportant)}
                className="w-full justify-start"
              >
                <Star className={`h-4 w-4 mr-2 ${isImportant ? 'fill-current' : ''}`} />
                {isImportant ? '重要' : '普通'}
              </Button>
            </div>
          </div>

          {/* 截止日期 */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              截止日期
            </label>
            <Input
              type="date"
              value={dueDate}
              onChange={(e) => setDueDate(e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={handleClose}>
            取消
          </Button>
          <Button 
            onClick={handleSave}
            disabled={!content.trim() || updateTask.isPending}
          >
            {updateTask.isPending ? '保存中...' : '保存'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
