/**
 * 测试 Mock 数据
 * 提供测试中使用的模拟数据和工厂函数
 */

import { vi } from 'vitest'
import type { Task, TaskStats, CreateTaskInput, UpdateTaskInput } from '../../shared/types/task'
import { TaskPriority } from '../../shared/types/task'

// 基础任务 Mock 数据
export const mockTask: Task = {
  id: 'task-1',
  content: '测试任务',
  isCompleted: false,
  priority: TaskPriority.MEDIUM,
  dueDate: null,
  orderIndex: 1000,
  createdAt: Date.now(),
  deletedAt: null,
  parentTaskId: null,
  taskType: 'task',
  description: null,
  estimatedDuration: null,
  actualDuration: null,
  progress: 0,
}

// 已完成任务 Mock 数据
export const mockCompletedTask: Task = {
  ...mockTask,
  id: 'task-2',
  content: '已完成的任务',
  isCompleted: true,
  orderIndex: 2000,
}

// 高优先级任务 Mock 数据
export const mockHighPriorityTask: Task = {
  ...mockTask,
  id: 'task-3',
  content: '高优先级任务',
  priority: TaskPriority.HIGH,
  dueDate: Date.now() + 86400000, // 明天
  orderIndex: 3000,
}

// 过期任务 Mock 数据
export const mockOverdueTask: Task = {
  ...mockTask,
  id: 'task-4',
  content: '过期任务',
  dueDate: Date.now() - 86400000, // 昨天
  orderIndex: 4000,
}

// 子任务 Mock 数据
export const mockSubtask: Task = {
  ...mockTask,
  id: 'subtask-1',
  content: '子任务',
  parentTaskId: 'task-1',
  taskType: 'subtask',
  orderIndex: 1100,
}

// 任务列表 Mock 数据
export const mockTasks: Task[] = [
  mockTask,
  mockCompletedTask,
  mockHighPriorityTask,
  mockOverdueTask,
  mockSubtask,
]

// 任务统计 Mock 数据
export const mockTaskStats: TaskStats = {
  total: 5,
  completed: 1,
  pending: 4,
  overdue: 1,
  withSubtasks: 1,
  templates: 0,
}

// 创建任务输入 Mock 数据
export const mockCreateTaskInput: CreateTaskInput = {
  content: '新任务',
  priority: TaskPriority.MEDIUM,
  dueDate: null,
  parentTaskId: null,
  taskType: 'task',
  description: null,
  estimatedDuration: null,
  progress: 0,
}

// 更新任务输入 Mock 数据
export const mockUpdateTaskInput: UpdateTaskInput = {
  content: '更新后的任务',
  isCompleted: true,
  priority: TaskPriority.HIGH,
}

// 任务工厂函数
export function createMockTask(overrides: Partial<Task> = {}): Task {
  return {
    ...mockTask,
    id: `task-${Math.random().toString(36).substr(2, 9)}`,
    createdAt: Date.now(),
    ...overrides,
  }
}

// 创建多个任务的工厂函数
export function createMockTasks(count: number, overrides: Partial<Task> = {}): Task[] {
  return Array.from({ length: count }, (_, index) =>
    createMockTask({
      id: `task-${index + 1}`,
      content: `任务 ${index + 1}`,
      orderIndex: (index + 1) * 1000,
      ...overrides,
    })
  )
}

// Mock Electron API 响应
export const mockElectronAPI = {
  task: {
    getAll: vi.fn().mockResolvedValue(mockTasks),
    create: vi.fn().mockImplementation((input: CreateTaskInput) =>
      Promise.resolve(createMockTask({
        content: input.content,
        priority: input.priority,
        dueDate: input.dueDate,
      }))
    ),
    update: vi.fn().mockImplementation((id: string, input: UpdateTaskInput) =>
      Promise.resolve(createMockTask({
        id,
        ...input,
      }))
    ),
    delete: vi.fn().mockResolvedValue(true),
    reorder: vi.fn().mockResolvedValue(true),
    getStats: vi.fn().mockResolvedValue(mockTaskStats),
  },
  app: {
    getVersion: vi.fn().mockResolvedValue('1.0.0'),
    quit: vi.fn().mockResolvedValue(undefined),
  },
  settings: {
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue(true),
  },
}

// 重置所有 Mock
export function resetAllMocks() {
  Object.values(mockElectronAPI.task).forEach(mock => {
    if (vi.isMockFunction(mock)) {
      mock.mockClear()
    }
  })
  Object.values(mockElectronAPI.app).forEach(mock => {
    if (vi.isMockFunction(mock)) {
      mock.mockClear()
    }
  })
  Object.values(mockElectronAPI.settings).forEach(mock => {
    if (vi.isMockFunction(mock)) {
      mock.mockClear()
    }
  })
}

// 设置 Mock 返回值的工具函数
export const mockHelpers = {
  setTasksResponse: (tasks: Task[]) => {
    mockElectronAPI.task.getAll.mockResolvedValue(tasks)
  },
  
  setStatsResponse: (stats: TaskStats) => {
    mockElectronAPI.task.getStats.mockResolvedValue(stats)
  },
  
  setCreateTaskResponse: (task: Task) => {
    mockElectronAPI.task.create.mockResolvedValue(task)
  },
  
  setUpdateTaskResponse: (task: Task) => {
    mockElectronAPI.task.update.mockResolvedValue(task)
  },
  
  setDeleteTaskResponse: (success: boolean = true) => {
    mockElectronAPI.task.delete.mockResolvedValue(success)
  },
  
  setReorderTasksResponse: (success: boolean = true) => {
    mockElectronAPI.task.reorder.mockResolvedValue(success)
  },
  
  makeTasksThrow: (error: Error) => {
    mockElectronAPI.task.getAll.mockRejectedValue(error)
  },
  
  makeCreateTaskThrow: (error: Error) => {
    mockElectronAPI.task.create.mockRejectedValue(error)
  },
}

// 导出默认的 Mock API
export default mockElectronAPI
