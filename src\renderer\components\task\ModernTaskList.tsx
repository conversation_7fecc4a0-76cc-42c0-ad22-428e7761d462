import React, { useMemo, useState } from 'react'
import { ModernTaskCard } from './ModernTaskCard'
import { ModernHierarchicalTaskCard } from './ModernHierarchicalTaskCard'
import { BatchActionBar } from './BatchActionBar'
import { TaskEditDialog } from './TaskEditDialog'
import { TaskDetailPanel } from './TaskDetailPanel'
import { ErrorState, LoadingState, EmptyState } from '../ui/error-boundary'
import { useTasks, useUpdateTask, useCreateTask, useHierarchicalTasks } from '../../hooks/useTasks'
import { useViewStore } from '../../stores/viewStore'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '../../lib/queryClient'
import type { Task } from '../../../shared/types/task'
import { CheckCircle2, Plus } from 'lucide-react'

export function ModernTaskList() {
  const { data: tasks = [], isLoading, error, refetch } = useTasks()
  const { data: hierarchies = [], isLoading: isHierarchyLoading, error: hierarchyError, refetch: refetchHierarchies } = useHierarchicalTasks()
  const updateTask = useUpdateTask()
  const createTask = useCreateTask()
  const queryClient = useQueryClient()
  const {
    activeView,
    searchQuery,
    sortBy,
    showCompleted,
    setTaskInputFocused
  } = useViewStore()

  // 使用层级视图
  const useHierarchicalView = true

  // 编辑对话框状态
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)

  // 任务详情面板状态
  const [selectedTask, setSelectedTask] = useState<Task | null>(null)
  const [isDetailPanelOpen, setIsDetailPanelOpen] = useState(false)

  // 过滤和排序任务
  const { parentTasks, taskMap } = useMemo(() => {
    const now = Date.now()
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayStart = today.getTime()
    const todayEnd = todayStart + 24 * 60 * 60 * 1000

    // 根据视图类型决定是否过滤子任务
    // 已完成视图显示所有任务（包括子任务），其他视图只显示主任务
    let filtered = tasks.filter(task => {
      return activeView === 'completed' || !task.parentTaskId
    })

    // 根据活动视图过滤
    switch (activeView) {
      case 'today':
        filtered = filtered.filter(task =>
          !task.isCompleted &&
          task.dueDate &&
          task.dueDate >= todayStart &&
          task.dueDate < todayEnd
        )
        break
      case 'important':
        filtered = filtered.filter(task =>
          !task.isCompleted &&
          (task.priority === 3 || task.isImportant)
        )
        break
      case 'planned':
        filtered = filtered.filter(task =>
          !task.isCompleted &&
          task.dueDate &&
          task.dueDate > now
        )
        break
      case 'completed':
        filtered = filtered.filter(task => task.isCompleted)
        break
      case 'all':
      default:
        // 根据 showCompleted 设置过滤已完成任务
        if (!showCompleted) {
          filtered = filtered.filter(task => !task.isCompleted)
        }
        break
    }

    // 应用搜索
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(task =>
        task.content.toLowerCase().includes(query)
      )
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'priority':
          return b.priority - a.priority
        case 'dueDate':
          if (!a.dueDate && !b.dueDate) return 0
          if (!a.dueDate) return 1
          if (!b.dueDate) return -1
          return a.dueDate - b.dueDate
        case 'created':
          return b.createdAt - a.createdAt
        case 'alphabetical':
          return a.content.localeCompare(b.content)
        case 'custom':
        default:
          return a.orderIndex - b.orderIndex
      }
    })

    // 创建任务映射，用于快速查找子任务
    const taskMap = new Map<string, Task[]>()
    tasks.forEach(task => {
      if (task.parentTaskId) {
        if (!taskMap.has(task.parentTaskId)) {
          taskMap.set(task.parentTaskId, [])
        }
        taskMap.get(task.parentTaskId)!.push(task)
      }
    })

    // 对每个父任务的子任务进行排序
    taskMap.forEach(subtasks => {
      subtasks.sort((a, b) => a.orderIndex - b.orderIndex)
    })

    return { parentTasks: filtered, taskMap }
  }, [tasks, activeView, searchQuery, sortBy, showCompleted])

  // 层级数据过滤
  const filteredHierarchies = useMemo(() => {
    if (!useHierarchicalView) return []

    return hierarchies.filter(hierarchy => {
      // 递归检查任务层级是否匹配过滤条件
      const matchesFilter = (h: any): boolean => {
        const task = h.task
        const now = Date.now()
        const todayStart = new Date().setHours(0, 0, 0, 0)
        const todayEnd = new Date().setHours(23, 59, 59, 999)

        // 视图过滤
        let matchesView = true
        switch (activeView) {
          case 'today':
            matchesView = !task.isCompleted &&
              task.dueDate &&
              task.dueDate >= todayStart &&
              task.dueDate <= todayEnd
            break
          case 'important':
            matchesView = !task.isCompleted &&
              (task.priority === 3 || task.isImportant)
            break
          case 'planned':
            matchesView = !task.isCompleted &&
              task.dueDate &&
              task.dueDate > now
            break
          case 'completed':
            matchesView = task.isCompleted
            break
          case 'all':
          default:
            if (!showCompleted) {
              matchesView = !task.isCompleted
            }
            break
        }

        // 搜索匹配
        const matchesSearch = !searchQuery.trim() ||
          task.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()))

        // 如果当前任务匹配，返回true
        if (matchesView && matchesSearch) {
          return true
        }

        // 如果子任务中有匹配的，也返回true
        return h.children.some((child: any) => matchesFilter(child))
      }

      return matchesFilter(hierarchy)
    })
  }, [hierarchies, activeView, searchQuery, showCompleted, useHierarchicalView])

  const handleToggleComplete = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId)
    if (task) {
      updateTask.mutate({
        id: taskId,
        input: { isCompleted: !task.isCompleted }
      })
    }
  }

  const handleAddSubtask = async (parentTaskId: string) => {
    try {
      await createTask.mutateAsync({
        content: '新子任务',
        priority: 2,
        parentTaskId,
        taskType: 'subtask'
      })
    } catch (error) {
      console.error('Failed to create subtask:', error)
    }
  }

  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    setIsEditDialogOpen(true)
  }

  const handleCloseEditDialog = () => {
    setIsEditDialogOpen(false)
    setEditingTask(null)
  }

  const handleTaskClick = (task: Task) => {
    setSelectedTask(task)
    setIsDetailPanelOpen(true)
  }

  const handleCloseDetailPanel = () => {
    setIsDetailPanelOpen(false)
    setSelectedTask(null)
  }

  const currentIsLoading = useHierarchicalView ? isHierarchyLoading : isLoading
  const currentError = useHierarchicalView ? hierarchyError : error
  const currentData = useHierarchicalView ? filteredHierarchies : { parentTasks, taskMap }
  const currentRefetch = useHierarchicalView ? refetchHierarchies : refetch

  // 重试函数
  const handleRetry = () => {
    currentRefetch()
    // 同时刷新相关查询
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.tasks })
    queryClient.invalidateQueries({ queryKey: QUERY_KEYS.taskStats })
  }

  if (currentIsLoading) {
    return (
      <LoadingState
        message="正在加载任务..."
        className="min-h-[300px]"
      />
    )
  }

  if (currentError) {
    return (
      <ErrorState
        title="任务加载失败"
        message="无法获取任务数据，请检查网络连接或稍后重试"
        onRetry={handleRetry}
        className="min-h-[300px]"
      />
    )
  }

  const isEmpty = useHierarchicalView ? filteredHierarchies.length === 0 : parentTasks.length === 0

  if (isEmpty) {
    const { title, message, showAction } = (() => {
      if (searchQuery.trim()) {
        return {
          title: '没有找到匹配的任务',
          message: `没有包含 "${searchQuery}" 的任务`,
          showAction: false
        }
      }

      switch (activeView) {
        case 'today':
          return {
            title: '今天还没有任务',
            message: '为今天添加一些任务来保持高效',
            showAction: true
          }
        case 'important':
          return {
            title: '没有重要任务',
            message: '标记重要任务来优先处理',
            showAction: false
          }
        case 'planned':
          return {
            title: '没有已计划的任务',
            message: '为任务设置截止日期来更好地规划',
            showAction: false
          }
        case 'completed':
          return {
            title: '还没有完成任何任务',
            message: '完成任务后会在这里显示',
            showAction: false
          }
        case 'all':
        default:
          return {
            title: '开始你的第一个任务',
            message: '创建任务来管理你的待办事项',
            showAction: true
          }
      }
    })()

    const handleAddTask = () => {
      setTaskInputFocused(true)
    }

    return (
      <EmptyState
        title={title}
        message={message}
        icon={<CheckCircle2 className="w-8 h-8 text-muted-foreground" />}
        action={showAction ? (
          <button
            onClick={handleAddTask}
            className="inline-flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg transition-colors duration-200"
          >
            <Plus className="w-4 h-4" />
            添加任务
          </button>
        ) : undefined}
        className="min-h-[300px]"
      />
    )
  }

  return (
    <>
      <div className="space-y-4">
        {useHierarchicalView ? (
          // 层级视图
          filteredHierarchies.map((hierarchy) => (
            <ModernHierarchicalTaskCard
              key={hierarchy.task.id}
              hierarchy={hierarchy}
              onToggleComplete={handleToggleComplete}
              onAddSubtask={handleAddSubtask}
              onEdit={handleEditTask}
              onTaskClick={handleTaskClick}
            />
          ))
        ) : (
          // 传统视图
          parentTasks.map((task) => (
            <ModernTaskCard
              key={task.id}
              task={task}
              subtasks={taskMap.get(task.id) || []}
              onToggleComplete={handleToggleComplete}
              onAddSubtask={handleAddSubtask}
              onEdit={handleEditTask}
            />
          ))
        )}
      </div>

      {/* 批量操作工具栏 */}
      <BatchActionBar />

      {/* 任务编辑对话框 */}
      <TaskEditDialog
        task={editingTask}
        isOpen={isEditDialogOpen}
        onClose={handleCloseEditDialog}
      />

      {/* 任务详情面板 */}
      <TaskDetailPanel
        task={selectedTask}
        isOpen={isDetailPanelOpen}
        onClose={handleCloseDetailPanel}
      />
    </>
  )
}
