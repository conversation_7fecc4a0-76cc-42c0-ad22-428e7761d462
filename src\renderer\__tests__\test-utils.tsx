/**
 * 测试工具函数
 * 提供测试中常用的工具函数和组件包装器
 */

import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { vi } from 'vitest'
import { ThemeProvider } from '../contexts/ThemeContext'
import { ToastProvider } from '../components/ui/toast'

// 创建测试专用的 QueryClient
export function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
        staleTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
    logger: {
      log: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
    },
  })
}

// 测试包装器组件
interface TestWrapperProps {
  children: React.ReactNode
  queryClient?: QueryClient
}

function TestWrapper({ children, queryClient }: TestWrapperProps) {
  const client = queryClient || createTestQueryClient()

  return (
    <QueryClientProvider client={client}>
      <ThemeProvider>
        <ToastProvider>
          {children}
        </ToastProvider>
      </ThemeProvider>
    </QueryClientProvider>
  )
}

// 自定义渲染函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient, ...renderOptions } = options

  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <TestWrapper queryClient={queryClient}>
      {children}
    </TestWrapper>
  )

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient: queryClient || createTestQueryClient(),
  }
}

// 等待异步操作完成的工具函数
export async function waitForQueryToSettle(queryClient: QueryClient) {
  await new Promise(resolve => setTimeout(resolve, 0))
  await queryClient.getQueryCache().getAll().forEach(query => {
    if (query.state.fetchStatus === 'fetching') {
      return new Promise(resolve => {
        const unsubscribe = query.subscribe(() => {
          if (query.state.fetchStatus !== 'fetching') {
            unsubscribe()
            resolve(undefined)
          }
        })
      })
    }
  })
}

// 模拟用户交互的工具函数
export const userEvent = {
  click: async (element: Element) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.click(element)
  },
  
  type: async (element: Element, text: string) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.change(element, { target: { value: text } })
  },
  
  clear: async (element: Element) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.change(element, { target: { value: '' } })
  },
  
  keyDown: async (element: Element, key: string) => {
    const { fireEvent } = await import('@testing-library/react')
    fireEvent.keyDown(element, { key })
  },
}

// 断言工具函数
export const assertions = {
  toBeInDocument: (element: Element | null) => {
    expect(element).toBeInTheDocument()
  },
  
  toHaveTextContent: (element: Element | null, text: string) => {
    expect(element).toHaveTextContent(text)
  },
  
  toHaveValue: (element: Element | null, value: string) => {
    expect(element).toHaveValue(value)
  },
  
  toBeDisabled: (element: Element | null) => {
    expect(element).toBeDisabled()
  },
  
  toBeEnabled: (element: Element | null) => {
    expect(element).toBeEnabled()
  },
}

// 重新导出常用的测试工具
export * from '@testing-library/react'
export { vi } from 'vitest'
export { renderWithProviders as render }
