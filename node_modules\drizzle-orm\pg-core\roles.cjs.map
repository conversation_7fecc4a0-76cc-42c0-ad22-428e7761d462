{"version": 3, "sources": ["../../src/pg-core/roles.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\n\nexport interface PgRoleConfig {\n\tcreateDb?: boolean;\n\tcreateRole?: boolean;\n\tinherit?: boolean;\n}\n\nexport class PgRole implements PgRoleConfig {\n\tstatic readonly [entityKind]: string = 'PgRole';\n\n\t/** @internal */\n\t_existing?: boolean;\n\n\t/** @internal */\n\treadonly createDb: PgRoleConfig['createDb'];\n\t/** @internal */\n\treadonly createRole: PgRoleConfig['createRole'];\n\t/** @internal */\n\treadonly inherit: PgRoleConfig['inherit'];\n\n\tconstructor(\n\t\treadonly name: string,\n\t\tconfig?: PgRoleConfig,\n\t) {\n\t\tif (config) {\n\t\t\tthis.createDb = config.createDb;\n\t\t\tthis.createRole = config.createRole;\n\t\t\tthis.inherit = config.inherit;\n\t\t}\n\t}\n\n\texisting(): this {\n\t\tthis._existing = true;\n\t\treturn this;\n\t}\n}\n\nexport function pgRole(name: string, config?: PgRoleConfig) {\n\treturn new PgRole(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAQpB,MAAM,OAA+B;AAAA,EAa3C,YACU,MACT,QACC;AAFQ;AAGT,QAAI,QAAQ;AACX,WAAK,WAAW,OAAO;AACvB,WAAK,aAAa,OAAO;AACzB,WAAK,UAAU,OAAO;AAAA,IACvB;AAAA,EACD;AAAA,EArBA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAGS;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA,EAaT,WAAiB;AAChB,SAAK,YAAY;AACjB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,OAAO,MAAc,QAAuB;AAC3D,SAAO,IAAI,OAAO,MAAM,MAAM;AAC/B;", "names": []}