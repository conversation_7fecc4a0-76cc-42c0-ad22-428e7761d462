/**
 * 简化的 useTasks Hook 测试
 * 基本功能验证
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'

// 简单的导入测试
describe('useTasks Hooks Import Test', () => {
  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks()
  })

  it('should import hooks without errors', async () => {
    try {
      const hooks = await import('../useTasks')
      
      expect(hooks.useTasks).toBeDefined()
      expect(hooks.useTaskStats).toBeDefined()
      expect(hooks.useCreateTask).toBeDefined()
      expect(hooks.useUpdateTask).toBeDefined()
      expect(hooks.useDeleteTask).toBeDefined()
      expect(hooks.useReorderTasks).toBeDefined()
      
      expect(typeof hooks.useTasks).toBe('function')
      expect(typeof hooks.useTaskStats).toBe('function')
      expect(typeof hooks.useCreateTask).toBe('function')
      expect(typeof hooks.useUpdateTask).toBe('function')
      expect(typeof hooks.useDeleteTask).toBe('function')
      expect(typeof hooks.useReorderTasks).toBe('function')
    } catch (error) {
      console.error('Import error:', error)
      throw error
    }
  })

  it('should have correct function names', async () => {
    const hooks = await import('../useTasks')
    
    expect(hooks.useTasks.name).toBe('useTasks')
    expect(hooks.useTaskStats.name).toBe('useTaskStats')
    expect(hooks.useCreateTask.name).toBe('useCreateTask')
    expect(hooks.useUpdateTask.name).toBe('useUpdateTask')
    expect(hooks.useDeleteTask.name).toBe('useDeleteTask')
    expect(hooks.useReorderTasks.name).toBe('useReorderTasks')
  })
})

// 基本的 Mock 测试
describe('Mock System Test', () => {
  it('should load mocks without errors', async () => {
    try {
      const mocks = await import('../../__tests__/mocks')
      
      expect(mocks.mockTask).toBeDefined()
      expect(mocks.mockTasks).toBeDefined()
      expect(mocks.mockElectronAPI).toBeDefined()
      expect(mocks.createMockTask).toBeDefined()
      expect(mocks.createMockTasks).toBeDefined()
      
      expect(typeof mocks.createMockTask).toBe('function')
      expect(typeof mocks.createMockTasks).toBe('function')
      expect(Array.isArray(mocks.mockTasks)).toBe(true)
    } catch (error) {
      console.error('Mock import error:', error)
      throw error
    }
  })

  it('should create mock tasks correctly', async () => {
    const { createMockTask, createMockTasks } = await import('../../__tests__/mocks')
    
    const singleTask = createMockTask()
    expect(singleTask).toBeDefined()
    expect(singleTask.id).toBeDefined()
    expect(singleTask.content).toBeDefined()
    expect(typeof singleTask.isCompleted).toBe('boolean')
    
    const multipleTasks = createMockTasks(3)
    expect(Array.isArray(multipleTasks)).toBe(true)
    expect(multipleTasks).toHaveLength(3)
    expect(multipleTasks[0].id).toBeDefined()
    expect(multipleTasks[1].id).toBeDefined()
    expect(multipleTasks[2].id).toBeDefined()
  })
})

// 基本的 API 测试
describe('API Mock Test', () => {
  it('should have mock electron API', async () => {
    const { mockElectronAPI } = await import('../../__tests__/mocks')
    
    expect(mockElectronAPI.task).toBeDefined()
    expect(mockElectronAPI.task.getAll).toBeDefined()
    expect(mockElectronAPI.task.create).toBeDefined()
    expect(mockElectronAPI.task.update).toBeDefined()
    expect(mockElectronAPI.task.delete).toBeDefined()
    expect(mockElectronAPI.task.reorder).toBeDefined()
    
    expect(typeof mockElectronAPI.task.getAll).toBe('function')
    expect(typeof mockElectronAPI.task.create).toBe('function')
    expect(typeof mockElectronAPI.task.update).toBe('function')
    expect(typeof mockElectronAPI.task.delete).toBe('function')
    expect(typeof mockElectronAPI.task.reorder).toBe('function')
  })

  it('should mock API calls correctly', async () => {
    const { mockElectronAPI, mockTasks } = await import('../../__tests__/mocks')
    
    // 测试 getAll mock
    const tasks = await mockElectronAPI.task.getAll()
    expect(Array.isArray(tasks)).toBe(true)
    expect(tasks).toEqual(mockTasks)
    
    // 测试 create mock
    const newTaskInput = {
      content: '测试任务',
      priority: 2,
      dueDate: null,
      parentTaskId: null,
      taskType: 'task' as const,
      description: null,
      estimatedDuration: null,
      progress: 0,
    }
    
    const createdTask = await mockElectronAPI.task.create(newTaskInput)
    expect(createdTask).toBeDefined()
    expect(createdTask.content).toBe('测试任务')
    expect(createdTask.priority).toBe(2)
  })
})

// 类型系统测试
describe('Type System Test', () => {
  it('should import types without errors', async () => {
    try {
      const types = await import('../../../shared/types/task')
      
      expect(types.TaskPriority).toBeDefined()
      expect(types.TaskSchema).toBeDefined()
      expect(types.CreateTaskSchema).toBeDefined()
      expect(types.UpdateTaskSchema).toBeDefined()
    } catch (error) {
      console.error('Type import error:', error)
      throw error
    }
  })
})
