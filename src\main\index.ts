import { app, BrowserWindow, ipcMain } from 'electron'
import path from 'path'
import { getDatabase, closeDatabase } from '../shared/db/index'
import { tasks, settings } from '../shared/db/schema'
import { eq, ne, desc, isNull, and, or } from 'drizzle-orm'
import { ulid } from 'ulid'
import { setupIpcHandlers } from './ipc'

// 简化的开发环境检测
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

// 数据库实例
let db: ReturnType<typeof getDatabase> | null = null

// 初始化数据库和示例数据
async function initializeDatabase() {
  try {
    db = getDatabase()
    console.log('✅ Database initialized successfully')

    // 检查是否需要插入示例数据
    const existingTasks = await db.select().from(tasks)
    if (existingTasks.length === 0) {
      console.log('📝 Inserting sample tasks...')

      const sampleTasks = [
        {
          id: ulid(),
          content: '欢迎使用灵感APP！点击复选框标记完成',
          isCompleted: false,
          priority: 2,
          dueDate: Date.now() + 24 * 60 * 60 * 1000, // 明天
          orderIndex: 1000,
          createdAt: Date.now() - 60 * 60 * 1000 // 1小时前
        },
        {
          id: ulid(),
          content: '尝试添加新任务',
          isCompleted: false,
          priority: 1,
          dueDate: null,
          orderIndex: 2000,
          createdAt: Date.now() - 30 * 60 * 1000 // 30分钟前
        },
        {
          id: ulid(),
          content: '测试拖拽排序功能',
          isCompleted: true,
          priority: 3,
          dueDate: null,
          orderIndex: 3000,
          createdAt: Date.now() - 10 * 60 * 1000 // 10分钟前
        }
      ]

      for (const task of sampleTasks) {
        await db.insert(tasks).values(task)
      }

      console.log('✅ Sample tasks inserted')
    }

    return db
  } catch (error) {
    console.error('❌ Failed to initialize database:', error)
    throw error
  }
}

// 基础的 IPC 处理器实现（已弃用，使用 ./ipc/index.ts 中的完整版本）
async function setupBasicIpcHandlers_DEPRECATED() {
  // 确保数据库已初始化
  if (!db) {
    await initializeDatabase()
  }

  // 获取所有任务
  ipcMain.handle('task:getAll', async () => {
    try {
      if (!db) throw new Error('Database not initialized')
      const allTasks = await db.select().from(tasks).orderBy(desc(tasks.orderIndex))
      console.log('IPC: Getting all tasks, count:', allTasks.length)
      return allTasks
    } catch (error) {
      console.error('IPC: Failed to get tasks:', error)
      throw error
    }
  })

  // 获取任务统计
  ipcMain.handle('task:getStats', async () => {
    try {
      if (!db) throw new Error('Database not initialized')
      // 只获取未删除的主任务（排除子任务）
      const allTasks = await db.select().from(tasks).where(
        and(
          isNull(tasks.deletedAt),
          or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
        )
      )
      const now = Date.now()

      const stats = {
        total: allTasks.length,
        completed: allTasks.filter(t => t.isCompleted).length,
        pending: allTasks.filter(t => !t.isCompleted).length,
        overdue: allTasks.filter(t =>
          !t.isCompleted &&
          t.dueDate &&
          t.dueDate < now
        ).length
      }
      console.log('IPC: Getting task stats (excluding subtasks):', stats)
      return stats
    } catch (error) {
      console.error('IPC: Failed to get task stats:', error)
      throw error
    }
  })

  // 创建新任务
  ipcMain.handle('task:create', async (event, input) => {
    try {
      if (!db) throw new Error('Database not initialized')
      console.log('IPC: Creating task:', input)

      // 获取最大orderIndex
      const allTasks = await db.select().from(tasks)
      const maxOrderIndex = Math.max(...allTasks.map(t => t.orderIndex), 0)

      const newTask = {
        id: ulid(),
        content: input.content,
        isCompleted: false,
        priority: input.priority || 2,
        dueDate: input.dueDate || null,
        orderIndex: maxOrderIndex + 1000,
        createdAt: Date.now()
      }

      await db.insert(tasks).values(newTask)
      console.log('IPC: Task created:', newTask.id)
      return newTask
    } catch (error) {
      console.error('IPC: Failed to create task:', error)
      throw error
    }
  })

  // 更新任务
  ipcMain.handle('task:update', async (event, id, input) => {
    try {
      if (!db) throw new Error('Database not initialized')
      console.log('IPC: Updating task:', id, input)

      await db.update(tasks).set(input).where(eq(tasks.id, id))

      // 返回更新后的任务
      const updatedTask = await db.select().from(tasks).where(eq(tasks.id, id)).limit(1)
      if (updatedTask.length === 0) {
        throw new Error('Task not found')
      }

      console.log('IPC: Task updated:', id)
      return updatedTask[0]
    } catch (error) {
      console.error('IPC: Failed to update task:', error)
      throw error
    }
  })

  // 删除任务
  ipcMain.handle('task:delete', async (event, id) => {
    try {
      if (!db) throw new Error('Database not initialized')
      console.log('IPC: Deleting task:', id)

      const result = await db.delete(tasks).where(eq(tasks.id, id))
      console.log('IPC: Task deleted:', id)
      return true
    } catch (error) {
      console.error('IPC: Failed to delete task:', error)
      return false
    }
  })

  // 重新排序任务
  ipcMain.handle('task:reorder', async (event, reorderData: Array<{id: string, orderIndex: number}>) => {
    try {
      if (!db) throw new Error('Database not initialized')
      console.log('IPC: Reordering tasks:', reorderData.length, 'items')

      // 批量更新任务顺序
      for (const item of reorderData) {
        await db.update(tasks).set({ orderIndex: item.orderIndex }).where(eq(tasks.id, item.id))
      }

      console.log('IPC: Tasks reordered successfully')
      return true
    } catch (error) {
      console.error('IPC: Failed to reorder tasks:', error)
      return false
    }
  })

  // 应用相关处理器
  ipcMain.handle('app:getVersion', async () => {
    return app.getVersion()
  })

  ipcMain.handle('app:quit', async () => {
    app.quit()
  })

  // 设置相关处理器
  ipcMain.handle('settings:get', async (event, key: string) => {
    try {
      if (!db) throw new Error('Database not initialized')
      const result = await db.select().from(settings).where(eq(settings.key, key)).limit(1)
      return result.length > 0 ? result[0].value : null
    } catch (error) {
      console.error('IPC: Failed to get setting:', error)
      return null
    }
  })

  ipcMain.handle('settings:set', async (event, key: string, value: string) => {
    try {
      if (!db) throw new Error('Database not initialized')
      console.log('IPC: Setting:', key, '=', value)

      // 使用 INSERT OR REPLACE 语法
      await db.insert(settings).values({
        key,
        value,
        updatedAt: Date.now()
      }).onConflictDoUpdate({
        target: settings.key,
        set: {
          value,
          updatedAt: Date.now()
        }
      })

      return true
    } catch (error) {
      console.error('IPC: Failed to set setting:', error)
      return false
    }
  })

  console.log('✅ Complete IPC handlers setup completed')
}

// 保持对窗口对象的全局引用，如果不这样做，当 JavaScript 对象被垃圾回收时，窗口会被自动关闭
let mainWindow: BrowserWindow | null = null

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, '../preload/index.js'),
    },
    titleBarStyle: 'default',
    show: false, // 先不显示，等待页面加载完成
  })

  // 加载应用
  if (isDev) {
    mainWindow.loadURL('http://localhost:5174')
    // 开发环境下打开开发者工具
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'))
  }

  // 当页面加载完成后显示窗口
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
    
    // 开发环境下聚焦窗口
    if (isDev) {
      mainWindow?.focus()
    }
  })

  // 当窗口被关闭时，取消引用 window 对象
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // 处理窗口创建请求（阻止新窗口）
  mainWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' }
  })
}

// 当 Electron 完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(async () => {
  try {
    // 初始化数据库
    await initializeDatabase()

    createWindow()

    // 设置完整的 IPC 处理程序
    setupIpcHandlers()

    app.on('activate', () => {
      // 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，通常会重新创建一个窗口
      if (BrowserWindow.getAllWindows().length === 0) {
        createWindow()
      }
    })
  } catch (error) {
    console.error('❌ Failed to initialize application:', error)
    app.quit()
  }
})

// 当所有窗口都被关闭时退出应用
app.on('window-all-closed', () => {
  // 关闭数据库连接
  closeDatabase()

  // 在 macOS 上，应用和它们的菜单栏通常会保持活动状态，直到用户使用 Cmd + Q 明确退出
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// 应用退出前清理
app.on('before-quit', () => {
  closeDatabase()
})

// 在这个文件中，你可以包含应用程序剩余的所有主进程代码
// 也可以拆分成几个文件，然后用 require 导入

// 安全性：防止新窗口创建
app.on('web-contents-created', (_, contents) => {
  contents.setWindowOpenHandler(() => {
    return { action: 'deny' }
  })
})
