#!/usr/bin/env node

/**
 * 子任务系统性能测试脚本
 * 测试子任务相关操作的性能表现
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');
const { ulid } = require('ulid');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const testDbPath = path.join(dataDir, 'test_subtask_performance.db');

console.log('🚀 开始子任务系统性能测试...');

// 测试配置
const TEST_CONFIG = {
  SMALL_HIERARCHY: { parents: 10, childrenPerParent: 5 },
  MEDIUM_HIERARCHY: { parents: 50, childrenPerParent: 10 },
  LARGE_HIERARCHY: { parents: 100, childrenPerParent: 20 },
  DEEP_HIERARCHY: { depth: 5, childrenPerLevel: 3 },
  QUERY_ITERATIONS: 5,
};

// 性能测试结果
const results = {
  dataGeneration: {},
  queries: {},
  operations: {}
};

// 创建测试数据库
function createTestDatabase() {
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  const db = new Database(testDbPath);
  
  // 应用优化设置
  db.pragma('journal_mode = WAL');
  db.pragma('synchronous = NORMAL');
  db.pragma('cache_size = 10000');
  db.pragma('foreign_keys = ON');
  db.pragma('temp_store = MEMORY');
  db.pragma('mmap_size = 268435456');

  // 创建表结构
  db.exec(`
    CREATE TABLE tasks (
      id TEXT PRIMARY KEY,
      content TEXT NOT NULL,
      is_completed INTEGER DEFAULT 0 NOT NULL,
      priority INTEGER DEFAULT 2 NOT NULL,
      due_date INTEGER,
      order_index INTEGER NOT NULL,
      created_at INTEGER DEFAULT (unixepoch() * 1000) NOT NULL,
      deleted_at INTEGER,
      parent_task_id TEXT,
      task_type TEXT DEFAULT 'task' NOT NULL,
      description TEXT,
      estimated_duration INTEGER,
      actual_duration INTEGER,
      progress INTEGER DEFAULT 0 NOT NULL
    );
  `);

  // 应用所有索引
  const indexes = [
    'CREATE INDEX IF NOT EXISTS tasks_order_index_idx ON tasks(order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_is_completed_idx ON tasks(is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_task_id_idx ON tasks(parent_task_id)',
    'CREATE INDEX IF NOT EXISTS tasks_deleted_at_idx ON tasks(deleted_at)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_status_idx ON tasks(parent_task_id, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_type_idx ON tasks(parent_task_id, task_type)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_order_idx ON tasks(parent_task_id, order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_hierarchy_idx ON tasks(parent_task_id, deleted_at, is_completed, order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_stats_idx ON tasks(parent_task_id, is_completed, deleted_at)',
    'CREATE INDEX IF NOT EXISTS tasks_type_parent_idx ON tasks(task_type, parent_task_id, deleted_at)',
  ];

  indexes.forEach(indexSQL => {
    db.exec(indexSQL);
  });

  db.exec('ANALYZE tasks');

  return db;
}

// 生成层级测试数据
function generateHierarchyData(db, config) {
  console.log(`📝 生成层级测试数据: ${config.parents} 个父任务，每个 ${config.childrenPerParent} 个子任务...`);
  
  const startTime = Date.now();
  const now = Date.now();
  
  const insertStmt = db.prepare(`
    INSERT INTO tasks (
      id, content, is_completed, priority, order_index, 
      created_at, parent_task_id, task_type, progress
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const transaction = db.transaction(() => {
    const parentIds = [];
    
    // 创建父任务
    for (let i = 0; i < config.parents; i++) {
      const parentId = ulid();
      insertStmt.run(
        parentId,
        `父任务 ${i + 1}`,
        0,
        Math.floor(Math.random() * 3) + 1,
        (i + 1) * 1000,
        now - Math.random() * 7 * 24 * 60 * 60 * 1000,
        null,
        'task',
        0
      );
      parentIds.push(parentId);
    }
    
    // 为每个父任务创建子任务
    parentIds.forEach((parentId, parentIndex) => {
      for (let j = 0; j < config.childrenPerParent; j++) {
        const subTaskId = ulid();
        const isCompleted = Math.random() < 0.3 ? 1 : 0;
        insertStmt.run(
          subTaskId,
          `子任务 ${parentIndex + 1}-${j + 1}`,
          isCompleted,
          Math.floor(Math.random() * 3) + 1,
          (j + 1) * 1000,
          now - Math.random() * 6 * 24 * 60 * 60 * 1000,
          parentId,
          'subtask',
          isCompleted ? 100 : Math.floor(Math.random() * 100)
        );
      }
    });
  });

  transaction();
  
  const duration = Date.now() - startTime;
  const totalTasks = config.parents + (config.parents * config.childrenPerParent);
  console.log(`✅ 生成 ${totalTasks} 个任务完成，耗时: ${duration}ms`);
  
  return { totalTasks, duration, parentIds: [] };
}

// 生成深层级测试数据
function generateDeepHierarchy(db, config) {
  console.log(`📝 生成深层级测试数据: ${config.depth} 层深度，每层 ${config.childrenPerLevel} 个子任务...`);
  
  const startTime = Date.now();
  const now = Date.now();
  
  const insertStmt = db.prepare(`
    INSERT INTO tasks (
      id, content, is_completed, priority, order_index, 
      created_at, parent_task_id, task_type, progress
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const transaction = db.transaction(() => {
    let currentLevelIds = [];
    
    // 创建根任务
    const rootId = ulid();
    insertStmt.run(rootId, '深层级根任务', 0, 2, 1000, now, null, 'task', 0);
    currentLevelIds.push(rootId);
    
    // 逐层创建子任务
    for (let depth = 1; depth < config.depth; depth++) {
      const nextLevelIds = [];
      
      currentLevelIds.forEach((parentId, parentIndex) => {
        for (let i = 0; i < config.childrenPerLevel; i++) {
          const childId = ulid();
          const isCompleted = Math.random() < 0.2 ? 1 : 0;
          insertStmt.run(
            childId,
            `第${depth}层任务 ${parentIndex + 1}-${i + 1}`,
            isCompleted,
            Math.floor(Math.random() * 3) + 1,
            (i + 1) * 1000,
            now - Math.random() * 5 * 24 * 60 * 60 * 1000,
            parentId,
            'subtask',
            isCompleted ? 100 : Math.floor(Math.random() * 100)
          );
          nextLevelIds.push(childId);
        }
      });
      
      currentLevelIds = nextLevelIds;
    }
  });

  transaction();
  
  const duration = Date.now() - startTime;
  console.log(`✅ 生成深层级结构完成，耗时: ${duration}ms`);
  
  return { duration };
}

// 测试子任务查询性能
function testSubTaskQueries(db, testName) {
  console.log(`\n🔍 测试子任务查询性能 (${testName})...`);
  
  // 获取一些父任务ID用于测试
  const parentIds = db.prepare(`
    SELECT DISTINCT parent_task_id 
    FROM tasks 
    WHERE parent_task_id IS NOT NULL 
    LIMIT 10
  `).all().map(row => row.parent_task_id);

  if (parentIds.length === 0) {
    console.log('   ⚠️  没有找到父任务，跳过查询测试');
    return {};
  }

  const queries = {
    'getSubTasks': {
      sql: 'SELECT * FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL ORDER BY order_index',
      params: [parentIds[0]]
    },
    'getSubTaskStats': {
      sql: `SELECT 
              COUNT(*) as total,
              SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed,
              SUM(CASE WHEN is_completed = 0 THEN 1 ELSE 0 END) as pending
            FROM tasks 
            WHERE parent_task_id = ? AND deleted_at IS NULL`,
      params: [parentIds[0]]
    },
    'getCompletedSubTasks': {
      sql: 'SELECT * FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL AND is_completed = 1',
      params: [parentIds[0]]
    },
    'getSubTasksByPriority': {
      sql: 'SELECT * FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL AND priority = 1 ORDER BY order_index',
      params: [parentIds[0]]
    },
    'getHierarchyLevel': {
      sql: `SELECT t1.id, t1.content, t2.id as child_id, t2.content as child_content
            FROM tasks t1 
            LEFT JOIN tasks t2 ON t1.id = t2.parent_task_id AND t2.deleted_at IS NULL
            WHERE t1.parent_task_id = ? AND t1.deleted_at IS NULL`,
      params: [parentIds[0]]
    },
    'batchSubTaskStats': {
      sql: `SELECT 
              parent_task_id,
              COUNT(*) as total,
              SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed
            FROM tasks 
            WHERE parent_task_id IN (${parentIds.slice(0, 5).map(() => '?').join(',')}) 
              AND deleted_at IS NULL 
            GROUP BY parent_task_id`,
      params: parentIds.slice(0, 5)
    }
  };

  const queryResults = {};

  Object.entries(queries).forEach(([name, { sql, params }]) => {
    console.log(`  测试查询: ${name}`);
    
    const times = [];
    
    for (let i = 0; i < TEST_CONFIG.QUERY_ITERATIONS; i++) {
      const start = Date.now();
      const result = db.prepare(sql).all(...params);
      const duration = Date.now() - start;
      times.push(duration);
    }
    
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    queryResults[name] = {
      avgTime: Math.round(avgTime * 100) / 100,
      minTime,
      maxTime,
      iterations: TEST_CONFIG.QUERY_ITERATIONS
    };
    
    console.log(`    平均耗时: ${queryResults[name].avgTime}ms (最小: ${minTime}ms, 最大: ${maxTime}ms)`);
  });

  return queryResults;
}

// 测试子任务操作性能
function testSubTaskOperations(db) {
  console.log('\n⚡ 测试子任务操作性能...');
  
  const operationResults = {};
  
  // 测试批量创建子任务
  console.log('  测试批量创建子任务...');
  const parentId = ulid();
  const now = Date.now();
  
  // 创建父任务
  db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `).run(parentId, '批量操作测试父任务', 0, 2, 1000, now, 'task');
  
  // 批量创建子任务
  const batchCreateStart = Date.now();
  const insertStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, parent_task_id, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  const transaction = db.transaction(() => {
    for (let i = 0; i < 50; i++) {
      const subTaskId = ulid();
      insertStmt.run(
        subTaskId,
        `批量子任务 ${i + 1}`,
        0,
        2,
        (i + 1) * 1000,
        now,
        parentId,
        'subtask'
      );
    }
  });
  
  transaction();
  const batchCreateDuration = Date.now() - batchCreateStart;
  operationResults.batchCreate = batchCreateDuration;
  console.log(`    批量创建50个子任务: ${batchCreateDuration}ms`);
  
  // 测试批量更新子任务
  console.log('  测试批量更新子任务...');
  const subTaskIds = db.prepare('SELECT id FROM tasks WHERE parent_task_id = ?').all(parentId).map(row => row.id);
  
  const batchUpdateStart = Date.now();
  const updateStmt = db.prepare('UPDATE tasks SET is_completed = ?, progress = ? WHERE id = ?');
  
  const updateTransaction = db.transaction(() => {
    subTaskIds.forEach((id, index) => {
      const isCompleted = index % 2 === 0 ? 1 : 0;
      const progress = isCompleted ? 100 : Math.floor(Math.random() * 100);
      updateStmt.run(isCompleted, progress, id);
    });
  });
  
  updateTransaction();
  const batchUpdateDuration = Date.now() - batchUpdateStart;
  operationResults.batchUpdate = batchUpdateDuration;
  console.log(`    批量更新${subTaskIds.length}个子任务: ${batchUpdateDuration}ms`);
  
  // 测试级联删除
  console.log('  测试级联删除...');
  const cascadeDeleteStart = Date.now();
  const deleteTime = Date.now();
  
  // 递归删除函数
  function recursiveDelete(taskId) {
    const children = db.prepare('SELECT id FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL').all(taskId);
    children.forEach(child => recursiveDelete(child.id));
    db.prepare('UPDATE tasks SET deleted_at = ? WHERE id = ?').run(deleteTime, taskId);
  }
  
  recursiveDelete(parentId);
  const cascadeDeleteDuration = Date.now() - cascadeDeleteStart;
  operationResults.cascadeDelete = cascadeDeleteDuration;
  console.log(`    级联删除任务树: ${cascadeDeleteDuration}ms`);
  
  return operationResults;
}

// 主测试函数
async function runSubTaskPerformanceTests() {
  try {
    const db = createTestDatabase();
    
    console.log('\n=== 第一阶段：中等规模层级测试 ===');
    const mediumResult = generateHierarchyData(db, TEST_CONFIG.MEDIUM_HIERARCHY);
    results.dataGeneration.medium = mediumResult;
    
    const mediumQueries = testSubTaskQueries(db, '中等规模');
    results.queries.medium = mediumQueries;
    
    console.log('\n=== 第二阶段：大规模层级测试 ===');
    const largeResult = generateHierarchyData(db, TEST_CONFIG.LARGE_HIERARCHY);
    results.dataGeneration.large = largeResult;
    
    const largeQueries = testSubTaskQueries(db, '大规模');
    results.queries.large = largeQueries;
    
    console.log('\n=== 第三阶段：深层级结构测试 ===');
    const deepResult = generateDeepHierarchy(db, TEST_CONFIG.DEEP_HIERARCHY);
    results.dataGeneration.deep = deepResult;
    
    console.log('\n=== 第四阶段：操作性能测试 ===');
    const operationResults = testSubTaskOperations(db);
    results.operations = operationResults;
    
    // 关闭数据库
    db.close();
    
    // 生成性能报告
    generatePerformanceReport();
    
  } catch (error) {
    console.error('❌ 子任务性能测试失败:', error);
    process.exit(1);
  }
}

// 生成性能报告
function generatePerformanceReport() {
  console.log('\n📊 === 子任务系统性能测试报告 ===');
  
  // 数据生成性能
  console.log('\n🚀 数据生成性能:');
  if (results.dataGeneration.medium) {
    const mediumThroughput = Math.round(results.dataGeneration.medium.totalTasks / results.dataGeneration.medium.duration * 1000);
    console.log(`   中等规模: ${results.dataGeneration.medium.duration}ms (${mediumThroughput} 任务/秒)`);
  }
  
  if (results.dataGeneration.large) {
    const largeThroughput = Math.round(results.dataGeneration.large.totalTasks / results.dataGeneration.large.duration * 1000);
    console.log(`   大规模: ${results.dataGeneration.large.duration}ms (${largeThroughput} 任务/秒)`);
  }
  
  // 查询性能对比
  console.log('\n🔍 查询性能对比:');
  console.log('查询类型'.padEnd(25) + '中等规模(ms)'.padEnd(15) + '大规模(ms)'.padEnd(15) + '性能变化');
  console.log('-'.repeat(70));
  
  const queryTypes = ['getSubTasks', 'getSubTaskStats', 'getCompletedSubTasks'];
  queryTypes.forEach(queryType => {
    const mediumTime = results.queries.medium?.[queryType]?.avgTime || 0;
    const largeTime = results.queries.large?.[queryType]?.avgTime || 0;
    const change = largeTime > 0 && mediumTime > 0 ? 
      `${((largeTime - mediumTime) / mediumTime * 100).toFixed(1)}%` : 'N/A';
    
    console.log(
      queryType.padEnd(25) + 
      mediumTime.toString().padEnd(15) + 
      largeTime.toString().padEnd(15) + 
      change
    );
  });
  
  // 操作性能
  console.log('\n⚡ 操作性能:');
  if (results.operations) {
    console.log(`   批量创建50个子任务: ${results.operations.batchCreate}ms`);
    console.log(`   批量更新子任务: ${results.operations.batchUpdate}ms`);
    console.log(`   级联删除: ${results.operations.cascadeDelete}ms`);
  }
  
  // 性能评估
  console.log('\n📈 性能评估:');
  const avgQueryTime = Object.values(results.queries.large || {})
    .reduce((sum, query) => sum + (query.avgTime || 0), 0) / 
    Object.keys(results.queries.large || {}).length;
  
  if (avgQueryTime < 20) {
    console.log('   ✅ 查询性能优秀 (平均 < 20ms)');
  } else if (avgQueryTime < 50) {
    console.log('   ✅ 查询性能良好 (平均 < 50ms)');
  } else {
    console.log('   ⚠️  查询性能需要优化 (平均 > 50ms)');
  }
  
  if (results.operations?.batchCreate < 100) {
    console.log('   ✅ 批量操作性能优秀');
  } else {
    console.log('   ⚠️  批量操作性能需要优化');
  }
  
  // 保存详细报告
  const reportPath = path.join(process.cwd(), 'subtask-performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  
  // 清理测试数据库
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
    console.log('\n🧹 测试数据库已清理');
  }
  
  console.log('\n🎉 子任务系统性能测试完成！');
}

// 运行测试
runSubTaskPerformanceTests();
