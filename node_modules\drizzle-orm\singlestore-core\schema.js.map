{"version": 3, "sources": ["../../src/singlestore-core/schema.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport { type SingleStoreTableFn, singlestoreTableWithSchema } from './table.ts';\n/* import { type singlestoreView, singlestoreViewWithSchema } from './view.ts'; */\n\nexport class SingleStoreSchema<TName extends string = string> {\n\tstatic readonly [entityKind]: string = 'SingleStoreSchema';\n\n\tconstructor(\n\t\tpublic readonly schemaName: TName,\n\t) {}\n\n\ttable: SingleStoreTableFn<TName> = (name, columns, extraConfig) => {\n\t\treturn singlestoreTableWithSchema(name, columns, extraConfig, this.schemaName);\n\t};\n\t/*\n\tview = ((name, columns) => {\n\t\treturn singlestoreViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof singlestoreView; */\n}\n\n/** @deprecated - use `instanceof SingleStoreSchema` */\nexport function isSingleStoreSchema(obj: unknown): obj is SingleStoreSchema {\n\treturn is(obj, SingleStoreSchema);\n}\n\n/**\n * Create a SingleStore schema.\n * https://docs.singlestore.com/cloud/create-a-database/\n *\n * @param name singlestore use schema name\n * @returns SingleStore schema\n */\nexport function singlestoreDatabase<TName extends string>(name: TName) {\n\treturn new SingleStoreSchema(name);\n}\n\n/**\n * @see singlestoreDatabase\n */\nexport const singlestoreSchema = singlestoreDatabase;\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAC/B,SAAkC,kCAAkC;AAG7D,MAAM,kBAAiD;AAAA,EAG7D,YACiB,YACf;AADe;AAAA,EACd;AAAA,EAJH,QAAiB,UAAU,IAAY;AAAA,EAMvC,QAAmC,CAAC,MAAM,SAAS,gBAAgB;AAClE,WAAO,2BAA2B,MAAM,SAAS,aAAa,KAAK,UAAU;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAKD;AAGO,SAAS,oBAAoB,KAAwC;AAC3E,SAAO,GAAG,KAAK,iBAAiB;AACjC;AASO,SAAS,oBAA0C,MAAa;AACtE,SAAO,IAAI,kBAAkB,IAAI;AAClC;AAKO,MAAM,oBAAoB;", "names": []}