"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SettingsService = void 0;
const drizzle_orm_1 = require("drizzle-orm");
const db_1 = require("../../shared/db");
const utils_1 = require("../utils");
const { settings } = db_1.schema;
class SettingsService {
    constructor() {
        this.db = null;
    }
    async getDb() {
        if (!this.db) {
            this.db = await (0, db_1.getDatabase)();
        }
        return this.db;
    }
    // 获取设置值
    async getSetting(key) {
        try {
            const db = await this.getDb();
            const result = await db
                .select({ value: settings.value })
                .from(settings)
                .where((0, drizzle_orm_1.eq)(settings.key, key))
                .limit(1);
            return result[0]?.value || null;
        }
        catch (error) {
            (0, utils_1.log)('error', 'Failed to get setting:', error);
            return null;
        }
    }
    // 设置值
    async setSetting(key, value) {
        try {
            const now = Date.now();
            // 使用 INSERT OR REPLACE 语法
            await this.db
                .insert(settings)
                .values({
                key,
                value,
                updatedAt: now,
            })
                .onConflictDoUpdate({
                target: settings.key,
                set: {
                    value: value,
                    updatedAt: now,
                },
            });
            (0, utils_1.log)('info', `Setting updated: ${key} = ${value}`);
            return true;
        }
        catch (error) {
            (0, utils_1.log)('error', 'Failed to set setting:', error);
            return false;
        }
    }
    // 获取所有设置
    async getAllSettings() {
        try {
            const result = await this.db
                .select()
                .from(settings);
            const settingsMap = {};
            result.forEach((setting) => {
                settingsMap[setting.key] = setting.value;
            });
            return settingsMap;
        }
        catch (error) {
            (0, utils_1.log)('error', 'Failed to get all settings:', error);
            return {};
        }
    }
}
exports.SettingsService = SettingsService;
