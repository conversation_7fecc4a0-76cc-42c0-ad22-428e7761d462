{"version": 3, "sources": ["../../src/gel-core/utils.ts"], "sourcesContent": ["import { is } from '~/entity.ts';\nimport { SQL } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport { type Check, CheckBuilder } from './checks.ts';\nimport type { AnyGelColumn } from './columns/index.ts';\nimport { type ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { Index } from './indexes.ts';\nimport { IndexBuilder } from './indexes.ts';\nimport { GelPolicy } from './policies.ts';\nimport { type PrimaryKey, PrimaryKeyBuilder } from './primary-keys.ts';\nimport { GelTable } from './table.ts';\nimport { type UniqueConstraint, UniqueConstraintBuilder } from './unique-constraint.ts';\nimport type { GelViewBase } from './view-base.ts';\nimport { GelViewConfig } from './view-common.ts';\nimport { type GelMaterializedView, GelMaterializedViewConfig, type GelView } from './view.ts';\n\nexport function getTableConfig<TTable extends GelTable>(table: TTable) {\n\tconst columns = Object.values(table[Table.Symbol.Columns]);\n\tconst indexes: Index[] = [];\n\tconst checks: Check[] = [];\n\tconst primaryKeys: PrimaryKey[] = [];\n\tconst foreignKeys: ForeignKey[] = Object.values(table[GelTable.Symbol.InlineForeignKeys]);\n\tconst uniqueConstraints: UniqueConstraint[] = [];\n\tconst name = table[Table.Symbol.Name];\n\tconst schema = table[Table.Symbol.Schema];\n\tconst policies: GelPolicy[] = [];\n\tconst enableRLS: boolean = table[GelTable.Symbol.EnableRLS];\n\n\tconst extraConfigBuilder = table[GelTable.Symbol.ExtraConfigBuilder];\n\n\tif (extraConfigBuilder !== undefined) {\n\t\tconst extraConfig = extraConfigBuilder(table[Table.Symbol.ExtraConfigColumns]);\n\t\tconst extraValues = Array.isArray(extraConfig) ? extraConfig.flat(1) as any[] : Object.values(extraConfig);\n\t\tfor (const builder of extraValues) {\n\t\t\tif (is(builder, IndexBuilder)) {\n\t\t\t\tindexes.push(builder.build(table));\n\t\t\t} else if (is(builder, CheckBuilder)) {\n\t\t\t\tchecks.push(builder.build(table));\n\t\t\t} else if (is(builder, UniqueConstraintBuilder)) {\n\t\t\t\tuniqueConstraints.push(builder.build(table));\n\t\t\t} else if (is(builder, PrimaryKeyBuilder)) {\n\t\t\t\tprimaryKeys.push(builder.build(table));\n\t\t\t} else if (is(builder, ForeignKeyBuilder)) {\n\t\t\t\tforeignKeys.push(builder.build(table));\n\t\t\t} else if (is(builder, GelPolicy)) {\n\t\t\t\tpolicies.push(builder);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tcolumns,\n\t\tindexes,\n\t\tforeignKeys,\n\t\tchecks,\n\t\tprimaryKeys,\n\t\tuniqueConstraints,\n\t\tname,\n\t\tschema,\n\t\tpolicies,\n\t\tenableRLS,\n\t};\n}\n\nexport function extractUsedTable(table: GelTable | Subquery | GelViewBase | SQL): string[] {\n\tif (is(table, GelTable)) {\n\t\treturn [`${table[Table.Symbol.BaseName]}`];\n\t}\n\tif (is(table, Subquery)) {\n\t\treturn table._.usedTables ?? [];\n\t}\n\tif (is(table, SQL)) {\n\t\treturn table.usedTables ?? [];\n\t}\n\treturn [];\n}\n\nexport function getViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: GelView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[GelViewConfig],\n\t};\n}\n\nexport function getMaterializedViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: GelMaterializedView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[GelMaterializedViewConfig],\n\t};\n}\n\nexport type ColumnsWithTable<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyGelColumn<{ tableName: TTableName }>[],\n> = { [Key in keyof TColumns]: AnyGelColumn<{ tableName: TForeignTableName }> };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAAmB;AACnB,iBAAoB;AACpB,sBAAyB;AACzB,mBAAsB;AACtB,yBAA+B;AAC/B,oBAAyC;AAEzC,0BAAmD;AAEnD,qBAA6B;AAC7B,sBAA0B;AAC1B,0BAAmD;AACnD,IAAAA,gBAAyB;AACzB,+BAA+D;AAE/D,IAAAC,sBAA8B;AAC9B,kBAAkF;AAE3E,SAAS,eAAwC,OAAe;AACtE,QAAM,UAAU,OAAO,OAAO,MAAM,mBAAM,OAAO,OAAO,CAAC;AACzD,QAAM,UAAmB,CAAC;AAC1B,QAAM,SAAkB,CAAC;AACzB,QAAM,cAA4B,CAAC;AACnC,QAAM,cAA4B,OAAO,OAAO,MAAM,uBAAS,OAAO,iBAAiB,CAAC;AACxF,QAAM,oBAAwC,CAAC;AAC/C,QAAM,OAAO,MAAM,mBAAM,OAAO,IAAI;AACpC,QAAM,SAAS,MAAM,mBAAM,OAAO,MAAM;AACxC,QAAM,WAAwB,CAAC;AAC/B,QAAM,YAAqB,MAAM,uBAAS,OAAO,SAAS;AAE1D,QAAM,qBAAqB,MAAM,uBAAS,OAAO,kBAAkB;AAEnE,MAAI,uBAAuB,QAAW;AACrC,UAAM,cAAc,mBAAmB,MAAM,mBAAM,OAAO,kBAAkB,CAAC;AAC7E,UAAM,cAAc,MAAM,QAAQ,WAAW,IAAI,YAAY,KAAK,CAAC,IAAa,OAAO,OAAO,WAAW;AACzG,eAAW,WAAW,aAAa;AAClC,cAAI,kBAAG,SAAS,2BAAY,GAAG;AAC9B,gBAAQ,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAClC,eAAW,kBAAG,SAAS,0BAAY,GAAG;AACrC,eAAO,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACjC,eAAW,kBAAG,SAAS,gDAAuB,GAAG;AAChD,0BAAkB,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MAC5C,eAAW,kBAAG,SAAS,qCAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC,eAAW,kBAAG,SAAS,qCAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;AAAA,MACtC,eAAW,kBAAG,SAAS,yBAAS,GAAG;AAClC,iBAAS,KAAK,OAAO;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEO,SAAS,iBAAiB,OAA0D;AAC1F,UAAI,kBAAG,OAAO,sBAAQ,GAAG;AACxB,WAAO,CAAC,GAAG,MAAM,mBAAM,OAAO,QAAQ,CAAC,EAAE;AAAA,EAC1C;AACA,UAAI,kBAAG,OAAO,wBAAQ,GAAG;AACxB,WAAO,MAAM,EAAE,cAAc,CAAC;AAAA,EAC/B;AACA,UAAI,kBAAG,OAAO,cAAG,GAAG;AACnB,WAAO,MAAM,cAAc,CAAC;AAAA,EAC7B;AACA,SAAO,CAAC;AACT;AAEO,SAAS,cAGd,MAAiC;AAClC,SAAO;AAAA,IACN,GAAG,KAAK,iCAAc;AAAA,IACtB,GAAG,KAAK,iCAAa;AAAA,EACtB;AACD;AAEO,SAAS,0BAGd,MAA6C;AAC9C,SAAO;AAAA,IACN,GAAG,KAAK,iCAAc;AAAA,IACtB,GAAG,KAAK,qCAAyB;AAAA,EAClC;AACD;", "names": ["import_table", "import_view_common"]}