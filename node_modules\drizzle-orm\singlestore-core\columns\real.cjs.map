{"version": 3, "sources": ["../../../src/singlestore-core/columns/real.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\n\nexport type SingleStoreRealBuilderInitial<TName extends string> = SingleStoreRealBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreReal';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreRealBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreReal'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<\n\t\tT,\n\t\tSingleStoreRealConfig\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreRealBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreRealConfig | undefined) {\n\t\tsuper(name, 'number', 'SingleStoreReal');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreReal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreReal<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreReal<T extends ColumnBaseConfig<'number', 'SingleStoreReal'>>\n\textends SingleStoreColumnWithAutoIncrement<\n\t\tT,\n\t\tSingleStoreRealConfig\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreReal';\n\n\tprecision: number | undefined = this.config.precision;\n\tscale: number | undefined = this.config.scale;\n\n\tgetSQLType(): string {\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\treturn `real(${this.precision}, ${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\treturn 'real';\n\t\t} else {\n\t\t\treturn `real(${this.precision})`;\n\t\t}\n\t}\n}\n\nexport interface SingleStoreRealConfig {\n\tprecision?: number;\n\tscale?: number;\n}\n\nexport function real(): SingleStoreRealBuilderInitial<''>;\nexport function real(\n\tconfig?: SingleStoreRealConfig,\n): SingleStoreRealBuilderInitial<''>;\nexport function real<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreRealConfig,\n): SingleStoreRealBuilderInitial<TName>;\nexport function real(a?: string | SingleStoreRealConfig, b: SingleStoreRealConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreRealConfig>(a, b);\n\treturn new SingleStoreRealBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA8F;AAYvF,MAAM,+BACJ,wDAIT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA2C;AACvE,UAAM,MAAM,UAAU,iBAAiB;AACvC,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAAA,EAC7B;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBACJ,iDAIT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EAExC,aAAqB;AACpB,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,aAAO,QAAQ,KAAK,SAAS,KAAK,KAAK,KAAK;AAAA,IAC7C,WAAW,KAAK,cAAc,QAAW;AACxC,aAAO;AAAA,IACR,OAAO;AACN,aAAO,QAAQ,KAAK,SAAS;AAAA,IAC9B;AAAA,EACD;AACD;AAeO,SAAS,KAAK,GAAoC,IAA2B,CAAC,GAAG;AACvF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA8C,GAAG,CAAC;AAC3E,SAAO,IAAI,uBAAuB,MAAM,MAAM;AAC/C;", "names": []}