/**
 * 错误报告和日志记录系统
 * 提供统一的错误处理、报告和日志记录功能
 */

import type { ErrorInfo } from 'react'

export interface ErrorReport {
  id: string
  type: 'react' | 'javascript' | 'promise' | 'network'
  message: string
  stack?: string
  componentStack?: string
  timestamp: string
  userAgent: string
  url: string
  userId?: string
  sessionId: string
  buildVersion?: string
  environment: 'development' | 'production'
  severity: 'low' | 'medium' | 'high' | 'critical'
  context?: Record<string, any>
  retryCount?: number
}

export interface ErrorReportingConfig {
  enableConsoleLogging: boolean
  enableLocalStorage: boolean
  enableRemoteReporting: boolean
  maxLocalReports: number
  reportingEndpoint?: string
  apiKey?: string
  userId?: string
}

class ErrorReportingService {
  private config: ErrorReportingConfig
  private sessionId: string
  private reportQueue: ErrorReport[] = []

  constructor(config: Partial<ErrorReportingConfig> = {}) {
    this.config = {
      enableConsoleLogging: true,
      enableLocalStorage: true,
      enableRemoteReporting: false,
      maxLocalReports: 100,
      ...config,
    }
    
    this.sessionId = this.generateSessionId()
    this.setupGlobalErrorHandlers()
    this.loadStoredReports()
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private setupGlobalErrorHandlers() {
    // 捕获未处理的 JavaScript 错误
    window.addEventListener('error', (event) => {
      this.reportError({
        type: 'javascript',
        message: event.message,
        stack: event.error?.stack,
        severity: 'high',
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      })
    })

    // 捕获未处理的 Promise 拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        type: 'promise',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        severity: 'medium',
        context: {
          reason: event.reason,
        },
      })
    })
  }

  private loadStoredReports() {
    if (!this.config.enableLocalStorage) return

    try {
      const stored = localStorage.getItem('errorReports')
      if (stored) {
        this.reportQueue = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to load stored error reports:', error)
    }
  }

  private saveReportsToStorage() {
    if (!this.config.enableLocalStorage) return

    try {
      // 只保留最近的报告
      const reportsToSave = this.reportQueue.slice(-this.config.maxLocalReports)
      localStorage.setItem('errorReports', JSON.stringify(reportsToSave))
    } catch (error) {
      console.warn('Failed to save error reports to storage:', error)
    }
  }

  public reportError(errorData: Partial<ErrorReport>): string {
    const errorReport: ErrorReport = {
      id: this.generateErrorId(),
      type: 'javascript',
      message: 'Unknown error',
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: this.sessionId,
      environment: process.env.NODE_ENV as 'development' | 'production',
      severity: 'medium',
      ...errorData,
    }

    // 添加构建版本信息
    if (window.electronAPI?.app?.getVersion) {
      window.electronAPI.app.getVersion().then(version => {
        errorReport.buildVersion = version
      }).catch(() => {
        // 忽略错误
      })
    }

    // 添加到队列
    this.reportQueue.push(errorReport)

    // 控制台日志
    if (this.config.enableConsoleLogging) {
      console.error('Error reported:', errorReport)
    }

    // 保存到本地存储
    this.saveReportsToStorage()

    // 发送到远程服务
    if (this.config.enableRemoteReporting) {
      this.sendToRemoteService(errorReport)
    }

    // 发送到 Electron 主进程
    this.sendToMainProcess(errorReport)

    return errorReport.id
  }

  public reportReactError(error: Error, errorInfo: ErrorInfo, context?: Record<string, any>): string {
    return this.reportError({
      type: 'react',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      severity: 'high',
      context,
    })
  }

  private async sendToRemoteService(errorReport: ErrorReport) {
    if (!this.config.reportingEndpoint) return

    try {
      const response = await fetch(this.config.reportingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify(errorReport),
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      console.log('Error report sent successfully:', errorReport.id)
    } catch (error) {
      console.warn('Failed to send error report to remote service:', error)
      // 可以考虑重试机制
    }
  }

  private sendToMainProcess(errorReport: ErrorReport) {
    try {
      // 如果有 Electron API，发送到主进程进行日志记录
      if (window.electronAPI?.invoke) {
        window.electronAPI.invoke('error:report', errorReport)
          .catch((error: Error) => {
            console.warn('Failed to send error report to main process:', error)
          })
      }
    } catch (error) {
      console.warn('Failed to communicate with main process:', error)
    }
  }

  public getReports(): ErrorReport[] {
    return [...this.reportQueue]
  }

  public getReportById(id: string): ErrorReport | undefined {
    return this.reportQueue.find(report => report.id === id)
  }

  public clearReports(): void {
    this.reportQueue = []
    this.saveReportsToStorage()
  }

  public getReportsByType(type: ErrorReport['type']): ErrorReport[] {
    return this.reportQueue.filter(report => report.type === type)
  }

  public getReportsBySeverity(severity: ErrorReport['severity']): ErrorReport[] {
    return this.reportQueue.filter(report => report.severity === severity)
  }

  public exportReports(): string {
    return JSON.stringify(this.reportQueue, null, 2)
  }

  public updateConfig(newConfig: Partial<ErrorReportingConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  public getStats() {
    const reports = this.reportQueue
    const now = Date.now()
    const oneHour = 60 * 60 * 1000
    const oneDay = 24 * oneHour

    return {
      total: reports.length,
      lastHour: reports.filter(r => now - new Date(r.timestamp).getTime() < oneHour).length,
      lastDay: reports.filter(r => now - new Date(r.timestamp).getTime() < oneDay).length,
      byType: {
        react: reports.filter(r => r.type === 'react').length,
        javascript: reports.filter(r => r.type === 'javascript').length,
        promise: reports.filter(r => r.type === 'promise').length,
        network: reports.filter(r => r.type === 'network').length,
      },
      bySeverity: {
        low: reports.filter(r => r.severity === 'low').length,
        medium: reports.filter(r => r.severity === 'medium').length,
        high: reports.filter(r => r.severity === 'high').length,
        critical: reports.filter(r => r.severity === 'critical').length,
      },
    }
  }
}

// 创建全局实例
export const errorReporting = new ErrorReportingService({
  enableConsoleLogging: true,
  enableLocalStorage: true,
  enableRemoteReporting: process.env.NODE_ENV === 'production',
  maxLocalReports: 100,
})

// 导出便捷函数
export const reportError = (error: Error | string, context?: Record<string, any>) => {
  if (typeof error === 'string') {
    return errorReporting.reportError({
      message: error,
      context,
    })
  } else {
    return errorReporting.reportError({
      message: error.message,
      stack: error.stack,
      context,
    })
  }
}

export const reportReactError = (error: Error, errorInfo: ErrorInfo, context?: Record<string, any>) => {
  return errorReporting.reportReactError(error, errorInfo, context)
}

export default errorReporting
