import React, { useState } from 'react'
import { ChevronRight, ChevronDown, Plus } from 'lucide-react'
import { cn } from '../lib/utils'
import { TaskItem } from './TaskItem'
import { useHierarchicalTasks } from '../hooks/useTasks'
import type { TaskHierarchy } from '../../shared/types/task'

interface HierarchicalTaskItemProps {
  hierarchy: TaskHierarchy
  onToggleComplete: (id: string, completed: boolean) => void
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  onAddSubtask?: (parentId: string) => void
}

function HierarchicalTaskItem({ 
  hierarchy, 
  onToggleComplete, 
  onEdit, 
  onDelete,
  onAddSubtask 
}: HierarchicalTaskItemProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const { task, children, depth } = hierarchy
  const hasChildren = children.length > 0

  // 计算缩进
  const indentLevel = depth * 24 // 每层缩进24px

  return (
    <div className="w-full">
      {/* 任务项 */}
      <div 
        className={cn(
          "flex items-center gap-2 py-2 px-3 hover:bg-gray-50 rounded-lg group",
          "border-l-2 border-transparent",
          depth > 0 && "border-l-gray-200"
        )}
        style={{ marginLeft: `${indentLevel}px` }}
      >
        {/* 展开/收起按钮 */}
        {hasChildren ? (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex-shrink-0 w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded"
          >
            {isExpanded ? (
              <ChevronDown className="w-3 h-3 text-gray-500" />
            ) : (
              <ChevronRight className="w-3 h-3 text-gray-500" />
            )}
          </button>
        ) : (
          <div className="w-4 h-4 flex-shrink-0" />
        )}

        {/* 任务内容 */}
        <div className="flex-1 min-w-0">
          <TaskItem
            task={task}
            onToggleComplete={onToggleComplete}
            onEdit={onEdit}
            onDelete={onDelete}
            showIndent={false} // 我们已经通过外层容器处理缩进
          />
        </div>

        {/* 添加子任务按钮 */}
        {onAddSubtask && (
          <button
            onClick={() => onAddSubtask(task.id)}
            className="flex-shrink-0 w-6 h-6 flex items-center justify-center hover:bg-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity"
            title="添加子任务"
          >
            <Plus className="w-3 h-3 text-gray-500" />
          </button>
        )}
      </div>

      {/* 子任务 */}
      {hasChildren && isExpanded && (
        <div className="space-y-1">
          {children.map((childHierarchy) => (
            <HierarchicalTaskItem
              key={childHierarchy.task.id}
              hierarchy={childHierarchy}
              onToggleComplete={onToggleComplete}
              onEdit={onEdit}
              onDelete={onDelete}
              onAddSubtask={onAddSubtask}
            />
          ))}
        </div>
      )}
    </div>
  )
}

interface HierarchicalTaskListProps {
  onToggleComplete: (id: string, completed: boolean) => void
  onEdit: (id: string) => void
  onDelete: (id: string) => void
  onAddSubtask?: (parentId: string) => void
  filter?: 'all' | 'pending' | 'completed'
  searchQuery?: string
}

export function HierarchicalTaskList({
  onToggleComplete,
  onEdit,
  onDelete,
  onAddSubtask,
  filter = 'all',
  searchQuery = ''
}: HierarchicalTaskListProps) {
  const { data: hierarchies = [], isLoading, error } = useHierarchicalTasks()

  // 过滤和搜索逻辑
  const filteredHierarchies = hierarchies.filter(hierarchy => {
    // 递归检查任务层级是否匹配过滤条件
    const matchesFilter = (h: TaskHierarchy): boolean => {
      const task = h.task
      
      // 搜索匹配
      const matchesSearch = !searchQuery || 
        task.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (task.description && task.description.toLowerCase().includes(searchQuery.toLowerCase()))
      
      // 状态过滤
      const matchesStatus = filter === 'all' || 
        (filter === 'completed' && task.isCompleted) ||
        (filter === 'pending' && !task.isCompleted)
      
      // 如果当前任务匹配，返回true
      if (matchesSearch && matchesStatus) {
        return true
      }
      
      // 如果子任务中有匹配的，也返回true
      return h.children.some(child => matchesFilter(child))
    }

    return matchesFilter(hierarchy)
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">加载失败，请重试</div>
      </div>
    )
  }

  if (filteredHierarchies.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">
          {searchQuery ? '没有找到匹配的任务' : '暂无任务'}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-1">
      {filteredHierarchies.map((hierarchy) => (
        <HierarchicalTaskItem
          key={hierarchy.task.id}
          hierarchy={hierarchy}
          onToggleComplete={onToggleComplete}
          onEdit={onEdit}
          onDelete={onDelete}
          onAddSubtask={onAddSubtask}
        />
      ))}
    </div>
  )
}

export default HierarchicalTaskList
