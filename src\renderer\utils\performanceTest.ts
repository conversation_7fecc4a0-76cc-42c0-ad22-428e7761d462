/**
 * 性能测试工具
 * 用于测试批量操作的性能表现
 */

export interface PerformanceTestResult {
  operation: string
  itemCount: number
  duration: number
  averageTimePerItem: number
  memoryUsage?: {
    before: number
    after: number
    delta: number
  }
}

export class PerformanceTestRunner {
  private results: PerformanceTestResult[] = []

  /**
   * 测试批量选择操作性能
   */
  async testBatchSelection(taskIds: string[]): Promise<PerformanceTestResult> {
    const memoryBefore = this.getMemoryUsage()
    const startTime = performance.now()

    // 模拟批量选择操作
    const { useSelectionStore } = await import('../stores/selectionStore')
    const store = useSelectionStore.getState()
    
    store.selectAllTasks(taskIds)

    const endTime = performance.now()
    const memoryAfter = this.getMemoryUsage()
    
    const result: PerformanceTestResult = {
      operation: 'batch_selection',
      itemCount: taskIds.length,
      duration: endTime - startTime,
      averageTimePerItem: (endTime - startTime) / taskIds.length,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        delta: memoryAfter - memoryBefore
      }
    }

    this.results.push(result)
    return result
  }

  /**
   * 测试批量删除操作性能
   */
  async testBatchDelete(taskIds: string[]): Promise<PerformanceTestResult> {
    const memoryBefore = this.getMemoryUsage()
    const startTime = performance.now()

    try {
      // 模拟批量删除API调用
      await window.electronAPI.task.batchSoftDelete(taskIds)
    } catch (error) {
      console.warn('Performance test: API call failed, continuing with timing measurement')
    }

    const endTime = performance.now()
    const memoryAfter = this.getMemoryUsage()
    
    const result: PerformanceTestResult = {
      operation: 'batch_delete',
      itemCount: taskIds.length,
      duration: endTime - startTime,
      averageTimePerItem: (endTime - startTime) / taskIds.length,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        delta: memoryAfter - memoryBefore
      }
    }

    this.results.push(result)
    return result
  }

  /**
   * 测试批量恢复操作性能
   */
  async testBatchRestore(taskIds: string[]): Promise<PerformanceTestResult> {
    const memoryBefore = this.getMemoryUsage()
    const startTime = performance.now()

    try {
      await window.electronAPI.task.batchRestore(taskIds)
    } catch (error) {
      console.warn('Performance test: API call failed, continuing with timing measurement')
    }

    const endTime = performance.now()
    const memoryAfter = this.getMemoryUsage()
    
    const result: PerformanceTestResult = {
      operation: 'batch_restore',
      itemCount: taskIds.length,
      duration: endTime - startTime,
      averageTimePerItem: (endTime - startTime) / taskIds.length,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        delta: memoryAfter - memoryBefore
      }
    }

    this.results.push(result)
    return result
  }

  /**
   * 测试大量任务渲染性能
   */
  async testTaskListRendering(taskCount: number): Promise<PerformanceTestResult> {
    const memoryBefore = this.getMemoryUsage()
    const startTime = performance.now()

    // 生成测试任务数据
    const testTasks = Array.from({ length: taskCount }, (_, index) => ({
      id: `test-task-${index}`,
      content: `Test Task ${index + 1}`,
      isCompleted: Math.random() > 0.5,
      priority: Math.floor(Math.random() * 3) + 1,
      dueDate: Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000,
      orderIndex: index * 1000,
      createdAt: Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000,
      parentTaskId: null,
      taskType: 'task' as const,
      description: null,
      estimatedDuration: null,
      actualDuration: null,
      progress: 0,
      deletedAt: null,
    }))

    // 模拟渲染时间（实际测试中需要真实渲染）
    await new Promise(resolve => setTimeout(resolve, Math.min(taskCount / 10, 100)))

    const endTime = performance.now()
    const memoryAfter = this.getMemoryUsage()
    
    const result: PerformanceTestResult = {
      operation: 'task_list_rendering',
      itemCount: taskCount,
      duration: endTime - startTime,
      averageTimePerItem: (endTime - startTime) / taskCount,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        delta: memoryAfter - memoryBefore
      }
    }

    this.results.push(result)
    return result
  }

  /**
   * 运行完整的性能测试套件
   */
  async runFullTestSuite(): Promise<PerformanceTestResult[]> {
    console.log('🚀 开始性能测试...')

    const testSizes = [10, 50, 100, 500, 1000]
    const allResults: PerformanceTestResult[] = []

    for (const size of testSizes) {
      console.log(`📊 测试 ${size} 个任务的性能...`)
      
      const taskIds = Array.from({ length: size }, (_, i) => `task-${i}`)

      // 测试批量选择
      const selectionResult = await this.testBatchSelection(taskIds)
      allResults.push(selectionResult)

      // 测试批量删除
      const deleteResult = await this.testBatchDelete(taskIds)
      allResults.push(deleteResult)

      // 测试批量恢复
      const restoreResult = await this.testBatchRestore(taskIds)
      allResults.push(restoreResult)

      // 测试渲染性能
      const renderResult = await this.testTaskListRendering(size)
      allResults.push(renderResult)

      // 等待一段时间以避免测试之间的干扰
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    this.results.push(...allResults)
    this.printResults()
    
    return allResults
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024 // MB
    }
    return 0
  }

  /**
   * 打印测试结果
   */
  printResults(): void {
    console.log('\n📈 性能测试结果:')
    console.log('=' .repeat(80))
    
    const groupedResults = this.groupResultsByOperation()
    
    Object.entries(groupedResults).forEach(([operation, results]) => {
      console.log(`\n🔍 ${operation.toUpperCase()}:`)
      console.log('-'.repeat(40))
      
      results.forEach(result => {
        console.log(`  📦 ${result.itemCount} 项: ${result.duration.toFixed(2)}ms (平均 ${result.averageTimePerItem.toFixed(3)}ms/项)`)
        if (result.memoryUsage) {
          console.log(`     💾 内存: ${result.memoryUsage.delta.toFixed(2)}MB`)
        }
      })
    })

    // 性能建议
    this.printPerformanceRecommendations()
  }

  /**
   * 按操作类型分组结果
   */
  private groupResultsByOperation(): Record<string, PerformanceTestResult[]> {
    return this.results.reduce((groups, result) => {
      if (!groups[result.operation]) {
        groups[result.operation] = []
      }
      groups[result.operation].push(result)
      return groups
    }, {} as Record<string, PerformanceTestResult[]>)
  }

  /**
   * 打印性能建议
   */
  private printPerformanceRecommendations(): void {
    console.log('\n💡 性能建议:')
    console.log('-'.repeat(40))

    const slowOperations = this.results.filter(r => r.averageTimePerItem > 1) // 超过1ms每项
    
    if (slowOperations.length === 0) {
      console.log('✅ 所有操作性能良好!')
    } else {
      slowOperations.forEach(op => {
        console.log(`⚠️  ${op.operation} 操作较慢 (${op.averageTimePerItem.toFixed(3)}ms/项)`)
        
        if (op.operation === 'batch_selection' && op.averageTimePerItem > 0.5) {
          console.log('   建议: 考虑使用虚拟化或分页来处理大量选择')
        }
        
        if (op.operation === 'task_list_rendering' && op.averageTimePerItem > 2) {
          console.log('   建议: 实现虚拟滚动来优化大列表渲染')
        }
        
        if (op.memoryUsage && op.memoryUsage.delta > 50) {
          console.log('   建议: 检查内存泄漏，优化数据结构')
        }
      })
    }
  }

  /**
   * 清除测试结果
   */
  clearResults(): void {
    this.results = []
  }

  /**
   * 获取所有测试结果
   */
  getResults(): PerformanceTestResult[] {
    return [...this.results]
  }
}

// 导出单例实例
export const performanceTestRunner = new PerformanceTestRunner()

// 开发环境下暴露到全局对象，方便调试
if (process.env.NODE_ENV === 'development') {
  (window as any).performanceTestRunner = performanceTestRunner
}
