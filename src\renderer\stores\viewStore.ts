import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export type ViewType = 'today' | 'important' | 'planned' | 'all' | 'completed'

export interface ViewState {
  activeView: ViewType
  setActiveView: (view: ViewType) => void
  
  // 搜索状态
  searchQuery: string
  setSearchQuery: (query: string) => void
  
  // 排序状态
  sortBy: 'custom' | 'priority' | 'dueDate' | 'created' | 'alphabetical'
  setSortBy: (sortBy: ViewState['sortBy']) => void
  
  // 显示选项
  showCompleted: boolean
  setShowCompleted: (show: boolean) => void
  
  // 任务输入状态
  isTaskInputFocused: boolean
  setTaskInputFocused: (focused: boolean) => void
}

export const useViewStore = create<ViewState>()(
  persist(
    (set) => ({
      activeView: 'today',
      setActiveView: (view) => set({ activeView: view }),
      
      searchQuery: '',
      setSearchQuery: (query) => set({ searchQuery: query }),
      
      sortBy: 'custom',
      setSortBy: (sortBy) => set({ sortBy }),
      
      showCompleted: true,
      setShowCompleted: (show) => set({ showCompleted: show }),
      
      isTaskInputFocused: false,
      setTaskInputFocused: (focused) => set({ isTaskInputFocused: focused }),
    }),
    {
      name: 'lingan-view-store',
      partialize: (state) => ({
        activeView: state.activeView,
        sortBy: state.sortBy,
        showCompleted: state.showCompleted,
      }),
    }
  )
)
