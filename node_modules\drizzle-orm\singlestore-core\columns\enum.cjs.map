{"version": 3, "sources": ["../../../src/singlestore-core/columns/enum.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tGeneratedColumnConfig,\n\tHasGenerated,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport type { SQL } from '~/sql/index.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreEnumColumnBuilderInitial<TName extends string, TEnum extends [string, ...string[]]> =\n\tSingleStoreEnumColumnBuilder<{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'SingleStoreEnumColumn';\n\t\tdata: TEnum[number];\n\t\tdriverParam: string;\n\t\tenumValues: TEnum;\n\t\tgenerated: undefined;\n\t}>;\n\nexport class SingleStoreEnumColumnBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreEnumColumn'>>\n\textends SingleStoreColumnBuilder<T, { enumValues: T['enumValues'] }>\n{\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\toverride generatedAlwaysAs(\n\t\tas: SQL<unknown> | (() => SQL) | T['data'],\n\t\tconfig?: Partial<GeneratedColumnConfig<unknown>>,\n\t): HasGenerated<this, {}> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n\tstatic override readonly [entityKind]: string = 'SingleStoreEnumColumnBuilder';\n\n\tconstructor(name: T['name'], values: T['enumValues']) {\n\t\tsuper(name, 'string', 'SingleStoreEnumColumn');\n\t\tthis.config.enumValues = values;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreEnumColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }> {\n\t\treturn new SingleStoreEnumColumn<MakeColumnConfig<T, TTableName> & { enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreEnumColumn<T extends ColumnBaseConfig<'string', 'SingleStoreEnumColumn'>>\n\textends SingleStoreColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreEnumColumn';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn `enum(${this.enumValues!.map((value) => `'${value}'`).join(',')})`;\n\t}\n}\n\nexport function singlestoreEnum<U extends string, T extends Readonly<[U, ...U[]]>>(\n\tvalues: T | Writable<T>,\n): SingleStoreEnumColumnBuilderInitial<'', Writable<T>>;\nexport function singlestoreEnum<TName extends string, U extends string, T extends Readonly<[U, ...U[]]>>(\n\tname: TName,\n\tvalues: T | Writable<T>,\n): SingleStoreEnumColumnBuilderInitial<TName, Writable<T>>;\nexport function singlestoreEnum(\n\ta?: string | readonly [string, ...string[]] | [string, ...string[]],\n\tb?: readonly [string, ...string[]] | [string, ...string[]],\n): any {\n\tconst { name, config: values } = getColumnNameAndConfig<readonly [string, ...string[]] | [string, ...string[]]>(a, b);\n\n\tif (values.length === 0) {\n\t\tthrow new Error(`You have an empty array for \"${name}\" enum values`);\n\t}\n\n\treturn new SingleStoreEnumColumnBuilder(name, values as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,oBAA2B;AAG3B,mBAAsD;AACtD,oBAA4D;AAarD,MAAM,qCACJ,uCACT;AAAA;AAAA,EAEU,kBACR,IACA,QACyB;AACzB,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAAA,EACA,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAyB;AACrD,UAAM,MAAM,UAAU,uBAAuB;AAC7C,SAAK,OAAO,aAAa;AAAA,EAC1B;AAAA;AAAA,EAGS,MACR,OAC2F;AAC3F,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,8BACJ,gCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,QAAQ,KAAK,WAAY,IAAI,CAAC,UAAU,IAAI,KAAK,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,EACvE;AACD;AASO,SAAS,gBACf,GACA,GACM;AACN,QAAM,EAAE,MAAM,QAAQ,OAAO,QAAI,qCAA+E,GAAG,CAAC;AAEpH,MAAI,OAAO,WAAW,GAAG;AACxB,UAAM,IAAI,MAAM,gCAAgC,IAAI,eAAe;AAAA,EACpE;AAEA,SAAO,IAAI,6BAA6B,MAAM,MAAa;AAC5D;", "names": []}