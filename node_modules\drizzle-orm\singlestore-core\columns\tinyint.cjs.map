{"version": 3, "sources": ["../../../src/singlestore-core/columns/tinyint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\nimport type { SingleStoreIntConfig } from './int.ts';\n\nexport type SingleStoreTinyIntBuilderInitial<TName extends string> = SingleStoreTinyIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreTinyInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreTinyIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreTinyInt'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreTinyIntBuilder';\n\n\tconstructor(name: T['name'], config?: SingleStoreIntConfig) {\n\t\tsuper(name, 'number', 'SingleStoreTinyInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreTinyInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreTinyInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreTinyInt<T extends ColumnBaseConfig<'number', 'SingleStoreTinyInt'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreTinyInt';\n\n\tgetSQLType(): string {\n\t\treturn `tinyint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function tinyint(): SingleStoreTinyIntBuilderInitial<''>;\nexport function tinyint(\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreTinyIntBuilderInitial<''>;\nexport function tinyint<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreTinyIntBuilderInitial<TName>;\nexport function tinyint(a?: string | SingleStoreIntConfig, b?: SingleStoreIntConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreIntConfig>(a, b);\n\treturn new SingleStoreTinyIntBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA8F;AAavF,MAAM,kCACJ,wDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA+B;AAC3D,UAAM,MAAM,UAAU,oBAAoB;AAC1C,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BACJ,iDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,UAAU,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACzD;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAUO,SAAS,QAAQ,GAAmC,GAA0B;AACpF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,0BAA0B,MAAM,MAAM;AAClD;", "names": []}