import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { UndoActionPanel } from '../UndoActionPanel'
import { useSelection } from '../../../stores/selectionStore'

// Mock dependencies
vi.mock('../../../stores/selectionStore')
vi.mock('../../../components/ui/toast')

// Mock window.electronAPI
const mockElectronAPI = {
  task: {
    restore: vi.fn(),
    batchRestore: vi.fn(),
  }
}

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
})

const mockUseSelection = vi.mocked(useSelection)

describe('UndoActionPanel', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Default mock implementation
    mockUseSelection.mockReturnValue({
      recentlyDeletedTasks: [],
      hasUndoableTasks: false,
      removeDeletedTask: vi.fn(),
      clearDeletedTasks: vi.fn(),
      selectedCount: 0,
      isSelectionMode: false,
      selectedTaskIds: [],
      isTaskSelected: vi.fn(),
      toggleTaskSelection: vi.fn(),
      selectAllTasks: vi.fn(),
      clearSelection: vi.fn(),
      enterSelectionMode: vi.fn(),
      exitSelectionMode: vi.fn(),
      toggleSelectionMode: vi.fn(),
      addDeletedTask: vi.fn(),
      getUndoableTask: vi.fn(),
    })
  })

  it('should not render when no undoable tasks', () => {
    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: false,
      recentlyDeletedTasks: [],
    })

    render(<UndoActionPanel />)
    
    expect(screen.queryByText(/最近删除/)).not.toBeInTheDocument()
  })

  it('should render when there are undoable tasks', () => {
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000, // 5 seconds ago
      },
    ]

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
    })

    render(<UndoActionPanel />)
    
    expect(screen.getByText('最近删除 (1)')).toBeInTheDocument()
    expect(screen.getByText('Deleted Task 1')).toBeInTheDocument()
    expect(screen.getByText('撤销')).toBeInTheDocument()
  })

  it('should display multiple deleted tasks', () => {
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000,
      },
      {
        id: 'task2',
        content: 'Deleted Task 2',
        deletedAt: Date.now() - 3000,
      },
    ]

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
    })

    render(<UndoActionPanel />)
    
    expect(screen.getByText('最近删除 (2)')).toBeInTheDocument()
    expect(screen.getByText('Deleted Task 1')).toBeInTheDocument()
    expect(screen.getByText('Deleted Task 2')).toBeInTheDocument()
    expect(screen.getByText('撤销全部 (2)')).toBeInTheDocument()
  })

  it('should handle individual task undo', async () => {
    const mockRemoveDeletedTask = vi.fn()
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000,
      },
    ]

    mockElectronAPI.task.restore.mockResolvedValue(true)

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
      removeDeletedTask: mockRemoveDeletedTask,
    })

    render(<UndoActionPanel />)
    
    const undoButton = screen.getByText('撤销')
    fireEvent.click(undoButton)

    await waitFor(() => {
      expect(mockElectronAPI.task.restore).toHaveBeenCalledWith('task1')
      expect(mockRemoveDeletedTask).toHaveBeenCalledWith('task1')
    })
  })

  it('should handle undo all tasks', async () => {
    const mockClearDeletedTasks = vi.fn()
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000,
      },
      {
        id: 'task2',
        content: 'Deleted Task 2',
        deletedAt: Date.now() - 3000,
      },
    ]

    mockElectronAPI.task.batchRestore.mockResolvedValue({
      restoredCount: 2,
      restoredTaskIds: ['task1', 'task2'],
    })

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
      clearDeletedTasks: mockClearDeletedTasks,
    })

    render(<UndoActionPanel />)
    
    const undoAllButton = screen.getByText('撤销全部 (2)')
    fireEvent.click(undoAllButton)

    await waitFor(() => {
      expect(mockElectronAPI.task.batchRestore).toHaveBeenCalledWith(['task1', 'task2'])
      expect(mockClearDeletedTasks).toHaveBeenCalled()
    })
  })

  it('should handle dismiss action', () => {
    const mockClearDeletedTasks = vi.fn()
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000,
      },
    ]

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
      clearDeletedTasks: mockClearDeletedTasks,
    })

    render(<UndoActionPanel />)
    
    const dismissButton = screen.getByRole('button', { name: '' }) // X button
    fireEvent.click(dismissButton)

    expect(mockClearDeletedTasks).toHaveBeenCalled()
  })

  it('should display countdown timer', () => {
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000, // 5 seconds ago, so 25 seconds remaining
      },
    ]

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
    })

    render(<UndoActionPanel />)
    
    // Should show remaining time (approximately 25 seconds)
    expect(screen.getByText(/2[0-9]s/)).toBeInTheDocument()
  })

  it('should handle API errors gracefully', async () => {
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000,
      },
    ]

    mockElectronAPI.task.restore.mockRejectedValue(new Error('API Error'))

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
    })

    render(<UndoActionPanel />)
    
    const undoButton = screen.getByText('撤销')
    fireEvent.click(undoButton)

    await waitFor(() => {
      expect(mockElectronAPI.task.restore).toHaveBeenCalledWith('task1')
      // Error should be handled gracefully (would show error toast in real app)
    })
  })

  it('should not show undo all button for single task', () => {
    const mockDeletedTasks = [
      {
        id: 'task1',
        content: 'Deleted Task 1',
        deletedAt: Date.now() - 5000,
      },
    ]

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      hasUndoableTasks: true,
      recentlyDeletedTasks: mockDeletedTasks,
    })

    render(<UndoActionPanel />)
    
    expect(screen.queryByText(/撤销全部/)).not.toBeInTheDocument()
  })
})
