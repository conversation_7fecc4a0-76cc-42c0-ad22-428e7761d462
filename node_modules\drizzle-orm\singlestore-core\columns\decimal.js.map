{"version": 3, "sources": ["../../../src/singlestore-core/columns/decimal.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\n\nexport type SingleStoreDecimalBuilderInitial<TName extends string> = SingleStoreDecimalBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreDecimal';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDecimalBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'SingleStoreDecimal'>,\n> extends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreDecimalConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDecimalBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreDecimalConfig | undefined) {\n\t\tsuper(name, 'string', 'SingleStoreDecimal');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDecimal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDecimal<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDecimal<T extends ColumnBaseConfig<'string', 'SingleStoreDecimal'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreDecimalConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDecimal';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\toverride mapFromDriverValue(value: unknown): string {\n\t\t// For RQBv2\n\t\tif (typeof value === 'string') return value;\n\n\t\treturn String(value);\n\t}\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `decimal(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'decimal';\n\t\t} else {\n\t\t\ttype += `decimal(${this.precision})`;\n\t\t}\n\t\ttype = type === 'decimal(10,0)' || type === 'decimal(10)' ? 'decimal' : type;\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport type SingleStoreDecimalNumberBuilderInitial<TName extends string> = SingleStoreDecimalNumberBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreDecimalNumber';\n\tdata: number;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDecimalNumberBuilder<\n\tT extends ColumnBuilderBaseConfig<'number', 'SingleStoreDecimalNumber'>,\n> extends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreDecimalConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDecimalNumberBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreDecimalConfig | undefined) {\n\t\tsuper(name, 'number', 'SingleStoreDecimalNumber');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDecimalNumber<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDecimalNumber<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDecimalNumber<T extends ColumnBaseConfig<'number', 'SingleStoreDecimalNumber'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreDecimalConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDecimalNumber';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\toverride mapFromDriverValue(value: unknown): number {\n\t\tif (typeof value === 'number') return value;\n\n\t\treturn Number(value);\n\t}\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `decimal(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'decimal';\n\t\t} else {\n\t\t\ttype += `decimal(${this.precision})`;\n\t\t}\n\t\ttype = type === 'decimal(10,0)' || type === 'decimal(10)' ? 'decimal' : type;\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport type SingleStoreDecimalBigIntBuilderInitial<TName extends string> = SingleStoreDecimalBigIntBuilder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'SingleStoreDecimalBigInt';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreDecimalBigIntBuilder<\n\tT extends ColumnBuilderBaseConfig<'bigint', 'SingleStoreDecimalBigInt'>,\n> extends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreDecimalConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreDecimalBigIntBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreDecimalConfig | undefined) {\n\t\tsuper(name, 'bigint', 'SingleStoreDecimalBigInt');\n\t\tthis.config.precision = config?.precision;\n\t\tthis.config.scale = config?.scale;\n\t\tthis.config.unsigned = config?.unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreDecimalBigInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreDecimalBigInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreDecimalBigInt<T extends ColumnBaseConfig<'bigint', 'SingleStoreDecimalBigInt'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreDecimalConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreDecimalBigInt';\n\n\treadonly precision: number | undefined = this.config.precision;\n\treadonly scale: number | undefined = this.config.scale;\n\treadonly unsigned: boolean | undefined = this.config.unsigned;\n\n\toverride mapFromDriverValue = BigInt;\n\n\toverride mapToDriverValue = String;\n\n\tgetSQLType(): string {\n\t\tlet type = '';\n\t\tif (this.precision !== undefined && this.scale !== undefined) {\n\t\t\ttype += `decimal(${this.precision},${this.scale})`;\n\t\t} else if (this.precision === undefined) {\n\t\t\ttype += 'decimal';\n\t\t} else {\n\t\t\ttype += `decimal(${this.precision})`;\n\t\t}\n\t\ttype = type === 'decimal(10,0)' || type === 'decimal(10)' ? 'decimal' : type;\n\t\treturn this.unsigned ? `${type} unsigned` : type;\n\t}\n}\n\nexport interface SingleStoreDecimalConfig<T extends 'string' | 'number' | 'bigint' = 'string' | 'number' | 'bigint'> {\n\tprecision?: number;\n\tscale?: number;\n\tunsigned?: boolean;\n\tmode?: T;\n}\n\nexport function decimal(): SingleStoreDecimalBuilderInitial<''>;\nexport function decimal<TMode extends 'string' | 'number' | 'bigint'>(\n\tconfig: SingleStoreDecimalConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? SingleStoreDecimalNumberBuilderInitial<''>\n\t: Equal<TMode, 'bigint'> extends true ? SingleStoreDecimalBigIntBuilderInitial<''>\n\t: SingleStoreDecimalBuilderInitial<''>;\nexport function decimal<TName extends string, TMode extends 'string' | 'number' | 'bigint'>(\n\tname: TName,\n\tconfig?: SingleStoreDecimalConfig<TMode>,\n): Equal<TMode, 'number'> extends true ? SingleStoreDecimalNumberBuilderInitial<TName>\n\t: Equal<TMode, 'bigint'> extends true ? SingleStoreDecimalBigIntBuilderInitial<TName>\n\t: SingleStoreDecimalBuilderInitial<TName>;\nexport function decimal(a?: string | SingleStoreDecimalConfig, b: SingleStoreDecimalConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreDecimalConfig>(a, b);\n\tconst mode = config?.mode;\n\treturn mode === 'number'\n\t\t? new SingleStoreDecimalNumberBuilder(name, config)\n\t\t: mode === 'bigint'\n\t\t? new SingleStoreDecimalBigIntBuilder(name, config)\n\t\t: new SingleStoreDecimalBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAqB,8BAA8B;AACnD,SAAS,2CAA2C,0CAA0C;AAYvF,MAAM,kCAEH,0CAAuE;AAAA,EAChF,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA8C;AAC1E,UAAM,MAAM,UAAU,oBAAoB;AAC1C,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OACsD;AACtD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BACJ,mCACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAE5C,mBAAmB,OAAwB;AAEnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,WAAW,KAAK,SAAS;AAAA,IAClC;AACA,WAAO,SAAS,mBAAmB,SAAS,gBAAgB,YAAY;AACxE,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAYO,MAAM,wCAEH,0CAAuE;AAAA,EAChF,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA8C;AAC1E,UAAM,MAAM,UAAU,0BAA0B;AAChD,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC4D;AAC5D,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,iCACJ,mCACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAE5C,mBAAmB,OAAwB;AACnD,QAAI,OAAO,UAAU,SAAU,QAAO;AAEtC,WAAO,OAAO,KAAK;AAAA,EACpB;AAAA,EAES,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,WAAW,KAAK,SAAS;AAAA,IAClC;AACA,WAAO,SAAS,mBAAmB,SAAS,gBAAgB,YAAY;AACxE,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAYO,MAAM,wCAEH,0CAAuE;AAAA,EAChF,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA8C;AAC1E,UAAM,MAAM,UAAU,0BAA0B;AAChD,SAAK,OAAO,YAAY,QAAQ;AAChC,SAAK,OAAO,QAAQ,QAAQ;AAC5B,SAAK,OAAO,WAAW,QAAQ;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC4D;AAC5D,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,iCACJ,mCACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEvC,YAAgC,KAAK,OAAO;AAAA,EAC5C,QAA4B,KAAK,OAAO;AAAA,EACxC,WAAgC,KAAK,OAAO;AAAA,EAE5C,qBAAqB;AAAA,EAErB,mBAAmB;AAAA,EAE5B,aAAqB;AACpB,QAAI,OAAO;AACX,QAAI,KAAK,cAAc,UAAa,KAAK,UAAU,QAAW;AAC7D,cAAQ,WAAW,KAAK,SAAS,IAAI,KAAK,KAAK;AAAA,IAChD,WAAW,KAAK,cAAc,QAAW;AACxC,cAAQ;AAAA,IACT,OAAO;AACN,cAAQ,WAAW,KAAK,SAAS;AAAA,IAClC;AACA,WAAO,SAAS,mBAAmB,SAAS,gBAAgB,YAAY;AACxE,WAAO,KAAK,WAAW,GAAG,IAAI,cAAc;AAAA,EAC7C;AACD;AAqBO,SAAS,QAAQ,GAAuC,IAA8B,CAAC,GAAG;AAChG,QAAM,EAAE,MAAM,OAAO,IAAI,uBAAiD,GAAG,CAAC;AAC9E,QAAM,OAAO,QAAQ;AACrB,SAAO,SAAS,WACb,IAAI,gCAAgC,MAAM,MAAM,IAChD,SAAS,WACT,IAAI,gCAAgC,MAAM,MAAM,IAChD,IAAI,0BAA0B,MAAM,MAAM;AAC9C;", "names": []}