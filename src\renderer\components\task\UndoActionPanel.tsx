import React, { useState, useEffect } from 'react'
import { Undo2, X, Clock, Trash2 } from 'lucide-react'
import { Button } from '../ui/button'
import { useSelection, type DeletedTaskInfo } from '../../stores/selectionStore'
import { useToastActions } from '../ui/toast'
import { useRestoreTask } from '../../hooks/useTasks'
import { cn } from '../../lib/utils'

interface UndoActionPanelProps {
  className?: string
}

export function UndoActionPanel({ className }: UndoActionPanelProps) {
  const [isVisible, setIsVisible] = useState(false)
  const {
    recentlyDeletedTasks,
    hasUndoableTasks,
    removeDeletedTask,
    clearDeletedTasks,
    addDeletedTask
  } = useSelection()
  const { showSuccess, showError } = useToastActions()
  const restoreTask = useRestoreTask()

  // 显示/隐藏面板
  useEffect(() => {
    setIsVisible(hasUndoableTasks)
  }, [hasUndoableTasks])

  const handleUndoTask = async (taskInfo: DeletedTaskInfo) => {
    try {
      // 先从列表中移除，防止重复点击
      removeDeletedTask(taskInfo.id)

      await restoreTask.mutateAsync(taskInfo.id)
      showSuccess('撤销成功', `"${taskInfo.content}" 已恢复`)
    } catch (error) {
      console.error('Failed to restore task:', error)

      // 如果恢复失败，提供更详细的错误信息
      let errorMessage = '无法恢复已删除的任务'
      if (error instanceof Error) {
        if (error.message.includes('任务不存在或未被删除')) {
          errorMessage = '该任务可能已经被恢复或不存在'
        } else {
          errorMessage = error.message
        }
      }

      showError('撤销失败', errorMessage)

      // 如果是因为任务已经恢复导致的错误，不需要重新添加到列表
      if (!(error instanceof Error && error.message.includes('任务不存在或未被删除'))) {
        // 恢复失败时，重新添加到列表
        addDeletedTask(taskInfo)
      }
    }
  }

  const handleUndoAll = async () => {
    const tasksToRestore = [...recentlyDeletedTasks] // 创建副本
    const taskIds = tasksToRestore.map(task => task.id)

    try {
      // 先清空列表，防止重复操作
      clearDeletedTasks()

      await window.electronAPI.task.batchRestore(taskIds)
      showSuccess('批量撤销成功', `已恢复 ${taskIds.length} 个任务`)
    } catch (error) {
      console.error('Failed to batch restore tasks:', error)

      // 批量恢复失败时，重新添加到列表
      tasksToRestore.forEach(task => addDeletedTask(task))

      let errorMessage = '无法恢复部分任务'
      if (error instanceof Error) {
        errorMessage = error.message
      }

      showError('批量撤销失败', errorMessage)
    }
  }

  const handleDismiss = () => {
    clearDeletedTasks()
  }

  const formatTimeRemaining = (deletedAt: number) => {
    const remaining = 30000 - (Date.now() - deletedAt) // 30秒撤销窗口
    const seconds = Math.max(0, Math.ceil(remaining / 1000))
    return `${seconds}s`
  }

  if (!isVisible || recentlyDeletedTasks.length === 0) {
    return null
  }

  return (
    <div className={cn(
      "fixed top-4 right-4 z-50 max-w-sm",
      "animate-in slide-in-from-top-full duration-300",
      className
    )}>
      <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Trash2 className="h-4 w-4 text-red-600" />
            <span className="text-sm font-medium text-gray-900">
              最近删除 ({recentlyDeletedTasks.length})
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="p-1 h-6 w-6"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-2 max-h-48 overflow-y-auto">
          {recentlyDeletedTasks.map((taskInfo) => (
            <div
              key={taskInfo.id}
              className="flex items-center justify-between p-2 bg-gray-50 rounded-md"
            >
              <div className="flex-1 min-w-0">
                <p className="text-sm text-gray-900 truncate">
                  {taskInfo.content}
                </p>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  {formatTimeRemaining(taskInfo.deletedAt)}
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleUndoTask(taskInfo)}
                className="ml-2 h-7 px-2 text-xs"
              >
                <Undo2 className="h-3 w-3 mr-1" />
                撤销
              </Button>
            </div>
          ))}
        </div>

        {recentlyDeletedTasks.length > 1 && (
          <div className="pt-2 border-t border-gray-200">
            <Button
              variant="outline"
              size="sm"
              onClick={handleUndoAll}
              className="w-full"
            >
              <Undo2 className="h-4 w-4 mr-2" />
              撤销全部 ({recentlyDeletedTasks.length})
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

// 倒计时组件
interface CountdownTimerProps {
  deletedAt: number
  onExpire: () => void
}

function CountdownTimer({ deletedAt, onExpire }: CountdownTimerProps) {
  const [timeRemaining, setTimeRemaining] = useState(0)

  useEffect(() => {
    const updateTimer = () => {
      const remaining = 30000 - (Date.now() - deletedAt)
      if (remaining <= 0) {
        onExpire()
        return
      }
      setTimeRemaining(remaining)
    }

    updateTimer()
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [deletedAt, onExpire])

  const seconds = Math.ceil(timeRemaining / 1000)
  const progress = (timeRemaining / 30000) * 100

  return (
    <div className="flex items-center gap-2">
      <div className="w-12 h-1 bg-gray-200 rounded-full overflow-hidden">
        <div 
          className="h-full bg-orange-500 transition-all duration-1000 ease-linear"
          style={{ width: `${progress}%` }}
        />
      </div>
      <span className="text-xs text-gray-500 min-w-[20px]">
        {seconds}s
      </span>
    </div>
  )
}
