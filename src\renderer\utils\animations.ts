/**
 * 动画工具类
 * 提供统一的动画效果和交互反馈
 */

export const ANIMATION_DURATIONS = {
  fast: 150,
  normal: 300,
  slow: 500,
} as const

export const ANIMATION_EASINGS = {
  easeOut: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  easeIn: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',
  easeInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
} as const

/**
 * 任务删除动画
 */
export const createDeleteAnimation = (element: HTMLElement): Promise<void> => {
  return new Promise((resolve) => {
    element.style.transition = `all ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeOut}`
    element.style.transform = 'translateX(-100%)'
    element.style.opacity = '0'
    element.style.height = '0'
    element.style.marginBottom = '0'
    element.style.paddingTop = '0'
    element.style.paddingBottom = '0'
    
    setTimeout(() => {
      resolve()
    }, ANIMATION_DURATIONS.normal)
  })
}

/**
 * 任务恢复动画
 */
export const createRestoreAnimation = (element: HTMLElement): Promise<void> => {
  return new Promise((resolve) => {
    // 初始状态
    element.style.transform = 'translateX(-100%)'
    element.style.opacity = '0'
    
    // 强制重绘
    element.offsetHeight
    
    // 动画到最终状态
    element.style.transition = `all ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.bounce}`
    element.style.transform = 'translateX(0)'
    element.style.opacity = '1'
    
    setTimeout(() => {
      element.style.transition = ''
      resolve()
    }, ANIMATION_DURATIONS.normal)
  })
}

/**
 * 选择状态切换动画
 */
export const createSelectionAnimation = (element: HTMLElement, isSelected: boolean): void => {
  element.style.transition = `all ${ANIMATION_DURATIONS.fast}ms ${ANIMATION_EASINGS.easeOut}`
  
  if (isSelected) {
    element.style.transform = 'scale(0.98)'
    element.style.boxShadow = '0 0 0 2px rgb(59 130 246 / 0.5)'
  } else {
    element.style.transform = 'scale(1)'
    element.style.boxShadow = ''
  }
}

/**
 * 批量操作工具栏滑入动画
 */
export const createToolbarSlideInAnimation = (element: HTMLElement): void => {
  element.style.transform = 'translateY(100%)'
  element.style.opacity = '0'
  
  // 强制重绘
  element.offsetHeight
  
  element.style.transition = `all ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeOut}`
  element.style.transform = 'translateY(0)'
  element.style.opacity = '1'
}

/**
 * 批量操作工具栏滑出动画
 */
export const createToolbarSlideOutAnimation = (element: HTMLElement): Promise<void> => {
  return new Promise((resolve) => {
    element.style.transition = `all ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeIn}`
    element.style.transform = 'translateY(100%)'
    element.style.opacity = '0'
    
    setTimeout(() => {
      resolve()
    }, ANIMATION_DURATIONS.normal)
  })
}

/**
 * Toast 通知滑入动画
 */
export const createToastSlideInAnimation = (element: HTMLElement): void => {
  element.style.transform = 'translateX(100%)'
  element.style.opacity = '0'
  
  // 强制重绘
  element.offsetHeight
  
  element.style.transition = `all ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeOut}`
  element.style.transform = 'translateX(0)'
  element.style.opacity = '1'
}

/**
 * 撤销面板滑入动画
 */
export const createUndoPanelSlideInAnimation = (element: HTMLElement): void => {
  element.style.transform = 'translateY(-100%)'
  element.style.opacity = '0'
  
  // 强制重绘
  element.offsetHeight
  
  element.style.transition = `all ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.bounce}`
  element.style.transform = 'translateY(0)'
  element.style.opacity = '1'
}

/**
 * 加载状态脉冲动画
 */
export const createPulseAnimation = (element: HTMLElement): void => {
  element.style.animation = `pulse ${ANIMATION_DURATIONS.slow * 2}ms ${ANIMATION_EASINGS.easeInOut} infinite`
}

/**
 * 移除所有动画
 */
export const clearAnimations = (element: HTMLElement): void => {
  element.style.transition = ''
  element.style.transform = ''
  element.style.opacity = ''
  element.style.animation = ''
  element.style.boxShadow = ''
}

/**
 * 震动反馈动画（用于错误状态）
 */
export const createShakeAnimation = (element: HTMLElement): void => {
  element.style.animation = `shake ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeInOut}`
  
  setTimeout(() => {
    element.style.animation = ''
  }, ANIMATION_DURATIONS.normal)
}

/**
 * 成功状态弹跳动画
 */
export const createSuccessBounceAnimation = (element: HTMLElement): void => {
  element.style.animation = `bounce ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.bounce}`
  
  setTimeout(() => {
    element.style.animation = ''
  }, ANIMATION_DURATIONS.normal)
}

/**
 * 创建自定义 CSS 动画关键帧
 */
export const injectAnimationStyles = (): void => {
  if (document.getElementById('custom-animations')) return
  
  const style = document.createElement('style')
  style.id = 'custom-animations'
  style.textContent = `
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
      20%, 40%, 60%, 80% { transform: translateX(2px); }
    }
    
    @keyframes bounce {
      0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
      40%, 43% { transform: translate3d(0, -8px, 0); }
      70% { transform: translate3d(0, -4px, 0); }
      90% { transform: translate3d(0, -2px, 0); }
    }
    
    @keyframes slideInFromRight {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideInFromBottom {
      from { transform: translateY(100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes slideInFromTop {
      from { transform: translateY(-100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.9); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    .animate-slide-in-right {
      animation: slideInFromRight ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeOut};
    }
    
    .animate-slide-in-bottom {
      animation: slideInFromBottom ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeOut};
    }
    
    .animate-slide-in-top {
      animation: slideInFromTop ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.bounce};
    }
    
    .animate-fade-in {
      animation: fadeIn ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeOut};
    }
    
    .animate-scale-in {
      animation: scaleIn ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.bounce};
    }
    
    .transition-all-fast {
      transition: all ${ANIMATION_DURATIONS.fast}ms ${ANIMATION_EASINGS.easeOut};
    }
    
    .transition-all-normal {
      transition: all ${ANIMATION_DURATIONS.normal}ms ${ANIMATION_EASINGS.easeOut};
    }
    
    .transition-all-slow {
      transition: all ${ANIMATION_DURATIONS.slow}ms ${ANIMATION_EASINGS.easeOut};
    }
  `
  
  document.head.appendChild(style)
}

/**
 * 初始化动画系统
 */
export const initializeAnimations = (): void => {
  injectAnimationStyles()
}

/**
 * 交互反馈工具
 */
export class InteractionFeedback {
  /**
   * 按钮点击反馈
   */
  static buttonClick(element: HTMLElement): void {
    element.style.transform = 'scale(0.95)'
    element.style.transition = `transform ${ANIMATION_DURATIONS.fast}ms ${ANIMATION_EASINGS.easeOut}`
    
    setTimeout(() => {
      element.style.transform = 'scale(1)'
    }, ANIMATION_DURATIONS.fast)
  }
  
  /**
   * 悬停反馈
   */
  static hover(element: HTMLElement, isHovering: boolean): void {
    element.style.transition = `transform ${ANIMATION_DURATIONS.fast}ms ${ANIMATION_EASINGS.easeOut}`
    element.style.transform = isHovering ? 'translateY(-1px)' : 'translateY(0)'
  }
  
  /**
   * 焦点反馈
   */
  static focus(element: HTMLElement, isFocused: boolean): void {
    element.style.transition = `box-shadow ${ANIMATION_DURATIONS.fast}ms ${ANIMATION_EASINGS.easeOut}`
    element.style.boxShadow = isFocused 
      ? '0 0 0 2px rgb(59 130 246 / 0.5)' 
      : ''
  }
}
