#!/usr/bin/env node

/**
 * 简化版数据库索引应用脚本
 * 用于将性能优化索引应用到现有数据库
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const dbPath = path.join(dataDir, 'app.db');

console.log('🚀 开始应用数据库索引优化...');

// 检查数据库是否存在
if (!fs.existsSync(dbPath)) {
  console.error('❌ 数据库文件不存在:', dbPath);
  process.exit(1);
}

// 连接数据库
const db = new Database(dbPath);

// 启用 WAL 模式和优化设置
db.pragma('journal_mode = WAL');
db.pragma('foreign_keys = ON');
db.pragma('synchronous = NORMAL');
db.pragma('cache_size = 10000');
db.pragma('temp_store = MEMORY');

try {
  console.log('📝 应用性能优化索引...');
  
  // 定义所有需要创建的索引
  const indexes = [
    // 单列索引
    'CREATE INDEX IF NOT EXISTS tasks_order_index_idx ON tasks(order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_is_completed_idx ON tasks(is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_due_date_idx ON tasks(due_date)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_task_id_idx ON tasks(parent_task_id)',
    'CREATE INDEX IF NOT EXISTS tasks_created_at_idx ON tasks(created_at)',
    'CREATE INDEX IF NOT EXISTS tasks_deleted_at_idx ON tasks(deleted_at)',
    'CREATE INDEX IF NOT EXISTS tasks_priority_idx ON tasks(priority)',
    'CREATE INDEX IF NOT EXISTS tasks_task_type_idx ON tasks(task_type)',
    
    // 复合索引
    'CREATE INDEX IF NOT EXISTS tasks_status_order_idx ON tasks(is_completed, order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_deleted_status_idx ON tasks(deleted_at, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_status_idx ON tasks(parent_task_id, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_due_date_status_idx ON tasks(due_date, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_priority_status_idx ON tasks(priority, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_type_status_idx ON tasks(task_type, is_completed)',
    
    // 时间范围查询索引
    'CREATE INDEX IF NOT EXISTS tasks_created_date_idx ON tasks(created_at, deleted_at)',
    'CREATE INDEX IF NOT EXISTS tasks_due_date_range_idx ON tasks(due_date, deleted_at, is_completed)',
  ];
  
  let successCount = 0;
  let skipCount = 0;
  
  // 开始事务
  const transaction = db.transaction(() => {
    for (const indexSQL of indexes) {
      try {
        db.exec(indexSQL);
        
        // 提取索引名称
        const indexName = indexSQL.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
        console.log(`✅ 创建索引: ${indexName}`);
        successCount++;
      } catch (error) {
        if (error.message.includes('already exists')) {
          const indexName = indexSQL.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
          console.log(`⏭️  索引已存在，跳过: ${indexName}`);
          skipCount++;
        } else {
          console.warn(`⚠️  创建索引失败: ${error.message}`);
          skipCount++;
        }
      }
    }
  });
  
  // 执行事务
  transaction();
  
  // 更新统计信息
  console.log('📊 更新数据库统计信息...');
  db.exec('ANALYZE tasks');
  
  console.log(`\n📊 索引应用结果:`);
  console.log(`   ✅ 成功创建: ${successCount} 个索引`);
  console.log(`   ⏭️  跳过: ${skipCount} 个索引`);
  
  // 获取当前索引列表
  console.log('\n📋 当前数据库索引列表:');
  const indexes_list = db.prepare(
    "SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%' AND tbl_name='tasks' ORDER BY name"
  ).all();
  
  indexes_list.forEach(index => {
    console.log(`   📊 ${index.name} (表: ${index.tbl_name})`);
  });
  
  // 获取数据库统计信息
  console.log('\n📈 数据库统计信息:');
  try {
    const taskCount = db.prepare("SELECT COUNT(*) as count FROM tasks WHERE deleted_at IS NULL").get();
    console.log(`   📝 活跃任务数量: ${taskCount.count}`);
    
    const totalTasks = db.prepare("SELECT COUNT(*) as count FROM tasks").get();
    console.log(`   📝 总任务数量: ${totalTasks.count}`);
    
    const dbSize = fs.statSync(dbPath).size;
    console.log(`   💾 数据库大小: ${(dbSize / 1024 / 1024).toFixed(2)} MB`);
    
    console.log(`   📊 索引总数: ${indexes_list.length}`);
  } catch (error) {
    console.log(`   ⚠️  无法获取统计信息: ${error.message}`);
  }
  
  console.log('\n🎉 数据库索引优化应用完成！');
  
} catch (error) {
  console.error('❌ 应用索引时发生错误:', error);
  process.exit(1);
} finally {
  db.close();
}

console.log('\n📋 后续步骤:');
console.log('1. 重启应用以使用优化后的数据库');
console.log('2. 运行性能测试验证优化效果');
console.log('3. 监控查询性能和应用响应时间');
console.log('4. 查看 docs/数据库维护指南.md 了解维护方法');
