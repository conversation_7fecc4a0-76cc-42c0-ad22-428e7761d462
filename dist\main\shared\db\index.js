"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.schema = void 0;
exports.getDatabase = getDatabase;
exports.initializeConnectionPool = initializeConnectionPool;
exports.getPooledConnection = getPooledConnection;
exports.releasePooledConnection = releasePooledConnection;
exports.withPooledConnection = withPooledConnection;
exports.closeDatabase = closeDatabase;
const better_sqlite3_1 = __importDefault(require("better-sqlite3"));
const better_sqlite3_2 = require("drizzle-orm/better-sqlite3");
const migrator_1 = require("drizzle-orm/better-sqlite3/migrator");
const schema = __importStar(require("./schema"));
exports.schema = schema;
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
// 数据库文件路径
const DB_DIR = path.join(process.cwd(), 'data');
const DB_PATH = path.join(DB_DIR, 'app.db');
// 确保数据目录存在
function ensureDataDirectory() {
    if (!fs.existsSync(DB_DIR)) {
        fs.mkdirSync(DB_DIR, { recursive: true });
        console.log('Created data directory:', DB_DIR);
    }
}
// 数据库实例
let dbInstance = null;
let sqliteInstance = null;
// 简单的连接池实现
class SimpleConnectionPool {
    constructor(config) {
        this.connections = [];
        this.activeConnections = new Set();
        this.lastCleanup = Date.now();
        this.config = config;
    }
    async acquire() {
        // 清理过期连接
        this.cleanup();
        // 尝试获取空闲连接
        if (this.connections.length > 0) {
            const connection = this.connections.pop();
            this.activeConnections.add(connection);
            return connection;
        }
        // 如果没有空闲连接且未达到最大连接数，创建新连接
        if (this.activeConnections.size < this.config.maxConnections) {
            const connection = this.createConnection();
            this.activeConnections.add(connection);
            return connection;
        }
        // 等待连接可用
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('获取数据库连接超时'));
            }, this.config.acquireTimeout);
            const checkForConnection = () => {
                if (this.connections.length > 0) {
                    clearTimeout(timeout);
                    const connection = this.connections.pop();
                    this.activeConnections.add(connection);
                    resolve(connection);
                }
                else {
                    setTimeout(checkForConnection, 10);
                }
            };
            checkForConnection();
        });
    }
    release(connection) {
        if (this.activeConnections.has(connection)) {
            this.activeConnections.delete(connection);
            this.connections.push(connection);
        }
    }
    createConnection() {
        const connection = new better_sqlite3_1.default(DB_PATH);
        // 应用相同的优化设置
        connection.pragma('journal_mode = WAL');
        connection.pragma('synchronous = NORMAL');
        connection.pragma('cache_size = 10000');
        connection.pragma('foreign_keys = ON');
        connection.pragma('temp_store = MEMORY');
        connection.pragma('mmap_size = 268435456');
        connection.pragma('page_size = 4096');
        connection.pragma('auto_vacuum = INCREMENTAL');
        return connection;
    }
    cleanup() {
        const now = Date.now();
        // 每分钟清理一次
        if (now - this.lastCleanup < 60000) {
            return;
        }
        this.lastCleanup = now;
        // 关闭多余的空闲连接
        while (this.connections.length > 2) {
            const connection = this.connections.pop();
            connection.close();
        }
    }
    close() {
        // 关闭所有连接
        this.connections.forEach(conn => conn.close());
        this.activeConnections.forEach(conn => conn.close());
        this.connections = [];
        this.activeConnections.clear();
    }
}
// 连接池实例
let connectionPool = null;
// 初始化数据库
function initializeDatabase() {
    try {
        // 确保数据目录存在
        ensureDataDirectory();
        // 创建SQLite连接
        sqliteInstance = new better_sqlite3_1.default(DB_PATH);
        // 启用WAL模式以提高并发性能
        sqliteInstance.pragma('journal_mode = WAL');
        sqliteInstance.pragma('synchronous = NORMAL');
        sqliteInstance.pragma('cache_size = 10000'); // 增加缓存大小到10MB
        sqliteInstance.pragma('foreign_keys = ON');
        sqliteInstance.pragma('temp_store = MEMORY'); // 临时表存储在内存中
        sqliteInstance.pragma('mmap_size = 268435456'); // 启用内存映射 (256MB)
        sqliteInstance.pragma('page_size = 4096'); // 优化页面大小
        sqliteInstance.pragma('auto_vacuum = INCREMENTAL'); // 启用增量自动清理
        // 创建Drizzle实例
        dbInstance = (0, better_sqlite3_2.drizzle)(sqliteInstance, { schema });
        // 检查表是否已存在
        const tablesExist = sqliteInstance.prepare(`
      SELECT name FROM sqlite_master
      WHERE type='table' AND name IN ('tasks', 'settings')
    `).all();
        if (tablesExist.length > 0) {
            console.log('Tables already exist, skipping migrations');
        }
        else {
            // 运行迁移（Drizzle会自动检查哪些迁移需要运行）
            console.log('Checking for database migrations...');
            const migrationsPath = path.join(process.cwd(), 'src', 'shared', 'db', 'migrations');
            (0, migrator_1.migrate)(dbInstance, { migrationsFolder: migrationsPath });
            console.log('Database migrations completed');
        }
        console.log('Database initialized successfully at:', DB_PATH);
        return dbInstance;
    }
    catch (error) {
        console.error('Failed to initialize database:', error);
        throw error;
    }
}
// 获取数据库实例
function getDatabase() {
    if (!dbInstance) {
        dbInstance = initializeDatabase();
    }
    return dbInstance;
}
// 初始化连接池
function initializeConnectionPool() {
    if (!connectionPool) {
        connectionPool = new SimpleConnectionPool({
            maxConnections: 10, // 最大连接数
            idleTimeout: 300000, // 5分钟空闲超时
            acquireTimeout: 5000, // 5秒获取连接超时
        });
        console.log('Database connection pool initialized');
    }
    return connectionPool;
}
// 获取连接池连接
async function getPooledConnection() {
    if (!connectionPool) {
        initializeConnectionPool();
    }
    return connectionPool.acquire();
}
// 释放连接池连接
function releasePooledConnection(connection) {
    if (connectionPool) {
        connectionPool.release(connection);
    }
}
// 执行带连接池的数据库操作
async function withPooledConnection(operation) {
    const connection = await getPooledConnection();
    const db = (0, better_sqlite3_2.drizzle)(connection, { schema });
    try {
        return await operation(db);
    }
    finally {
        releasePooledConnection(connection);
    }
}
// 关闭数据库连接
function closeDatabase() {
    if (connectionPool) {
        connectionPool.close();
        connectionPool = null;
        console.log('Database connection pool closed');
    }
    if (sqliteInstance) {
        sqliteInstance.close();
        sqliteInstance = null;
        dbInstance = null;
        console.log('Database connection closed');
    }
}
