{"version": 3, "sources": ["../../../src/singlestore-core/columns/mediumint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\nimport type { SingleStoreIntConfig } from './int.ts';\n\nexport type SingleStoreMediumIntBuilderInitial<TName extends string> = SingleStoreMediumIntBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreMediumInt';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreMediumIntBuilder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreMediumInt'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreMediumIntBuilder';\n\n\tconstructor(name: T['name'], config?: SingleStoreIntConfig) {\n\t\tsuper(name, 'number', 'SingleStoreMediumInt');\n\t\tthis.config.unsigned = config ? config.unsigned : false;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreMediumInt<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreMediumInt<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreMediumInt<T extends ColumnBaseConfig<'number', 'SingleStoreMediumInt'>>\n\textends SingleStoreColumnWithAutoIncrement<T, SingleStoreIntConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreMediumInt';\n\n\tgetSQLType(): string {\n\t\treturn `mediumint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'string') {\n\t\t\treturn Number(value);\n\t\t}\n\t\treturn value;\n\t}\n}\n\nexport function mediumint(): SingleStoreMediumIntBuilderInitial<''>;\nexport function mediumint(\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreMediumIntBuilderInitial<''>;\nexport function mediumint<TName extends string>(\n\tname: TName,\n\tconfig?: SingleStoreIntConfig,\n): SingleStoreMediumIntBuilderInitial<TName>;\nexport function mediumint(a?: string | SingleStoreIntConfig, b?: SingleStoreIntConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreIntConfig>(a, b);\n\treturn new SingleStoreMediumIntBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,8BAA8B;AACvC,SAAS,2CAA2C,0CAA0C;AAavF,MAAM,oCACJ,0CACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA+B;AAC3D,UAAM,MAAM,UAAU,sBAAsB;AAC5C,SAAK,OAAO,WAAW,SAAS,OAAO,WAAW;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OACwD;AACxD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,6BACJ,mCACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,YAAY,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EAC3D;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO,OAAO,KAAK;AAAA,IACpB;AACA,WAAO;AAAA,EACR;AACD;AAUO,SAAS,UAAU,GAAmC,GAA0B;AACtF,QAAM,EAAE,MAAM,OAAO,IAAI,uBAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,4BAA4B,MAAM,MAAM;AACpD;", "names": []}