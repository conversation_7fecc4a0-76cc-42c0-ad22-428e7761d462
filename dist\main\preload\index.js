"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// 实现 API
const electronAPI = {
    // 通用调用方法
    invoke: (channel, ...args) => {
        return electron_1.ipcRenderer.invoke(channel, ...args);
    },
    // 任务 API
    task: {
        getAll: () => electron_1.ipcRenderer.invoke('task:getAll'),
        getHierarchical: () => electron_1.ipcRenderer.invoke('task:getHierarchical'),
        create: (input) => electron_1.ipcRenderer.invoke('task:create', input),
        update: (id, input) => electron_1.ipcRenderer.invoke('task:update', id, input),
        delete: (id) => electron_1.ipcRenderer.invoke('task:delete', id),
        reorder: (data) => electron_1.ipcRenderer.invoke('task:reorder', data),
        getStats: () => electron_1.ipcRenderer.invoke('task:getStats'),
        // 软删除和恢复
        softDelete: (id) => electron_1.ipcRenderer.invoke('task:softDelete', id),
        restore: (id) => electron_1.ipcRenderer.invoke('task:restore', id),
        getById: (id) => electron_1.ipcRenderer.invoke('task:getById', id),
        // 批量操作
        batchSoftDelete: (taskIds) => electron_1.ipcRenderer.invoke('task:batchSoftDelete', taskIds),
        batchRestore: (taskIds) => electron_1.ipcRenderer.invoke('task:batchRestore', taskIds),
        // 自动清理
        cleanupDeleted: (retentionPeriod) => electron_1.ipcRenderer.invoke('task:cleanupDeleted', retentionPeriod),
        getDeletedStats: () => electron_1.ipcRenderer.invoke('task:getDeletedStats'),
        getUndoable: () => electron_1.ipcRenderer.invoke('task:getUndoable'),
    },
    // 应用 API
    app: {
        getVersion: () => electron_1.ipcRenderer.invoke('app:getVersion'),
        quit: () => electron_1.ipcRenderer.invoke('app:quit'),
    },
    // 设置 API
    settings: {
        get: (key) => electron_1.ipcRenderer.invoke('settings:get', key),
        set: (key, value) => electron_1.ipcRenderer.invoke('settings:set', key, value),
    },
};
// 将 API 暴露给渲染进程
electron_1.contextBridge.exposeInMainWorld('electronAPI', electronAPI);
