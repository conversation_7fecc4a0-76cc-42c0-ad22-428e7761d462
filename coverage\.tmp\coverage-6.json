{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 114}], "isBlockCoverage": true}, {"functionName": "getPropertyValue", "ranges": [{"startOffset": 2430, "endOffset": 2608, "count": 135}, {"startOffset": 2483, "endOffset": 2555, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 0}], "isBlockCoverage": false}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/__tests__/ErrorBoundary.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 38484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 38484, "count": 1}], "isBlockCoverage": true}, {"functionName": "ThrowError", "ranges": [{"startOffset": 1016, "endOffset": 1386, "count": 66}, {"startOffset": 1082, "endOffset": 1122, "count": 64}, {"startOffset": 1122, "endOffset": 1385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1882, "endOffset": 19438, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1925, "endOffset": 2070, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2051, "endOffset": 2064, "count": 80}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2126, "endOffset": 3924, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2210, "endOffset": 2957, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3044, "endOffset": 3918, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3978, "endOffset": 6565, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4077, "endOffset": 4948, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5012, "endOffset": 5760, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5816, "endOffset": 6559, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6619, "endOffset": 9992, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6684, "endOffset": 7426, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7492, "endOffset": 8236, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8302, "endOffset": 9046, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9129, "endOffset": 9986, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10057, "endOffset": 14116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10148, "endOffset": 10205, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10222, "endOffset": 10277, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10366, "endOffset": 11111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11190, "endOffset": 12060, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12153, "endOffset": 13024, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13121, "endOffset": 14110, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14176, "endOffset": 15289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14259, "endOffset": 15283, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15344, "endOffset": 16643, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15428, "endOffset": 16637, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16702, "endOffset": 18294, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16789, "endOffset": 18288, "count": 1}], "isBlockCoverage": true}, {"functionName": "TestComponent", "ranges": [{"startOffset": 16825, "endOffset": 17434, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16973, "endOffset": 17131, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 18347, "endOffset": 19434, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18418, "endOffset": 19428, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1568", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ErrorBoundary.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 33531, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 33531, "count": 1}], "isBlockCoverage": true}, {"functionName": "<instance_members_initializer>", "ranges": [{"startOffset": 1015, "endOffset": 15545, "count": 32}], "isBlockCoverage": true}, {"functionName": "Error<PERSON>ou<PERSON><PERSON>", "ranges": [{"startOffset": 1055, "endOffset": 1184, "count": 33}], "isBlockCoverage": true}, {"functionName": "getDerivedStateFromError", "ranges": [{"startOffset": 1194, "endOffset": 1343, "count": 32}], "isBlockCoverage": true}, {"functionName": "componentDidCatch", "ranges": [{"startOffset": 1346, "endOffset": 1576, "count": 16}, {"startOffset": 1511, "endOffset": 1531, "count": 1}], "isBlockCoverage": true}, {"functionName": "generateErrorId", "ranges": [{"startOffset": 1579, "endOffset": 1679, "count": 65}], "isBlockCoverage": true}, {"functionName": "reportError", "ranges": [{"startOffset": 1696, "endOffset": 2461, "count": 16}, {"startOffset": 2262, "endOffset": 2356, "count": 4}, {"startOffset": 2363, "endOffset": 2457, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleReset", "ranges": [{"startOffset": 2479, "endOffset": 2648, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleRestart", "ranges": [{"startOffset": 2668, "endOffset": 2808, "count": 1}, {"startOffset": 2759, "endOffset": 2804, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleGoHome", "ranges": [{"startOffset": 2827, "endOffset": 2862, "count": 0}], "isBlockCoverage": false}, {"functionName": "copyErrorDetails", "ranges": [{"startOffset": 2885, "endOffset": 3393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3235, "endOffset": 3304, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3312, "endOffset": 3387, "count": 0}], "isBlockCoverage": false}, {"functionName": "render", "ranges": [{"startOffset": 3397, "endOffset": 15543, "count": 82}, {"startOffset": 3437, "endOffset": 15507, "count": 48}, {"startOffset": 3470, "endOffset": 3515, "count": 3}, {"startOffset": 3515, "endOffset": 3624, "count": 45}, {"startOffset": 6318, "endOffset": 6327, "count": 45}, {"startOffset": 6328, "endOffset": 6337, "count": 0}, {"startOffset": 7268, "endOffset": 7284, "count": 45}, {"startOffset": 7285, "endOffset": 8305, "count": 45}, {"startOffset": 10462, "endOffset": 14595, "count": 12}, {"startOffset": 12486, "endOffset": 13540, "count": 4}, {"startOffset": 15507, "endOffset": 15542, "count": 34}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 15647, "endOffset": 15676, "count": 17}], "isBlockCoverage": true}]}, {"scriptId": "1569", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/button.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1951, "endOffset": 2495, "count": 147}, {"startOffset": 2044, "endOffset": 2072, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2625, "endOffset": 2647, "count": 147}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2753, "endOffset": 2783, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1578", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 550, "count": 372}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 641, "endOffset": 659, "count": 372}], "isBlockCoverage": true}, {"functionName": "formatDate", "ranges": [{"startOffset": 663, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1280, "endOffset": 1306, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDueDate", "ranges": [{"startOffset": 1310, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1984, "endOffset": 2013, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTaskOverdue", "ranges": [{"startOffset": 2017, "endOffset": 2113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2244, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 2248, "endOffset": 2454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2551, "endOffset": 2575, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 2579, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2908, "endOffset": 2932, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateOrderIndex", "ranges": [{"startOffset": 2936, "endOffset": 3207, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3314, "endOffset": 3348, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1582", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/card.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 8892, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 8892, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 604, "endOffset": 1014, "count": 45}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1104, "endOffset": 1469, "count": 45}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1570, "endOffset": 1973, "count": 45}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2078, "endOffset": 2441, "count": 45}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2554, "endOffset": 2862, "count": 45}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2966, "endOffset": 3328, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3463, "endOffset": 3483, "count": 45}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3585, "endOffset": 3611, "count": 45}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3713, "endOffset": 3739, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3840, "endOffset": 3865, "count": 45}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3972, "endOffset": 4003, "count": 45}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4106, "endOffset": 4133, "count": 45}], "isBlockCoverage": true}]}]}