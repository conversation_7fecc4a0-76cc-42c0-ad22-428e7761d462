import React, { useState, useMemo } from 'react'
import {
  Home,
  Calendar,
  Star,
  CheckSquare,
  Settings,
  Plus,
  Search,
  Menu,
  Sun,
  Moon,
  MoreHorizontal,
  Filter,
  SortAsc,
  Eye,
  EyeOff
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { cn } from '../../lib/utils'
import { useTheme } from '../../contexts/ThemeContext'
import { useViewStore, type ViewType } from '../../stores/viewStore'
import { useTasks } from '../../hooks/useTasks'

interface ModernLayoutProps {
  children: React.ReactNode
  onAddTaskClick?: () => void
}

interface SidebarItem {
  id: ViewType
  label: string
  icon: React.ComponentType<{ className?: string }>
  count?: number
  active?: boolean
}

export function ModernLayout({ children, onAddTaskClick }: ModernLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const { currentTheme, toggleMode } = useTheme()
  const { data: tasks = [] } = useTasks()

  const {
    activeView,
    setActiveView,
    searchQuery,
    setSearchQuery,
    sortBy,
    setSortBy,
    showCompleted,
    setShowCompleted,
    setTaskInputFocused
  } = useViewStore()

  // 计算各个视图的任务数量
  const taskCounts = useMemo(() => {
    const now = Date.now()
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayStart = today.getTime()
    const todayEnd = todayStart + 24 * 60 * 60 * 1000

    return {
      today: tasks.filter(task =>
        !task.isCompleted &&
        task.dueDate &&
        task.dueDate >= todayStart &&
        task.dueDate < todayEnd
      ).length,
      important: tasks.filter(task =>
        !task.isCompleted &&
        (task.priority === 3 || task.isImportant)
      ).length,
      planned: tasks.filter(task =>
        !task.isCompleted &&
        task.dueDate &&
        task.dueDate > now
      ).length,
      all: tasks.filter(task => !task.isCompleted).length,
      completed: tasks.filter(task => task.isCompleted).length,
    }
  }, [tasks])

  const sidebarItems: SidebarItem[] = [
    { id: 'today', label: '我的一天', icon: Sun, count: taskCounts.today, active: activeView === 'today' },
    { id: 'important', label: '重要', icon: Star, count: taskCounts.important, active: activeView === 'important' },
    { id: 'planned', label: '已计划', icon: Calendar, count: taskCounts.planned, active: activeView === 'planned' },
    { id: 'all', label: '全部', icon: Home, count: taskCounts.all, active: activeView === 'all' },
    { id: 'completed', label: '已完成', icon: CheckSquare, count: taskCounts.completed, active: activeView === 'completed' },
  ]

  const handleAddTaskClick = () => {
    setTaskInputFocused(true)
    onAddTaskClick?.()
  }

  return (
    <div className="flex h-screen bg-background">
      {/* 左侧导航栏 */}
      <div className={cn(
        "flex flex-col bg-white border-r border-gray-200 transition-all duration-300 shadow-sm",
        sidebarCollapsed ? "w-16" : "w-80"
      )}>
        {/* 顶部标题区域 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50/50">
          <div className={cn(
            "flex items-center gap-3",
            sidebarCollapsed && "justify-center"
          )}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
            >
              <Menu className="h-5 w-5 text-gray-600" />
            </Button>
            {!sidebarCollapsed && (
              <h1 className="text-xl font-bold text-gray-800 tracking-tight">灵感 APP</h1>
            )}
          </div>
          {!sidebarCollapsed && (
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMode}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title={`当前主题: ${currentTheme.name} (${currentTheme.mode === 'dark' ? '深色' : '浅色'})`}
            >
              {currentTheme.mode === 'dark' ?
                <Sun className="h-4 w-4 text-gray-600" /> :
                <Moon className="h-4 w-4 text-gray-600" />
              }
            </Button>
          )}
        </div>

        {/* 搜索框 */}
        {!sidebarCollapsed && (
          <div className="p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索任务..."
                className={cn(
                  "ms-input pl-10 bg-gray-50 border-gray-200 rounded-lg",
                  "focus:bg-white focus:border-blue-400 focus:shadow-sm",
                  "placeholder:text-gray-400 text-gray-700"
                )}
              />
            </div>
          </div>
        )}

        {/* 导航项 */}
        <div className="flex-1 px-3 py-2">
          <nav className="space-y-1">
            {sidebarItems.map((item) => (
              <Button
                key={item.id}
                variant="ghost"
                className={cn(
                  "ms-nav-item w-full justify-start gap-3 h-12 px-4 rounded-lg transition-all duration-200",
                  sidebarCollapsed && "justify-center px-2",
                  item.active
                    ? "ms-nav-item active bg-blue-50 text-blue-600 hover:bg-blue-100 border-r-3 border-blue-500 font-semibold shadow-sm"
                    : "text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                )}
                onClick={() => setActiveView(item.id)}
              >
                <item.icon className={cn(
                  "h-5 w-5 flex-shrink-0 transition-colors duration-200",
                  item.active ? "text-blue-600" : "text-gray-500"
                )} />
                {!sidebarCollapsed && (
                  <>
                    <span className="flex-1 text-left font-medium">{item.label}</span>
                    {item.count !== undefined && (
                      <span className={cn(
                        "text-xs px-2 py-1 rounded-full min-w-[20px] text-center font-medium transition-colors duration-200",
                        item.active
                          ? "bg-blue-100 text-blue-700"
                          : "bg-gray-100 text-gray-600"
                      )}>
                        {item.count}
                      </span>
                    )}
                  </>
                )}
              </Button>
            ))}
          </nav>
        </div>

        {/* 底部设置 */}
        <div className="p-3 border-t border-gray-200 bg-gray-50/30">
          <Button
            variant="ghost"
            className={cn(
              "w-full justify-start gap-3 h-12 px-4 rounded-lg transition-all duration-200",
              "text-gray-700 hover:bg-gray-100 hover:text-gray-800",
              sidebarCollapsed && "justify-center px-2"
            )}
          >
            <Settings className="h-5 w-5 flex-shrink-0 text-gray-500" />
            {!sidebarCollapsed && <span className="font-medium">设置</span>}
          </Button>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 顶部工具栏 */}
        <div className="flex items-center justify-between p-6 border-b border-border bg-card/50">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-semibold text-foreground">
              {sidebarItems.find(item => item.id === activeView)?.label || '我的一天'}
            </h2>

            {/* 工具栏按钮 */}
            <div className="flex items-center gap-2">
              {/* 排序按钮 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const sortOptions: ViewState['sortBy'][] = ['custom', 'priority', 'dueDate', 'created', 'alphabetical']
                  const currentIndex = sortOptions.indexOf(sortBy)
                  const nextIndex = (currentIndex + 1) % sortOptions.length
                  setSortBy(sortOptions[nextIndex])
                }}
                title={`当前排序: ${sortBy === 'custom' ? '自定义' : sortBy === 'priority' ? '优先级' : sortBy === 'dueDate' ? '截止日期' : sortBy === 'created' ? '创建时间' : '字母顺序'}`}
              >
                <SortAsc className="h-4 w-4" />
              </Button>

              {/* 显示/隐藏已完成任务 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCompleted(!showCompleted)}
                title={showCompleted ? '隐藏已完成任务' : '显示已完成任务'}
              >
                {showCompleted ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              </Button>

              {/* 更多选项 */}
              <Button variant="ghost" size="sm" title="更多选项">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <Button onClick={handleAddTaskClick} className="gap-2">
            <Plus className="h-4 w-4" />
            添加任务
          </Button>
        </div>

        {/* 主内容 */}
        <div className="flex-1 overflow-auto">
          <div className="max-w-4xl mx-auto p-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
