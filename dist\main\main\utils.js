"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isDev = void 0;
exports.getAppVersion = getAppVersion;
exports.quitApp = quitApp;
exports.log = log;
const electron_1 = require("electron");
// 判断是否为开发环境
exports.isDev = process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
// 获取应用版本
function getAppVersion() {
    return electron_1.app.getVersion();
}
// 安全退出应用
function quitApp() {
    electron_1.app.quit();
}
// 日志工具
function log(level, message, ...args) {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
    switch (level) {
        case 'info':
            console.log(prefix, message, ...args);
            break;
        case 'warn':
            console.warn(prefix, message, ...args);
            break;
        case 'error':
            console.error(prefix, message, ...args);
            break;
    }
}
