#!/usr/bin/env node

/**
 * LinganApp 数据库性能测试脚本
 * 用于测试查询优化效果和验证功能正确性
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');
const { ulid } = require('ulid');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const dbPath = path.join(dataDir, 'app.db');
const testDbPath = path.join(dataDir, 'test_performance.db');

console.log('🚀 开始数据库性能测试...');

// 测试配置
const TEST_CONFIG = {
  SMALL_DATASET: 100,
  MEDIUM_DATASET: 1000,
  LARGE_DATASET: 10000,
  QUERY_ITERATIONS: 10,
};

// 性能测试结果
const results = {
  dataGeneration: {},
  queries: {},
  comparisons: {}
};

// 创建测试数据库
function createTestDatabase() {
  // 如果测试数据库存在，删除它
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  const db = new Database(testDbPath);
  
  // 应用优化设置
  db.pragma('journal_mode = WAL');
  db.pragma('synchronous = NORMAL');
  db.pragma('cache_size = 10000');
  db.pragma('foreign_keys = ON');
  db.pragma('temp_store = MEMORY');
  db.pragma('mmap_size = 268435456');
  db.pragma('page_size = 4096');
  db.pragma('auto_vacuum = INCREMENTAL');

  // 创建表结构
  db.exec(`
    CREATE TABLE tasks (
      id TEXT PRIMARY KEY,
      content TEXT NOT NULL,
      is_completed INTEGER DEFAULT 0 NOT NULL,
      priority INTEGER DEFAULT 2 NOT NULL,
      due_date INTEGER,
      order_index INTEGER NOT NULL,
      created_at INTEGER DEFAULT (unixepoch() * 1000) NOT NULL,
      deleted_at INTEGER,
      parent_task_id TEXT,
      task_type TEXT DEFAULT 'task' NOT NULL,
      description TEXT,
      estimated_duration INTEGER,
      actual_duration INTEGER,
      progress INTEGER DEFAULT 0 NOT NULL
    );
  `);

  return db;
}

// 生成测试数据
function generateTestData(db, count) {
  console.log(`📝 生成 ${count} 条测试数据...`);
  
  const startTime = Date.now();
  const now = Date.now();
  
  // 准备批量插入语句
  const insertStmt = db.prepare(`
    INSERT INTO tasks (
      id, content, is_completed, priority, due_date, order_index, 
      created_at, parent_task_id, task_type, description, progress
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const transaction = db.transaction((tasks) => {
    for (const task of tasks) {
      insertStmt.run(
        task.id,
        task.content,
        task.isCompleted ? 1 : 0,
        task.priority,
        task.dueDate,
        task.orderIndex,
        task.createdAt,
        task.parentTaskId,
        task.taskType,
        task.description,
        task.progress
      );
    }
  });

  // 生成任务数据
  const tasks = [];
  for (let i = 0; i < count; i++) {
    const isCompleted = Math.random() < 0.3; // 30% 完成率
    const priority = Math.floor(Math.random() * 3) + 1; // 1-3
    const hasDueDate = Math.random() < 0.6; // 60% 有截止日期
    const hasParent = Math.random() < 0.1; // 10% 有父任务
    
    tasks.push({
      id: ulid(),
      content: `测试任务 ${i + 1} - ${generateRandomText()}`,
      isCompleted,
      priority,
      dueDate: hasDueDate ? now + Math.random() * 30 * 24 * 60 * 60 * 1000 : null, // 30天内
      orderIndex: (i + 1) * 1000,
      createdAt: now - Math.random() * 7 * 24 * 60 * 60 * 1000, // 7天内创建
      parentTaskId: hasParent && i > 0 ? tasks[Math.floor(Math.random() * i)].id : null,
      taskType: Math.random() < 0.9 ? 'task' : 'subtask',
      description: Math.random() < 0.5 ? `详细描述 ${i + 1}` : null,
      progress: isCompleted ? 100 : Math.floor(Math.random() * 100)
    });
  }

  // 执行批量插入
  transaction(tasks);
  
  const duration = Date.now() - startTime;
  console.log(`✅ 生成 ${count} 条数据完成，耗时: ${duration}ms`);
  
  return { count, duration };
}

// 生成随机文本
function generateRandomText() {
  const words = ['学习', '工作', '生活', '健康', '娱乐', '购物', '旅行', '阅读', '运动', '编程'];
  const actions = ['完成', '开始', '计划', '准备', '整理', '优化', '改进', '学习', '练习', '复习'];
  
  return `${actions[Math.floor(Math.random() * actions.length)]}${words[Math.floor(Math.random() * words.length)]}`;
}

// 应用索引
function applyIndexes(db) {
  console.log('📊 应用性能索引...');
  
  const startTime = Date.now();
  
  const indexes = [
    'CREATE INDEX IF NOT EXISTS tasks_order_index_idx ON tasks(order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_is_completed_idx ON tasks(is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_due_date_idx ON tasks(due_date)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_task_id_idx ON tasks(parent_task_id)',
    'CREATE INDEX IF NOT EXISTS tasks_created_at_idx ON tasks(created_at)',
    'CREATE INDEX IF NOT EXISTS tasks_deleted_at_idx ON tasks(deleted_at)',
    'CREATE INDEX IF NOT EXISTS tasks_priority_idx ON tasks(priority)',
    'CREATE INDEX IF NOT EXISTS tasks_task_type_idx ON tasks(task_type)',
    'CREATE INDEX IF NOT EXISTS tasks_status_order_idx ON tasks(is_completed, order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_deleted_status_idx ON tasks(deleted_at, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_status_idx ON tasks(parent_task_id, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_due_date_status_idx ON tasks(due_date, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_priority_status_idx ON tasks(priority, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_created_date_idx ON tasks(created_at, deleted_at)',
  ];

  indexes.forEach(indexSQL => {
    db.exec(indexSQL);
  });

  // 分析表
  db.exec('ANALYZE tasks');
  
  const duration = Date.now() - startTime;
  console.log(`✅ 索引应用完成，耗时: ${duration}ms`);
  
  return duration;
}

// 执行查询测试
function runQueryTests(db, datasetSize) {
  console.log(`\n🔍 开始查询性能测试 (数据集大小: ${datasetSize})...`);
  
  const queries = {
    // 基础查询
    'getAllTasks': 'SELECT * FROM tasks WHERE deleted_at IS NULL ORDER BY order_index',
    
    // 条件查询
    'getCompletedTasks': 'SELECT * FROM tasks WHERE deleted_at IS NULL AND is_completed = 1 ORDER BY order_index',
    'getPendingTasks': 'SELECT * FROM tasks WHERE deleted_at IS NULL AND is_completed = 0 ORDER BY order_index',
    'getHighPriorityTasks': 'SELECT * FROM tasks WHERE deleted_at IS NULL AND priority = 1 ORDER BY order_index',
    
    // 复合条件查询
    'getPendingHighPriority': 'SELECT * FROM tasks WHERE deleted_at IS NULL AND is_completed = 0 AND priority = 1 ORDER BY order_index',
    'getTasksWithDueDate': 'SELECT * FROM tasks WHERE deleted_at IS NULL AND due_date IS NOT NULL ORDER BY due_date',
    
    // 聚合查询
    'getTaskStats': `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN is_completed = 0 THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN is_completed = 0 AND due_date IS NOT NULL AND due_date < unixepoch() * 1000 THEN 1 ELSE 0 END) as overdue
      FROM tasks WHERE deleted_at IS NULL
    `,
    
    // 分页查询
    'getTasksPaginated': 'SELECT * FROM tasks WHERE deleted_at IS NULL ORDER BY order_index LIMIT 50 OFFSET 0',
    
    // 搜索查询
    'searchTasks': "SELECT * FROM tasks WHERE deleted_at IS NULL AND (content LIKE '%学习%' OR description LIKE '%学习%') ORDER BY order_index"
  };

  const queryResults = {};

  Object.entries(queries).forEach(([name, sql]) => {
    console.log(`  测试查询: ${name}`);
    
    const times = [];
    
    for (let i = 0; i < TEST_CONFIG.QUERY_ITERATIONS; i++) {
      const startTime = Date.now();
      const result = db.prepare(sql).all();
      const duration = Date.now() - startTime;
      times.push(duration);
    }
    
    const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    
    queryResults[name] = {
      avgTime: Math.round(avgTime * 100) / 100,
      minTime,
      maxTime,
      iterations: TEST_CONFIG.QUERY_ITERATIONS
    };
    
    console.log(`    平均耗时: ${queryResults[name].avgTime}ms (最小: ${minTime}ms, 最大: ${maxTime}ms)`);
  });

  return queryResults;
}

// 主测试函数
async function runPerformanceTests() {
  try {
    // 创建测试数据库
    const db = createTestDatabase();
    
    console.log('\n=== 第一阶段：无索引性能测试 ===');
    
    // 生成中等规模数据集
    const dataGenResult = generateTestData(db, TEST_CONFIG.MEDIUM_DATASET);
    results.dataGeneration.medium = dataGenResult;
    
    // 无索引查询测试
    const noIndexResults = runQueryTests(db, TEST_CONFIG.MEDIUM_DATASET);
    results.queries.noIndex = noIndexResults;
    
    console.log('\n=== 第二阶段：有索引性能测试 ===');
    
    // 应用索引
    const indexTime = applyIndexes(db);
    results.indexCreation = indexTime;
    
    // 有索引查询测试
    const withIndexResults = runQueryTests(db, TEST_CONFIG.MEDIUM_DATASET);
    results.queries.withIndex = withIndexResults;
    
    console.log('\n=== 第三阶段：大数据集测试 ===');
    
    // 生成大数据集
    const largeDataGenResult = generateTestData(db, TEST_CONFIG.LARGE_DATASET - TEST_CONFIG.MEDIUM_DATASET);
    results.dataGeneration.large = largeDataGenResult;
    
    // 大数据集查询测试
    const largeDatasetResults = runQueryTests(db, TEST_CONFIG.LARGE_DATASET);
    results.queries.largeDataset = largeDatasetResults;
    
    // 关闭数据库
    db.close();
    
    // 生成性能报告
    generatePerformanceReport();
    
  } catch (error) {
    console.error('❌ 性能测试失败:', error);
    process.exit(1);
  }
}

// 生成性能报告
function generatePerformanceReport() {
  console.log('\n📊 === 性能测试报告 ===');
  
  // 计算性能提升
  const improvements = {};
  Object.keys(results.queries.noIndex).forEach(queryName => {
    const noIndexTime = results.queries.noIndex[queryName].avgTime;
    const withIndexTime = results.queries.withIndex[queryName].avgTime;
    const improvement = ((noIndexTime - withIndexTime) / noIndexTime * 100);
    
    improvements[queryName] = {
      noIndex: noIndexTime,
      withIndex: withIndexTime,
      improvement: Math.round(improvement * 100) / 100,
      speedup: Math.round((noIndexTime / withIndexTime) * 100) / 100
    };
  });
  
  console.log('\n🚀 查询性能对比 (1000条数据):');
  console.log('查询类型'.padEnd(25) + '无索引(ms)'.padEnd(12) + '有索引(ms)'.padEnd(12) + '提升(%)'.padEnd(10) + '加速倍数');
  console.log('-'.repeat(70));
  
  Object.entries(improvements).forEach(([name, data]) => {
    console.log(
      name.padEnd(25) + 
      data.noIndex.toString().padEnd(12) + 
      data.withIndex.toString().padEnd(12) + 
      `${data.improvement > 0 ? '+' : ''}${data.improvement}`.padEnd(10) + 
      `${data.speedup}x`
    );
  });
  
  // 计算平均性能提升
  const avgImprovement = Object.values(improvements)
    .reduce((sum, data) => sum + data.improvement, 0) / Object.keys(improvements).length;
  
  console.log('\n📈 总体性能提升:');
  console.log(`   平均查询性能提升: ${Math.round(avgImprovement * 100) / 100}%`);
  console.log(`   索引创建耗时: ${results.indexCreation}ms`);
  console.log(`   数据生成性能: ${Math.round(TEST_CONFIG.MEDIUM_DATASET / results.dataGeneration.medium.duration * 1000)} 条/秒`);
  
  // 保存详细报告到文件
  const reportPath = path.join(process.cwd(), 'performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  
  // 验证性能目标
  if (avgImprovement >= 50) {
    console.log('\n🎉 性能优化目标达成！平均性能提升超过50%');
  } else {
    console.log(`\n⚠️  性能优化目标未完全达成，当前提升: ${Math.round(avgImprovement * 100) / 100}%，目标: 50%`);
  }
  
  // 清理测试数据库
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
    console.log('\n🧹 测试数据库已清理');
  }
}

// 运行测试
runPerformanceTests();
