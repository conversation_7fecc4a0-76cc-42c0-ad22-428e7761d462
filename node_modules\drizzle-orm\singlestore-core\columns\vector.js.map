{"version": 3, "sources": ["../../../src/singlestore-core/columns/vector.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { SQL } from '~/sql/index.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder, SingleStoreGeneratedColumnConfig } from './common.ts';\n\nexport type SingleStoreVectorBuilderInitial<TName extends string> = SingleStoreVectorBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'SingleStoreVector';\n\tdata: Array<number>;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class SingleStoreVectorBuilder<T extends ColumnBuilderBaseConfig<'array', 'SingleStoreVector'>>\n\textends SingleStoreColumnBuilder<T, SingleStoreVectorConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreVectorBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreVectorConfig) {\n\t\tsuper(name, 'array', 'SingleStoreVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t\tthis.config.elementType = config.elementType;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreVector<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreVector<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n\n\t/** @internal */\n\toverride generatedAlwaysAs(as: SQL<unknown> | (() => SQL) | T['data'], config?: SingleStoreGeneratedColumnConfig) {\n\t\tthrow new Error('not implemented');\n\t}\n}\n\nexport class SingleStoreVector<T extends ColumnBaseConfig<'array', 'SingleStoreVector'>>\n\textends SingleStoreColumn<T, SingleStoreVectorConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreVector';\n\n\tdimensions: number = this.config.dimensions;\n\telementType: ElementType | undefined = this.config.elementType;\n\n\tgetSQLType(): string {\n\t\treturn `vector(${this.dimensions}, ${this.elementType || 'F32'})`;\n\t}\n\n\toverride mapToDriverValue(value: Array<number>) {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: string): Array<number> {\n\t\treturn JSON.parse(value);\n\t}\n}\n\ntype ElementType = 'I8' | 'I16' | 'I32' | 'I64' | 'F32' | 'F64';\n\nexport interface SingleStoreVectorConfig {\n\tdimensions: number;\n\telementType?: ElementType;\n}\n\nexport function vector(\n\tconfig: SingleStoreVectorConfig,\n): SingleStoreVectorBuilderInitial<''>;\nexport function vector<TName extends string>(\n\tname: TName,\n\tconfig: SingleStoreVectorConfig,\n): SingleStoreVectorBuilderInitial<TName>;\nexport function vector(a: string | SingleStoreVectorConfig, b?: SingleStoreVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreVectorConfig>(a, b);\n\treturn new SingleStoreVectorBuilder(name, config);\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAG3B,SAAS,8BAA8B;AACvC,SAAS,mBAAmB,gCAAkE;AAWvF,MAAM,iCACJ,yBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAiC;AAC7D,UAAM,MAAM,SAAS,mBAAmB;AACxC,SAAK,OAAO,aAAa,OAAO;AAChC,SAAK,OAAO,cAAc,OAAO;AAAA,EAClC;AAAA;AAAA,EAGS,MACR,OACqD;AACrD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA;AAAA,EAGS,kBAAkB,IAA4C,QAA2C;AACjH,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AACD;AAEO,MAAM,0BACJ,kBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB,KAAK,OAAO;AAAA,EACjC,cAAuC,KAAK,OAAO;AAAA,EAEnD,aAAqB;AACpB,WAAO,UAAU,KAAK,UAAU,KAAK,KAAK,eAAe,KAAK;AAAA,EAC/D;AAAA,EAES,iBAAiB,OAAsB;AAC/C,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AAAA,EAES,mBAAmB,OAA8B;AACzD,WAAO,KAAK,MAAM,KAAK;AAAA,EACxB;AACD;AAgBO,SAAS,OAAO,GAAqC,GAA6B;AACxF,QAAM,EAAE,MAAM,OAAO,IAAI,uBAAgD,GAAG,CAAC;AAC7E,SAAO,IAAI,yBAAyB,MAAM,MAAM;AACjD;", "names": []}