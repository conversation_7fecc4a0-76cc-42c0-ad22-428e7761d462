{"version": 3, "sources": ["../../../src/gel-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn, GelColumnBuilder } from './common.ts';\n\ntype GelTextBuilderInitial<TName extends string> = GelTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'GelText';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n}>;\n\nexport class GelTextBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'GelText'>,\n> extends GelColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'GelTextBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t) {\n\t\tsuper(name, 'string', 'GelText');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelText<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelText<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class GelText<T extends ColumnBaseConfig<'string', 'GelText'>>\n\textends GelColumn<T, { enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'GelText';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\tgetSQLType(): string {\n\t\treturn 'text';\n\t}\n}\n\nexport function text(): GelTextBuilderInitial<''>;\nexport function text<TName extends string>(name: TName): GelTextBuilderInitial<TName>;\nexport function text(name?: string): any {\n\treturn new GelTextBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,WAAW,wBAAwB;AAWrC,MAAM,uBAEH,iBAAoB;AAAA,EAC7B,QAA0B,UAAU,IAAY;AAAA,EAEhD,YACC,MACC;AACD,UAAM,MAAM,UAAU,SAAS;AAAA,EAChC;AAAA;AAAA,EAGS,MACR,OAC2C;AAC3C,WAAO,IAAI,QAAyC,OAAO,KAAK,MAA8C;AAAA,EAC/G;AACD;AAEO,MAAM,gBACJ,UACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAoB;AACxC,SAAO,IAAI,eAAe,QAAQ,EAAE;AACrC;", "names": []}