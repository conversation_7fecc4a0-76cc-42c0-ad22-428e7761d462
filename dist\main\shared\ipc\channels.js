"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ipcChannels = exports.ErrorReportSchema = void 0;
exports.validateIpcInput = validateIpcInput;
exports.validateIpcOutput = validateIpcOutput;
const zod_1 = require("zod");
const task_1 = require("../types/task");
// 错误报告 Schema
exports.ErrorReportSchema = zod_1.z.object({
    id: zod_1.z.string(),
    type: zod_1.z.enum(['react', 'javascript', 'promise', 'network']),
    message: zod_1.z.string(),
    stack: zod_1.z.string().optional(),
    componentStack: zod_1.z.string().optional(),
    timestamp: zod_1.z.string(),
    userAgent: zod_1.z.string(),
    url: zod_1.z.string(),
    userId: zod_1.z.string().optional(),
    sessionId: zod_1.z.string(),
    buildVersion: zod_1.z.string().optional(),
    environment: zod_1.z.enum(['development', 'production']),
    severity: zod_1.z.enum(['low', 'medium', 'high', 'critical']),
    context: zod_1.z.record(zod_1.z.any()).optional(),
    retryCount: zod_1.z.number().optional(),
});
// IPC 通道定义，使用 zod 进行类型验证
exports.ipcChannels = {
    // 任务相关操作
    'task:getAll': {
        input: zod_1.z.tuple([]),
        output: zod_1.z.array(task_1.TaskSchema),
    },
    'task:getHierarchical': {
        input: zod_1.z.tuple([]),
        output: zod_1.z.array(task_1.TaskHierarchySchema),
    },
    'task:create': {
        input: zod_1.z.tuple([task_1.CreateTaskSchema]),
        output: task_1.TaskSchema,
    },
    'task:update': {
        input: zod_1.z.tuple([zod_1.z.string(), task_1.UpdateTaskSchema]),
        output: task_1.TaskSchema,
    },
    'task:delete': {
        input: zod_1.z.tuple([zod_1.z.string()]),
        output: zod_1.z.boolean(),
    },
    'task:reorder': {
        input: zod_1.z.tuple([zod_1.z.array(task_1.ReorderTaskSchema)]),
        output: zod_1.z.boolean(),
    },
    'task:getStats': {
        input: zod_1.z.tuple([]),
        output: zod_1.z.object({
            total: zod_1.z.number(),
            completed: zod_1.z.number(),
            pending: zod_1.z.number(),
            overdue: zod_1.z.number(),
        }),
    },
    // 单个任务软删除和恢复
    'task:softDelete': {
        input: zod_1.z.tuple([zod_1.z.string()]),
        output: zod_1.z.boolean(),
    },
    'task:restore': {
        input: zod_1.z.tuple([zod_1.z.string()]),
        output: zod_1.z.boolean(),
    },
    'task:getById': {
        input: zod_1.z.tuple([zod_1.z.string()]),
        output: task_1.TaskSchema.nullable(),
    },
    // 批量操作
    'task:batchSoftDelete': {
        input: zod_1.z.tuple([zod_1.z.array(zod_1.z.string())]),
        output: zod_1.z.object({
            deletedCount: zod_1.z.number(),
            deletedTaskIds: zod_1.z.array(zod_1.z.string()),
        }),
    },
    'task:batchRestore': {
        input: zod_1.z.tuple([zod_1.z.array(zod_1.z.string())]),
        output: zod_1.z.object({
            restoredCount: zod_1.z.number(),
            restoredTaskIds: zod_1.z.array(zod_1.z.string()),
        }),
    },
    // 自动清理
    'task:cleanupDeleted': {
        input: zod_1.z.tuple([zod_1.z.number().optional()]),
        output: zod_1.z.object({
            cleanedCount: zod_1.z.number(),
            cleanedTaskIds: zod_1.z.array(zod_1.z.string()),
        }),
    },
    'task:getDeletedStats': {
        input: zod_1.z.tuple([]),
        output: zod_1.z.object({
            count: zod_1.z.number(),
            oldestDeletedAt: zod_1.z.number().nullable(),
            totalSize: zod_1.z.number(),
        }),
    },
    'task:getUndoable': {
        input: zod_1.z.tuple([]),
        output: zod_1.z.array(task_1.TaskSchema),
    },
    // 应用相关操作
    'app:getVersion': {
        input: zod_1.z.tuple([]),
        output: zod_1.z.string(),
    },
    'app:quit': {
        input: zod_1.z.tuple([]),
        output: zod_1.z.void(),
    },
    // 设置相关操作
    'settings:get': {
        input: zod_1.z.tuple([zod_1.z.string()]),
        output: zod_1.z.string().nullable(),
    },
    'settings:set': {
        input: zod_1.z.tuple([zod_1.z.string(), zod_1.z.string()]),
        output: zod_1.z.boolean(),
    },
    // 错误报告操作
    'error:report': {
        input: zod_1.z.tuple([exports.ErrorReportSchema]),
        output: zod_1.z.object({
            success: zod_1.z.boolean(),
            id: zod_1.z.string(),
        }),
    },
};
// 验证函数
function validateIpcInput(channel, input) {
    return exports.ipcChannels[channel].input.parse(input);
}
function validateIpcOutput(channel, output) {
    return exports.ipcChannels[channel].output.parse(output);
}
