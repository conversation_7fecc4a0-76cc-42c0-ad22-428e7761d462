import { bigint } from "./bigint.cjs";
import { binary } from "./binary.cjs";
import { boolean } from "./boolean.cjs";
import { char } from "./char.cjs";
import { customType } from "./custom.cjs";
import { date } from "./date.cjs";
import { datetime } from "./datetime.cjs";
import { decimal } from "./decimal.cjs";
import { double } from "./double.cjs";
import { singlestoreEnum } from "./enum.cjs";
import { float } from "./float.cjs";
import { int } from "./int.cjs";
import { json } from "./json.cjs";
import { mediumint } from "./mediumint.cjs";
import { real } from "./real.cjs";
import { serial } from "./serial.cjs";
import { smallint } from "./smallint.cjs";
import { longtext, mediumtext, text, tinytext } from "./text.cjs";
import { time } from "./time.cjs";
import { timestamp } from "./timestamp.cjs";
import { tinyint } from "./tinyint.cjs";
import { varbinary } from "./varbinary.cjs";
import { varchar } from "./varchar.cjs";
import { vector } from "./vector.cjs";
import { year } from "./year.cjs";
export declare function getSingleStoreColumnBuilders(): {
    bigint: typeof bigint;
    binary: typeof binary;
    boolean: typeof boolean;
    char: typeof char;
    customType: typeof customType;
    date: typeof date;
    datetime: typeof datetime;
    decimal: typeof decimal;
    double: typeof double;
    singlestoreEnum: typeof singlestoreEnum;
    float: typeof float;
    int: typeof int;
    json: typeof json;
    mediumint: typeof mediumint;
    real: typeof real;
    serial: typeof serial;
    smallint: typeof smallint;
    longtext: typeof longtext;
    mediumtext: typeof mediumtext;
    text: typeof text;
    tinytext: typeof tinytext;
    time: typeof time;
    timestamp: typeof timestamp;
    tinyint: typeof tinyint;
    varbinary: typeof varbinary;
    varchar: typeof varchar;
    vector: typeof vector;
    year: typeof year;
};
export type SingleStoreColumnBuilders = ReturnType<typeof getSingleStoreColumnBuilders>;
