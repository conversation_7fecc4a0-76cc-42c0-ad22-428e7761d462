{"passed": 34, "failed": 0, "tests": [{"name": "创建父任务成功", "status": "PASSED"}, {"name": "创建子任务成功", "status": "PASSED"}, {"name": "子任务父子关系正确", "status": "PASSED"}, {"name": "子任务类型正确", "status": "PASSED"}, {"name": "获取子任务列表成功", "status": "PASSED"}, {"name": "子任务ID正确", "status": "PASSED"}, {"name": "更新子任务成功", "status": "PASSED"}, {"name": "子任务状态更新正确", "status": "PASSED"}, {"name": "子任务内容更新正确", "status": "PASSED"}, {"name": "软删除子任务成功", "status": "PASSED"}, {"name": "软删除后子任务列表为空", "status": "PASSED"}, {"name": "根任务的子任务正确", "status": "PASSED"}, {"name": "第一层子任务的子任务正确", "status": "PASSED"}, {"name": "第二层子任务的子任务正确", "status": "PASSED"}, {"name": "第三层子任务没有子任务", "status": "PASSED"}, {"name": "根任务深度为0", "status": "PASSED"}, {"name": "第一层任务深度为1", "status": "PASSED"}, {"name": "第二层任务深度为2", "status": "PASSED"}, {"name": "第三层任务深度为3", "status": "PASSED"}, {"name": "子任务总数统计正确", "status": "PASSED"}, {"name": "已完成子任务数统计正确", "status": "PASSED"}, {"name": "待完成子任务数统计正确", "status": "PASSED"}, {"name": "子任务完成率计算正确", "status": "PASSED"}, {"name": "按优先级分组统计正确", "status": "PASSED"}, {"name": "层级结构创建成功", "status": "PASSED"}, {"name": "级联删除成功", "status": "PASSED"}, {"name": "所有相关任务都被正确删除", "status": "PASSED"}, {"name": "已删除任务的子任务查询为空", "status": "PASSED"}, {"name": "批量创建100个子任务成功", "status": "PASSED"}, {"name": "获取所有子任务 - 查询性能良好 (1ms < 50ms)", "status": "PASSED"}, {"name": "获取已完成子任务 - 查询性能良好 (0ms < 50ms)", "status": "PASSED"}, {"name": "子任务统计查询 - 查询性能良好 (1ms < 50ms)", "status": "PASSED"}, {"name": "层级查询 - 查询性能良好 (0ms < 50ms)", "status": "PASSED"}, {"name": "子任务查询使用了索引优化", "status": "PASSED"}]}