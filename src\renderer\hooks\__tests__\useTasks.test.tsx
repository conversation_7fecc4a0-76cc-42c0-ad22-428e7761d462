/**
 * useTasks Hook 单元测试
 * 测试任务相关的所有 hooks 功能
 */

import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import {
  useTasks,
  useTaskStats,
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
  useReorderTasks
} from '../useTasks'
import { 
  mockTasks, 
  mockTaskStats, 
  mockCreateTaskInput, 
  mockUpdateTaskInput,
  createMockTask,
  mockElectronAPI,
  resetAllMocks,
  mockHelpers
} from '../../__tests__/mocks'
import { createTestQueryClient } from '../../__tests__/test-utils'
import type { Task } from '../../../shared/types/task'

// 测试包装器
function createWrapper() {
  const queryClient = createTestQueryClient()
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useTasks', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = createTestQueryClient()
    resetAllMocks()
  })

  describe('useTasks Hook', () => {
    it('should fetch tasks successfully', async () => {
      mockHelpers.setTasksResponse(mockTasks)

      const { result } = renderHook(() => useTasks(), {
        wrapper: createWrapper(),
      })

      // 初始状态应该是 loading
      expect(result.current.isLoading).toBe(true)
      expect(result.current.data).toBeUndefined()

      // 等待数据加载完成
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockTasks)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.error).toBeNull()
      expect(mockElectronAPI.task.getAll).toHaveBeenCalledTimes(1)
    })

    it('should handle fetch tasks error', async () => {
      const error = new Error('Failed to fetch tasks')
      mockHelpers.makeTasksThrow(error)

      const { result } = renderHook(() => useTasks(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(error)
      expect(result.current.data).toBeUndefined()
      expect(mockElectronAPI.task.getAll).toHaveBeenCalledTimes(1)
    })

    it('should return empty array when no tasks', async () => {
      mockHelpers.setTasksResponse([])

      const { result } = renderHook(() => useTasks(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual([])
    })
  })

  describe('useTaskStats Hook', () => {
    it('should fetch task stats successfully', async () => {
      mockHelpers.setStatsResponse(mockTaskStats)

      const { result } = renderHook(() => useTaskStats(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockTaskStats)
      expect(mockElectronAPI.task.getStats).toHaveBeenCalledTimes(1)
    })

    it('should handle fetch stats error', async () => {
      const error = new Error('Failed to fetch stats')
      mockElectronAPI.task.getStats.mockRejectedValue(error)

      const { result } = renderHook(() => useTaskStats(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(error)
    })
  })

  describe('useCreateTask Hook', () => {
    it('should create task successfully', async () => {
      const newTask = createMockTask({ content: mockCreateTaskInput.content })
      mockHelpers.setCreateTaskResponse(newTask)
      mockHelpers.setTasksResponse([...mockTasks, newTask])

      const { result } = renderHook(() => useCreateTask(), {
        wrapper: createWrapper(),
      })

      // 执行创建任务
      result.current.mutate(mockCreateTaskInput)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(newTask)
      expect(mockElectronAPI.task.create).toHaveBeenCalledWith(mockCreateTaskInput)
    })

    it('should handle create task error', async () => {
      const error = new Error('Failed to create task')
      mockHelpers.makeCreateTaskThrow(error)

      const { result } = renderHook(() => useCreateTask(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(mockCreateTaskInput)

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(error)
    })

    it('should optimistically update task list on success', async () => {
      const newTask = createMockTask({ content: mockCreateTaskInput.content })
      mockHelpers.setCreateTaskResponse(newTask)

      // 先设置初始任务列表
      queryClient.setQueryData(['tasks'], mockTasks)

      const { result } = renderHook(() => useCreateTask(), {
        wrapper: ({ children }) => (
          <QueryClientProvider client={queryClient}>
            {children}
          </QueryClientProvider>
        ),
      })

      result.current.mutate(mockCreateTaskInput)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // 检查任务列表是否被乐观更新
      const updatedTasks = queryClient.getQueryData<Task[]>(['tasks'])
      expect(updatedTasks).toContain(newTask)
    })
  })

  describe('useUpdateTask Hook', () => {
    it('should update task successfully', async () => {
      const updatedTask = createMockTask({ 
        id: 'task-1', 
        ...mockUpdateTaskInput 
      })
      mockHelpers.setUpdateTaskResponse(updatedTask)

      const { result } = renderHook(() => useUpdateTask(), {
        wrapper: createWrapper(),
      })

      result.current.mutate({ 
        id: 'task-1', 
        input: mockUpdateTaskInput 
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(updatedTask)
      expect(mockElectronAPI.task.update).toHaveBeenCalledWith('task-1', mockUpdateTaskInput)
    })

    it('should handle update task error', async () => {
      const error = new Error('Failed to update task')
      mockElectronAPI.task.update.mockRejectedValue(error)

      const { result } = renderHook(() => useUpdateTask(), {
        wrapper: createWrapper(),
      })

      result.current.mutate({ 
        id: 'task-1', 
        input: mockUpdateTaskInput 
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(error)
    })

    it('should optimistically update task in list', async () => {
      const updatedTask = createMockTask({ 
        id: 'task-1', 
        ...mockUpdateTaskInput 
      })
      mockHelpers.setUpdateTaskResponse(updatedTask)

      // 设置初始任务列表
      queryClient.setQueryData(['tasks'], mockTasks)

      const { result } = renderHook(() => useUpdateTask(), {
        wrapper: ({ children }) => (
          <QueryClientProvider client={queryClient}>
            {children}
          </QueryClientProvider>
        ),
      })

      result.current.mutate({ 
        id: 'task-1', 
        input: mockUpdateTaskInput 
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // 检查任务是否被更新
      const tasks = queryClient.getQueryData<Task[]>(['tasks'])
      const task = tasks?.find(t => t.id === 'task-1')
      expect(task).toEqual(updatedTask)
    })
  })

  describe('useDeleteTask Hook', () => {
    it('should delete task successfully', async () => {
      mockHelpers.setDeleteTaskResponse(true)

      const { result } = renderHook(() => useDeleteTask(), {
        wrapper: createWrapper(),
      })

      result.current.mutate('task-1')

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toBe(true)
      expect(mockElectronAPI.task.delete).toHaveBeenCalledWith('task-1')
    })

    it('should handle delete task error', async () => {
      const error = new Error('Failed to delete task')
      mockElectronAPI.task.delete.mockRejectedValue(error)

      const { result } = renderHook(() => useDeleteTask(), {
        wrapper: createWrapper(),
      })

      result.current.mutate('task-1')

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(error)
    })

    it('should optimistically remove task from list', async () => {
      mockHelpers.setDeleteTaskResponse(true)

      // 设置初始任务列表
      queryClient.setQueryData(['tasks'], mockTasks)

      const { result } = renderHook(() => useDeleteTask(), {
        wrapper: ({ children }) => (
          <QueryClientProvider client={queryClient}>
            {children}
          </QueryClientProvider>
        ),
      })

      result.current.mutate('task-1')

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // 检查任务是否被移除
      const tasks = queryClient.getQueryData<Task[]>(['tasks'])
      const task = tasks?.find(t => t.id === 'task-1')
      expect(task).toBeUndefined()
    })
  })

  describe('useReorderTasks Hook', () => {
    it('should reorder tasks successfully', async () => {
      const reorderData = [
        { id: 'task-1', orderIndex: 2000 },
        { id: 'task-2', orderIndex: 1000 },
      ]
      mockHelpers.setReorderTasksResponse(true)

      const { result } = renderHook(() => useReorderTasks(), {
        wrapper: createWrapper(),
      })

      result.current.mutate(reorderData)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toBe(true)
      expect(mockElectronAPI.task.reorder).toHaveBeenCalledWith(reorderData)
    })

    it('should handle reorder tasks error', async () => {
      const error = new Error('Failed to reorder tasks')
      mockElectronAPI.task.reorder.mockRejectedValue(error)

      const { result } = renderHook(() => useReorderTasks(), {
        wrapper: createWrapper(),
      })

      const reorderData = [
        { id: 'task-1', orderIndex: 2000 },
        { id: 'task-2', orderIndex: 1000 },
      ]

      result.current.mutate(reorderData)

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toEqual(error)
    })

    it('should optimistically update task order', async () => {
      const reorderData = [
        { id: 'task-1', orderIndex: 2000 },
        { id: 'task-2', orderIndex: 1000 },
      ]
      mockHelpers.setReorderTasksResponse(true)

      // 设置初始任务列表
      queryClient.setQueryData(['tasks'], mockTasks)

      const { result } = renderHook(() => useReorderTasks(), {
        wrapper: ({ children }) => (
          <QueryClientProvider client={queryClient}>
            {children}
          </QueryClientProvider>
        ),
      })

      result.current.mutate(reorderData)

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // 检查任务顺序是否被更新
      const tasks = queryClient.getQueryData<Task[]>(['tasks'])
      const task1 = tasks?.find(t => t.id === 'task-1')
      const task2 = tasks?.find(t => t.id === 'task-2')
      
      expect(task1?.orderIndex).toBe(2000)
      expect(task2?.orderIndex).toBe(1000)
    })
  })
})
