const fs = require('fs')
const path = require('path')

console.log('🎨 LinganApp 主题切换功能全面测试')
console.log('='.repeat(50))

// 测试结果记录
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
}

function logTest(category, testName, status, details = '') {
  const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️'
  const message = `${statusIcon} [${category}] ${testName}`
  console.log(message)
  
  if (details) {
    console.log(`   ${details}`)
  }
  
  testResults.details.push({ category, testName, status, details })
  
  if (status === 'PASS') testResults.passed++
  else if (status === 'FAIL') testResults.failed++
  else testResults.warnings++
}

// 1. 基础功能测试
console.log('\n📋 1. 基础功能测试')
console.log('-'.repeat(30))

// 检查主题切换按钮实现
const modernLayoutContent = fs.readFileSync('src/renderer/components/layout/ModernLayout.tsx', 'utf8')

// 检查 toggleMode 函数调用
if (modernLayoutContent.includes('onClick={toggleMode}')) {
  logTest('基础功能', '主题切换按钮点击事件绑定', 'PASS', '正确绑定 toggleMode 函数')
} else {
  logTest('基础功能', '主题切换按钮点击事件绑定', 'FAIL', '未找到 toggleMode 绑定')
}

// 检查图标切换逻辑
if (modernLayoutContent.includes('currentTheme.mode === \'dark\' ? <Sun') && 
    modernLayoutContent.includes('Moon')) {
  logTest('基础功能', '主题图标切换逻辑', 'PASS', '深色模式显示太阳图标，浅色模式显示月亮图标')
} else {
  logTest('基础功能', '主题图标切换逻辑', 'FAIL', '图标切换逻辑不正确')
}

// 检查 ThemeContext 中的 toggleMode 实现
const themeContextContent = fs.readFileSync('src/renderer/contexts/ThemeContext.tsx', 'utf8')

// 检查三种模式循环切换
const toggleModeMatch = themeContextContent.match(/const toggleMode.*?=.*?useCallback\(async \(\) => \{([\s\S]*?)\}, \[.*?\]\)/m)
if (toggleModeMatch) {
  const toggleModeCode = toggleModeMatch[1]
  if (toggleModeCode.includes('light') && toggleModeCode.includes('dark') && toggleModeCode.includes('system')) {
    logTest('基础功能', '三种主题模式循环切换', 'PASS', '支持 浅色 → 深色 → 系统跟随 循环')
  } else {
    logTest('基础功能', '三种主题模式循环切换', 'FAIL', '未找到完整的三种模式切换逻辑')
  }
} else {
  logTest('基础功能', '三种主题模式循环切换', 'FAIL', '未找到 toggleMode 函数实现')
}

// 2. 视觉效果验证
console.log('\n🎭 2. 视觉效果验证')
console.log('-'.repeat(30))

// 检查 CSS 过渡动画
const cssContent = fs.readFileSync('src/renderer/globals.css', 'utf8')

// 检查过渡持续时间
if (cssContent.includes('--theme-transition-duration: 300ms')) {
  logTest('视觉效果', '过渡动画持续时间设置', 'PASS', '设置为 300ms')
} else {
  logTest('视觉效果', '过渡动画持续时间设置', 'FAIL', '未找到 300ms 过渡时间设置')
}

// 检查主题过渡动画类
if (cssContent.includes('.theme-transition') && 
    cssContent.includes('background-color var(--theme-transition-duration)') &&
    cssContent.includes('color var(--theme-transition-duration)')) {
  logTest('视觉效果', '主题过渡动画类', 'PASS', '包含背景色和文字颜色过渡')
} else {
  logTest('视觉效果', '主题过渡动画类', 'FAIL', '主题过渡动画类不完整')
}

// 检查根元素过渡
if (cssContent.includes(':root') && cssContent.includes('transition:')) {
  logTest('视觉效果', '根元素过渡动画', 'PASS', '根元素包含过渡动画')
} else {
  logTest('视觉效果', '根元素过渡动画', 'FAIL', '根元素缺少过渡动画')
}

// 检查内置主题定义
const themeTypesContent = fs.readFileSync('src/shared/types/theme.ts', 'utf8')

// 检查浅色和深色主题
if (themeTypesContent.includes('light-default') && themeTypesContent.includes('dark-default')) {
  logTest('视觉效果', '内置主题定义', 'PASS', '包含浅色和深色默认主题')
} else {
  logTest('视觉效果', '内置主题定义', 'FAIL', '缺少基础主题定义')
}

// 检查主题颜色配置完整性
const colorProperties = [
  'background', 'foreground', 'card', 'cardForeground', 'primary', 'primaryForeground',
  'secondary', 'muted', 'accent', 'border', 'input', 'ring'
]

let missingColors = []
colorProperties.forEach(color => {
  if (!themeTypesContent.includes(`${color}:`)) {
    missingColors.push(color)
  }
})

if (missingColors.length === 0) {
  logTest('视觉效果', '主题颜色配置完整性', 'PASS', '所有必需的颜色属性都已定义')
} else {
  logTest('视觉效果', '主题颜色配置完整性', 'WARN', `缺少颜色属性: ${missingColors.join(', ')}`)
}

// 3. 状态持久化测试
console.log('\n💾 3. 状态持久化测试')
console.log('-'.repeat(30))

// 检查设置 API 调用
if (themeContextContent.includes('settingsAPI.set') && 
    themeContextContent.includes('theme_config')) {
  logTest('状态持久化', '主题配置保存', 'PASS', '使用 settingsAPI 保存主题配置')
} else {
  logTest('状态持久化', '主题配置保存', 'FAIL', '未找到主题配置保存逻辑')
}

// 检查设置加载
if (themeContextContent.includes('settingsAPI.get') && 
    themeContextContent.includes('theme_config')) {
  logTest('状态持久化', '主题配置加载', 'PASS', '应用启动时加载保存的主题配置')
} else {
  logTest('状态持久化', '主题配置加载', 'FAIL', '未找到主题配置加载逻辑')
}

// 检查默认配置
if (themeTypesContent.includes('DEFAULT_THEME_CONFIG')) {
  logTest('状态持久化', '默认主题配置', 'PASS', '定义了默认主题配置')
} else {
  logTest('状态持久化', '默认主题配置', 'FAIL', '未找到默认主题配置')
}

// 4. 系统主题跟随测试
console.log('\n🖥️ 4. 系统主题跟随测试')
console.log('-'.repeat(30))

// 检查系统主题检测
if (themeContextContent.includes('useSystemTheme') && 
    themeContextContent.includes('window.matchMedia') &&
    themeContextContent.includes('prefers-color-scheme: dark')) {
  logTest('系统主题跟随', '系统主题检测', 'PASS', '使用 matchMedia 检测系统主题偏好')
} else {
  logTest('系统主题跟随', '系统主题检测', 'FAIL', '未找到系统主题检测逻辑')
}

// 检查系统主题监听
if (themeContextContent.includes('addEventListener') && 
    themeContextContent.includes('change')) {
  logTest('系统主题跟随', '系统主题变化监听', 'PASS', '监听系统主题变化事件')
} else {
  logTest('系统主题跟随', '系统主题变化监听', 'FAIL', '未找到系统主题变化监听')
}

// 检查有效主题获取逻辑
if (themeContextContent.includes('getEffectiveTheme') && 
    themeContextContent.includes('config.mode === \'system\'')) {
  logTest('系统主题跟随', '系统模式主题选择', 'PASS', '系统模式下根据系统主题选择对应主题')
} else {
  logTest('系统主题跟随', '系统模式主题选择', 'FAIL', '未找到系统模式主题选择逻辑')
}

// 5. 工具提示测试
console.log('\n💬 5. 工具提示测试')
console.log('-'.repeat(30))

// 检查工具提示实现
if (modernLayoutContent.includes('title=') && 
    modernLayoutContent.includes('当前主题') &&
    modernLayoutContent.includes('currentTheme.name')) {
  logTest('工具提示', '主题切换按钮工具提示', 'PASS', '显示当前主题名称和模式')
} else {
  logTest('工具提示', '主题切换按钮工具提示', 'FAIL', '未找到工具提示实现')
}

// 6. 错误处理测试
console.log('\n🛡️ 6. 错误处理测试')
console.log('-'.repeat(30))

// 检查 useTheme hook 错误处理
if (themeContextContent.includes('throw new Error') && 
    themeContextContent.includes('useTheme must be used within a ThemeProvider')) {
  logTest('错误处理', 'useTheme Hook 错误处理', 'PASS', '在 ThemeProvider 外使用时抛出错误')
} else {
  logTest('错误处理', 'useTheme Hook 错误处理', 'FAIL', '缺少 useTheme 错误处理')
}

// 检查主题应用错误处理
if (themeContextContent.includes('try') && themeContextContent.includes('catch')) {
  logTest('错误处理', '主题配置加载错误处理', 'PASS', '包含 try-catch 错误处理')
} else {
  logTest('错误处理', '主题配置加载错误处理', 'WARN', '建议添加更多错误处理')
}

// 检查主题 ID 验证
if (themeTypesContent.includes('isValidThemeId') || 
    themeContextContent.includes('getThemeById')) {
  logTest('错误处理', '主题 ID 验证', 'PASS', '包含主题 ID 验证逻辑')
} else {
  logTest('错误处理', '主题 ID 验证', 'WARN', '建议添加主题 ID 验证')
}

// 7. 集成测试
console.log('\n🔗 7. 集成测试')
console.log('-'.repeat(30))

// 检查 App.tsx 集成
const appContent = fs.readFileSync('src/renderer/App.tsx', 'utf8')

if (appContent.includes('ThemeProvider') && 
    appContent.includes('theme-transition')) {
  logTest('集成测试', 'App.tsx 主题集成', 'PASS', 'ThemeProvider 正确包装应用并添加过渡类')
} else {
  logTest('集成测试', 'App.tsx 主题集成', 'FAIL', 'App.tsx 主题集成不完整')
}

// 检查 ModernLayout 集成
if (modernLayoutContent.includes('useTheme') && 
    modernLayoutContent.includes('ThemeContext')) {
  logTest('集成测试', 'ModernLayout 主题集成', 'PASS', '正确使用 ThemeContext')
} else {
  logTest('集成测试', 'ModernLayout 主题集成', 'FAIL', 'ModernLayout 主题集成不正确')
}

// 输出测试总结
console.log('\n' + '='.repeat(50))
console.log('📊 测试结果总结')
console.log('='.repeat(50))

console.log(`✅ 通过: ${testResults.passed} 项`)
console.log(`❌ 失败: ${testResults.failed} 项`)
console.log(`⚠️  警告: ${testResults.warnings} 项`)
console.log(`📋 总计: ${testResults.passed + testResults.failed + testResults.warnings} 项测试`)

const successRate = ((testResults.passed / (testResults.passed + testResults.failed + testResults.warnings)) * 100).toFixed(1)
console.log(`🎯 成功率: ${successRate}%`)

if (testResults.failed === 0) {
  console.log('\n🎉 所有关键功能测试通过！主题切换功能实现完整。')
  
  console.log('\n📋 功能确认清单:')
  console.log('✅ 基础主题切换功能 (浅色 → 深色 → 系统跟随)')
  console.log('✅ 主题图标正确显示 (太阳/月亮)')
  console.log('✅ 平滑过渡动画 (300ms)')
  console.log('✅ 主题状态持久化')
  console.log('✅ 系统主题跟随')
  console.log('✅ 工具提示显示')
  console.log('✅ 错误处理机制')
  console.log('✅ 应用集成完整')
  
} else {
  console.log('\n⚠️ 发现问题，需要修复以下失败项:')
  testResults.details
    .filter(test => test.status === 'FAIL')
    .forEach(test => {
      console.log(`❌ [${test.category}] ${test.testName}: ${test.details}`)
    })
}

if (testResults.warnings > 0) {
  console.log('\n💡 建议改进项:')
  testResults.details
    .filter(test => test.status === 'WARN')
    .forEach(test => {
      console.log(`⚠️ [${test.category}] ${test.testName}: ${test.details}`)
    })
}

console.log('\n🔧 手动测试建议:')
console.log('1. 启动应用后点击左侧边栏的主题切换按钮')
console.log('2. 观察主题是否按 浅色 → 深色 → 系统跟随 顺序切换')
console.log('3. 检查图标是否正确更新 (深色模式显示太阳，浅色模式显示月亮)')
console.log('4. 验证界面颜色和过渡动画效果')
console.log('5. 重启应用确认主题设置是否保持')
console.log('6. 悬停按钮查看工具提示')
console.log('7. 更改系统主题设置测试跟随功能')

process.exit(testResults.failed > 0 ? 1 : 0)
