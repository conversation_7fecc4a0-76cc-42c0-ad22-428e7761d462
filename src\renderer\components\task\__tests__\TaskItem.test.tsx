/**
 * TaskItem 组件集成测试
 * 测试任务项组件的渲染和交互功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { TaskItem } from '../TaskItem'
import { 
  mockTask, 
  mockCompletedTask, 
  mockHighPriorityTask, 
  mockOverdueTask,
  resetAllMocks 
} from '../../../__tests__/mocks'
import { renderWithProviders } from '../../../__tests__/test-utils'
import { TaskPriority } from '../../../../shared/types/task'

describe('TaskItem', () => {
  beforeEach(() => {
    resetAllMocks()
  })

  describe('Rendering', () => {
    it('should render task content', () => {
      renderWithProviders(<TaskItem task={mockTask} />)
      
      expect(screen.getByText(mockTask.content)).toBeInTheDocument()
    })

    it('should render task with correct priority', () => {
      renderWithProviders(<TaskItem task={mockHighPriorityTask} />)
      
      // 检查是否显示高优先级标识
      expect(screen.getByText('高')).toBeInTheDocument()
    })

    it('should render completed task with strikethrough', () => {
      renderWithProviders(<TaskItem task={mockCompletedTask} />)
      
      const taskContent = screen.getByText(mockCompletedTask.content)
      expect(taskContent).toHaveClass('line-through')
    })

    it('should render overdue task with warning style', () => {
      renderWithProviders(<TaskItem task={mockOverdueTask} />)
      
      // 检查是否有过期标识
      const container = screen.getByTestId(`task-item-${mockOverdueTask.id}`)
      expect(container).toHaveClass('border-red-200')
    })

    it('should render due date when present', () => {
      const taskWithDueDate = {
        ...mockTask,
        dueDate: Date.now() + 86400000, // 明天
      }
      
      renderWithProviders(<TaskItem task={taskWithDueDate} />)
      
      // 检查是否显示截止日期
      expect(screen.getByText(/明天/)).toBeInTheDocument()
    })

    it('should not render due date when null', () => {
      renderWithProviders(<TaskItem task={mockTask} />)
      
      // 不应该显示截止日期相关文本
      expect(screen.queryByText(/截止/)).not.toBeInTheDocument()
    })
  })

  describe('Interactions', () => {
    it('should toggle completion status when checkbox clicked', async () => {
      const onToggle = vi.fn()
      renderWithProviders(
        <TaskItem task={mockTask} onToggle={onToggle} />
      )
      
      const checkbox = screen.getByRole('checkbox')
      fireEvent.click(checkbox)
      
      expect(onToggle).toHaveBeenCalledWith(mockTask.id)
    })

    it('should call onEdit when edit button clicked', async () => {
      const onEdit = vi.fn()
      renderWithProviders(
        <TaskItem task={mockTask} onEdit={onEdit} />
      )
      
      const editButton = screen.getByLabelText('编辑任务')
      fireEvent.click(editButton)
      
      expect(onEdit).toHaveBeenCalledWith(mockTask)
    })

    it('should call onDelete when delete button clicked', async () => {
      const onDelete = vi.fn()
      renderWithProviders(
        <TaskItem task={mockTask} onDelete={onDelete} />
      )
      
      const deleteButton = screen.getByLabelText('删除任务')
      fireEvent.click(deleteButton)
      
      expect(onDelete).toHaveBeenCalledWith(mockTask.id)
    })

    it('should enter edit mode when double clicked', async () => {
      const onEdit = vi.fn()
      renderWithProviders(
        <TaskItem task={mockTask} onEdit={onEdit} />
      )
      
      const taskContent = screen.getByText(mockTask.content)
      fireEvent.doubleClick(taskContent)
      
      expect(onEdit).toHaveBeenCalledWith(mockTask)
    })

    it('should handle keyboard navigation', async () => {
      const onToggle = vi.fn()
      renderWithProviders(
        <TaskItem task={mockTask} onToggle={onToggle} />
      )
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      
      // 按 Space 键应该切换完成状态
      fireEvent.keyDown(taskItem, { key: ' ' })
      expect(onToggle).toHaveBeenCalledWith(mockTask.id)
    })

    it('should handle Enter key for edit', async () => {
      const onEdit = vi.fn()
      renderWithProviders(
        <TaskItem task={mockTask} onEdit={onEdit} />
      )
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      
      // 按 Enter 键应该进入编辑模式
      fireEvent.keyDown(taskItem, { key: 'Enter' })
      expect(onEdit).toHaveBeenCalledWith(mockTask)
    })
  })

  describe('Priority Display', () => {
    it('should display high priority correctly', () => {
      const highPriorityTask = {
        ...mockTask,
        priority: TaskPriority.HIGH,
      }
      
      renderWithProviders(<TaskItem task={highPriorityTask} />)
      
      const priorityBadge = screen.getByText('高')
      expect(priorityBadge).toHaveClass('text-red-600')
    })

    it('should display medium priority correctly', () => {
      const mediumPriorityTask = {
        ...mockTask,
        priority: TaskPriority.MEDIUM,
      }
      
      renderWithProviders(<TaskItem task={mediumPriorityTask} />)
      
      const priorityBadge = screen.getByText('中')
      expect(priorityBadge).toHaveClass('text-yellow-600')
    })

    it('should display low priority correctly', () => {
      const lowPriorityTask = {
        ...mockTask,
        priority: TaskPriority.LOW,
      }
      
      renderWithProviders(<TaskItem task={lowPriorityTask} />)
      
      const priorityBadge = screen.getByText('低')
      expect(priorityBadge).toHaveClass('text-green-600')
    })
  })

  describe('Drag and Drop', () => {
    it('should be draggable when drag handlers provided', () => {
      const dragHandlers = {
        onDragStart: vi.fn(),
        onDragEnd: vi.fn(),
      }
      
      renderWithProviders(
        <TaskItem task={mockTask} {...dragHandlers} />
      )
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      expect(taskItem).toHaveAttribute('draggable', 'true')
    })

    it('should call onDragStart when drag starts', () => {
      const onDragStart = vi.fn()
      
      renderWithProviders(
        <TaskItem task={mockTask} onDragStart={onDragStart} />
      )
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      fireEvent.dragStart(taskItem)
      
      expect(onDragStart).toHaveBeenCalled()
    })

    it('should call onDragEnd when drag ends', () => {
      const onDragEnd = vi.fn()
      
      renderWithProviders(
        <TaskItem task={mockTask} onDragEnd={onDragEnd} />
      )
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      fireEvent.dragEnd(taskItem)
      
      expect(onDragEnd).toHaveBeenCalled()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      renderWithProviders(<TaskItem task={mockTask} />)
      
      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).toHaveAttribute('aria-label', `标记任务"${mockTask.content}"为已完成`)
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      expect(taskItem).toHaveAttribute('role', 'listitem')
    })

    it('should be keyboard accessible', () => {
      renderWithProviders(<TaskItem task={mockTask} />)
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      expect(taskItem).toHaveAttribute('tabIndex', '0')
    })

    it('should have proper focus management', async () => {
      renderWithProviders(<TaskItem task={mockTask} />)
      
      const taskItem = screen.getByTestId(`task-item-${mockTask.id}`)
      taskItem.focus()
      
      expect(taskItem).toHaveFocus()
    })
  })

  describe('Loading States', () => {
    it('should show loading state when updating', () => {
      renderWithProviders(
        <TaskItem task={mockTask} isUpdating={true} />
      )
      
      // 检查是否显示加载指示器
      expect(screen.getByTestId('task-loading')).toBeInTheDocument()
    })

    it('should disable interactions when loading', () => {
      renderWithProviders(
        <TaskItem task={mockTask} isUpdating={true} />
      )
      
      const checkbox = screen.getByRole('checkbox')
      expect(checkbox).toBeDisabled()
    })
  })

  describe('Error States', () => {
    it('should show error state when operation fails', () => {
      renderWithProviders(
        <TaskItem task={mockTask} hasError={true} />
      )
      
      // 检查是否显示错误指示器
      expect(screen.getByTestId('task-error')).toBeInTheDocument()
    })

    it('should show retry button on error', () => {
      const onRetry = vi.fn()
      renderWithProviders(
        <TaskItem task={mockTask} hasError={true} onRetry={onRetry} />
      )
      
      const retryButton = screen.getByLabelText('重试')
      fireEvent.click(retryButton)
      
      expect(onRetry).toHaveBeenCalledWith(mockTask.id)
    })
  })
})
