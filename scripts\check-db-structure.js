#!/usr/bin/env node

/**
 * 检查数据库结构脚本
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const dbPath = path.join(dataDir, 'app.db');

console.log('🔍 检查数据库结构...');

if (!fs.existsSync(dbPath)) {
  console.error('❌ 数据库文件不存在:', dbPath);
  process.exit(1);
}

const db = new Database(dbPath);

try {
  // 获取表结构
  console.log('\n📊 tasks 表结构:');
  const tableInfo = db.prepare("PRAGMA table_info(tasks)").all();
  
  tableInfo.forEach(column => {
    console.log(`   ${column.name} (${column.type}) - ${column.notnull ? 'NOT NULL' : 'NULL'} - ${column.dflt_value ? 'DEFAULT: ' + column.dflt_value : 'NO DEFAULT'}`);
  });
  
  // 获取现有索引
  console.log('\n📋 现有索引:');
  const indexes = db.prepare(`
    SELECT name, sql 
    FROM sqlite_master 
    WHERE type='index' 
      AND name NOT LIKE 'sqlite_%' 
      AND tbl_name='tasks'
    ORDER BY name
  `).all();
  
  indexes.forEach(index => {
    console.log(`   ${index.name}`);
    if (index.sql) {
      console.log(`      ${index.sql}`);
    }
  });
  
  // 检查数据
  console.log('\n📝 数据统计:');
  const totalTasks = db.prepare("SELECT COUNT(*) as count FROM tasks").get();
  console.log(`   总任务数: ${totalTasks.count}`);
  
  if (totalTasks.count > 0) {
    // 检查是否有父子关系数据
    const hasParentTasks = db.prepare("SELECT COUNT(*) as count FROM tasks WHERE parent_task_id IS NOT NULL").get();
    console.log(`   有父任务的任务数: ${hasParentTasks.count}`);
    
    const taskTypes = db.prepare("SELECT task_type, COUNT(*) as count FROM tasks GROUP BY task_type").all();
    console.log('   任务类型分布:');
    taskTypes.forEach(type => {
      console.log(`      ${type.task_type}: ${type.count}`);
    });
  }
  
} catch (error) {
  console.error('❌ 检查数据库结构时发生错误:', error);
} finally {
  db.close();
}
