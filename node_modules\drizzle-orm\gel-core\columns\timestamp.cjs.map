{"version": 3, "sources": ["../../../src/gel-core/columns/timestamp.ts"], "sourcesContent": ["import type { LocalDateTime } from 'gel';\nimport type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyGelTable } from '~/gel-core/table.ts';\nimport { GelColumn } from './common.ts';\nimport { GelLocalDateColumnBaseBuilder } from './date.common.ts';\n\nexport type GelTimestampBuilderInitial<TName extends string> = GelTimestampBuilder<{\n\tname: TName;\n\tdataType: 'localDateTime';\n\tcolumnType: 'GelTimestamp';\n\tdata: LocalDateTime;\n\tdriverParam: LocalDateTime;\n\tenumValues: undefined;\n}>;\n\nexport class GelTimestampBuilder<T extends ColumnBuilderBaseConfig<'localDateTime', 'GelTimestamp'>>\n\textends GelLocalDateColumnBaseBuilder<\n\t\tT\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'GelTimestampBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t) {\n\t\tsuper(name, 'localDateTime', 'GelTimestamp');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyGelTable<{ name: TTableName }>,\n\t): GelTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new GelTimestamp<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class GelTimestamp<T extends ColumnBaseConfig<'localDateTime', 'GelTimestamp'>> extends GelColumn<T> {\n\tstatic override readonly [entityKind]: string = 'GelTimestamp';\n\n\tconstructor(table: AnyGelTable<{ name: T['tableName'] }>, config: GelTimestampBuilder<T>['config']) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn 'cal::local_datetime';\n\t}\n}\n\nexport function timestamp(): GelTimestampBuilderInitial<''>;\nexport function timestamp<TName extends string>(\n\tname: TName,\n): GelTimestampBuilderInitial<TName>;\nexport function timestamp(name?: string) {\n\treturn new GelTimestampBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,oBAA2B;AAE3B,oBAA0B;AAC1B,yBAA8C;AAWvC,MAAM,4BACJ,iDAGT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YACC,MACC;AACD,UAAM,MAAM,iBAAiB,cAAc;AAAA,EAC5C;AAAA;AAAA,EAGS,MACR,OACgD;AAChD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBAAkF,wBAAa;AAAA,EAC3G,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,OAA8C,QAA0C;AACnG,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAMO,SAAS,UAAU,MAAe;AACxC,SAAO,IAAI,oBAAoB,QAAQ,EAAE;AAC1C;", "names": []}