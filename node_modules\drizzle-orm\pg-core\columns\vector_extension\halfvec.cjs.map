{"version": 3, "sources": ["../../../../src/pg-core/columns/vector_extension/halfvec.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgHalfVectorBuilderInitial<TName extends string, TDimensions extends number> = PgHalfVectorBuilder<{\n\tname: TName;\n\tdataType: 'array';\n\tcolumnType: 'PgHalfVector';\n\tdata: number[];\n\tdriverParam: string;\n\tenumValues: undefined;\n\tdimensions: TDimensions;\n}>;\n\nexport class PgHalfVectorBuilder<T extends ColumnBuilderBaseConfig<'array', 'PgHalfVector'> & { dimensions: number }>\n\textends PgColumnBuilder<\n\t\tT,\n\t\t{ dimensions: T['dimensions'] },\n\t\t{ dimensions: T['dimensions'] }\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'PgHalfVectorBuilder';\n\n\tconstructor(name: string, config: PgHalfVectorConfig<T['dimensions']>) {\n\t\tsuper(name, 'array', 'PgHalfVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgHalfVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }> {\n\t\treturn new PgHalfVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgHalfVector<T extends ColumnBaseConfig<'array', 'PgHalfVector'> & { dimensions: number }>\n\textends PgColumn<T, { dimensions: T['dimensions'] }, { dimensions: T['dimensions'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgHalfVector';\n\n\treadonly dimensions: T['dimensions'] = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `halfvec(${this.dimensions})`;\n\t}\n\n\toverride mapToDriverValue(value: unknown): unknown {\n\t\treturn JSON.stringify(value);\n\t}\n\n\toverride mapFromDriverValue(value: string): unknown {\n\t\treturn value\n\t\t\t.slice(1, -1)\n\t\t\t.split(',')\n\t\t\t.map((v) => Number.parseFloat(v));\n\t}\n}\n\nexport interface PgHalfVectorConfig<TDimensions extends number = number> {\n\tdimensions: TDimensions;\n}\n\nexport function halfvec<D extends number>(\n\tconfig: PgHalfVectorConfig<D>,\n): PgHalfVectorBuilderInitial<'', D>;\nexport function halfvec<TName extends string, D extends number>(\n\tname: TName,\n\tconfig: PgHalfVectorConfig,\n): PgHalfVectorBuilderInitial<TName, D>;\nexport function halfvec(a: string | PgHalfVectorConfig, b?: PgHalfVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgHalfVectorConfig>(a, b);\n\treturn new PgHalfVectorBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA0C;AAYnC,MAAM,4BACJ,8BAKT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAc,QAA6C;AACtE,UAAM,MAAM,SAAS,cAAc;AACnC,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OACkF;AAClF,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,qBACJ,uBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,aAA8B,KAAK,OAAO;AAAA,EAEnD,aAAqB;AACpB,WAAO,WAAW,KAAK,UAAU;AAAA,EAClC;AAAA,EAES,iBAAiB,OAAyB;AAClD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AAAA,EAES,mBAAmB,OAAwB;AACnD,WAAO,MACL,MAAM,GAAG,EAAE,EACX,MAAM,GAAG,EACT,IAAI,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;AAAA,EAClC;AACD;AAaO,SAAS,QAAQ,GAAgC,GAAwB;AAC/E,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA2C,GAAG,CAAC;AACxE,SAAO,IAAI,oBAAoB,MAAM,MAAM;AAC5C;", "names": []}