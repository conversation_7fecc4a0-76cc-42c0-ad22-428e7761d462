# LinganApp 数据库维护指南

## 📋 概述

本指南提供了 LinganApp 数据库的日常维护、监控和故障排除方法，确保数据库系统的稳定运行和最佳性能。

## 🔧 日常维护任务

### 1. 每日检查

#### 1.1 数据库状态检查
```bash
# 检查数据库文件是否存在且可访问
ls -la data/app.db

# 检查数据库文件大小
du -h data/app.db
```

#### 1.2 性能监控
```javascript
// 在应用中获取性能统计
const taskService = new TaskService();
const stats = taskService.getPerformanceStats();

console.log('数据库性能统计:');
console.log(`- 平均查询时间: ${stats.averageDuration}ms`);
console.log(`- 慢查询数量: ${stats.slowQueries}`);
console.log(`- 总查询数: ${stats.totalQueries}`);
```

#### 1.3 错误日志检查
```bash
# 检查应用日志中的数据库错误
grep -i "database\|sqlite\|error" logs/app.log | tail -20
```

### 2. 每周维护

#### 2.1 统计信息更新
```sql
-- 更新表统计信息以优化查询计划
ANALYZE tasks;
ANALYZE task_templates;
ANALYZE task_dependencies;
ANALYZE settings;
ANALYZE tags;
ANALYZE task_tags;
ANALYZE recurring_rules;
```

#### 2.2 数据库完整性检查
```sql
-- 检查数据库完整性
PRAGMA integrity_check;

-- 检查外键约束
PRAGMA foreign_key_check;
```

#### 2.3 性能分析
```sql
-- 查看查询计划
EXPLAIN QUERY PLAN SELECT * FROM tasks WHERE is_completed = 0 ORDER BY order_index;

-- 检查索引使用情况
SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%';
```

### 3. 每月维护

#### 3.1 数据库清理
```sql
-- 增量清理删除的数据
PRAGMA incremental_vacuum;

-- 检查清理效果
PRAGMA freelist_count;
```

#### 3.2 备份验证
```bash
# 创建数据库备份
cp data/app.db data/backup/app_$(date +%Y%m%d).db

# 验证备份完整性
sqlite3 data/backup/app_$(date +%Y%m%d).db "PRAGMA integrity_check;"
```

#### 3.3 容量分析
```sql
-- 分析表大小
SELECT 
    name,
    COUNT(*) as record_count,
    SUM(LENGTH(content)) as content_size
FROM (
    SELECT 'tasks' as name, content FROM tasks
    UNION ALL
    SELECT 'task_templates' as name, content FROM task_templates
) GROUP BY name;
```

## 📊 监控指标

### 1. 性能指标

#### 1.1 关键性能指标 (KPI)
- **平均查询时间**: < 50ms (目标)
- **慢查询比例**: < 5% (目标)
- **数据库响应时间**: < 100ms (目标)
- **并发连接数**: < 10 (当前限制)

#### 1.2 监控脚本
```javascript
// scripts/monitor-performance.js
const Database = require('better-sqlite3');
const path = require('path');

function monitorPerformance() {
    const dbPath = path.join(process.cwd(), 'data', 'app.db');
    const db = new Database(dbPath);
    
    // 测试查询性能
    const queries = [
        'SELECT COUNT(*) FROM tasks WHERE deleted_at IS NULL',
        'SELECT * FROM tasks WHERE is_completed = 0 ORDER BY order_index LIMIT 10',
        'SELECT * FROM tasks WHERE priority = 1 AND is_completed = 0 LIMIT 10'
    ];
    
    queries.forEach(sql => {
        const start = Date.now();
        db.prepare(sql).all();
        const duration = Date.now() - start;
        
        console.log(`查询: ${sql.substring(0, 50)}...`);
        console.log(`耗时: ${duration}ms`);
        
        if (duration > 100) {
            console.warn(`⚠️ 慢查询检测: ${duration}ms`);
        }
    });
    
    db.close();
}

// 每小时运行一次
setInterval(monitorPerformance, 60 * 60 * 1000);
```

### 2. 容量指标

#### 2.1 存储监控
```sql
-- 数据库大小
SELECT page_count * page_size / 1024 / 1024 as size_mb 
FROM pragma_page_count(), pragma_page_size();

-- 各表记录数
SELECT 
    'tasks' as table_name, 
    COUNT(*) as record_count,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_records
FROM tasks
UNION ALL
SELECT 'task_templates', COUNT(*), COUNT(*) FROM task_templates
UNION ALL
SELECT 'settings', COUNT(*), COUNT(*) FROM settings;
```

#### 2.2 增长趋势分析
```sql
-- 按月统计任务创建数量
SELECT 
    strftime('%Y-%m', datetime(created_at/1000, 'unixepoch')) as month,
    COUNT(*) as tasks_created
FROM tasks 
WHERE created_at > (unixepoch() - 365*24*60*60) * 1000
GROUP BY month
ORDER BY month;
```

## 🚨 故障排除

### 1. 常见问题

#### 1.1 数据库锁定
**症状**: 查询超时或返回 "database is locked" 错误

**解决方案**:
```sql
-- 检查是否有长时间运行的事务
PRAGMA wal_checkpoint;

-- 如果问题持续，重启应用
```

**预防措施**:
- 确保所有事务及时提交或回滚
- 避免长时间持有数据库连接
- 使用连接池管理连接

#### 1.2 查询性能下降
**症状**: 查询时间显著增加

**诊断步骤**:
```sql
-- 1. 检查统计信息是否过期
SELECT name FROM sqlite_master WHERE type='table';
ANALYZE;

-- 2. 检查索引是否存在
SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'tasks_%';

-- 3. 分析查询计划
EXPLAIN QUERY PLAN SELECT * FROM tasks WHERE is_completed = 0;
```

**解决方案**:
- 更新统计信息: `ANALYZE`
- 重建索引: `REINDEX`
- 检查是否需要新索引

#### 1.3 数据库文件损坏
**症状**: 完整性检查失败

**诊断**:
```sql
PRAGMA integrity_check;
```

**恢复步骤**:
```bash
# 1. 停止应用
# 2. 从最近的备份恢复
cp data/backup/app_latest.db data/app.db

# 3. 验证恢复的数据库
sqlite3 data/app.db "PRAGMA integrity_check;"

# 4. 重启应用
```

### 2. 性能优化

#### 2.1 慢查询优化
```sql
-- 识别慢查询模式
-- 检查是否缺少索引
EXPLAIN QUERY PLAN [慢查询SQL];

-- 添加必要的索引
CREATE INDEX IF NOT EXISTS idx_name ON table_name(column_name);
```

#### 2.2 内存优化
```sql
-- 调整缓存大小
PRAGMA cache_size = 10000;  -- 10MB缓存

-- 检查内存使用
PRAGMA cache_size;
PRAGMA page_size;
```

## 🔄 备份和恢复

### 1. 备份策略

#### 1.1 自动备份脚本
```bash
#!/bin/bash
# scripts/backup-database.sh

BACKUP_DIR="data/backup"
DB_FILE="data/app.db"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/app_$DATE.db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 在线备份（不锁定数据库）
sqlite3 $DB_FILE ".backup $BACKUP_FILE"

# 验证备份
if sqlite3 $BACKUP_FILE "PRAGMA integrity_check;" | grep -q "ok"; then
    echo "✅ 备份成功: $BACKUP_FILE"
    
    # 清理7天前的备份
    find $BACKUP_DIR -name "app_*.db" -mtime +7 -delete
else
    echo "❌ 备份验证失败: $BACKUP_FILE"
    rm -f $BACKUP_FILE
    exit 1
fi
```

#### 1.2 备份计划
- **每日备份**: 保留7天
- **每周备份**: 保留4周
- **每月备份**: 保留12个月

### 2. 恢复流程

#### 2.1 完整恢复
```bash
# 1. 停止应用
systemctl stop linganapp

# 2. 备份当前数据库
cp data/app.db data/app.db.corrupted

# 3. 恢复备份
cp data/backup/app_20250628.db data/app.db

# 4. 验证恢复
sqlite3 data/app.db "PRAGMA integrity_check;"

# 5. 重启应用
systemctl start linganapp
```

#### 2.2 部分恢复
```sql
-- 从备份中恢复特定数据
ATTACH DATABASE 'data/backup/app_20250628.db' AS backup;

-- 恢复意外删除的任务
INSERT INTO tasks 
SELECT * FROM backup.tasks 
WHERE id IN ('task_id_1', 'task_id_2');

DETACH DATABASE backup;
```

## 📈 容量规划

### 1. 增长预测

#### 1.1 数据增长模型
```sql
-- 分析历史增长趋势
WITH monthly_growth AS (
    SELECT 
        strftime('%Y-%m', datetime(created_at/1000, 'unixepoch')) as month,
        COUNT(*) as new_tasks
    FROM tasks 
    WHERE created_at > (unixepoch() - 365*24*60*60) * 1000
    GROUP BY month
)
SELECT 
    month,
    new_tasks,
    AVG(new_tasks) OVER (ORDER BY month ROWS 2 PRECEDING) as moving_avg
FROM monthly_growth
ORDER BY month;
```

#### 1.2 容量阈值
- **警告阈值**: 50,000 任务
- **严重阈值**: 100,000 任务
- **最大容量**: 500,000 任务（理论值）

### 2. 扩展策略

#### 2.1 垂直扩展
- 增加内存缓存大小
- 使用更快的存储设备
- 优化数据库配置参数

#### 2.2 水平扩展
- 实现读写分离
- 数据分区策略
- 迁移到分布式数据库

## 🔧 维护工具

### 1. 自动化脚本

#### 1.1 健康检查脚本
```bash
#!/bin/bash
# scripts/health-check.sh

echo "🔍 数据库健康检查..."

# 检查数据库文件
if [ ! -f "data/app.db" ]; then
    echo "❌ 数据库文件不存在"
    exit 1
fi

# 检查完整性
if ! sqlite3 data/app.db "PRAGMA integrity_check;" | grep -q "ok"; then
    echo "❌ 数据库完整性检查失败"
    exit 1
fi

# 检查性能
QUERY_TIME=$(sqlite3 data/app.db ".timer on" "SELECT COUNT(*) FROM tasks;" 2>&1 | grep "Run Time" | awk '{print $3}')
if (( $(echo "$QUERY_TIME > 0.1" | bc -l) )); then
    echo "⚠️ 查询性能下降: ${QUERY_TIME}s"
fi

echo "✅ 数据库健康检查通过"
```

#### 1.2 性能报告脚本
```javascript
// scripts/performance-report.js
const fs = require('fs');
const Database = require('better-sqlite3');

function generatePerformanceReport() {
    const db = new Database('data/app.db');
    
    const report = {
        timestamp: new Date().toISOString(),
        database_size: fs.statSync('data/app.db').size,
        record_counts: {},
        query_performance: {}
    };
    
    // 获取记录数
    const tables = ['tasks', 'task_templates', 'settings'];
    tables.forEach(table => {
        const result = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();
        report.record_counts[table] = result.count;
    });
    
    // 测试查询性能
    const queries = {
        'simple_select': 'SELECT COUNT(*) FROM tasks',
        'filtered_select': 'SELECT * FROM tasks WHERE is_completed = 0 LIMIT 10',
        'complex_query': 'SELECT * FROM tasks WHERE priority = 1 AND is_completed = 0'
    };
    
    Object.entries(queries).forEach(([name, sql]) => {
        const start = Date.now();
        db.prepare(sql).all();
        report.query_performance[name] = Date.now() - start;
    });
    
    db.close();
    
    // 保存报告
    fs.writeFileSync(
        `reports/performance_${Date.now()}.json`,
        JSON.stringify(report, null, 2)
    );
    
    console.log('📊 性能报告已生成');
}
```

## 📞 支持和联系

如果遇到本指南未涵盖的问题，请：

1. 查看应用日志获取详细错误信息
2. 检查数据库完整性
3. 尝试从最近的备份恢复
4. 联系技术支持团队

**紧急联系方式**:
- 技术支持: <EMAIL>
- 文档更新: <EMAIL>

---

*本指南最后更新: 2025年6月28日*
