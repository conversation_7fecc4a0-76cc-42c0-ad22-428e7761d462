{"version": 3, "sources": ["../../src/singlestore-core/view-base.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { ColumnsSelection } from '~/sql/sql.ts';\nimport { View } from '~/sql/sql.ts';\n\nexport abstract class SingleStoreViewBase<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends View<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreViewBase';\n\n\tdeclare readonly _: View<TName, TExisting, TSelectedFields>['_'] & {\n\t\treadonly viewBrand: 'SingleStoreViewBase';\n\t};\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAE3B,SAAS,YAAY;AAEd,MAAe,4BAIZ,KAAwC;AAAA,EACjD,QAA0B,UAAU,IAAY;AAKjD;", "names": []}