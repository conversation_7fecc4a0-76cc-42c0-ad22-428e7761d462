#!/usr/bin/env node

/**
 * 诊断任务加载问题
 * 检查数据库连接、任务查询、IPC通信等
 */

const { TaskService } = require('../dist/main/services/taskService.js')

async function diagnoseTaskLoading() {
  console.log('🔍 开始诊断任务加载问题...\n')
  
  const taskService = new TaskService()
  
  try {
    // 1. 测试数据库连接
    console.log('1️⃣ 测试数据库连接...')
    const db = await taskService.getDb()
    console.log('✅ 数据库连接成功')

    // 2. 测试基本任务查询
    console.log('\n2️⃣ 测试基本任务查询...')
    const allTasks = await taskService.getAllTasks()
    console.log(`✅ 成功获取 ${allTasks.length} 个任务`)
    
    if (allTasks.length > 0) {
      const firstTask = allTasks[0]
      console.log(`   示例任务: ${firstTask.content} (ID: ${firstTask.id})`)
      console.log(`   任务类型: ${firstTask.taskType || 'task'}`)
      console.log(`   父任务ID: ${firstTask.parentTaskId || 'null'}`)
    }

    // 3. 测试任务统计
    console.log('\n3️⃣ 测试任务统计...')
    const stats = await taskService.getTaskStats()
    console.log('✅ 任务统计:')
    console.log(`   - 总计: ${stats.total}`)
    console.log(`   - 已完成: ${stats.completed}`)
    console.log(`   - 待完成: ${stats.pending}`)
    console.log(`   - 逾期: ${stats.overdue}`)

    // 4. 测试层级任务查询
    console.log('\n4️⃣ 测试层级任务查询...')
    try {
      const hierarchies = await taskService.getHierarchicalTasks()
      console.log(`✅ 成功获取 ${hierarchies.length} 个根任务层级`)
      
      if (hierarchies.length > 0) {
        const firstHierarchy = hierarchies[0]
        console.log(`   示例层级: ${firstHierarchy.task.content}`)
        console.log(`   子任务数: ${firstHierarchy.children.length}`)
        console.log(`   深度: ${firstHierarchy.depth}`)
      }
    } catch (error) {
      console.log('❌ 层级任务查询失败:', error.message)
      console.log('   这可能是导致前端加载失败的原因')
    }

    // 5. 检查任务数据结构
    console.log('\n5️⃣ 检查任务数据结构...')
    const tasksWithParent = allTasks.filter(task => task.parentTaskId)
    const rootTasks = allTasks.filter(task => !task.parentTaskId)
    
    console.log(`✅ 数据结构分析:`)
    console.log(`   - 根任务: ${rootTasks.length}`)
    console.log(`   - 子任务: ${tasksWithParent.length}`)
    
    // 6. 检查数据库Schema
    console.log('\n6️⃣ 检查数据库Schema...')
    const sampleTask = allTasks[0]
    const hasRequiredFields = sampleTask && 
      typeof sampleTask.id === 'string' &&
      typeof sampleTask.content === 'string' &&
      typeof sampleTask.isCompleted === 'boolean'
    
    console.log(`✅ Schema检查: ${hasRequiredFields ? '通过' : '失败'}`)
    
    if (sampleTask) {
      console.log('   任务字段:')
      Object.keys(sampleTask).forEach(key => {
        console.log(`     - ${key}: ${typeof sampleTask[key]}`)
      })
    }

    // 7. 性能测试
    console.log('\n7️⃣ 性能测试...')
    const startTime = Date.now()
    await taskService.getAllTasks()
    const endTime = Date.now()
    console.log(`✅ 查询性能: ${endTime - startTime}ms`)

    console.log('\n🎉 诊断完成！基本功能正常。')
    
  } catch (error) {
    console.error('\n❌ 诊断过程中发现错误:')
    console.error('错误类型:', error.constructor.name)
    console.error('错误信息:', error.message)
    console.error('错误堆栈:', error.stack)
    
    // 提供解决建议
    console.log('\n💡 可能的解决方案:')
    if (error.message.includes('database')) {
      console.log('- 检查数据库文件是否存在和可访问')
      console.log('- 重新运行数据库迁移')
    }
    if (error.message.includes('better-sqlite3')) {
      console.log('- 重新编译 better-sqlite3: npm rebuild better-sqlite3')
      console.log('- 或使用 electron-rebuild: npx electron-rebuild')
    }
    if (error.message.includes('TaskHierarchy')) {
      console.log('- 检查 TaskHierarchy 类型定义')
      console.log('- 重新编译 TypeScript: npm run build')
    }
    
    process.exit(1)
  }
}

// 运行诊断
if (require.main === module) {
  diagnoseTaskLoading()
    .then(() => {
      console.log('\n✅ 诊断完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n❌ 诊断失败:', error)
      process.exit(1)
    })
}

module.exports = { diagnoseTaskLoading }
