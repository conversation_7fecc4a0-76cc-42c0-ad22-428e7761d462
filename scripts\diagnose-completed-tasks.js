const path = require('path')
const fs = require('fs')

console.log('🔍 LinganApp 已完成任务显示问题诊断')
console.log('='.repeat(50))

async function diagnoseCompletedTasks() {
  try {
    // 检查数据库文件是否存在
    const dbPath = path.join(__dirname, '..', 'data', 'app.db')
    console.log(`📁 数据库路径: ${dbPath}`)

    if (!fs.existsSync(dbPath)) {
      console.log('❌ 数据库文件不存在')
      return
    }

    // 使用 SQLite3 命令行工具查询
    const { execSync } = require('child_process')

    console.log('✅ 开始数据库查询')
    
    // 1. 检查数据库中的所有任务
    console.log('\n📊 1. 数据库任务总览')
    console.log('-'.repeat(30))

    try {
      const allTasksQuery = `
        SELECT
          id,
          content,
          isCompleted,
          deletedAt,
          createdAt,
          parentTaskId,
          taskType
        FROM tasks
        ORDER BY createdAt DESC;
      `

      const allTasksResult = execSync(`sqlite3 "${dbPath}" "${allTasksQuery}"`, { encoding: 'utf8' })
      const allTasksLines = allTasksResult.trim().split('\n').filter(line => line.trim())

      console.log(`总任务数: ${allTasksLines.length}`)

      if (allTasksLines.length > 0) {
        console.log('\n所有任务详情:')
        allTasksLines.forEach((line, index) => {
          const parts = line.split('|')
          if (parts.length >= 7) {
            const [id, content, isCompleted, deletedAt, createdAt, parentTaskId, taskType] = parts
            const status = isCompleted === '1' ? '✅ 已完成' : '⏳ 未完成'
            const deleted = deletedAt && deletedAt !== '' ? '🗑️ 已删除' : '📝 正常'
            const type = taskType || 'task'
            const parent = parentTaskId && parentTaskId !== '' ? `(子任务: ${parentTaskId.slice(-8)})` : '(主任务)'

            console.log(`${index + 1}. [${id.slice(-8)}] ${content}`)
            console.log(`   状态: ${status} | ${deleted} | 类型: ${type} ${parent}`)
            console.log(`   创建时间: ${new Date(parseInt(createdAt)).toLocaleString()}`)
            if (deletedAt && deletedAt !== '') {
              console.log(`   删除时间: ${new Date(parseInt(deletedAt)).toLocaleString()}`)
            }
            console.log('')
          }
        })
      }

      // 2. 统计分析
      console.log('\n📈 2. 任务统计分析')
      console.log('-'.repeat(30))

      // 统计查询
      const statsQueries = [
        "SELECT COUNT(*) as total FROM tasks;",
        "SELECT COUNT(*) as active FROM tasks WHERE deletedAt IS NULL;",
        "SELECT COUNT(*) as deleted FROM tasks WHERE deletedAt IS NOT NULL;",
        "SELECT COUNT(*) as completed FROM tasks WHERE isCompleted = 1 AND deletedAt IS NULL;",
        "SELECT COUNT(*) as pending FROM tasks WHERE isCompleted = 0 AND deletedAt IS NULL;",
        "SELECT COUNT(*) as completed_all FROM tasks WHERE isCompleted = 1;",
        "SELECT COUNT(*) as completed_deleted FROM tasks WHERE isCompleted = 1 AND deletedAt IS NOT NULL;"
      ]

      const stats = {}
      statsQueries.forEach(query => {
        const result = execSync(`sqlite3 "${dbPath}" "${query}"`, { encoding: 'utf8' }).trim()
        const key = query.match(/as (\w+)/)[1]
        stats[key] = parseInt(result)
      })

      console.log(`📋 总任务数: ${stats.total}`)
      console.log(`📝 活跃任务: ${stats.active}`)
      console.log(`🗑️ 已删除任务: ${stats.deleted}`)
      console.log(`✅ 已完成任务(活跃): ${stats.completed}`)
      console.log(`⏳ 待完成任务: ${stats.pending}`)
      console.log(`✅ 已完成任务(全部): ${stats.completed_all}`)
      console.log(`🗑️ 已完成但已删除: ${stats.completed_deleted}`)
    
      // 3. 检查已完成任务详情
      console.log('\n✅ 3. 已完成任务详细信息')
      console.log('-'.repeat(30))

      if (stats.completed > 0) {
        console.log(`发现 ${stats.completed} 个已完成的活跃任务:`)
        const completedQuery = `
          SELECT id, content, isCompleted, deletedAt, createdAt, parentTaskId, taskType
          FROM tasks
          WHERE isCompleted = 1 AND deletedAt IS NULL
          ORDER BY createdAt DESC;
        `
        const completedResult = execSync(`sqlite3 "${dbPath}" "${completedQuery}"`, { encoding: 'utf8' })
        const completedLines = completedResult.trim().split('\n').filter(line => line.trim())

        completedLines.forEach((line, index) => {
          const parts = line.split('|')
          if (parts.length >= 7) {
            const [id, content, isCompleted, deletedAt, createdAt, parentTaskId, taskType] = parts
            console.log(`${index + 1}. [${id}]`)
            console.log(`   内容: ${content}`)
            console.log(`   完成状态: ${isCompleted}`)
            console.log(`   删除状态: ${deletedAt || '未删除'}`)
            console.log(`   任务类型: ${taskType || 'task'}`)
            console.log(`   父任务ID: ${parentTaskId || '无'}`)
            console.log('')
          }
        })
      } else {
        console.log('❌ 没有找到已完成的活跃任务')
      }

      if (stats.completed_deleted > 0) {
        console.log(`⚠️ 发现 ${stats.completed_deleted} 个已完成但被删除的任务`)
      }

      // 4. 问题诊断
      console.log('\n🔧 4. 问题诊断')
      console.log('-'.repeat(30))

      // 模拟主进程中的错误统计逻辑（不过滤删除的任务）
      const wrongStatsQuery = "SELECT COUNT(*) as completed_wrong FROM tasks WHERE isCompleted = 1;"
      const wrongStats = execSync(`sqlite3 "${dbPath}" "${wrongStatsQuery}"`, { encoding: 'utf8' }).trim()
      const wrongCompletedCount = parseInt(wrongStats)

      console.log('🔍 模拟主进程统计逻辑:')
      console.log(`❌ 错误的已完成任务统计 (不过滤删除): ${wrongCompletedCount}`)
      console.log(`✅ 正确的已完成任务统计 (过滤删除): ${stats.completed}`)

      if (wrongCompletedCount > stats.completed) {
        console.log('\n🎯 问题确认:')
        console.log('主进程的统计逻辑没有过滤已删除的任务！')
        console.log(`差异: ${wrongCompletedCount - stats.completed} 个已完成但已删除的任务被错误计入`)
        console.log('\n📝 解决方案:')
        console.log('1. 修改 src/main/index.ts 中的 task:getStats 处理器')
        console.log('2. 添加 deletedAt IS NULL 过滤条件')
        console.log('3. 或者使用 TaskService 的优化统计方法')
      } else if (wrongCompletedCount === stats.completed) {
        console.log('\n✅ 统计逻辑正确，问题可能在前端过滤')
      }

    } catch (queryError) {
      console.error('❌ 查询失败:', queryError.message)
    }

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error.message)
    console.error('错误详情:', error)
  }
}

// 运行诊断
diagnoseCompletedTasks().then(() => {
  console.log('\n🎯 诊断完成')
  console.log('请查看上述分析结果，确定问题根源')
}).catch(error => {
  console.error('❌ 诊断失败:', error)
  process.exit(1)
})
