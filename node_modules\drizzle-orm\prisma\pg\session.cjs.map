{"version": 3, "sources": ["../../../src/prisma/pg/session.ts"], "sourcesContent": ["import type { PrismaClient } from '@prisma/client/extension';\n\nimport { entityKind } from '~/entity.ts';\nimport { type Lo<PERSON>, NoopLogger } from '~/logger.ts';\nimport type {\n\tPgDialect,\n\tPgQueryResultHKT,\n\tPgTransaction,\n\tPgTransactionConfig,\n\tPreparedQueryConfig,\n} from '~/pg-core/index.ts';\nimport { PgPreparedQuery, PgSession } from '~/pg-core/index.ts';\nimport type { Query, SQL } from '~/sql/sql.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\n\nexport class PrismaPgPreparedQuery<T> extends PgPreparedQuery<PreparedQueryConfig & { execute: T }> {\n\tstatic override readonly [entityKind]: string = 'PrismaPgPreparedQuery';\n\n\tconstructor(\n\t\tprivate readonly prisma: PrismaClient,\n\t\tquery: Query,\n\t\tprivate readonly logger: Logger,\n\t) {\n\t\tsuper(query, undefined, undefined, undefined);\n\t}\n\n\toverride execute(placeholderValues?: Record<string, unknown>): Promise<T> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.prisma.$queryRawUnsafe(this.query.sql, ...params);\n\t}\n\n\toverride all(): Promise<unknown> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n\n\toverride isResponseInArrayMode(): boolean {\n\t\treturn false;\n\t}\n}\n\nexport interface PrismaPgSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class PrismaPgSession extends PgSession {\n\tstatic override readonly [entityKind]: string = 'PrismaPgSession';\n\n\tprivate readonly logger: Logger;\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\tprivate readonly prisma: PrismaClient,\n\t\tprivate readonly options: PrismaPgSessionOptions,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\toverride execute<T>(query: SQL): Promise<T> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(this.dialect.sqlToQuery(query)).execute();\n\t}\n\n\toverride prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(query: Query): PgPreparedQuery<T> {\n\t\treturn new PrismaPgPreparedQuery(this.prisma, query, this.logger);\n\t}\n\n\toverride transaction<T>(\n\t\t_transaction: (tx: PgTransaction<PgQueryResultHKT, Record<string, never>, Record<string, never>>) => Promise<T>,\n\t\t_config?: PgTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n}\n\nexport interface PrismaPgQueryResultHKT extends PgQueryResultHKT {\n\ttype: [];\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAC3B,oBAAwC;AAQxC,qBAA2C;AAE3C,iBAAiC;AAE1B,MAAM,8BAAiC,+BAAsD;AAAA,EAGnG,YACkB,QACjB,OACiB,QAChB;AACD,UAAM,OAAO,QAAW,QAAW,MAAS;AAJ3B;AAEA;AAAA,EAGlB;AAAA,EARA,QAA0B,wBAAU,IAAY;AAAA,EAUvC,QAAQ,mBAAyD;AACzE,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,gBAAgB,KAAK,MAAM,KAAK,GAAG,MAAM;AAAA,EAC7D;AAAA,EAES,MAAwB;AAChC,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAAA,EAES,wBAAiC;AACzC,WAAO;AAAA,EACR;AACD;AAMO,MAAM,wBAAwB,yBAAU;AAAA,EAK9C,YACC,SACiB,QACA,SAChB;AACD,UAAM,OAAO;AAHI;AACA;AAGjB,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAXA,QAA0B,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAWR,QAAW,OAAwB;AAC3C,WAAO,KAAK,aAAmD,KAAK,QAAQ,WAAW,KAAK,CAAC,EAAE,QAAQ;AAAA,EACxG;AAAA,EAES,aAAkE,OAAkC;AAC5G,WAAO,IAAI,sBAAsB,KAAK,QAAQ,OAAO,KAAK,MAAM;AAAA,EACjE;AAAA,EAES,YACR,cACA,SACa;AACb,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AACD;", "names": []}