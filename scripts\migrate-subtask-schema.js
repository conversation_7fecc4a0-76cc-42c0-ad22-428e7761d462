#!/usr/bin/env node

/**
 * 子任务系统数据库迁移脚本
 * 为现有数据库添加子任务相关字段和索引
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const dbPath = path.join(dataDir, 'app.db');

console.log('🚀 开始子任务系统数据库迁移...');

// 检查数据库是否存在
if (!fs.existsSync(dbPath)) {
  console.error('❌ 数据库文件不存在:', dbPath);
  process.exit(1);
}

// 连接数据库
const db = new Database(dbPath);

// 启用优化设置
db.pragma('journal_mode = WAL');
db.pragma('foreign_keys = ON');
db.pragma('synchronous = NORMAL');

try {
  console.log('📝 检查当前表结构...');
  
  // 检查是否已经有子任务字段
  const tableInfo = db.prepare("PRAGMA table_info(tasks)").all();
  const existingColumns = tableInfo.map(col => col.name);
  
  console.log('   现有字段:', existingColumns.join(', '));
  
  const requiredColumns = [
    'parent_task_id',
    'task_type', 
    'description',
    'estimated_duration',
    'actual_duration',
    'progress'
  ];
  
  const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));
  
  if (missingColumns.length === 0) {
    console.log('✅ 所有必需字段已存在，跳过字段添加');
  } else {
    console.log('📝 需要添加的字段:', missingColumns.join(', '));
    
    // 开始事务
    const transaction = db.transaction(() => {
      // 添加缺失的字段
      missingColumns.forEach(column => {
        let sql = '';
        
        switch (column) {
          case 'parent_task_id':
            sql = 'ALTER TABLE tasks ADD COLUMN parent_task_id TEXT';
            break;
          case 'task_type':
            sql = "ALTER TABLE tasks ADD COLUMN task_type TEXT DEFAULT 'task' NOT NULL";
            break;
          case 'description':
            sql = 'ALTER TABLE tasks ADD COLUMN description TEXT';
            break;
          case 'estimated_duration':
            sql = 'ALTER TABLE tasks ADD COLUMN estimated_duration INTEGER';
            break;
          case 'actual_duration':
            sql = 'ALTER TABLE tasks ADD COLUMN actual_duration INTEGER';
            break;
          case 'progress':
            sql = 'ALTER TABLE tasks ADD COLUMN progress INTEGER DEFAULT 0 NOT NULL';
            break;
        }
        
        if (sql) {
          try {
            db.exec(sql);
            console.log(`✅ 添加字段: ${column}`);
          } catch (error) {
            console.warn(`⚠️  添加字段失败 ${column}: ${error.message}`);
          }
        }
      });
    });
    
    // 执行事务
    transaction();
  }
  
  console.log('\n📊 添加子任务系统索引...');
  
  // 定义子任务系统索引
  const subtaskIndexes = [
    // 基础索引（如果不存在）
    'CREATE INDEX IF NOT EXISTS tasks_order_index_idx ON tasks(order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_is_completed_idx ON tasks(is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_priority_idx ON tasks(priority)',
    'CREATE INDEX IF NOT EXISTS tasks_created_at_idx ON tasks(created_at)',
    
    // 子任务系统专用索引
    'CREATE INDEX IF NOT EXISTS tasks_parent_task_id_idx ON tasks(parent_task_id)',
    'CREATE INDEX IF NOT EXISTS tasks_task_type_idx ON tasks(task_type)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_type_idx ON tasks(parent_task_id, task_type)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_order_idx ON tasks(parent_task_id, order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_progress_idx ON tasks(parent_task_id, progress)',
    
    // 复合索引优化
    'CREATE INDEX IF NOT EXISTS tasks_parent_status_idx ON tasks(parent_task_id, is_completed)',
    'CREATE INDEX IF NOT EXISTS tasks_hierarchy_idx ON tasks(parent_task_id, deleted_at, is_completed, order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_stats_idx ON tasks(parent_task_id, is_completed, deleted_at)',
    'CREATE INDEX IF NOT EXISTS tasks_type_parent_idx ON tasks(task_type, parent_task_id, deleted_at)',
  ];
  
  let successCount = 0;
  let skipCount = 0;
  
  // 应用索引
  const indexTransaction = db.transaction(() => {
    for (const indexSQL of subtaskIndexes) {
      try {
        db.exec(indexSQL);
        
        // 提取索引名称
        const indexName = indexSQL.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
        console.log(`✅ 创建索引: ${indexName}`);
        successCount++;
      } catch (error) {
        if (error.message.includes('already exists')) {
          const indexName = indexSQL.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
          console.log(`⏭️  索引已存在，跳过: ${indexName}`);
          skipCount++;
        } else {
          console.warn(`⚠️  创建索引失败: ${error.message}`);
          skipCount++;
        }
      }
    }
  });
  
  // 执行索引事务
  indexTransaction();
  
  // 更新统计信息
  console.log('\n📊 更新数据库统计信息...');
  db.exec('ANALYZE tasks');
  
  console.log(`\n📊 迁移结果:`);
  console.log(`   ✅ 成功创建索引: ${successCount} 个`);
  console.log(`   ⏭️  跳过索引: ${skipCount} 个`);
  
  // 验证迁移结果
  console.log('\n🔍 验证迁移结果...');
  
  // 检查新字段
  const newTableInfo = db.prepare("PRAGMA table_info(tasks)").all();
  const newColumns = newTableInfo.map(col => col.name);
  
  const hasAllRequired = requiredColumns.every(col => newColumns.includes(col));
  if (hasAllRequired) {
    console.log('✅ 所有必需字段已添加');
  } else {
    console.log('❌ 部分字段添加失败');
  }
  
  // 检查索引
  const indexes = db.prepare(`
    SELECT name 
    FROM sqlite_master 
    WHERE type='index' 
      AND name NOT LIKE 'sqlite_%' 
      AND tbl_name='tasks'
      AND name LIKE '%parent%'
  `).all();
  
  console.log(`✅ 子任务相关索引数量: ${indexes.length}`);
  indexes.forEach(index => {
    console.log(`   📊 ${index.name}`);
  });
  
  // 测试基础查询
  console.log('\n⚡ 测试基础查询...');
  
  try {
    // 测试子任务查询
    const subTaskQuery = db.prepare(`
      SELECT COUNT(*) as count 
      FROM tasks 
      WHERE parent_task_id IS NOT NULL AND deleted_at IS NULL
    `);
    const subTaskCount = subTaskQuery.get();
    console.log(`✅ 子任务查询测试通过，当前子任务数: ${subTaskCount.count}`);
    
    // 测试任务类型查询
    const typeQuery = db.prepare(`
      SELECT task_type, COUNT(*) as count 
      FROM tasks 
      GROUP BY task_type
    `);
    const typeStats = typeQuery.all();
    console.log('✅ 任务类型查询测试通过:');
    typeStats.forEach(stat => {
      console.log(`   ${stat.task_type}: ${stat.count}`);
    });
    
  } catch (error) {
    console.error('❌ 查询测试失败:', error.message);
  }
  
  // 数据库统计信息
  console.log('\n📈 数据库统计信息:');
  try {
    const totalTasks = db.prepare("SELECT COUNT(*) as count FROM tasks").get();
    console.log(`   📝 总任务数量: ${totalTasks.count}`);
    
    const dbSize = fs.statSync(dbPath).size;
    console.log(`   💾 数据库大小: ${(dbSize / 1024 / 1024).toFixed(2)} MB`);
    
    const totalIndexes = db.prepare(`
      SELECT COUNT(*) as count 
      FROM sqlite_master 
      WHERE type='index' AND name NOT LIKE 'sqlite_%' AND tbl_name='tasks'
    `).all();
    console.log(`   📊 总索引数量: ${totalIndexes[0].count}`);
    
  } catch (error) {
    console.log(`   ⚠️  无法获取统计信息: ${error.message}`);
  }
  
  console.log('\n🎉 子任务系统数据库迁移完成！');
  
} catch (error) {
  console.error('❌ 迁移过程中发生错误:', error);
  process.exit(1);
} finally {
  db.close();
}

console.log('\n📋 后续步骤:');
console.log('1. 重启应用以使用新的数据库结构');
console.log('2. 运行子任务功能测试验证迁移效果');
console.log('3. 开始使用子任务系统功能');
console.log('4. 查看子任务系统设计文档了解详细用法');
