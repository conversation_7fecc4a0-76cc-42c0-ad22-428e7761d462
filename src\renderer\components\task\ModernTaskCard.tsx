import React, { useState } from 'react'
import {
  Calendar,
  Star,
  MoreHorizontal,
  Plus,
  ChevronRight,
  ChevronDown,
  Circle,
  CheckCircle2,
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  Archive
} from 'lucide-react'
import { But<PERSON> } from '../ui/button'
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from '../ui/dropdown-menu'
import { cn, formatDueDate, isTaskOverdue } from '../../lib/utils'
import { useUpdateTask, useDeleteTask, useSoftDeleteTask, useRestoreTask } from '../../hooks/useTasks'
import { useSelection } from '../../stores/selectionStore'
import { useLongPress } from '../../hooks/useLongPress'
import { createSelectionAnimation, InteractionFeedback } from '../../utils/animations'
import { useToastActions } from '../ui/toast'
import { Checkbox } from '../ui/checkbox'
import type { Task } from '../../../shared/types/task'
import { TaskPriority, PRIORITY_COLORS } from '../../../shared/types/task'

interface ModernTaskCardProps {
  task: Task
  subtasks?: Task[]
  onToggleComplete?: (taskId: string) => void
  onAddSubtask?: (parentTaskId: string) => void
  onEdit?: (task: Task) => void
  className?: string
}

export function ModernTaskCard({
  task,
  subtasks = [],
  onToggleComplete,
  onAddSubtask,
  onEdit,
  className
}: ModernTaskCardProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [isHovered, setIsHovered] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const cardRef = React.useRef<HTMLDivElement>(null)

  const updateTask = useUpdateTask()
  const deleteTask = useDeleteTask()
  const softDeleteTask = useSoftDeleteTask()
  const restoreTask = useRestoreTask()

  const {
    isSelectionMode,
    isTaskSelected,
    toggleTaskSelection,
    enterSelectionMode,
    addDeletedTask,
    removeDeletedTask
  } = useSelection()

  const { showDeleteSuccess, showError } = useToastActions()

  const isOverdue = isTaskOverdue(task.dueDate)
  const hasSubtasks = subtasks.length > 0
  const completedSubtasks = subtasks.filter(t => t.isCompleted).length
  const progress = hasSubtasks ? (completedSubtasks / subtasks.length) * 100 : 0
  const isSelected = isTaskSelected(task.id)

  const handleToggleComplete = () => {
    if (isSelectionMode) {
      toggleTaskSelection(task.id)
    } else {
      onToggleComplete?.(task.id)
    }
  }

  const handleCardClick = () => {
    if (isSelectionMode) {
      toggleTaskSelection(task.id)
      // 添加选择动画反馈
      if (cardRef.current) {
        createSelectionAnimation(cardRef.current, !isSelected)
      }
    }
  }

  const handleLongPress = () => {
    if (!isSelectionMode) {
      enterSelectionMode()
      toggleTaskSelection(task.id)
    }
  }

  const longPressProps = useLongPress({
    onLongPress: handleLongPress,
    onClick: handleCardClick,
    delay: 500
  })

  const handleAddSubtask = () => {
    onAddSubtask?.(task.id)
  }

  const handleToggleImportant = () => {
    updateTask.mutate({
      id: task.id,
      input: { isImportant: !task.isImportant }
    })
  }

  const handleSoftDelete = async () => {
    if (isDeleting) return

    setIsDeleting(true)

    try {
      // 添加删除动画
      if (cardRef.current) {
        InteractionFeedback.buttonClick(cardRef.current)
      }

      // 执行软删除
      await softDeleteTask.mutateAsync(task.id)

      // 添加到撤销列表
      addDeletedTask({
        id: task.id,
        content: task.content,
        deletedAt: Date.now()
      })

      // 显示成功提示和撤销选项
      showDeleteSuccess(task.content, async () => {
        try {
          // 先从撤销列表中移除，防止重复操作
          removeDeletedTask(task.id)

          await restoreTask.mutateAsync(task.id)
          showDeleteSuccess('撤销成功', () => {})
        } catch (error) {
          console.error('Failed to restore task:', error)

          let errorMessage = '无法恢复已删除的任务'
          if (error instanceof Error) {
            if (error.message.includes('任务不存在或未被删除')) {
              errorMessage = '该任务可能已经被恢复'
            } else {
              errorMessage = error.message
            }
          }

          showError('撤销失败', errorMessage)
        }
      })
    } catch (error) {
      console.error('Failed to soft delete task:', error)

      // 提供更详细的错误处理
      let errorTitle = '删除失败'
      let errorMessage = '删除任务时发生错误'

      if (error instanceof Error) {
        if (error.message.includes('任务已经被删除')) {
          errorTitle = '任务已删除'
          errorMessage = '该任务已经被删除，请刷新页面查看最新状态'
        } else if (error.message.includes('任务不存在')) {
          errorTitle = '任务不存在'
          errorMessage = '该任务可能已被删除或不存在，请刷新页面'
        } else {
          errorMessage = error.message
        }
      }

      showError(errorTitle, errorMessage)
    } finally {
      setIsDeleting(false)
    }
  }

  const handleCopy = () => {
    // TODO: 实现复制任务功能
    console.log('Copy task:', task.id)
  }

  const handleArchive = () => {
    // TODO: 实现归档任务功能
    console.log('Archive task:', task.id)
  }

  const getPriorityColor = (priority: TaskPriority) => {
    return PRIORITY_COLORS[priority] || 'bg-gray-100'
  }

  return (
    <div
      ref={cardRef}
      {...longPressProps}
      className={cn(
        "group bg-card rounded-xl border border-border/50 transition-all duration-200 hover:border-border hover:shadow-sm",
        task.isCompleted && "opacity-75",
        isOverdue && !task.isCompleted && "border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20",
        isSelectionMode && "cursor-pointer",
        isSelected && "border-primary bg-primary/5 shadow-md transform scale-[0.98]",
        isDeleting && "opacity-50 pointer-events-none",
        className
      )}
      onMouseEnter={() => {
        setIsHovered(true)
        if (cardRef.current && !isSelectionMode) {
          InteractionFeedback.hover(cardRef.current, true)
        }
      }}
      onMouseLeave={() => {
        setIsHovered(false)
        if (cardRef.current && !isSelectionMode) {
          InteractionFeedback.hover(cardRef.current, false)
        }
      }}
    >
      {/* 主任务区域 */}
      <div className="flex items-start gap-4 p-4">
        {/* 完成状态按钮或选择复选框 */}
        {isSelectionMode ? (
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => toggleTaskSelection(task.id)}
            className="mt-1"
          />
        ) : (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleToggleComplete}
            className="p-0 h-6 w-6 rounded-full hover:bg-transparent"
          >
            {task.isCompleted ? (
              <CheckCircle2 className="h-6 w-6 text-green-600 fill-green-100" />
            ) : (
              <Circle className="h-6 w-6 text-muted-foreground hover:text-primary transition-colors" />
            )}
          </Button>
        )}

        {/* 任务内容 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <h3 
                className={cn(
                  "text-base font-medium text-foreground cursor-pointer hover:text-primary transition-colors",
                  task.isCompleted && "line-through text-muted-foreground"
                )}
                onClick={() => onEdit?.(task)}
              >
                {task.content}
              </h3>
              
              {/* 任务元信息 */}
              <div className="flex items-center gap-3 mt-2">
                {/* 优先级指示器 */}
                {task.priority !== TaskPriority.MEDIUM && (
                  <div className={cn(
                    "w-2 h-2 rounded-full",
                    getPriorityColor(task.priority)
                  )} />
                )}
                
                {/* 截止日期 */}
                {task.dueDate && (
                  <div className={cn(
                    "flex items-center gap-1 text-xs px-2 py-1 rounded-md",
                    isOverdue && !task.isCompleted 
                      ? "text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-950/30" 
                      : "text-muted-foreground bg-muted/50"
                  )}>
                    <Calendar className="h-3 w-3" />
                    <span>{formatDueDate(task.dueDate)}</span>
                  </div>
                )}

                {/* 子任务进度 */}
                {hasSubtasks && (
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <div className="w-12 h-1.5 bg-muted rounded-full overflow-hidden">
                        <div 
                          className="h-full bg-primary transition-all duration-300"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                      <span>{completedSubtasks}/{subtasks.length}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className={cn(
              "flex items-center gap-1 transition-opacity",
              isHovered ? "opacity-100" : "opacity-0"
            )}>
              {/* 重要标记 */}
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "p-1 h-8 w-8",
                  task.isImportant && "text-yellow-600"
                )}
                onClick={handleToggleImportant}
                title={task.isImportant ? "取消重要标记" : "标记为重要"}
              >
                <Star className={cn("h-4 w-4", task.isImportant && "fill-current")} />
              </Button>

              {/* 更多操作 - 临时简化版本 */}
              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-8 w-8"
                onClick={() => onEdit?.(task)}
                title="编辑任务"
              >
                <Edit3 className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="p-1 h-8 w-8 text-red-600 hover:text-red-700"
                onClick={handleSoftDelete}
                title="删除任务"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 子任务区域 */}
      {hasSubtasks && (
        <div className="border-t border-border/30">
          {/* 子任务标题栏 */}
          <div className="flex items-center justify-between px-4 py-2 bg-muted/20">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground p-1 h-auto"
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
              <span>子任务 ({completedSubtasks}/{subtasks.length})</span>
            </Button>
            
            {onAddSubtask && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddSubtask}
                className="p-1 h-auto text-muted-foreground hover:text-foreground"
              >
                <Plus className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* 子任务列表 - 微软 To Do 风格 */}
          {isExpanded && (
            <div className="px-4 pb-3 space-y-1">
              {subtasks.map((subtask, index) => (
                <div
                  key={subtask.id}
                  className="flex items-start gap-3 py-2 pl-4 relative group hover:bg-muted/30 rounded-md transition-colors"
                >
                  {/* 连接线 */}
                  <div className="absolute left-0 top-0 bottom-0 w-px bg-border/40" />
                  <div className="absolute left-0 top-[18px] w-4 h-px bg-border/40" />

                  {/* 子任务复选框 */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onToggleComplete?.(subtask.id)}
                    className="p-0 h-5 w-5 rounded-full hover:bg-transparent flex-shrink-0 mt-0.5"
                  >
                    {subtask.isCompleted ? (
                      <CheckCircle2 className="h-5 w-5 text-green-600 fill-green-100" />
                    ) : (
                      <Circle className="h-5 w-5 text-muted-foreground hover:text-primary transition-colors" />
                    )}
                  </Button>

                  {/* 子任务内容 */}
                  <div className="flex-1 min-w-0">
                    <span
                      className={cn(
                        "text-sm text-foreground cursor-pointer hover:text-primary transition-colors block",
                        subtask.isCompleted && "line-through text-muted-foreground"
                      )}
                      onClick={() => onEdit?.(subtask)}
                    >
                      {subtask.content}
                    </span>

                    {/* 子任务元信息 */}
                    {(subtask.dueDate || subtask.isImportant) && (
                      <div className="flex items-center gap-2 mt-1">
                        {subtask.isImportant && (
                          <Star className="h-3 w-3 text-yellow-500 fill-current" />
                        )}
                        {subtask.dueDate && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDueDate(subtask.dueDate)}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 子任务操作按钮 */}
                  <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-1 h-6 w-6"
                      onClick={() => onEdit?.(subtask)}
                      title="编辑子任务"
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* 添加子任务按钮（当没有子任务时显示） */}
      {!hasSubtasks && onAddSubtask && isHovered && (
        <div className="border-t border-border/30 px-4 py-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleAddSubtask}
            className="text-muted-foreground hover:text-foreground text-sm gap-2 h-auto p-1"
          >
            <Plus className="h-4 w-4" />
            添加子任务
          </Button>
        </div>
      )}
    </div>
  )
}
