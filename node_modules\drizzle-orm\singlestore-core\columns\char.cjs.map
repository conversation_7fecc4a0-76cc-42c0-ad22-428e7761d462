{"version": 3, "sources": ["../../../src/singlestore-core/columns/char.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreCharBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = SingleStoreCharBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreChar';\n\tdata: TEnum[number];\n\tdriverParam: number | string;\n\tenumValues: TEnum;\n\tgenerated: undefined;\n\tlength: TLength;\n}>;\n\nexport class SingleStoreCharBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'SingleStoreChar'> & { length?: number | undefined },\n> extends SingleStoreColumnBuilder<\n\tT,\n\tSingleStoreCharConfig<T['enumValues'], T['length']>,\n\t{ length: T['length'] }\n> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreCharBuilder';\n\n\tconstructor(name: T['name'], config: SingleStoreCharConfig<T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'SingleStoreChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enum = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreChar<MakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }> {\n\t\treturn new SingleStoreChar<MakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreChar<T extends ColumnBaseConfig<'string', 'SingleStoreChar'> & { length?: number | undefined }>\n\textends SingleStoreColumn<T, SingleStoreCharConfig<T['enumValues'], T['length']>, { length: T['length'] }>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreChar';\n\n\treadonly length: T['length'] = this.config.length;\n\toverride readonly enumValues = this.config.enum;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `char` : `char(${this.length})`;\n\t}\n}\n\nexport interface SingleStoreCharConfig<\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> {\n\tenum?: TEnum;\n\tlength?: TLength;\n}\n\nexport function char(): SingleStoreCharBuilderInitial<'', [string, ...string[]], undefined>;\nexport function char<U extends string, T extends Readonly<[U, ...U[]]>, L extends number | undefined>(\n\tconfig?: SingleStoreCharConfig<T | Writable<T>, L>,\n): SingleStoreCharBuilderInitial<'', Writable<T>, L>;\nexport function char<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tname: TName,\n\tconfig?: SingleStoreCharConfig<T | Writable<T>, L>,\n): SingleStoreCharBuilderInitial<TName, Writable<T>, L>;\nexport function char(a?: string | SingleStoreCharConfig, b: SingleStoreCharConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreCharConfig>(a, b);\n\treturn new SingleStoreCharBuilder(name, config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAsD;AACtD,oBAA4D;AAiBrD,MAAM,+BAEH,uCAIR;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAA6D;AACzF,UAAM,MAAM,UAAU,iBAAiB;AACvC,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,OAAO,OAAO;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OAC0G;AAC1G,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBACJ,gCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,SAAsB,KAAK,OAAO;AAAA,EACzB,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,SAAS,QAAQ,KAAK,MAAM;AAAA,EAChE;AACD;AAuBO,SAAS,KAAK,GAAoC,IAA2B,CAAC,GAAQ;AAC5F,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA8C,GAAG,CAAC;AAC3E,SAAO,IAAI,uBAAuB,MAAM,MAAa;AACtD;", "names": []}