var Wx=Object.defineProperty;var dh=e=>{throw TypeError(e)};var Qx=(e,t,n)=>t in e?Wx(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Ut=(e,t,n)=>Qx(e,typeof t!="symbol"?t+"":t,n),sc=(e,t,n)=>t.has(e)||dh("Cannot "+n);var C=(e,t,n)=>(sc(e,t,"read from private field"),n?n.call(e):t.get(e)),G=(e,t,n)=>t.has(e)?dh("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),F=(e,t,n,r)=>(sc(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),ie=(e,t,n)=>(sc(e,t,"access private method"),n);var Vi=(e,t,n,r)=>({set _(s){F(e,t,s,n)},get _(){return C(e,t,r)}});function Zx(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in e)){const o=Object.getOwnPropertyDescriptor(r,s);o&&Object.defineProperty(e,s,o.get?o:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();function Ed(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var $m={exports:{}},wl={},Um={exports:{}},ue={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ri=Symbol.for("react.element"),Kx=Symbol.for("react.portal"),qx=Symbol.for("react.fragment"),Gx=Symbol.for("react.strict_mode"),Yx=Symbol.for("react.profiler"),Xx=Symbol.for("react.provider"),Jx=Symbol.for("react.context"),ew=Symbol.for("react.forward_ref"),tw=Symbol.for("react.suspense"),nw=Symbol.for("react.memo"),rw=Symbol.for("react.lazy"),fh=Symbol.iterator;function sw(e){return e===null||typeof e!="object"?null:(e=fh&&e[fh]||e["@@iterator"],typeof e=="function"?e:null)}var Bm={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Vm=Object.assign,Hm={};function go(e,t,n){this.props=e,this.context=t,this.refs=Hm,this.updater=n||Bm}go.prototype.isReactComponent={};go.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};go.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Wm(){}Wm.prototype=go.prototype;function _d(e,t,n){this.props=e,this.context=t,this.refs=Hm,this.updater=n||Bm}var Nd=_d.prototype=new Wm;Nd.constructor=_d;Vm(Nd,go.prototype);Nd.isPureReactComponent=!0;var hh=Array.isArray,Qm=Object.prototype.hasOwnProperty,Td={current:null},Zm={key:!0,ref:!0,__self:!0,__source:!0};function Km(e,t,n){var r,s={},o=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(o=""+t.key),t)Qm.call(t,r)&&!Zm.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var l=Array(a),c=0;c<a;c++)l[c]=arguments[c+2];s.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:Ri,type:e,key:o,ref:i,props:s,_owner:Td.current}}function ow(e,t){return{$$typeof:Ri,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function jd(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ri}function iw(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ph=/\/+/g;function oc(e,t){return typeof e=="object"&&e!==null&&e.key!=null?iw(""+e.key):t.toString(36)}function ga(e,t,n,r,s){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(o){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Ri:case Kx:i=!0}}if(i)return i=e,s=s(i),e=r===""?"."+oc(i,0):r,hh(s)?(n="",e!=null&&(n=e.replace(ph,"$&/")+"/"),ga(s,t,n,"",function(c){return c})):s!=null&&(jd(s)&&(s=ow(s,n+(!s.key||i&&i.key===s.key?"":(""+s.key).replace(ph,"$&/")+"/")+e)),t.push(s)),1;if(i=0,r=r===""?".":r+":",hh(e))for(var a=0;a<e.length;a++){o=e[a];var l=r+oc(o,a);i+=ga(o,t,n,l,s)}else if(l=sw(e),typeof l=="function")for(e=l.call(e),a=0;!(o=e.next()).done;)o=o.value,l=r+oc(o,a++),i+=ga(o,t,n,l,s);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Hi(e,t,n){if(e==null)return e;var r=[],s=0;return ga(e,r,"","",function(o){return t.call(n,o,s++)}),r}function aw(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var dt={current:null},ya={transition:null},lw={ReactCurrentDispatcher:dt,ReactCurrentBatchConfig:ya,ReactCurrentOwner:Td};function qm(){throw Error("act(...) is not supported in production builds of React.")}ue.Children={map:Hi,forEach:function(e,t,n){Hi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Hi(e,function(){t++}),t},toArray:function(e){return Hi(e,function(t){return t})||[]},only:function(e){if(!jd(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ue.Component=go;ue.Fragment=qx;ue.Profiler=Yx;ue.PureComponent=_d;ue.StrictMode=Gx;ue.Suspense=tw;ue.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=lw;ue.act=qm;ue.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Vm({},e.props),s=e.key,o=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,i=Td.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Qm.call(t,l)&&!Zm.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var c=0;c<l;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:Ri,type:e.type,key:s,ref:o,props:r,_owner:i}};ue.createContext=function(e){return e={$$typeof:Jx,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Xx,_context:e},e.Consumer=e};ue.createElement=Km;ue.createFactory=function(e){var t=Km.bind(null,e);return t.type=e,t};ue.createRef=function(){return{current:null}};ue.forwardRef=function(e){return{$$typeof:ew,render:e}};ue.isValidElement=jd;ue.lazy=function(e){return{$$typeof:rw,_payload:{_status:-1,_result:e},_init:aw}};ue.memo=function(e,t){return{$$typeof:nw,type:e,compare:t===void 0?null:t}};ue.startTransition=function(e){var t=ya.transition;ya.transition={};try{e()}finally{ya.transition=t}};ue.unstable_act=qm;ue.useCallback=function(e,t){return dt.current.useCallback(e,t)};ue.useContext=function(e){return dt.current.useContext(e)};ue.useDebugValue=function(){};ue.useDeferredValue=function(e){return dt.current.useDeferredValue(e)};ue.useEffect=function(e,t){return dt.current.useEffect(e,t)};ue.useId=function(){return dt.current.useId()};ue.useImperativeHandle=function(e,t,n){return dt.current.useImperativeHandle(e,t,n)};ue.useInsertionEffect=function(e,t){return dt.current.useInsertionEffect(e,t)};ue.useLayoutEffect=function(e,t){return dt.current.useLayoutEffect(e,t)};ue.useMemo=function(e,t){return dt.current.useMemo(e,t)};ue.useReducer=function(e,t,n){return dt.current.useReducer(e,t,n)};ue.useRef=function(e){return dt.current.useRef(e)};ue.useState=function(e){return dt.current.useState(e)};ue.useSyncExternalStore=function(e,t,n){return dt.current.useSyncExternalStore(e,t,n)};ue.useTransition=function(){return dt.current.useTransition()};ue.version="18.3.1";Um.exports=ue;var g=Um.exports;const We=Ed(g),Gm=Zx({__proto__:null,default:We},[g]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cw=g,uw=Symbol.for("react.element"),dw=Symbol.for("react.fragment"),fw=Object.prototype.hasOwnProperty,hw=cw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,pw={key:!0,ref:!0,__self:!0,__source:!0};function Ym(e,t,n){var r,s={},o=null,i=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)fw.call(t,r)&&!pw.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:uw,type:e,key:o,ref:i,props:s,_owner:hw.current}}wl.Fragment=dw;wl.jsx=Ym;wl.jsxs=Ym;$m.exports=wl;var u=$m.exports,Zc={},Xm={exports:{}},jt={},Jm={exports:{}},eg={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,D){var $=T.length;T.push(D);e:for(;0<$;){var O=$-1>>>1,se=T[O];if(0<s(se,D))T[O]=D,T[$]=se,$=O;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var D=T[0],$=T.pop();if($!==D){T[0]=$;e:for(var O=0,se=T.length,oe=se>>>1;O<oe;){var he=2*(O+1)-1,_e=T[he],q=he+1,M=T[q];if(0>s(_e,$))q<se&&0>s(M,_e)?(T[O]=M,T[q]=$,O=q):(T[O]=_e,T[he]=$,O=he);else if(q<se&&0>s(M,$))T[O]=M,T[q]=$,O=q;else break e}}return D}function s(T,D){var $=T.sortIndex-D.sortIndex;return $!==0?$:T.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var i=Date,a=i.now();e.unstable_now=function(){return i.now()-a}}var l=[],c=[],d=1,f=null,y=3,x=!1,k=!1,m=!1,w=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(T){for(var D=n(c);D!==null;){if(D.callback===null)r(c);else if(D.startTime<=T)r(c),D.sortIndex=D.expirationTime,t(l,D);else break;D=n(c)}}function S(T){if(m=!1,v(T),!k)if(n(l)!==null)k=!0,K(b);else{var D=n(c);D!==null&&ee(S,D.startTime-T)}}function b(T,D){k=!1,m&&(m=!1,p(N),N=-1),x=!0;var $=y;try{for(v(D),f=n(l);f!==null&&(!(f.expirationTime>D)||T&&!L());){var O=f.callback;if(typeof O=="function"){f.callback=null,y=f.priorityLevel;var se=O(f.expirationTime<=D);D=e.unstable_now(),typeof se=="function"?f.callback=se:f===n(l)&&r(l),v(D)}else r(l);f=n(l)}if(f!==null)var oe=!0;else{var he=n(c);he!==null&&ee(S,he.startTime-D),oe=!1}return oe}finally{f=null,y=$,x=!1}}var E=!1,_=null,N=-1,j=5,R=-1;function L(){return!(e.unstable_now()-R<j)}function A(){if(_!==null){var T=e.unstable_now();R=T;var D=!0;try{D=_(!0,T)}finally{D?Z():(E=!1,_=null)}}else E=!1}var Z;if(typeof h=="function")Z=function(){h(A)};else if(typeof MessageChannel<"u"){var J=new MessageChannel,ae=J.port2;J.port1.onmessage=A,Z=function(){ae.postMessage(null)}}else Z=function(){w(A,0)};function K(T){_=T,E||(E=!0,Z())}function ee(T,D){N=w(function(){T(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){k||x||(k=!0,K(b))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return y},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(T){switch(y){case 1:case 2:case 3:var D=3;break;default:D=y}var $=y;y=D;try{return T()}finally{y=$}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,D){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var $=y;y=T;try{return D()}finally{y=$}},e.unstable_scheduleCallback=function(T,D,$){var O=e.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?O+$:O):$=O,T){case 1:var se=-1;break;case 2:se=250;break;case 5:se=**********;break;case 4:se=1e4;break;default:se=5e3}return se=$+se,T={id:d++,callback:D,priorityLevel:T,startTime:$,expirationTime:se,sortIndex:-1},$>O?(T.sortIndex=$,t(c,T),n(l)===null&&T===n(c)&&(m?(p(N),N=-1):m=!0,ee(S,$-O))):(T.sortIndex=se,t(l,T),k||x||(k=!0,K(b))),T},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(T){var D=y;return function(){var $=y;y=D;try{return T.apply(this,arguments)}finally{y=$}}}})(eg);Jm.exports=eg;var mw=Jm.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gw=g,_t=mw;function I(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var tg=new Set,ni={};function ds(e,t){Js(e,t),Js(e+"Capture",t)}function Js(e,t){for(ni[e]=t,e=0;e<t.length;e++)tg.add(t[e])}var jn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Kc=Object.prototype.hasOwnProperty,yw=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,mh={},gh={};function vw(e){return Kc.call(gh,e)?!0:Kc.call(mh,e)?!1:yw.test(e)?gh[e]=!0:(mh[e]=!0,!1)}function xw(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ww(e,t,n,r){if(t===null||typeof t>"u"||xw(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ft(e,t,n,r,s,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var Xe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Xe[e]=new ft(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Xe[t]=new ft(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Xe[e]=new ft(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Xe[e]=new ft(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Xe[e]=new ft(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Xe[e]=new ft(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Xe[e]=new ft(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Xe[e]=new ft(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Xe[e]=new ft(e,5,!1,e.toLowerCase(),null,!1,!1)});var Rd=/[\-:]([a-z])/g;function Id(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Rd,Id);Xe[t]=new ft(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Rd,Id);Xe[t]=new ft(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Rd,Id);Xe[t]=new ft(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Xe[e]=new ft(e,1,!1,e.toLowerCase(),null,!1,!1)});Xe.xlinkHref=new ft("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Xe[e]=new ft(e,1,!1,e.toLowerCase(),null,!0,!0)});function Pd(e,t,n,r){var s=Xe.hasOwnProperty(t)?Xe[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ww(t,n,s,r)&&(n=null),r||s===null?vw(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Ln=gw.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Wi=Symbol.for("react.element"),vs=Symbol.for("react.portal"),xs=Symbol.for("react.fragment"),Dd=Symbol.for("react.strict_mode"),qc=Symbol.for("react.profiler"),ng=Symbol.for("react.provider"),rg=Symbol.for("react.context"),Od=Symbol.for("react.forward_ref"),Gc=Symbol.for("react.suspense"),Yc=Symbol.for("react.suspense_list"),Ad=Symbol.for("react.memo"),Vn=Symbol.for("react.lazy"),sg=Symbol.for("react.offscreen"),yh=Symbol.iterator;function To(e){return e===null||typeof e!="object"?null:(e=yh&&e[yh]||e["@@iterator"],typeof e=="function"?e:null)}var Oe=Object.assign,ic;function Fo(e){if(ic===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ic=t&&t[1]||""}return`
`+ic+e}var ac=!1;function lc(e,t){if(!e||ac)return"";ac=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var s=c.stack.split(`
`),o=r.stack.split(`
`),i=s.length-1,a=o.length-1;1<=i&&0<=a&&s[i]!==o[a];)a--;for(;1<=i&&0<=a;i--,a--)if(s[i]!==o[a]){if(i!==1||a!==1)do if(i--,a--,0>a||s[i]!==o[a]){var l=`
`+s[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=i&&0<=a);break}}}finally{ac=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Fo(e):""}function Sw(e){switch(e.tag){case 5:return Fo(e.type);case 16:return Fo("Lazy");case 13:return Fo("Suspense");case 19:return Fo("SuspenseList");case 0:case 2:case 15:return e=lc(e.type,!1),e;case 11:return e=lc(e.type.render,!1),e;case 1:return e=lc(e.type,!0),e;default:return""}}function Xc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case xs:return"Fragment";case vs:return"Portal";case qc:return"Profiler";case Dd:return"StrictMode";case Gc:return"Suspense";case Yc:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case rg:return(e.displayName||"Context")+".Consumer";case ng:return(e._context.displayName||"Context")+".Provider";case Od:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ad:return t=e.displayName||null,t!==null?t:Xc(e.type)||"Memo";case Vn:t=e._payload,e=e._init;try{return Xc(e(t))}catch{}}return null}function kw(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Xc(t);case 8:return t===Dd?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function vr(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function og(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function bw(e){var t=og(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(i){r=""+i,o.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Qi(e){e._valueTracker||(e._valueTracker=bw(e))}function ig(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=og(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Da(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Jc(e,t){var n=t.checked;return Oe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function vh(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=vr(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ag(e,t){t=t.checked,t!=null&&Pd(e,"checked",t,!1)}function eu(e,t){ag(e,t);var n=vr(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?tu(e,t.type,n):t.hasOwnProperty("defaultValue")&&tu(e,t.type,vr(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function xh(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function tu(e,t,n){(t!=="number"||Da(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var zo=Array.isArray;function Is(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+vr(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function nu(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(I(91));return Oe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function wh(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(I(92));if(zo(n)){if(1<n.length)throw Error(I(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:vr(n)}}function lg(e,t){var n=vr(t.value),r=vr(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Sh(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function cg(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ru(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?cg(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Zi,ug=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Zi=Zi||document.createElement("div"),Zi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Zi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ri(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Qo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Cw=["Webkit","ms","Moz","O"];Object.keys(Qo).forEach(function(e){Cw.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Qo[t]=Qo[e]})});function dg(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Qo.hasOwnProperty(e)&&Qo[e]?(""+t).trim():t+"px"}function fg(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=dg(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Ew=Oe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function su(e,t){if(t){if(Ew[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(I(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(I(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(I(61))}if(t.style!=null&&typeof t.style!="object")throw Error(I(62))}}function ou(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var iu=null;function Md(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var au=null,Ps=null,Ds=null;function kh(e){if(e=Di(e)){if(typeof au!="function")throw Error(I(280));var t=e.stateNode;t&&(t=El(t),au(e.stateNode,e.type,t))}}function hg(e){Ps?Ds?Ds.push(e):Ds=[e]:Ps=e}function pg(){if(Ps){var e=Ps,t=Ds;if(Ds=Ps=null,kh(e),t)for(e=0;e<t.length;e++)kh(t[e])}}function mg(e,t){return e(t)}function gg(){}var cc=!1;function yg(e,t,n){if(cc)return e(t,n);cc=!0;try{return mg(e,t,n)}finally{cc=!1,(Ps!==null||Ds!==null)&&(gg(),pg())}}function si(e,t){var n=e.stateNode;if(n===null)return null;var r=El(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(I(231,t,typeof n));return n}var lu=!1;if(jn)try{var jo={};Object.defineProperty(jo,"passive",{get:function(){lu=!0}}),window.addEventListener("test",jo,jo),window.removeEventListener("test",jo,jo)}catch{lu=!1}function _w(e,t,n,r,s,o,i,a,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(d){this.onError(d)}}var Zo=!1,Oa=null,Aa=!1,cu=null,Nw={onError:function(e){Zo=!0,Oa=e}};function Tw(e,t,n,r,s,o,i,a,l){Zo=!1,Oa=null,_w.apply(Nw,arguments)}function jw(e,t,n,r,s,o,i,a,l){if(Tw.apply(this,arguments),Zo){if(Zo){var c=Oa;Zo=!1,Oa=null}else throw Error(I(198));Aa||(Aa=!0,cu=c)}}function fs(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function vg(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function bh(e){if(fs(e)!==e)throw Error(I(188))}function Rw(e){var t=e.alternate;if(!t){if(t=fs(e),t===null)throw Error(I(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var o=s.alternate;if(o===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return bh(s),e;if(o===r)return bh(s),t;o=o.sibling}throw Error(I(188))}if(n.return!==r.return)n=s,r=o;else{for(var i=!1,a=s.child;a;){if(a===n){i=!0,n=s,r=o;break}if(a===r){i=!0,r=s,n=o;break}a=a.sibling}if(!i){for(a=o.child;a;){if(a===n){i=!0,n=o,r=s;break}if(a===r){i=!0,r=o,n=s;break}a=a.sibling}if(!i)throw Error(I(189))}}if(n.alternate!==r)throw Error(I(190))}if(n.tag!==3)throw Error(I(188));return n.stateNode.current===n?e:t}function xg(e){return e=Rw(e),e!==null?wg(e):null}function wg(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=wg(e);if(t!==null)return t;e=e.sibling}return null}var Sg=_t.unstable_scheduleCallback,Ch=_t.unstable_cancelCallback,Iw=_t.unstable_shouldYield,Pw=_t.unstable_requestPaint,Fe=_t.unstable_now,Dw=_t.unstable_getCurrentPriorityLevel,Ld=_t.unstable_ImmediatePriority,kg=_t.unstable_UserBlockingPriority,Ma=_t.unstable_NormalPriority,Ow=_t.unstable_LowPriority,bg=_t.unstable_IdlePriority,Sl=null,fn=null;function Aw(e){if(fn&&typeof fn.onCommitFiberRoot=="function")try{fn.onCommitFiberRoot(Sl,e,void 0,(e.current.flags&128)===128)}catch{}}var Yt=Math.clz32?Math.clz32:Fw,Mw=Math.log,Lw=Math.LN2;function Fw(e){return e>>>=0,e===0?32:31-(Mw(e)/Lw|0)|0}var Ki=64,qi=4194304;function $o(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function La(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,o=e.pingedLanes,i=n&268435455;if(i!==0){var a=i&~s;a!==0?r=$o(a):(o&=i,o!==0&&(r=$o(o)))}else i=n&~s,i!==0?r=$o(i):o!==0&&(r=$o(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,o=t&-t,s>=o||s===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Yt(t),s=1<<n,r|=e[n],t&=~s;return r}function zw(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function $w(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-Yt(o),a=1<<i,l=s[i];l===-1?(!(a&n)||a&r)&&(s[i]=zw(a,t)):l<=t&&(e.expiredLanes|=a),o&=~a}}function uu(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Cg(){var e=Ki;return Ki<<=1,!(Ki&4194240)&&(Ki=64),e}function uc(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ii(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Yt(t),e[t]=n}function Uw(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Yt(n),o=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~o}}function Fd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Yt(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var ke=0;function Eg(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var _g,zd,Ng,Tg,jg,du=!1,Gi=[],ar=null,lr=null,cr=null,oi=new Map,ii=new Map,Qn=[],Bw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Eh(e,t){switch(e){case"focusin":case"focusout":ar=null;break;case"dragenter":case"dragleave":lr=null;break;case"mouseover":case"mouseout":cr=null;break;case"pointerover":case"pointerout":oi.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ii.delete(t.pointerId)}}function Ro(e,t,n,r,s,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[s]},t!==null&&(t=Di(t),t!==null&&zd(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function Vw(e,t,n,r,s){switch(t){case"focusin":return ar=Ro(ar,e,t,n,r,s),!0;case"dragenter":return lr=Ro(lr,e,t,n,r,s),!0;case"mouseover":return cr=Ro(cr,e,t,n,r,s),!0;case"pointerover":var o=s.pointerId;return oi.set(o,Ro(oi.get(o)||null,e,t,n,r,s)),!0;case"gotpointercapture":return o=s.pointerId,ii.set(o,Ro(ii.get(o)||null,e,t,n,r,s)),!0}return!1}function Rg(e){var t=Ar(e.target);if(t!==null){var n=fs(t);if(n!==null){if(t=n.tag,t===13){if(t=vg(n),t!==null){e.blockedOn=t,jg(e.priority,function(){Ng(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function va(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=fu(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);iu=r,n.target.dispatchEvent(r),iu=null}else return t=Di(n),t!==null&&zd(t),e.blockedOn=n,!1;t.shift()}return!0}function _h(e,t,n){va(e)&&n.delete(t)}function Hw(){du=!1,ar!==null&&va(ar)&&(ar=null),lr!==null&&va(lr)&&(lr=null),cr!==null&&va(cr)&&(cr=null),oi.forEach(_h),ii.forEach(_h)}function Io(e,t){e.blockedOn===t&&(e.blockedOn=null,du||(du=!0,_t.unstable_scheduleCallback(_t.unstable_NormalPriority,Hw)))}function ai(e){function t(s){return Io(s,e)}if(0<Gi.length){Io(Gi[0],e);for(var n=1;n<Gi.length;n++){var r=Gi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(ar!==null&&Io(ar,e),lr!==null&&Io(lr,e),cr!==null&&Io(cr,e),oi.forEach(t),ii.forEach(t),n=0;n<Qn.length;n++)r=Qn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Qn.length&&(n=Qn[0],n.blockedOn===null);)Rg(n),n.blockedOn===null&&Qn.shift()}var Os=Ln.ReactCurrentBatchConfig,Fa=!0;function Ww(e,t,n,r){var s=ke,o=Os.transition;Os.transition=null;try{ke=1,$d(e,t,n,r)}finally{ke=s,Os.transition=o}}function Qw(e,t,n,r){var s=ke,o=Os.transition;Os.transition=null;try{ke=4,$d(e,t,n,r)}finally{ke=s,Os.transition=o}}function $d(e,t,n,r){if(Fa){var s=fu(e,t,n,r);if(s===null)wc(e,t,r,za,n),Eh(e,r);else if(Vw(s,e,t,n,r))r.stopPropagation();else if(Eh(e,r),t&4&&-1<Bw.indexOf(e)){for(;s!==null;){var o=Di(s);if(o!==null&&_g(o),o=fu(e,t,n,r),o===null&&wc(e,t,r,za,n),o===s)break;s=o}s!==null&&r.stopPropagation()}else wc(e,t,r,null,n)}}var za=null;function fu(e,t,n,r){if(za=null,e=Md(r),e=Ar(e),e!==null)if(t=fs(e),t===null)e=null;else if(n=t.tag,n===13){if(e=vg(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return za=e,null}function Ig(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Dw()){case Ld:return 1;case kg:return 4;case Ma:case Ow:return 16;case bg:return 536870912;default:return 16}default:return 16}}var sr=null,Ud=null,xa=null;function Pg(){if(xa)return xa;var e,t=Ud,n=t.length,r,s="value"in sr?sr.value:sr.textContent,o=s.length;for(e=0;e<n&&t[e]===s[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===s[o-r];r++);return xa=s.slice(e,1<r?1-r:void 0)}function wa(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Yi(){return!0}function Nh(){return!1}function Rt(e){function t(n,r,s,o,i){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=o,this.target=i,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Yi:Nh,this.isPropagationStopped=Nh,this}return Oe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Yi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Yi)},persist:function(){},isPersistent:Yi}),t}var yo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Bd=Rt(yo),Pi=Oe({},yo,{view:0,detail:0}),Zw=Rt(Pi),dc,fc,Po,kl=Oe({},Pi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Vd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Po&&(Po&&e.type==="mousemove"?(dc=e.screenX-Po.screenX,fc=e.screenY-Po.screenY):fc=dc=0,Po=e),dc)},movementY:function(e){return"movementY"in e?e.movementY:fc}}),Th=Rt(kl),Kw=Oe({},kl,{dataTransfer:0}),qw=Rt(Kw),Gw=Oe({},Pi,{relatedTarget:0}),hc=Rt(Gw),Yw=Oe({},yo,{animationName:0,elapsedTime:0,pseudoElement:0}),Xw=Rt(Yw),Jw=Oe({},yo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),e1=Rt(Jw),t1=Oe({},yo,{data:0}),jh=Rt(t1),n1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},r1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},s1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function o1(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=s1[e])?!!t[e]:!1}function Vd(){return o1}var i1=Oe({},Pi,{key:function(e){if(e.key){var t=n1[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=wa(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?r1[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Vd,charCode:function(e){return e.type==="keypress"?wa(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?wa(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),a1=Rt(i1),l1=Oe({},kl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Rh=Rt(l1),c1=Oe({},Pi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Vd}),u1=Rt(c1),d1=Oe({},yo,{propertyName:0,elapsedTime:0,pseudoElement:0}),f1=Rt(d1),h1=Oe({},kl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),p1=Rt(h1),m1=[9,13,27,32],Hd=jn&&"CompositionEvent"in window,Ko=null;jn&&"documentMode"in document&&(Ko=document.documentMode);var g1=jn&&"TextEvent"in window&&!Ko,Dg=jn&&(!Hd||Ko&&8<Ko&&11>=Ko),Ih=" ",Ph=!1;function Og(e,t){switch(e){case"keyup":return m1.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ag(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ws=!1;function y1(e,t){switch(e){case"compositionend":return Ag(t);case"keypress":return t.which!==32?null:(Ph=!0,Ih);case"textInput":return e=t.data,e===Ih&&Ph?null:e;default:return null}}function v1(e,t){if(ws)return e==="compositionend"||!Hd&&Og(e,t)?(e=Pg(),xa=Ud=sr=null,ws=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Dg&&t.locale!=="ko"?null:t.data;default:return null}}var x1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Dh(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!x1[e.type]:t==="textarea"}function Mg(e,t,n,r){hg(r),t=$a(t,"onChange"),0<t.length&&(n=new Bd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qo=null,li=null;function w1(e){Zg(e,0)}function bl(e){var t=bs(e);if(ig(t))return e}function S1(e,t){if(e==="change")return t}var Lg=!1;if(jn){var pc;if(jn){var mc="oninput"in document;if(!mc){var Oh=document.createElement("div");Oh.setAttribute("oninput","return;"),mc=typeof Oh.oninput=="function"}pc=mc}else pc=!1;Lg=pc&&(!document.documentMode||9<document.documentMode)}function Ah(){qo&&(qo.detachEvent("onpropertychange",Fg),li=qo=null)}function Fg(e){if(e.propertyName==="value"&&bl(li)){var t=[];Mg(t,li,e,Md(e)),yg(w1,t)}}function k1(e,t,n){e==="focusin"?(Ah(),qo=t,li=n,qo.attachEvent("onpropertychange",Fg)):e==="focusout"&&Ah()}function b1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return bl(li)}function C1(e,t){if(e==="click")return bl(t)}function E1(e,t){if(e==="input"||e==="change")return bl(t)}function _1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Jt=typeof Object.is=="function"?Object.is:_1;function ci(e,t){if(Jt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Kc.call(t,s)||!Jt(e[s],t[s]))return!1}return!0}function Mh(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Lh(e,t){var n=Mh(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Mh(n)}}function zg(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?zg(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function $g(){for(var e=window,t=Da();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Da(e.document)}return t}function Wd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function N1(e){var t=$g(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&zg(n.ownerDocument.documentElement,n)){if(r!==null&&Wd(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,o=Math.min(r.start,s);r=r.end===void 0?o:Math.min(r.end,s),!e.extend&&o>r&&(s=r,r=o,o=s),s=Lh(n,o);var i=Lh(n,r);s&&i&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var T1=jn&&"documentMode"in document&&11>=document.documentMode,Ss=null,hu=null,Go=null,pu=!1;function Fh(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;pu||Ss==null||Ss!==Da(r)||(r=Ss,"selectionStart"in r&&Wd(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Go&&ci(Go,r)||(Go=r,r=$a(hu,"onSelect"),0<r.length&&(t=new Bd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ss)))}function Xi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ks={animationend:Xi("Animation","AnimationEnd"),animationiteration:Xi("Animation","AnimationIteration"),animationstart:Xi("Animation","AnimationStart"),transitionend:Xi("Transition","TransitionEnd")},gc={},Ug={};jn&&(Ug=document.createElement("div").style,"AnimationEvent"in window||(delete ks.animationend.animation,delete ks.animationiteration.animation,delete ks.animationstart.animation),"TransitionEvent"in window||delete ks.transitionend.transition);function Cl(e){if(gc[e])return gc[e];if(!ks[e])return e;var t=ks[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Ug)return gc[e]=t[n];return e}var Bg=Cl("animationend"),Vg=Cl("animationiteration"),Hg=Cl("animationstart"),Wg=Cl("transitionend"),Qg=new Map,zh="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Er(e,t){Qg.set(e,t),ds(t,[e])}for(var yc=0;yc<zh.length;yc++){var vc=zh[yc],j1=vc.toLowerCase(),R1=vc[0].toUpperCase()+vc.slice(1);Er(j1,"on"+R1)}Er(Bg,"onAnimationEnd");Er(Vg,"onAnimationIteration");Er(Hg,"onAnimationStart");Er("dblclick","onDoubleClick");Er("focusin","onFocus");Er("focusout","onBlur");Er(Wg,"onTransitionEnd");Js("onMouseEnter",["mouseout","mouseover"]);Js("onMouseLeave",["mouseout","mouseover"]);Js("onPointerEnter",["pointerout","pointerover"]);Js("onPointerLeave",["pointerout","pointerover"]);ds("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));ds("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));ds("onBeforeInput",["compositionend","keypress","textInput","paste"]);ds("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));ds("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));ds("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Uo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),I1=new Set("cancel close invalid load scroll toggle".split(" ").concat(Uo));function $h(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,jw(r,t,void 0,e),e.currentTarget=null}function Zg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var a=r[i],l=a.instance,c=a.currentTarget;if(a=a.listener,l!==o&&s.isPropagationStopped())break e;$h(s,a,c),o=l}else for(i=0;i<r.length;i++){if(a=r[i],l=a.instance,c=a.currentTarget,a=a.listener,l!==o&&s.isPropagationStopped())break e;$h(s,a,c),o=l}}}if(Aa)throw e=cu,Aa=!1,cu=null,e}function Te(e,t){var n=t[xu];n===void 0&&(n=t[xu]=new Set);var r=e+"__bubble";n.has(r)||(Kg(t,e,2,!1),n.add(r))}function xc(e,t,n){var r=0;t&&(r|=4),Kg(n,e,r,t)}var Ji="_reactListening"+Math.random().toString(36).slice(2);function ui(e){if(!e[Ji]){e[Ji]=!0,tg.forEach(function(n){n!=="selectionchange"&&(I1.has(n)||xc(n,!1,e),xc(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ji]||(t[Ji]=!0,xc("selectionchange",!1,t))}}function Kg(e,t,n,r){switch(Ig(t)){case 1:var s=Ww;break;case 4:s=Qw;break;default:s=$d}n=s.bind(null,t,n,e),s=void 0,!lu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function wc(e,t,n,r,s){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(i===4)for(i=r.return;i!==null;){var l=i.tag;if((l===3||l===4)&&(l=i.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;i=i.return}for(;a!==null;){if(i=Ar(a),i===null)return;if(l=i.tag,l===5||l===6){r=o=i;continue e}a=a.parentNode}}r=r.return}yg(function(){var c=o,d=Md(n),f=[];e:{var y=Qg.get(e);if(y!==void 0){var x=Bd,k=e;switch(e){case"keypress":if(wa(n)===0)break e;case"keydown":case"keyup":x=a1;break;case"focusin":k="focus",x=hc;break;case"focusout":k="blur",x=hc;break;case"beforeblur":case"afterblur":x=hc;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":x=Th;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":x=qw;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":x=u1;break;case Bg:case Vg:case Hg:x=Xw;break;case Wg:x=f1;break;case"scroll":x=Zw;break;case"wheel":x=p1;break;case"copy":case"cut":case"paste":x=e1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":x=Rh}var m=(t&4)!==0,w=!m&&e==="scroll",p=m?y!==null?y+"Capture":null:y;m=[];for(var h=c,v;h!==null;){v=h;var S=v.stateNode;if(v.tag===5&&S!==null&&(v=S,p!==null&&(S=si(h,p),S!=null&&m.push(di(h,S,v)))),w)break;h=h.return}0<m.length&&(y=new x(y,k,null,n,d),f.push({event:y,listeners:m}))}}if(!(t&7)){e:{if(y=e==="mouseover"||e==="pointerover",x=e==="mouseout"||e==="pointerout",y&&n!==iu&&(k=n.relatedTarget||n.fromElement)&&(Ar(k)||k[Rn]))break e;if((x||y)&&(y=d.window===d?d:(y=d.ownerDocument)?y.defaultView||y.parentWindow:window,x?(k=n.relatedTarget||n.toElement,x=c,k=k?Ar(k):null,k!==null&&(w=fs(k),k!==w||k.tag!==5&&k.tag!==6)&&(k=null)):(x=null,k=c),x!==k)){if(m=Th,S="onMouseLeave",p="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(m=Rh,S="onPointerLeave",p="onPointerEnter",h="pointer"),w=x==null?y:bs(x),v=k==null?y:bs(k),y=new m(S,h+"leave",x,n,d),y.target=w,y.relatedTarget=v,S=null,Ar(d)===c&&(m=new m(p,h+"enter",k,n,d),m.target=v,m.relatedTarget=w,S=m),w=S,x&&k)t:{for(m=x,p=k,h=0,v=m;v;v=hs(v))h++;for(v=0,S=p;S;S=hs(S))v++;for(;0<h-v;)m=hs(m),h--;for(;0<v-h;)p=hs(p),v--;for(;h--;){if(m===p||p!==null&&m===p.alternate)break t;m=hs(m),p=hs(p)}m=null}else m=null;x!==null&&Uh(f,y,x,m,!1),k!==null&&w!==null&&Uh(f,w,k,m,!0)}}e:{if(y=c?bs(c):window,x=y.nodeName&&y.nodeName.toLowerCase(),x==="select"||x==="input"&&y.type==="file")var b=S1;else if(Dh(y))if(Lg)b=E1;else{b=b1;var E=k1}else(x=y.nodeName)&&x.toLowerCase()==="input"&&(y.type==="checkbox"||y.type==="radio")&&(b=C1);if(b&&(b=b(e,c))){Mg(f,b,n,d);break e}E&&E(e,y,c),e==="focusout"&&(E=y._wrapperState)&&E.controlled&&y.type==="number"&&tu(y,"number",y.value)}switch(E=c?bs(c):window,e){case"focusin":(Dh(E)||E.contentEditable==="true")&&(Ss=E,hu=c,Go=null);break;case"focusout":Go=hu=Ss=null;break;case"mousedown":pu=!0;break;case"contextmenu":case"mouseup":case"dragend":pu=!1,Fh(f,n,d);break;case"selectionchange":if(T1)break;case"keydown":case"keyup":Fh(f,n,d)}var _;if(Hd)e:{switch(e){case"compositionstart":var N="onCompositionStart";break e;case"compositionend":N="onCompositionEnd";break e;case"compositionupdate":N="onCompositionUpdate";break e}N=void 0}else ws?Og(e,n)&&(N="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(N="onCompositionStart");N&&(Dg&&n.locale!=="ko"&&(ws||N!=="onCompositionStart"?N==="onCompositionEnd"&&ws&&(_=Pg()):(sr=d,Ud="value"in sr?sr.value:sr.textContent,ws=!0)),E=$a(c,N),0<E.length&&(N=new jh(N,e,null,n,d),f.push({event:N,listeners:E}),_?N.data=_:(_=Ag(n),_!==null&&(N.data=_)))),(_=g1?y1(e,n):v1(e,n))&&(c=$a(c,"onBeforeInput"),0<c.length&&(d=new jh("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:c}),d.data=_))}Zg(f,t)})}function di(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $a(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,o=s.stateNode;s.tag===5&&o!==null&&(s=o,o=si(e,n),o!=null&&r.unshift(di(e,o,s)),o=si(e,t),o!=null&&r.push(di(e,o,s))),e=e.return}return r}function hs(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Uh(e,t,n,r,s){for(var o=t._reactName,i=[];n!==null&&n!==r;){var a=n,l=a.alternate,c=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&c!==null&&(a=c,s?(l=si(n,o),l!=null&&i.unshift(di(n,l,a))):s||(l=si(n,o),l!=null&&i.push(di(n,l,a)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var P1=/\r\n?/g,D1=/\u0000|\uFFFD/g;function Bh(e){return(typeof e=="string"?e:""+e).replace(P1,`
`).replace(D1,"")}function ea(e,t,n){if(t=Bh(t),Bh(e)!==t&&n)throw Error(I(425))}function Ua(){}var mu=null,gu=null;function yu(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var vu=typeof setTimeout=="function"?setTimeout:void 0,O1=typeof clearTimeout=="function"?clearTimeout:void 0,Vh=typeof Promise=="function"?Promise:void 0,A1=typeof queueMicrotask=="function"?queueMicrotask:typeof Vh<"u"?function(e){return Vh.resolve(null).then(e).catch(M1)}:vu;function M1(e){setTimeout(function(){throw e})}function Sc(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),ai(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);ai(t)}function ur(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Hh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var vo=Math.random().toString(36).slice(2),cn="__reactFiber$"+vo,fi="__reactProps$"+vo,Rn="__reactContainer$"+vo,xu="__reactEvents$"+vo,L1="__reactListeners$"+vo,F1="__reactHandles$"+vo;function Ar(e){var t=e[cn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Rn]||n[cn]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Hh(e);e!==null;){if(n=e[cn])return n;e=Hh(e)}return t}e=n,n=e.parentNode}return null}function Di(e){return e=e[cn]||e[Rn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function bs(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(I(33))}function El(e){return e[fi]||null}var wu=[],Cs=-1;function _r(e){return{current:e}}function je(e){0>Cs||(e.current=wu[Cs],wu[Cs]=null,Cs--)}function Ee(e,t){Cs++,wu[Cs]=e.current,e.current=t}var xr={},st=_r(xr),vt=_r(!1),qr=xr;function eo(e,t){var n=e.type.contextTypes;if(!n)return xr;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},o;for(o in n)s[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function xt(e){return e=e.childContextTypes,e!=null}function Ba(){je(vt),je(st)}function Wh(e,t,n){if(st.current!==xr)throw Error(I(168));Ee(st,t),Ee(vt,n)}function qg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(I(108,kw(e)||"Unknown",s));return Oe({},n,r)}function Va(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||xr,qr=st.current,Ee(st,e),Ee(vt,vt.current),!0}function Qh(e,t,n){var r=e.stateNode;if(!r)throw Error(I(169));n?(e=qg(e,t,qr),r.__reactInternalMemoizedMergedChildContext=e,je(vt),je(st),Ee(st,e)):je(vt),Ee(vt,n)}var kn=null,_l=!1,kc=!1;function Gg(e){kn===null?kn=[e]:kn.push(e)}function z1(e){_l=!0,Gg(e)}function Nr(){if(!kc&&kn!==null){kc=!0;var e=0,t=ke;try{var n=kn;for(ke=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}kn=null,_l=!1}catch(s){throw kn!==null&&(kn=kn.slice(e+1)),Sg(Ld,Nr),s}finally{ke=t,kc=!1}}return null}var Es=[],_s=0,Ha=null,Wa=0,Dt=[],Ot=0,Gr=null,En=1,_n="";function Dr(e,t){Es[_s++]=Wa,Es[_s++]=Ha,Ha=e,Wa=t}function Yg(e,t,n){Dt[Ot++]=En,Dt[Ot++]=_n,Dt[Ot++]=Gr,Gr=e;var r=En;e=_n;var s=32-Yt(r)-1;r&=~(1<<s),n+=1;var o=32-Yt(t)+s;if(30<o){var i=s-s%5;o=(r&(1<<i)-1).toString(32),r>>=i,s-=i,En=1<<32-Yt(t)+s|n<<s|r,_n=o+e}else En=1<<o|n<<s|r,_n=e}function Qd(e){e.return!==null&&(Dr(e,1),Yg(e,1,0))}function Zd(e){for(;e===Ha;)Ha=Es[--_s],Es[_s]=null,Wa=Es[--_s],Es[_s]=null;for(;e===Gr;)Gr=Dt[--Ot],Dt[Ot]=null,_n=Dt[--Ot],Dt[Ot]=null,En=Dt[--Ot],Dt[Ot]=null}var Ct=null,bt=null,Ie=!1,Kt=null;function Xg(e,t){var n=Mt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Zh(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ct=e,bt=ur(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ct=e,bt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Gr!==null?{id:En,overflow:_n}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Mt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ct=e,bt=null,!0):!1;default:return!1}}function Su(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ku(e){if(Ie){var t=bt;if(t){var n=t;if(!Zh(e,t)){if(Su(e))throw Error(I(418));t=ur(n.nextSibling);var r=Ct;t&&Zh(e,t)?Xg(r,n):(e.flags=e.flags&-4097|2,Ie=!1,Ct=e)}}else{if(Su(e))throw Error(I(418));e.flags=e.flags&-4097|2,Ie=!1,Ct=e}}}function Kh(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ct=e}function ta(e){if(e!==Ct)return!1;if(!Ie)return Kh(e),Ie=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!yu(e.type,e.memoizedProps)),t&&(t=bt)){if(Su(e))throw Jg(),Error(I(418));for(;t;)Xg(e,t),t=ur(t.nextSibling)}if(Kh(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(I(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){bt=ur(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}bt=null}}else bt=Ct?ur(e.stateNode.nextSibling):null;return!0}function Jg(){for(var e=bt;e;)e=ur(e.nextSibling)}function to(){bt=Ct=null,Ie=!1}function Kd(e){Kt===null?Kt=[e]:Kt.push(e)}var $1=Ln.ReactCurrentBatchConfig;function Do(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(I(309));var r=n.stateNode}if(!r)throw Error(I(147,e));var s=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(i){var a=s.refs;i===null?delete a[o]:a[o]=i},t._stringRef=o,t)}if(typeof e!="string")throw Error(I(284));if(!n._owner)throw Error(I(290,e))}return e}function na(e,t){throw e=Object.prototype.toString.call(t),Error(I(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function qh(e){var t=e._init;return t(e._payload)}function ey(e){function t(p,h){if(e){var v=p.deletions;v===null?(p.deletions=[h],p.flags|=16):v.push(h)}}function n(p,h){if(!e)return null;for(;h!==null;)t(p,h),h=h.sibling;return null}function r(p,h){for(p=new Map;h!==null;)h.key!==null?p.set(h.key,h):p.set(h.index,h),h=h.sibling;return p}function s(p,h){return p=pr(p,h),p.index=0,p.sibling=null,p}function o(p,h,v){return p.index=v,e?(v=p.alternate,v!==null?(v=v.index,v<h?(p.flags|=2,h):v):(p.flags|=2,h)):(p.flags|=1048576,h)}function i(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,h,v,S){return h===null||h.tag!==6?(h=jc(v,p.mode,S),h.return=p,h):(h=s(h,v),h.return=p,h)}function l(p,h,v,S){var b=v.type;return b===xs?d(p,h,v.props.children,S,v.key):h!==null&&(h.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===Vn&&qh(b)===h.type)?(S=s(h,v.props),S.ref=Do(p,h,v),S.return=p,S):(S=Na(v.type,v.key,v.props,null,p.mode,S),S.ref=Do(p,h,v),S.return=p,S)}function c(p,h,v,S){return h===null||h.tag!==4||h.stateNode.containerInfo!==v.containerInfo||h.stateNode.implementation!==v.implementation?(h=Rc(v,p.mode,S),h.return=p,h):(h=s(h,v.children||[]),h.return=p,h)}function d(p,h,v,S,b){return h===null||h.tag!==7?(h=Kr(v,p.mode,S,b),h.return=p,h):(h=s(h,v),h.return=p,h)}function f(p,h,v){if(typeof h=="string"&&h!==""||typeof h=="number")return h=jc(""+h,p.mode,v),h.return=p,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case Wi:return v=Na(h.type,h.key,h.props,null,p.mode,v),v.ref=Do(p,null,h),v.return=p,v;case vs:return h=Rc(h,p.mode,v),h.return=p,h;case Vn:var S=h._init;return f(p,S(h._payload),v)}if(zo(h)||To(h))return h=Kr(h,p.mode,v,null),h.return=p,h;na(p,h)}return null}function y(p,h,v,S){var b=h!==null?h.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return b!==null?null:a(p,h,""+v,S);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Wi:return v.key===b?l(p,h,v,S):null;case vs:return v.key===b?c(p,h,v,S):null;case Vn:return b=v._init,y(p,h,b(v._payload),S)}if(zo(v)||To(v))return b!==null?null:d(p,h,v,S,null);na(p,v)}return null}function x(p,h,v,S,b){if(typeof S=="string"&&S!==""||typeof S=="number")return p=p.get(v)||null,a(h,p,""+S,b);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Wi:return p=p.get(S.key===null?v:S.key)||null,l(h,p,S,b);case vs:return p=p.get(S.key===null?v:S.key)||null,c(h,p,S,b);case Vn:var E=S._init;return x(p,h,v,E(S._payload),b)}if(zo(S)||To(S))return p=p.get(v)||null,d(h,p,S,b,null);na(h,S)}return null}function k(p,h,v,S){for(var b=null,E=null,_=h,N=h=0,j=null;_!==null&&N<v.length;N++){_.index>N?(j=_,_=null):j=_.sibling;var R=y(p,_,v[N],S);if(R===null){_===null&&(_=j);break}e&&_&&R.alternate===null&&t(p,_),h=o(R,h,N),E===null?b=R:E.sibling=R,E=R,_=j}if(N===v.length)return n(p,_),Ie&&Dr(p,N),b;if(_===null){for(;N<v.length;N++)_=f(p,v[N],S),_!==null&&(h=o(_,h,N),E===null?b=_:E.sibling=_,E=_);return Ie&&Dr(p,N),b}for(_=r(p,_);N<v.length;N++)j=x(_,p,N,v[N],S),j!==null&&(e&&j.alternate!==null&&_.delete(j.key===null?N:j.key),h=o(j,h,N),E===null?b=j:E.sibling=j,E=j);return e&&_.forEach(function(L){return t(p,L)}),Ie&&Dr(p,N),b}function m(p,h,v,S){var b=To(v);if(typeof b!="function")throw Error(I(150));if(v=b.call(v),v==null)throw Error(I(151));for(var E=b=null,_=h,N=h=0,j=null,R=v.next();_!==null&&!R.done;N++,R=v.next()){_.index>N?(j=_,_=null):j=_.sibling;var L=y(p,_,R.value,S);if(L===null){_===null&&(_=j);break}e&&_&&L.alternate===null&&t(p,_),h=o(L,h,N),E===null?b=L:E.sibling=L,E=L,_=j}if(R.done)return n(p,_),Ie&&Dr(p,N),b;if(_===null){for(;!R.done;N++,R=v.next())R=f(p,R.value,S),R!==null&&(h=o(R,h,N),E===null?b=R:E.sibling=R,E=R);return Ie&&Dr(p,N),b}for(_=r(p,_);!R.done;N++,R=v.next())R=x(_,p,N,R.value,S),R!==null&&(e&&R.alternate!==null&&_.delete(R.key===null?N:R.key),h=o(R,h,N),E===null?b=R:E.sibling=R,E=R);return e&&_.forEach(function(A){return t(p,A)}),Ie&&Dr(p,N),b}function w(p,h,v,S){if(typeof v=="object"&&v!==null&&v.type===xs&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Wi:e:{for(var b=v.key,E=h;E!==null;){if(E.key===b){if(b=v.type,b===xs){if(E.tag===7){n(p,E.sibling),h=s(E,v.props.children),h.return=p,p=h;break e}}else if(E.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===Vn&&qh(b)===E.type){n(p,E.sibling),h=s(E,v.props),h.ref=Do(p,E,v),h.return=p,p=h;break e}n(p,E);break}else t(p,E);E=E.sibling}v.type===xs?(h=Kr(v.props.children,p.mode,S,v.key),h.return=p,p=h):(S=Na(v.type,v.key,v.props,null,p.mode,S),S.ref=Do(p,h,v),S.return=p,p=S)}return i(p);case vs:e:{for(E=v.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===v.containerInfo&&h.stateNode.implementation===v.implementation){n(p,h.sibling),h=s(h,v.children||[]),h.return=p,p=h;break e}else{n(p,h);break}else t(p,h);h=h.sibling}h=Rc(v,p.mode,S),h.return=p,p=h}return i(p);case Vn:return E=v._init,w(p,h,E(v._payload),S)}if(zo(v))return k(p,h,v,S);if(To(v))return m(p,h,v,S);na(p,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,h!==null&&h.tag===6?(n(p,h.sibling),h=s(h,v),h.return=p,p=h):(n(p,h),h=jc(v,p.mode,S),h.return=p,p=h),i(p)):n(p,h)}return w}var no=ey(!0),ty=ey(!1),Qa=_r(null),Za=null,Ns=null,qd=null;function Gd(){qd=Ns=Za=null}function Yd(e){var t=Qa.current;je(Qa),e._currentValue=t}function bu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function As(e,t){Za=e,qd=Ns=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(yt=!0),e.firstContext=null)}function Ft(e){var t=e._currentValue;if(qd!==e)if(e={context:e,memoizedValue:t,next:null},Ns===null){if(Za===null)throw Error(I(308));Ns=e,Za.dependencies={lanes:0,firstContext:e}}else Ns=Ns.next=e;return t}var Mr=null;function Xd(e){Mr===null?Mr=[e]:Mr.push(e)}function ny(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,Xd(t)):(n.next=s.next,s.next=n),t.interleaved=n,In(e,r)}function In(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Hn=!1;function Jd(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ry(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Nn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function dr(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ge&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,In(e,n)}return s=r.interleaved,s===null?(t.next=t,Xd(r)):(t.next=s.next,s.next=t),r.interleaved=t,In(e,n)}function Sa(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fd(e,n)}}function Gh(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?s=o=i:o=o.next=i,n=n.next}while(n!==null);o===null?s=o=t:o=o.next=t}else s=o=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ka(e,t,n,r){var s=e.updateQueue;Hn=!1;var o=s.firstBaseUpdate,i=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var l=a,c=l.next;l.next=null,i===null?o=c:i.next=c,i=l;var d=e.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==i&&(a===null?d.firstBaseUpdate=c:a.next=c,d.lastBaseUpdate=l))}if(o!==null){var f=s.baseState;i=0,d=c=l=null,a=o;do{var y=a.lane,x=a.eventTime;if((r&y)===y){d!==null&&(d=d.next={eventTime:x,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var k=e,m=a;switch(y=t,x=n,m.tag){case 1:if(k=m.payload,typeof k=="function"){f=k.call(x,f,y);break e}f=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=m.payload,y=typeof k=="function"?k.call(x,f,y):k,y==null)break e;f=Oe({},f,y);break e;case 2:Hn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,y=s.effects,y===null?s.effects=[a]:y.push(a))}else x={eventTime:x,lane:y,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(c=d=x,l=f):d=d.next=x,i|=y;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;y=a,a=y.next,y.next=null,s.lastBaseUpdate=y,s.shared.pending=null}}while(!0);if(d===null&&(l=f),s.baseState=l,s.firstBaseUpdate=c,s.lastBaseUpdate=d,t=s.shared.interleaved,t!==null){s=t;do i|=s.lane,s=s.next;while(s!==t)}else o===null&&(s.shared.lanes=0);Xr|=i,e.lanes=i,e.memoizedState=f}}function Yh(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(I(191,s));s.call(r)}}}var Oi={},hn=_r(Oi),hi=_r(Oi),pi=_r(Oi);function Lr(e){if(e===Oi)throw Error(I(174));return e}function ef(e,t){switch(Ee(pi,t),Ee(hi,e),Ee(hn,Oi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ru(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ru(t,e)}je(hn),Ee(hn,t)}function ro(){je(hn),je(hi),je(pi)}function sy(e){Lr(pi.current);var t=Lr(hn.current),n=ru(t,e.type);t!==n&&(Ee(hi,e),Ee(hn,n))}function tf(e){hi.current===e&&(je(hn),je(hi))}var Pe=_r(0);function qa(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var bc=[];function nf(){for(var e=0;e<bc.length;e++)bc[e]._workInProgressVersionPrimary=null;bc.length=0}var ka=Ln.ReactCurrentDispatcher,Cc=Ln.ReactCurrentBatchConfig,Yr=0,De=null,Ue=null,He=null,Ga=!1,Yo=!1,mi=0,U1=0;function Je(){throw Error(I(321))}function rf(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Jt(e[n],t[n]))return!1;return!0}function sf(e,t,n,r,s,o){if(Yr=o,De=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ka.current=e===null||e.memoizedState===null?W1:Q1,e=n(r,s),Yo){o=0;do{if(Yo=!1,mi=0,25<=o)throw Error(I(301));o+=1,He=Ue=null,t.updateQueue=null,ka.current=Z1,e=n(r,s)}while(Yo)}if(ka.current=Ya,t=Ue!==null&&Ue.next!==null,Yr=0,He=Ue=De=null,Ga=!1,t)throw Error(I(300));return e}function of(){var e=mi!==0;return mi=0,e}function sn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return He===null?De.memoizedState=He=e:He=He.next=e,He}function zt(){if(Ue===null){var e=De.alternate;e=e!==null?e.memoizedState:null}else e=Ue.next;var t=He===null?De.memoizedState:He.next;if(t!==null)He=t,Ue=e;else{if(e===null)throw Error(I(310));Ue=e,e={memoizedState:Ue.memoizedState,baseState:Ue.baseState,baseQueue:Ue.baseQueue,queue:Ue.queue,next:null},He===null?De.memoizedState=He=e:He=He.next=e}return He}function gi(e,t){return typeof t=="function"?t(e):t}function Ec(e){var t=zt(),n=t.queue;if(n===null)throw Error(I(311));n.lastRenderedReducer=e;var r=Ue,s=r.baseQueue,o=n.pending;if(o!==null){if(s!==null){var i=s.next;s.next=o.next,o.next=i}r.baseQueue=s=o,n.pending=null}if(s!==null){o=s.next,r=r.baseState;var a=i=null,l=null,c=o;do{var d=c.lane;if((Yr&d)===d)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(a=l=f,i=r):l=l.next=f,De.lanes|=d,Xr|=d}c=c.next}while(c!==null&&c!==o);l===null?i=r:l.next=a,Jt(r,t.memoizedState)||(yt=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do o=s.lane,De.lanes|=o,Xr|=o,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function _c(e){var t=zt(),n=t.queue;if(n===null)throw Error(I(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,o=t.memoizedState;if(s!==null){n.pending=null;var i=s=s.next;do o=e(o,i.action),i=i.next;while(i!==s);Jt(o,t.memoizedState)||(yt=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function oy(){}function iy(e,t){var n=De,r=zt(),s=t(),o=!Jt(r.memoizedState,s);if(o&&(r.memoizedState=s,yt=!0),r=r.queue,af(cy.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||He!==null&&He.memoizedState.tag&1){if(n.flags|=2048,yi(9,ly.bind(null,n,r,s,t),void 0,null),Qe===null)throw Error(I(349));Yr&30||ay(n,t,s)}return s}function ay(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=De.updateQueue,t===null?(t={lastEffect:null,stores:null},De.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function ly(e,t,n,r){t.value=n,t.getSnapshot=r,uy(t)&&dy(e)}function cy(e,t,n){return n(function(){uy(t)&&dy(e)})}function uy(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Jt(e,n)}catch{return!0}}function dy(e){var t=In(e,1);t!==null&&Xt(t,e,1,-1)}function Xh(e){var t=sn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:gi,lastRenderedState:e},t.queue=e,e=e.dispatch=H1.bind(null,De,e),[t.memoizedState,e]}function yi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=De.updateQueue,t===null?(t={lastEffect:null,stores:null},De.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function fy(){return zt().memoizedState}function ba(e,t,n,r){var s=sn();De.flags|=e,s.memoizedState=yi(1|t,n,void 0,r===void 0?null:r)}function Nl(e,t,n,r){var s=zt();r=r===void 0?null:r;var o=void 0;if(Ue!==null){var i=Ue.memoizedState;if(o=i.destroy,r!==null&&rf(r,i.deps)){s.memoizedState=yi(t,n,o,r);return}}De.flags|=e,s.memoizedState=yi(1|t,n,o,r)}function Jh(e,t){return ba(8390656,8,e,t)}function af(e,t){return Nl(2048,8,e,t)}function hy(e,t){return Nl(4,2,e,t)}function py(e,t){return Nl(4,4,e,t)}function my(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function gy(e,t,n){return n=n!=null?n.concat([e]):null,Nl(4,4,my.bind(null,t,e),n)}function lf(){}function yy(e,t){var n=zt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&rf(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function vy(e,t){var n=zt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&rf(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function xy(e,t,n){return Yr&21?(Jt(n,t)||(n=Cg(),De.lanes|=n,Xr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,yt=!0),e.memoizedState=n)}function B1(e,t){var n=ke;ke=n!==0&&4>n?n:4,e(!0);var r=Cc.transition;Cc.transition={};try{e(!1),t()}finally{ke=n,Cc.transition=r}}function wy(){return zt().memoizedState}function V1(e,t,n){var r=hr(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Sy(e))ky(t,n);else if(n=ny(e,t,n,r),n!==null){var s=ut();Xt(n,e,r,s),by(n,t,r)}}function H1(e,t,n){var r=hr(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Sy(e))ky(t,s);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var i=t.lastRenderedState,a=o(i,n);if(s.hasEagerState=!0,s.eagerState=a,Jt(a,i)){var l=t.interleaved;l===null?(s.next=s,Xd(t)):(s.next=l.next,l.next=s),t.interleaved=s;return}}catch{}finally{}n=ny(e,t,s,r),n!==null&&(s=ut(),Xt(n,e,r,s),by(n,t,r))}}function Sy(e){var t=e.alternate;return e===De||t!==null&&t===De}function ky(e,t){Yo=Ga=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function by(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Fd(e,n)}}var Ya={readContext:Ft,useCallback:Je,useContext:Je,useEffect:Je,useImperativeHandle:Je,useInsertionEffect:Je,useLayoutEffect:Je,useMemo:Je,useReducer:Je,useRef:Je,useState:Je,useDebugValue:Je,useDeferredValue:Je,useTransition:Je,useMutableSource:Je,useSyncExternalStore:Je,useId:Je,unstable_isNewReconciler:!1},W1={readContext:Ft,useCallback:function(e,t){return sn().memoizedState=[e,t===void 0?null:t],e},useContext:Ft,useEffect:Jh,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ba(4194308,4,my.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ba(4194308,4,e,t)},useInsertionEffect:function(e,t){return ba(4,2,e,t)},useMemo:function(e,t){var n=sn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=sn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=V1.bind(null,De,e),[r.memoizedState,e]},useRef:function(e){var t=sn();return e={current:e},t.memoizedState=e},useState:Xh,useDebugValue:lf,useDeferredValue:function(e){return sn().memoizedState=e},useTransition:function(){var e=Xh(!1),t=e[0];return e=B1.bind(null,e[1]),sn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=De,s=sn();if(Ie){if(n===void 0)throw Error(I(407));n=n()}else{if(n=t(),Qe===null)throw Error(I(349));Yr&30||ay(r,t,n)}s.memoizedState=n;var o={value:n,getSnapshot:t};return s.queue=o,Jh(cy.bind(null,r,o,e),[e]),r.flags|=2048,yi(9,ly.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=sn(),t=Qe.identifierPrefix;if(Ie){var n=_n,r=En;n=(r&~(1<<32-Yt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=mi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=U1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Q1={readContext:Ft,useCallback:yy,useContext:Ft,useEffect:af,useImperativeHandle:gy,useInsertionEffect:hy,useLayoutEffect:py,useMemo:vy,useReducer:Ec,useRef:fy,useState:function(){return Ec(gi)},useDebugValue:lf,useDeferredValue:function(e){var t=zt();return xy(t,Ue.memoizedState,e)},useTransition:function(){var e=Ec(gi)[0],t=zt().memoizedState;return[e,t]},useMutableSource:oy,useSyncExternalStore:iy,useId:wy,unstable_isNewReconciler:!1},Z1={readContext:Ft,useCallback:yy,useContext:Ft,useEffect:af,useImperativeHandle:gy,useInsertionEffect:hy,useLayoutEffect:py,useMemo:vy,useReducer:_c,useRef:fy,useState:function(){return _c(gi)},useDebugValue:lf,useDeferredValue:function(e){var t=zt();return Ue===null?t.memoizedState=e:xy(t,Ue.memoizedState,e)},useTransition:function(){var e=_c(gi)[0],t=zt().memoizedState;return[e,t]},useMutableSource:oy,useSyncExternalStore:iy,useId:wy,unstable_isNewReconciler:!1};function Ht(e,t){if(e&&e.defaultProps){t=Oe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Cu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Oe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Tl={isMounted:function(e){return(e=e._reactInternals)?fs(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ut(),s=hr(e),o=Nn(r,s);o.payload=t,n!=null&&(o.callback=n),t=dr(e,o,s),t!==null&&(Xt(t,e,s,r),Sa(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ut(),s=hr(e),o=Nn(r,s);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=dr(e,o,s),t!==null&&(Xt(t,e,s,r),Sa(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ut(),r=hr(e),s=Nn(n,r);s.tag=2,t!=null&&(s.callback=t),t=dr(e,s,r),t!==null&&(Xt(t,e,r,n),Sa(t,e,r))}};function ep(e,t,n,r,s,o,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,i):t.prototype&&t.prototype.isPureReactComponent?!ci(n,r)||!ci(s,o):!0}function Cy(e,t,n){var r=!1,s=xr,o=t.contextType;return typeof o=="object"&&o!==null?o=Ft(o):(s=xt(t)?qr:st.current,r=t.contextTypes,o=(r=r!=null)?eo(e,s):xr),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Tl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=o),t}function tp(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Tl.enqueueReplaceState(t,t.state,null)}function Eu(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Jd(e);var o=t.contextType;typeof o=="object"&&o!==null?s.context=Ft(o):(o=xt(t)?qr:st.current,s.context=eo(e,o)),s.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(Cu(e,t,o,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Tl.enqueueReplaceState(s,s.state,null),Ka(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function so(e,t){try{var n="",r=t;do n+=Sw(r),r=r.return;while(r);var s=n}catch(o){s=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:s,digest:null}}function Nc(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function _u(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var K1=typeof WeakMap=="function"?WeakMap:Map;function Ey(e,t,n){n=Nn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ja||(Ja=!0,Mu=r),_u(e,t)},n}function _y(e,t,n){n=Nn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){_u(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){_u(e,t),typeof r!="function"&&(fr===null?fr=new Set([this]):fr.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function np(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new K1;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=lS.bind(null,e,t,n),t.then(e,e))}function rp(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function sp(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Nn(-1,1),t.tag=2,dr(n,t,1))),n.lanes|=1),e)}var q1=Ln.ReactCurrentOwner,yt=!1;function lt(e,t,n,r){t.child=e===null?ty(t,null,n,r):no(t,e.child,n,r)}function op(e,t,n,r,s){n=n.render;var o=t.ref;return As(t,s),r=sf(e,t,n,r,o,s),n=of(),e!==null&&!yt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Pn(e,t,s)):(Ie&&n&&Qd(t),t.flags|=1,lt(e,t,r,s),t.child)}function ip(e,t,n,r,s){if(e===null){var o=n.type;return typeof o=="function"&&!gf(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Ny(e,t,o,r,s)):(e=Na(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&s)){var i=o.memoizedProps;if(n=n.compare,n=n!==null?n:ci,n(i,r)&&e.ref===t.ref)return Pn(e,t,s)}return t.flags|=1,e=pr(o,r),e.ref=t.ref,e.return=t,t.child=e}function Ny(e,t,n,r,s){if(e!==null){var o=e.memoizedProps;if(ci(o,r)&&e.ref===t.ref)if(yt=!1,t.pendingProps=r=o,(e.lanes&s)!==0)e.flags&131072&&(yt=!0);else return t.lanes=e.lanes,Pn(e,t,s)}return Nu(e,t,n,r,s)}function Ty(e,t,n){var r=t.pendingProps,s=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ee(js,St),St|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ee(js,St),St|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,Ee(js,St),St|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ee(js,St),St|=r;return lt(e,t,s,n),t.child}function jy(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Nu(e,t,n,r,s){var o=xt(n)?qr:st.current;return o=eo(t,o),As(t,s),n=sf(e,t,n,r,o,s),r=of(),e!==null&&!yt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,Pn(e,t,s)):(Ie&&r&&Qd(t),t.flags|=1,lt(e,t,n,s),t.child)}function ap(e,t,n,r,s){if(xt(n)){var o=!0;Va(t)}else o=!1;if(As(t,s),t.stateNode===null)Ca(e,t),Cy(t,n,r),Eu(t,n,r,s),r=!0;else if(e===null){var i=t.stateNode,a=t.memoizedProps;i.props=a;var l=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=Ft(c):(c=xt(n)?qr:st.current,c=eo(t,c));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==r||l!==c)&&tp(t,i,r,c),Hn=!1;var y=t.memoizedState;i.state=y,Ka(t,r,i,s),l=t.memoizedState,a!==r||y!==l||vt.current||Hn?(typeof d=="function"&&(Cu(t,n,d,r),l=t.memoizedState),(a=Hn||ep(t,n,a,r,y,l,c))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=c,r=a):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,ry(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Ht(t.type,a),i.props=c,f=t.pendingProps,y=i.context,l=n.contextType,typeof l=="object"&&l!==null?l=Ft(l):(l=xt(n)?qr:st.current,l=eo(t,l));var x=n.getDerivedStateFromProps;(d=typeof x=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(a!==f||y!==l)&&tp(t,i,r,l),Hn=!1,y=t.memoizedState,i.state=y,Ka(t,r,i,s);var k=t.memoizedState;a!==f||y!==k||vt.current||Hn?(typeof x=="function"&&(Cu(t,n,x,r),k=t.memoizedState),(c=Hn||ep(t,n,c,r,y,k,l)||!1)?(d||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,k,l),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,k,l)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=k),i.props=r,i.state=k,i.context=l,r=c):(typeof i.componentDidUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&y===e.memoizedState||(t.flags|=1024),r=!1)}return Tu(e,t,n,r,o,s)}function Tu(e,t,n,r,s,o){jy(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return s&&Qh(t,n,!1),Pn(e,t,o);r=t.stateNode,q1.current=t;var a=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=no(t,e.child,null,o),t.child=no(t,null,a,o)):lt(e,t,a,o),t.memoizedState=r.state,s&&Qh(t,n,!0),t.child}function Ry(e){var t=e.stateNode;t.pendingContext?Wh(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Wh(e,t.context,!1),ef(e,t.containerInfo)}function lp(e,t,n,r,s){return to(),Kd(s),t.flags|=256,lt(e,t,n,r),t.child}var ju={dehydrated:null,treeContext:null,retryLane:0};function Ru(e){return{baseLanes:e,cachePool:null,transitions:null}}function Iy(e,t,n){var r=t.pendingProps,s=Pe.current,o=!1,i=(t.flags&128)!==0,a;if((a=i)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),Ee(Pe,s&1),e===null)return ku(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,o?(r=t.mode,o=t.child,i={mode:"hidden",children:i},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=i):o=Il(i,r,0,null),e=Kr(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Ru(n),t.memoizedState=ju,e):cf(t,i));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return G1(e,t,i,r,a,s,n);if(o){o=r.fallback,i=t.mode,s=e.child,a=s.sibling;var l={mode:"hidden",children:r.children};return!(i&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=pr(s,l),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?o=pr(a,o):(o=Kr(o,i,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,i=e.child.memoizedState,i=i===null?Ru(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},o.memoizedState=i,o.childLanes=e.childLanes&~n,t.memoizedState=ju,r}return o=e.child,e=o.sibling,r=pr(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function cf(e,t){return t=Il({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ra(e,t,n,r){return r!==null&&Kd(r),no(t,e.child,null,n),e=cf(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function G1(e,t,n,r,s,o,i){if(n)return t.flags&256?(t.flags&=-257,r=Nc(Error(I(422))),ra(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,s=t.mode,r=Il({mode:"visible",children:r.children},s,0,null),o=Kr(o,s,i,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&no(t,e.child,null,i),t.child.memoizedState=Ru(i),t.memoizedState=ju,o);if(!(t.mode&1))return ra(e,t,i,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(I(419)),r=Nc(o,r,void 0),ra(e,t,i,r)}if(a=(i&e.childLanes)!==0,yt||a){if(r=Qe,r!==null){switch(i&-i){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|i)?0:s,s!==0&&s!==o.retryLane&&(o.retryLane=s,In(e,s),Xt(r,e,s,-1))}return mf(),r=Nc(Error(I(421))),ra(e,t,i,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=cS.bind(null,e),s._reactRetry=t,null):(e=o.treeContext,bt=ur(s.nextSibling),Ct=t,Ie=!0,Kt=null,e!==null&&(Dt[Ot++]=En,Dt[Ot++]=_n,Dt[Ot++]=Gr,En=e.id,_n=e.overflow,Gr=t),t=cf(t,r.children),t.flags|=4096,t)}function cp(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),bu(e.return,t,n)}function Tc(e,t,n,r,s){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=s)}function Py(e,t,n){var r=t.pendingProps,s=r.revealOrder,o=r.tail;if(lt(e,t,r.children,n),r=Pe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&cp(e,n,t);else if(e.tag===19)cp(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ee(Pe,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&qa(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),Tc(t,!1,s,n,o);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&qa(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}Tc(t,!0,n,null,o);break;case"together":Tc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ca(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Pn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Xr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(I(153));if(t.child!==null){for(e=t.child,n=pr(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=pr(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Y1(e,t,n){switch(t.tag){case 3:Ry(t),to();break;case 5:sy(t);break;case 1:xt(t.type)&&Va(t);break;case 4:ef(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;Ee(Qa,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(Ee(Pe,Pe.current&1),t.flags|=128,null):n&t.child.childLanes?Iy(e,t,n):(Ee(Pe,Pe.current&1),e=Pn(e,t,n),e!==null?e.sibling:null);Ee(Pe,Pe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Py(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),Ee(Pe,Pe.current),r)break;return null;case 22:case 23:return t.lanes=0,Ty(e,t,n)}return Pn(e,t,n)}var Dy,Iu,Oy,Ay;Dy=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Iu=function(){};Oy=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,Lr(hn.current);var o=null;switch(n){case"input":s=Jc(e,s),r=Jc(e,r),o=[];break;case"select":s=Oe({},s,{value:void 0}),r=Oe({},r,{value:void 0}),o=[];break;case"textarea":s=nu(e,s),r=nu(e,r),o=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ua)}su(n,r);var i;n=null;for(c in s)if(!r.hasOwnProperty(c)&&s.hasOwnProperty(c)&&s[c]!=null)if(c==="style"){var a=s[c];for(i in a)a.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(ni.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var l=r[c];if(a=s!=null?s[c]:void 0,r.hasOwnProperty(c)&&l!==a&&(l!=null||a!=null))if(c==="style")if(a){for(i in a)!a.hasOwnProperty(i)||l&&l.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in l)l.hasOwnProperty(i)&&a[i]!==l[i]&&(n||(n={}),n[i]=l[i])}else n||(o||(o=[]),o.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(o=o||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(o=o||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(ni.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&Te("scroll",e),o||a===l||(o=[])):(o=o||[]).push(c,l))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};Ay=function(e,t,n,r){n!==r&&(t.flags|=4)};function Oo(e,t){if(!Ie)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function et(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function X1(e,t,n){var r=t.pendingProps;switch(Zd(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return et(t),null;case 1:return xt(t.type)&&Ba(),et(t),null;case 3:return r=t.stateNode,ro(),je(vt),je(st),nf(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(ta(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Kt!==null&&(zu(Kt),Kt=null))),Iu(e,t),et(t),null;case 5:tf(t);var s=Lr(pi.current);if(n=t.type,e!==null&&t.stateNode!=null)Oy(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(I(166));return et(t),null}if(e=Lr(hn.current),ta(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[cn]=t,r[fi]=o,e=(t.mode&1)!==0,n){case"dialog":Te("cancel",r),Te("close",r);break;case"iframe":case"object":case"embed":Te("load",r);break;case"video":case"audio":for(s=0;s<Uo.length;s++)Te(Uo[s],r);break;case"source":Te("error",r);break;case"img":case"image":case"link":Te("error",r),Te("load",r);break;case"details":Te("toggle",r);break;case"input":vh(r,o),Te("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Te("invalid",r);break;case"textarea":wh(r,o),Te("invalid",r)}su(n,o),s=null;for(var i in o)if(o.hasOwnProperty(i)){var a=o[i];i==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&ea(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&ea(r.textContent,a,e),s=["children",""+a]):ni.hasOwnProperty(i)&&a!=null&&i==="onScroll"&&Te("scroll",r)}switch(n){case"input":Qi(r),xh(r,o,!0);break;case"textarea":Qi(r),Sh(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ua)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=cg(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[cn]=t,e[fi]=r,Dy(e,t,!1,!1),t.stateNode=e;e:{switch(i=ou(n,r),n){case"dialog":Te("cancel",e),Te("close",e),s=r;break;case"iframe":case"object":case"embed":Te("load",e),s=r;break;case"video":case"audio":for(s=0;s<Uo.length;s++)Te(Uo[s],e);s=r;break;case"source":Te("error",e),s=r;break;case"img":case"image":case"link":Te("error",e),Te("load",e),s=r;break;case"details":Te("toggle",e),s=r;break;case"input":vh(e,r),s=Jc(e,r),Te("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=Oe({},r,{value:void 0}),Te("invalid",e);break;case"textarea":wh(e,r),s=nu(e,r),Te("invalid",e);break;default:s=r}su(n,s),a=s;for(o in a)if(a.hasOwnProperty(o)){var l=a[o];o==="style"?fg(e,l):o==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&ug(e,l)):o==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ri(e,l):typeof l=="number"&&ri(e,""+l):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(ni.hasOwnProperty(o)?l!=null&&o==="onScroll"&&Te("scroll",e):l!=null&&Pd(e,o,l,i))}switch(n){case"input":Qi(e),xh(e,r,!1);break;case"textarea":Qi(e),Sh(e);break;case"option":r.value!=null&&e.setAttribute("value",""+vr(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Is(e,!!r.multiple,o,!1):r.defaultValue!=null&&Is(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Ua)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return et(t),null;case 6:if(e&&t.stateNode!=null)Ay(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(I(166));if(n=Lr(pi.current),Lr(hn.current),ta(t)){if(r=t.stateNode,n=t.memoizedProps,r[cn]=t,(o=r.nodeValue!==n)&&(e=Ct,e!==null))switch(e.tag){case 3:ea(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ea(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[cn]=t,t.stateNode=r}return et(t),null;case 13:if(je(Pe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Ie&&bt!==null&&t.mode&1&&!(t.flags&128))Jg(),to(),t.flags|=98560,o=!1;else if(o=ta(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(I(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(I(317));o[cn]=t}else to(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;et(t),o=!1}else Kt!==null&&(zu(Kt),Kt=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Pe.current&1?Ve===0&&(Ve=3):mf())),t.updateQueue!==null&&(t.flags|=4),et(t),null);case 4:return ro(),Iu(e,t),e===null&&ui(t.stateNode.containerInfo),et(t),null;case 10:return Yd(t.type._context),et(t),null;case 17:return xt(t.type)&&Ba(),et(t),null;case 19:if(je(Pe),o=t.memoizedState,o===null)return et(t),null;if(r=(t.flags&128)!==0,i=o.rendering,i===null)if(r)Oo(o,!1);else{if(Ve!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=qa(e),i!==null){for(t.flags|=128,Oo(o,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,i=o.alternate,i===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=i.childLanes,o.lanes=i.lanes,o.child=i.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=i.memoizedProps,o.memoizedState=i.memoizedState,o.updateQueue=i.updateQueue,o.type=i.type,e=i.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ee(Pe,Pe.current&1|2),t.child}e=e.sibling}o.tail!==null&&Fe()>oo&&(t.flags|=128,r=!0,Oo(o,!1),t.lanes=4194304)}else{if(!r)if(e=qa(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Oo(o,!0),o.tail===null&&o.tailMode==="hidden"&&!i.alternate&&!Ie)return et(t),null}else 2*Fe()-o.renderingStartTime>oo&&n!==1073741824&&(t.flags|=128,r=!0,Oo(o,!1),t.lanes=4194304);o.isBackwards?(i.sibling=t.child,t.child=i):(n=o.last,n!==null?n.sibling=i:t.child=i,o.last=i)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Fe(),t.sibling=null,n=Pe.current,Ee(Pe,r?n&1|2:n&1),t):(et(t),null);case 22:case 23:return pf(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?St&1073741824&&(et(t),t.subtreeFlags&6&&(t.flags|=8192)):et(t),null;case 24:return null;case 25:return null}throw Error(I(156,t.tag))}function J1(e,t){switch(Zd(t),t.tag){case 1:return xt(t.type)&&Ba(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ro(),je(vt),je(st),nf(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return tf(t),null;case 13:if(je(Pe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(I(340));to()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return je(Pe),null;case 4:return ro(),null;case 10:return Yd(t.type._context),null;case 22:case 23:return pf(),null;case 24:return null;default:return null}}var sa=!1,rt=!1,eS=typeof WeakSet=="function"?WeakSet:Set,U=null;function Ts(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Le(e,t,r)}else n.current=null}function Pu(e,t,n){try{n()}catch(r){Le(e,t,r)}}var up=!1;function tS(e,t){if(mu=Fa,e=$g(),Wd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var i=0,a=-1,l=-1,c=0,d=0,f=e,y=null;t:for(;;){for(var x;f!==n||s!==0&&f.nodeType!==3||(a=i+s),f!==o||r!==0&&f.nodeType!==3||(l=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(x=f.firstChild)!==null;)y=f,f=x;for(;;){if(f===e)break t;if(y===n&&++c===s&&(a=i),y===o&&++d===r&&(l=i),(x=f.nextSibling)!==null)break;f=y,y=f.parentNode}f=x}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(gu={focusedElem:e,selectionRange:n},Fa=!1,U=t;U!==null;)if(t=U,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,U=e;else for(;U!==null;){t=U;try{var k=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var m=k.memoizedProps,w=k.memoizedState,p=t.stateNode,h=p.getSnapshotBeforeUpdate(t.elementType===t.type?m:Ht(t.type,m),w);p.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(I(163))}}catch(S){Le(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,U=e;break}U=t.return}return k=up,up=!1,k}function Xo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var o=s.destroy;s.destroy=void 0,o!==void 0&&Pu(t,n,o)}s=s.next}while(s!==r)}}function jl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Du(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function My(e){var t=e.alternate;t!==null&&(e.alternate=null,My(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[cn],delete t[fi],delete t[xu],delete t[L1],delete t[F1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Ly(e){return e.tag===5||e.tag===3||e.tag===4}function dp(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Ly(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ou(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ua));else if(r!==4&&(e=e.child,e!==null))for(Ou(e,t,n),e=e.sibling;e!==null;)Ou(e,t,n),e=e.sibling}function Au(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Au(e,t,n),e=e.sibling;e!==null;)Au(e,t,n),e=e.sibling}var Ge=null,Zt=!1;function Fn(e,t,n){for(n=n.child;n!==null;)Fy(e,t,n),n=n.sibling}function Fy(e,t,n){if(fn&&typeof fn.onCommitFiberUnmount=="function")try{fn.onCommitFiberUnmount(Sl,n)}catch{}switch(n.tag){case 5:rt||Ts(n,t);case 6:var r=Ge,s=Zt;Ge=null,Fn(e,t,n),Ge=r,Zt=s,Ge!==null&&(Zt?(e=Ge,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ge.removeChild(n.stateNode));break;case 18:Ge!==null&&(Zt?(e=Ge,n=n.stateNode,e.nodeType===8?Sc(e.parentNode,n):e.nodeType===1&&Sc(e,n),ai(e)):Sc(Ge,n.stateNode));break;case 4:r=Ge,s=Zt,Ge=n.stateNode.containerInfo,Zt=!0,Fn(e,t,n),Ge=r,Zt=s;break;case 0:case 11:case 14:case 15:if(!rt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var o=s,i=o.destroy;o=o.tag,i!==void 0&&(o&2||o&4)&&Pu(n,t,i),s=s.next}while(s!==r)}Fn(e,t,n);break;case 1:if(!rt&&(Ts(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Le(n,t,a)}Fn(e,t,n);break;case 21:Fn(e,t,n);break;case 22:n.mode&1?(rt=(r=rt)||n.memoizedState!==null,Fn(e,t,n),rt=r):Fn(e,t,n);break;default:Fn(e,t,n)}}function fp(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new eS),t.forEach(function(r){var s=uS.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Bt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var o=e,i=t,a=i;e:for(;a!==null;){switch(a.tag){case 5:Ge=a.stateNode,Zt=!1;break e;case 3:Ge=a.stateNode.containerInfo,Zt=!0;break e;case 4:Ge=a.stateNode.containerInfo,Zt=!0;break e}a=a.return}if(Ge===null)throw Error(I(160));Fy(o,i,s),Ge=null,Zt=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(c){Le(s,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)zy(t,e),t=t.sibling}function zy(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Bt(t,e),rn(e),r&4){try{Xo(3,e,e.return),jl(3,e)}catch(m){Le(e,e.return,m)}try{Xo(5,e,e.return)}catch(m){Le(e,e.return,m)}}break;case 1:Bt(t,e),rn(e),r&512&&n!==null&&Ts(n,n.return);break;case 5:if(Bt(t,e),rn(e),r&512&&n!==null&&Ts(n,n.return),e.flags&32){var s=e.stateNode;try{ri(s,"")}catch(m){Le(e,e.return,m)}}if(r&4&&(s=e.stateNode,s!=null)){var o=e.memoizedProps,i=n!==null?n.memoizedProps:o,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&ag(s,o),ou(a,i);var c=ou(a,o);for(i=0;i<l.length;i+=2){var d=l[i],f=l[i+1];d==="style"?fg(s,f):d==="dangerouslySetInnerHTML"?ug(s,f):d==="children"?ri(s,f):Pd(s,d,f,c)}switch(a){case"input":eu(s,o);break;case"textarea":lg(s,o);break;case"select":var y=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!o.multiple;var x=o.value;x!=null?Is(s,!!o.multiple,x,!1):y!==!!o.multiple&&(o.defaultValue!=null?Is(s,!!o.multiple,o.defaultValue,!0):Is(s,!!o.multiple,o.multiple?[]:"",!1))}s[fi]=o}catch(m){Le(e,e.return,m)}}break;case 6:if(Bt(t,e),rn(e),r&4){if(e.stateNode===null)throw Error(I(162));s=e.stateNode,o=e.memoizedProps;try{s.nodeValue=o}catch(m){Le(e,e.return,m)}}break;case 3:if(Bt(t,e),rn(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ai(t.containerInfo)}catch(m){Le(e,e.return,m)}break;case 4:Bt(t,e),rn(e);break;case 13:Bt(t,e),rn(e),s=e.child,s.flags&8192&&(o=s.memoizedState!==null,s.stateNode.isHidden=o,!o||s.alternate!==null&&s.alternate.memoizedState!==null||(ff=Fe())),r&4&&fp(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(rt=(c=rt)||d,Bt(t,e),rt=c):Bt(t,e),rn(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!d&&e.mode&1)for(U=e,d=e.child;d!==null;){for(f=U=d;U!==null;){switch(y=U,x=y.child,y.tag){case 0:case 11:case 14:case 15:Xo(4,y,y.return);break;case 1:Ts(y,y.return);var k=y.stateNode;if(typeof k.componentWillUnmount=="function"){r=y,n=y.return;try{t=r,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(m){Le(r,n,m)}}break;case 5:Ts(y,y.return);break;case 22:if(y.memoizedState!==null){pp(f);continue}}x!==null?(x.return=y,U=x):pp(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{s=f.stateNode,c?(o=s.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=f.stateNode,l=f.memoizedProps.style,i=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=dg("display",i))}catch(m){Le(e,e.return,m)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(m){Le(e,e.return,m)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Bt(t,e),rn(e),r&4&&fp(e);break;case 21:break;default:Bt(t,e),rn(e)}}function rn(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Ly(n)){var r=n;break e}n=n.return}throw Error(I(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(ri(s,""),r.flags&=-33);var o=dp(e);Au(e,o,s);break;case 3:case 4:var i=r.stateNode.containerInfo,a=dp(e);Ou(e,a,i);break;default:throw Error(I(161))}}catch(l){Le(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function nS(e,t,n){U=e,$y(e)}function $y(e,t,n){for(var r=(e.mode&1)!==0;U!==null;){var s=U,o=s.child;if(s.tag===22&&r){var i=s.memoizedState!==null||sa;if(!i){var a=s.alternate,l=a!==null&&a.memoizedState!==null||rt;a=sa;var c=rt;if(sa=i,(rt=l)&&!c)for(U=s;U!==null;)i=U,l=i.child,i.tag===22&&i.memoizedState!==null?mp(s):l!==null?(l.return=i,U=l):mp(s);for(;o!==null;)U=o,$y(o),o=o.sibling;U=s,sa=a,rt=c}hp(e)}else s.subtreeFlags&8772&&o!==null?(o.return=s,U=o):hp(e)}}function hp(e){for(;U!==null;){var t=U;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:rt||jl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!rt)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Ht(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Yh(t,o,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Yh(t,i,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var d=c.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&ai(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(I(163))}rt||t.flags&512&&Du(t)}catch(y){Le(t,t.return,y)}}if(t===e){U=null;break}if(n=t.sibling,n!==null){n.return=t.return,U=n;break}U=t.return}}function pp(e){for(;U!==null;){var t=U;if(t===e){U=null;break}var n=t.sibling;if(n!==null){n.return=t.return,U=n;break}U=t.return}}function mp(e){for(;U!==null;){var t=U;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{jl(4,t)}catch(l){Le(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(l){Le(t,s,l)}}var o=t.return;try{Du(t)}catch(l){Le(t,o,l)}break;case 5:var i=t.return;try{Du(t)}catch(l){Le(t,i,l)}}}catch(l){Le(t,t.return,l)}if(t===e){U=null;break}var a=t.sibling;if(a!==null){a.return=t.return,U=a;break}U=t.return}}var rS=Math.ceil,Xa=Ln.ReactCurrentDispatcher,uf=Ln.ReactCurrentOwner,Lt=Ln.ReactCurrentBatchConfig,ge=0,Qe=null,$e=null,Ye=0,St=0,js=_r(0),Ve=0,vi=null,Xr=0,Rl=0,df=0,Jo=null,gt=null,ff=0,oo=1/0,Sn=null,Ja=!1,Mu=null,fr=null,oa=!1,or=null,el=0,ei=0,Lu=null,Ea=-1,_a=0;function ut(){return ge&6?Fe():Ea!==-1?Ea:Ea=Fe()}function hr(e){return e.mode&1?ge&2&&Ye!==0?Ye&-Ye:$1.transition!==null?(_a===0&&(_a=Cg()),_a):(e=ke,e!==0||(e=window.event,e=e===void 0?16:Ig(e.type)),e):1}function Xt(e,t,n,r){if(50<ei)throw ei=0,Lu=null,Error(I(185));Ii(e,n,r),(!(ge&2)||e!==Qe)&&(e===Qe&&(!(ge&2)&&(Rl|=n),Ve===4&&Zn(e,Ye)),wt(e,r),n===1&&ge===0&&!(t.mode&1)&&(oo=Fe()+500,_l&&Nr()))}function wt(e,t){var n=e.callbackNode;$w(e,t);var r=La(e,e===Qe?Ye:0);if(r===0)n!==null&&Ch(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ch(n),t===1)e.tag===0?z1(gp.bind(null,e)):Gg(gp.bind(null,e)),A1(function(){!(ge&6)&&Nr()}),n=null;else{switch(Eg(r)){case 1:n=Ld;break;case 4:n=kg;break;case 16:n=Ma;break;case 536870912:n=bg;break;default:n=Ma}n=Ky(n,Uy.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Uy(e,t){if(Ea=-1,_a=0,ge&6)throw Error(I(327));var n=e.callbackNode;if(Ms()&&e.callbackNode!==n)return null;var r=La(e,e===Qe?Ye:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=tl(e,r);else{t=r;var s=ge;ge|=2;var o=Vy();(Qe!==e||Ye!==t)&&(Sn=null,oo=Fe()+500,Zr(e,t));do try{iS();break}catch(a){By(e,a)}while(!0);Gd(),Xa.current=o,ge=s,$e!==null?t=0:(Qe=null,Ye=0,t=Ve)}if(t!==0){if(t===2&&(s=uu(e),s!==0&&(r=s,t=Fu(e,s))),t===1)throw n=vi,Zr(e,0),Zn(e,r),wt(e,Fe()),n;if(t===6)Zn(e,r);else{if(s=e.current.alternate,!(r&30)&&!sS(s)&&(t=tl(e,r),t===2&&(o=uu(e),o!==0&&(r=o,t=Fu(e,o))),t===1))throw n=vi,Zr(e,0),Zn(e,r),wt(e,Fe()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(I(345));case 2:Or(e,gt,Sn);break;case 3:if(Zn(e,r),(r&130023424)===r&&(t=ff+500-Fe(),10<t)){if(La(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){ut(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=vu(Or.bind(null,e,gt,Sn),t);break}Or(e,gt,Sn);break;case 4:if(Zn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var i=31-Yt(r);o=1<<i,i=t[i],i>s&&(s=i),r&=~o}if(r=s,r=Fe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*rS(r/1960))-r,10<r){e.timeoutHandle=vu(Or.bind(null,e,gt,Sn),r);break}Or(e,gt,Sn);break;case 5:Or(e,gt,Sn);break;default:throw Error(I(329))}}}return wt(e,Fe()),e.callbackNode===n?Uy.bind(null,e):null}function Fu(e,t){var n=Jo;return e.current.memoizedState.isDehydrated&&(Zr(e,t).flags|=256),e=tl(e,t),e!==2&&(t=gt,gt=n,t!==null&&zu(t)),e}function zu(e){gt===null?gt=e:gt.push.apply(gt,e)}function sS(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],o=s.getSnapshot;s=s.value;try{if(!Jt(o(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Zn(e,t){for(t&=~df,t&=~Rl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Yt(t),r=1<<n;e[n]=-1,t&=~r}}function gp(e){if(ge&6)throw Error(I(327));Ms();var t=La(e,0);if(!(t&1))return wt(e,Fe()),null;var n=tl(e,t);if(e.tag!==0&&n===2){var r=uu(e);r!==0&&(t=r,n=Fu(e,r))}if(n===1)throw n=vi,Zr(e,0),Zn(e,t),wt(e,Fe()),n;if(n===6)throw Error(I(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Or(e,gt,Sn),wt(e,Fe()),null}function hf(e,t){var n=ge;ge|=1;try{return e(t)}finally{ge=n,ge===0&&(oo=Fe()+500,_l&&Nr())}}function Jr(e){or!==null&&or.tag===0&&!(ge&6)&&Ms();var t=ge;ge|=1;var n=Lt.transition,r=ke;try{if(Lt.transition=null,ke=1,e)return e()}finally{ke=r,Lt.transition=n,ge=t,!(ge&6)&&Nr()}}function pf(){St=js.current,je(js)}function Zr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,O1(n)),$e!==null)for(n=$e.return;n!==null;){var r=n;switch(Zd(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ba();break;case 3:ro(),je(vt),je(st),nf();break;case 5:tf(r);break;case 4:ro();break;case 13:je(Pe);break;case 19:je(Pe);break;case 10:Yd(r.type._context);break;case 22:case 23:pf()}n=n.return}if(Qe=e,$e=e=pr(e.current,null),Ye=St=t,Ve=0,vi=null,df=Rl=Xr=0,gt=Jo=null,Mr!==null){for(t=0;t<Mr.length;t++)if(n=Mr[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,o=n.pending;if(o!==null){var i=o.next;o.next=s,r.next=i}n.pending=r}Mr=null}return e}function By(e,t){do{var n=$e;try{if(Gd(),ka.current=Ya,Ga){for(var r=De.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Ga=!1}if(Yr=0,He=Ue=De=null,Yo=!1,mi=0,uf.current=null,n===null||n.return===null){Ve=1,vi=t,$e=null;break}e:{var o=e,i=n.return,a=n,l=t;if(t=Ye,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,d=a,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var y=d.alternate;y?(d.updateQueue=y.updateQueue,d.memoizedState=y.memoizedState,d.lanes=y.lanes):(d.updateQueue=null,d.memoizedState=null)}var x=rp(i);if(x!==null){x.flags&=-257,sp(x,i,a,o,t),x.mode&1&&np(o,c,t),t=x,l=c;var k=t.updateQueue;if(k===null){var m=new Set;m.add(l),t.updateQueue=m}else k.add(l);break e}else{if(!(t&1)){np(o,c,t),mf();break e}l=Error(I(426))}}else if(Ie&&a.mode&1){var w=rp(i);if(w!==null){!(w.flags&65536)&&(w.flags|=256),sp(w,i,a,o,t),Kd(so(l,a));break e}}o=l=so(l,a),Ve!==4&&(Ve=2),Jo===null?Jo=[o]:Jo.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var p=Ey(o,l,t);Gh(o,p);break e;case 1:a=l;var h=o.type,v=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(fr===null||!fr.has(v)))){o.flags|=65536,t&=-t,o.lanes|=t;var S=_y(o,a,t);Gh(o,S);break e}}o=o.return}while(o!==null)}Wy(n)}catch(b){t=b,$e===n&&n!==null&&($e=n=n.return);continue}break}while(!0)}function Vy(){var e=Xa.current;return Xa.current=Ya,e===null?Ya:e}function mf(){(Ve===0||Ve===3||Ve===2)&&(Ve=4),Qe===null||!(Xr&268435455)&&!(Rl&268435455)||Zn(Qe,Ye)}function tl(e,t){var n=ge;ge|=2;var r=Vy();(Qe!==e||Ye!==t)&&(Sn=null,Zr(e,t));do try{oS();break}catch(s){By(e,s)}while(!0);if(Gd(),ge=n,Xa.current=r,$e!==null)throw Error(I(261));return Qe=null,Ye=0,Ve}function oS(){for(;$e!==null;)Hy($e)}function iS(){for(;$e!==null&&!Iw();)Hy($e)}function Hy(e){var t=Zy(e.alternate,e,St);e.memoizedProps=e.pendingProps,t===null?Wy(e):$e=t,uf.current=null}function Wy(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=J1(n,t),n!==null){n.flags&=32767,$e=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ve=6,$e=null;return}}else if(n=X1(n,t,St),n!==null){$e=n;return}if(t=t.sibling,t!==null){$e=t;return}$e=t=e}while(t!==null);Ve===0&&(Ve=5)}function Or(e,t,n){var r=ke,s=Lt.transition;try{Lt.transition=null,ke=1,aS(e,t,n,r)}finally{Lt.transition=s,ke=r}return null}function aS(e,t,n,r){do Ms();while(or!==null);if(ge&6)throw Error(I(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(I(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Uw(e,o),e===Qe&&($e=Qe=null,Ye=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||oa||(oa=!0,Ky(Ma,function(){return Ms(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Lt.transition,Lt.transition=null;var i=ke;ke=1;var a=ge;ge|=4,uf.current=null,tS(e,n),zy(n,e),N1(gu),Fa=!!mu,gu=mu=null,e.current=n,nS(n),Pw(),ge=a,ke=i,Lt.transition=o}else e.current=n;if(oa&&(oa=!1,or=e,el=s),o=e.pendingLanes,o===0&&(fr=null),Aw(n.stateNode),wt(e,Fe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Ja)throw Ja=!1,e=Mu,Mu=null,e;return el&1&&e.tag!==0&&Ms(),o=e.pendingLanes,o&1?e===Lu?ei++:(ei=0,Lu=e):ei=0,Nr(),null}function Ms(){if(or!==null){var e=Eg(el),t=Lt.transition,n=ke;try{if(Lt.transition=null,ke=16>e?16:e,or===null)var r=!1;else{if(e=or,or=null,el=0,ge&6)throw Error(I(331));var s=ge;for(ge|=4,U=e.current;U!==null;){var o=U,i=o.child;if(U.flags&16){var a=o.deletions;if(a!==null){for(var l=0;l<a.length;l++){var c=a[l];for(U=c;U!==null;){var d=U;switch(d.tag){case 0:case 11:case 15:Xo(8,d,o)}var f=d.child;if(f!==null)f.return=d,U=f;else for(;U!==null;){d=U;var y=d.sibling,x=d.return;if(My(d),d===c){U=null;break}if(y!==null){y.return=x,U=y;break}U=x}}}var k=o.alternate;if(k!==null){var m=k.child;if(m!==null){k.child=null;do{var w=m.sibling;m.sibling=null,m=w}while(m!==null)}}U=o}}if(o.subtreeFlags&2064&&i!==null)i.return=o,U=i;else e:for(;U!==null;){if(o=U,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Xo(9,o,o.return)}var p=o.sibling;if(p!==null){p.return=o.return,U=p;break e}U=o.return}}var h=e.current;for(U=h;U!==null;){i=U;var v=i.child;if(i.subtreeFlags&2064&&v!==null)v.return=i,U=v;else e:for(i=h;U!==null;){if(a=U,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:jl(9,a)}}catch(b){Le(a,a.return,b)}if(a===i){U=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,U=S;break e}U=a.return}}if(ge=s,Nr(),fn&&typeof fn.onPostCommitFiberRoot=="function")try{fn.onPostCommitFiberRoot(Sl,e)}catch{}r=!0}return r}finally{ke=n,Lt.transition=t}}return!1}function yp(e,t,n){t=so(n,t),t=Ey(e,t,1),e=dr(e,t,1),t=ut(),e!==null&&(Ii(e,1,t),wt(e,t))}function Le(e,t,n){if(e.tag===3)yp(e,e,n);else for(;t!==null;){if(t.tag===3){yp(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(fr===null||!fr.has(r))){e=so(n,e),e=_y(t,e,1),t=dr(t,e,1),e=ut(),t!==null&&(Ii(t,1,e),wt(t,e));break}}t=t.return}}function lS(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ut(),e.pingedLanes|=e.suspendedLanes&n,Qe===e&&(Ye&n)===n&&(Ve===4||Ve===3&&(Ye&130023424)===Ye&&500>Fe()-ff?Zr(e,0):df|=n),wt(e,t)}function Qy(e,t){t===0&&(e.mode&1?(t=qi,qi<<=1,!(qi&130023424)&&(qi=4194304)):t=1);var n=ut();e=In(e,t),e!==null&&(Ii(e,t,n),wt(e,n))}function cS(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Qy(e,n)}function uS(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(I(314))}r!==null&&r.delete(t),Qy(e,n)}var Zy;Zy=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||vt.current)yt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return yt=!1,Y1(e,t,n);yt=!!(e.flags&131072)}else yt=!1,Ie&&t.flags&1048576&&Yg(t,Wa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ca(e,t),e=t.pendingProps;var s=eo(t,st.current);As(t,n),s=sf(null,t,r,e,s,n);var o=of();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,xt(r)?(o=!0,Va(t)):o=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Jd(t),s.updater=Tl,t.stateNode=s,s._reactInternals=t,Eu(t,r,e,n),t=Tu(null,t,r,!0,o,n)):(t.tag=0,Ie&&o&&Qd(t),lt(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ca(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=fS(r),e=Ht(r,e),s){case 0:t=Nu(null,t,r,e,n);break e;case 1:t=ap(null,t,r,e,n);break e;case 11:t=op(null,t,r,e,n);break e;case 14:t=ip(null,t,r,Ht(r.type,e),n);break e}throw Error(I(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ht(r,s),Nu(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ht(r,s),ap(e,t,r,s,n);case 3:e:{if(Ry(t),e===null)throw Error(I(387));r=t.pendingProps,o=t.memoizedState,s=o.element,ry(e,t),Ka(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){s=so(Error(I(423)),t),t=lp(e,t,r,n,s);break e}else if(r!==s){s=so(Error(I(424)),t),t=lp(e,t,r,n,s);break e}else for(bt=ur(t.stateNode.containerInfo.firstChild),Ct=t,Ie=!0,Kt=null,n=ty(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(to(),r===s){t=Pn(e,t,n);break e}lt(e,t,r,n)}t=t.child}return t;case 5:return sy(t),e===null&&ku(t),r=t.type,s=t.pendingProps,o=e!==null?e.memoizedProps:null,i=s.children,yu(r,s)?i=null:o!==null&&yu(r,o)&&(t.flags|=32),jy(e,t),lt(e,t,i,n),t.child;case 6:return e===null&&ku(t),null;case 13:return Iy(e,t,n);case 4:return ef(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=no(t,null,r,n):lt(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ht(r,s),op(e,t,r,s,n);case 7:return lt(e,t,t.pendingProps,n),t.child;case 8:return lt(e,t,t.pendingProps.children,n),t.child;case 12:return lt(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,o=t.memoizedProps,i=s.value,Ee(Qa,r._currentValue),r._currentValue=i,o!==null)if(Jt(o.value,i)){if(o.children===s.children&&!vt.current){t=Pn(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){i=o.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(o.tag===1){l=Nn(-1,n&-n),l.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var d=c.pending;d===null?l.next=l:(l.next=d.next,d.next=l),c.pending=l}}o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),bu(o.return,n,t),a.lanes|=n;break}l=l.next}}else if(o.tag===10)i=o.type===t.type?null:o.child;else if(o.tag===18){if(i=o.return,i===null)throw Error(I(341));i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),bu(i,n,t),i=o.sibling}else i=o.child;if(i!==null)i.return=o;else for(i=o;i!==null;){if(i===t){i=null;break}if(o=i.sibling,o!==null){o.return=i.return,i=o;break}i=i.return}o=i}lt(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,As(t,n),s=Ft(s),r=r(s),t.flags|=1,lt(e,t,r,n),t.child;case 14:return r=t.type,s=Ht(r,t.pendingProps),s=Ht(r.type,s),ip(e,t,r,s,n);case 15:return Ny(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Ht(r,s),Ca(e,t),t.tag=1,xt(r)?(e=!0,Va(t)):e=!1,As(t,n),Cy(t,r,s),Eu(t,r,s,n),Tu(null,t,r,!0,e,n);case 19:return Py(e,t,n);case 22:return Ty(e,t,n)}throw Error(I(156,t.tag))};function Ky(e,t){return Sg(e,t)}function dS(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mt(e,t,n,r){return new dS(e,t,n,r)}function gf(e){return e=e.prototype,!(!e||!e.isReactComponent)}function fS(e){if(typeof e=="function")return gf(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Od)return 11;if(e===Ad)return 14}return 2}function pr(e,t){var n=e.alternate;return n===null?(n=Mt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Na(e,t,n,r,s,o){var i=2;if(r=e,typeof e=="function")gf(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case xs:return Kr(n.children,s,o,t);case Dd:i=8,s|=8;break;case qc:return e=Mt(12,n,t,s|2),e.elementType=qc,e.lanes=o,e;case Gc:return e=Mt(13,n,t,s),e.elementType=Gc,e.lanes=o,e;case Yc:return e=Mt(19,n,t,s),e.elementType=Yc,e.lanes=o,e;case sg:return Il(n,s,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ng:i=10;break e;case rg:i=9;break e;case Od:i=11;break e;case Ad:i=14;break e;case Vn:i=16,r=null;break e}throw Error(I(130,e==null?e:typeof e,""))}return t=Mt(i,n,t,s),t.elementType=e,t.type=r,t.lanes=o,t}function Kr(e,t,n,r){return e=Mt(7,e,r,t),e.lanes=n,e}function Il(e,t,n,r){return e=Mt(22,e,r,t),e.elementType=sg,e.lanes=n,e.stateNode={isHidden:!1},e}function jc(e,t,n){return e=Mt(6,e,null,t),e.lanes=n,e}function Rc(e,t,n){return t=Mt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function hS(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=uc(0),this.expirationTimes=uc(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=uc(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function yf(e,t,n,r,s,o,i,a,l){return e=new hS(e,t,n,a,l),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Mt(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Jd(o),e}function pS(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:vs,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function qy(e){if(!e)return xr;e=e._reactInternals;e:{if(fs(e)!==e||e.tag!==1)throw Error(I(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(xt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(I(171))}if(e.tag===1){var n=e.type;if(xt(n))return qg(e,n,t)}return t}function Gy(e,t,n,r,s,o,i,a,l){return e=yf(n,r,!0,e,s,o,i,a,l),e.context=qy(null),n=e.current,r=ut(),s=hr(n),o=Nn(r,s),o.callback=t??null,dr(n,o,s),e.current.lanes=s,Ii(e,s,r),wt(e,r),e}function Pl(e,t,n,r){var s=t.current,o=ut(),i=hr(s);return n=qy(n),t.context===null?t.context=n:t.pendingContext=n,t=Nn(o,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=dr(s,t,i),e!==null&&(Xt(e,s,i,o),Sa(e,s,i)),i}function nl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function vp(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function vf(e,t){vp(e,t),(e=e.alternate)&&vp(e,t)}function mS(){return null}var Yy=typeof reportError=="function"?reportError:function(e){console.error(e)};function xf(e){this._internalRoot=e}Dl.prototype.render=xf.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(I(409));Pl(e,t,null,null)};Dl.prototype.unmount=xf.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Jr(function(){Pl(null,e,null,null)}),t[Rn]=null}};function Dl(e){this._internalRoot=e}Dl.prototype.unstable_scheduleHydration=function(e){if(e){var t=Tg();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Qn.length&&t!==0&&t<Qn[n].priority;n++);Qn.splice(n,0,e),n===0&&Rg(e)}};function wf(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ol(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function xp(){}function gS(e,t,n,r,s){if(s){if(typeof r=="function"){var o=r;r=function(){var c=nl(i);o.call(c)}}var i=Gy(t,r,e,0,null,!1,!1,"",xp);return e._reactRootContainer=i,e[Rn]=i.current,ui(e.nodeType===8?e.parentNode:e),Jr(),i}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var c=nl(l);a.call(c)}}var l=yf(e,0,!1,null,null,!1,!1,"",xp);return e._reactRootContainer=l,e[Rn]=l.current,ui(e.nodeType===8?e.parentNode:e),Jr(function(){Pl(t,l,n,r)}),l}function Al(e,t,n,r,s){var o=n._reactRootContainer;if(o){var i=o;if(typeof s=="function"){var a=s;s=function(){var l=nl(i);a.call(l)}}Pl(t,i,e,s)}else i=gS(n,t,e,s,r);return nl(i)}_g=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=$o(t.pendingLanes);n!==0&&(Fd(t,n|1),wt(t,Fe()),!(ge&6)&&(oo=Fe()+500,Nr()))}break;case 13:Jr(function(){var r=In(e,1);if(r!==null){var s=ut();Xt(r,e,1,s)}}),vf(e,1)}};zd=function(e){if(e.tag===13){var t=In(e,134217728);if(t!==null){var n=ut();Xt(t,e,134217728,n)}vf(e,134217728)}};Ng=function(e){if(e.tag===13){var t=hr(e),n=In(e,t);if(n!==null){var r=ut();Xt(n,e,t,r)}vf(e,t)}};Tg=function(){return ke};jg=function(e,t){var n=ke;try{return ke=e,t()}finally{ke=n}};au=function(e,t,n){switch(t){case"input":if(eu(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=El(r);if(!s)throw Error(I(90));ig(r),eu(r,s)}}}break;case"textarea":lg(e,n);break;case"select":t=n.value,t!=null&&Is(e,!!n.multiple,t,!1)}};mg=hf;gg=Jr;var yS={usingClientEntryPoint:!1,Events:[Di,bs,El,hg,pg,hf]},Ao={findFiberByHostInstance:Ar,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},vS={bundleType:Ao.bundleType,version:Ao.version,rendererPackageName:Ao.rendererPackageName,rendererConfig:Ao.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ln.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=xg(e),e===null?null:e.stateNode},findFiberByHostInstance:Ao.findFiberByHostInstance||mS,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ia=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ia.isDisabled&&ia.supportsFiber)try{Sl=ia.inject(vS),fn=ia}catch{}}jt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=yS;jt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!wf(t))throw Error(I(200));return pS(e,t,null,n)};jt.createRoot=function(e,t){if(!wf(e))throw Error(I(299));var n=!1,r="",s=Yy;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=yf(e,1,!1,null,null,n,!1,r,s),e[Rn]=t.current,ui(e.nodeType===8?e.parentNode:e),new xf(t)};jt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(I(188)):(e=Object.keys(e).join(","),Error(I(268,e)));return e=xg(t),e=e===null?null:e.stateNode,e};jt.flushSync=function(e){return Jr(e)};jt.hydrate=function(e,t,n){if(!Ol(t))throw Error(I(200));return Al(null,e,t,!0,n)};jt.hydrateRoot=function(e,t,n){if(!wf(e))throw Error(I(405));var r=n!=null&&n.hydratedSources||null,s=!1,o="",i=Yy;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=Gy(t,null,e,1,n??null,s,!1,o,i),e[Rn]=t.current,ui(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new Dl(t)};jt.render=function(e,t,n){if(!Ol(t))throw Error(I(200));return Al(null,e,t,!1,n)};jt.unmountComponentAtNode=function(e){if(!Ol(e))throw Error(I(40));return e._reactRootContainer?(Jr(function(){Al(null,null,e,!1,function(){e._reactRootContainer=null,e[Rn]=null})}),!0):!1};jt.unstable_batchedUpdates=hf;jt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ol(n))throw Error(I(200));if(e==null||e._reactInternals===void 0)throw Error(I(38));return Al(e,t,n,!1,r)};jt.version="18.3.1-next-f1338f8080-20240426";function Xy(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Xy)}catch(e){console.error(e)}}Xy(),Xm.exports=jt;var xo=Xm.exports;const xS=Ed(xo);var wp=xo;Zc.createRoot=wp.createRoot,Zc.hydrateRoot=wp.hydrateRoot;var wo=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},es=typeof window>"u"||"Deno"in globalThis;function ct(){}function wS(e,t){return typeof e=="function"?e(t):e}function $u(e){return typeof e=="number"&&e>=0&&e!==1/0}function Jy(e,t){return Math.max(e+(t||0)-Date.now(),0)}function mr(e,t){return typeof e=="function"?e(t):e}function qt(e,t){return typeof e=="function"?e(t):e}function Sp(e,t){const{type:n="all",exact:r,fetchStatus:s,predicate:o,queryKey:i,stale:a}=e;if(i){if(r){if(t.queryHash!==Sf(i,t.options))return!1}else if(!xi(t.queryKey,i))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||s&&s!==t.state.fetchStatus||o&&!o(t))}function kp(e,t){const{exact:n,status:r,predicate:s,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(n){if(ts(t.options.mutationKey)!==ts(o))return!1}else if(!xi(t.options.mutationKey,o))return!1}return!(r&&t.state.status!==r||s&&!s(t))}function Sf(e,t){return((t==null?void 0:t.queryKeyHashFn)||ts)(e)}function ts(e){return JSON.stringify(e,(t,n)=>Uu(n)?Object.keys(n).sort().reduce((r,s)=>(r[s]=n[s],r),{}):n)}function xi(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(n=>xi(e[n],t[n])):!1}function ev(e,t){if(e===t)return e;const n=bp(e)&&bp(t);if(n||Uu(e)&&Uu(t)){const r=n?e:Object.keys(e),s=r.length,o=n?t:Object.keys(t),i=o.length,a=n?[]:{},l=new Set(r);let c=0;for(let d=0;d<i;d++){const f=n?d:o[d];(!n&&l.has(f)||n)&&e[f]===void 0&&t[f]===void 0?(a[f]=void 0,c++):(a[f]=ev(e[f],t[f]),a[f]===e[f]&&e[f]!==void 0&&c++)}return s===i&&c===s?e:a}return t}function rl(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function bp(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Uu(e){if(!Cp(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Cp(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Cp(e){return Object.prototype.toString.call(e)==="[object Object]"}function SS(e){return new Promise(t=>{setTimeout(t,e)})}function Bu(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?ev(e,t):t}function kS(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function bS(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var kf=Symbol();function tv(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===kf?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function nv(e,t){return typeof e=="function"?e(...t):!!e}var Fr,Kn,Us,Rm,CS=(Rm=class extends wo{constructor(){super();G(this,Fr);G(this,Kn);G(this,Us);F(this,Us,t=>{if(!es&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){C(this,Kn)||this.setEventListener(C(this,Us))}onUnsubscribe(){var t;this.hasListeners()||((t=C(this,Kn))==null||t.call(this),F(this,Kn,void 0))}setEventListener(t){var n;F(this,Us,t),(n=C(this,Kn))==null||n.call(this),F(this,Kn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){C(this,Fr)!==t&&(F(this,Fr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof C(this,Fr)=="boolean"?C(this,Fr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Fr=new WeakMap,Kn=new WeakMap,Us=new WeakMap,Rm),bf=new CS,Bs,qn,Vs,Im,ES=(Im=class extends wo{constructor(){super();G(this,Bs,!0);G(this,qn);G(this,Vs);F(this,Vs,t=>{if(!es&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){C(this,qn)||this.setEventListener(C(this,Vs))}onUnsubscribe(){var t;this.hasListeners()||((t=C(this,qn))==null||t.call(this),F(this,qn,void 0))}setEventListener(t){var n;F(this,Vs,t),(n=C(this,qn))==null||n.call(this),F(this,qn,t(this.setOnline.bind(this)))}setOnline(t){C(this,Bs)!==t&&(F(this,Bs,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return C(this,Bs)}},Bs=new WeakMap,qn=new WeakMap,Vs=new WeakMap,Im),sl=new ES;function Vu(){let e,t;const n=new Promise((s,o)=>{e=s,t=o});n.status="pending",n.catch(()=>{});function r(s){Object.assign(n,s),delete n.resolve,delete n.reject}return n.resolve=s=>{r({status:"fulfilled",value:s}),e(s)},n.reject=s=>{r({status:"rejected",reason:s}),t(s)},n}function _S(e){return Math.min(1e3*2**e,3e4)}function rv(e){return(e??"online")==="online"?sl.isOnline():!0}var sv=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Ic(e){return e instanceof sv}function ov(e){let t=!1,n=0,r=!1,s;const o=Vu(),i=m=>{var w;r||(y(new sv(m)),(w=e.abort)==null||w.call(e))},a=()=>{t=!0},l=()=>{t=!1},c=()=>bf.isFocused()&&(e.networkMode==="always"||sl.isOnline())&&e.canRun(),d=()=>rv(e.networkMode)&&e.canRun(),f=m=>{var w;r||(r=!0,(w=e.onSuccess)==null||w.call(e,m),s==null||s(),o.resolve(m))},y=m=>{var w;r||(r=!0,(w=e.onError)==null||w.call(e,m),s==null||s(),o.reject(m))},x=()=>new Promise(m=>{var w;s=p=>{(r||c())&&m(p)},(w=e.onPause)==null||w.call(e)}).then(()=>{var m;s=void 0,r||(m=e.onContinue)==null||m.call(e)}),k=()=>{if(r)return;let m;const w=n===0?e.initialPromise:void 0;try{m=w??e.fn()}catch(p){m=Promise.reject(p)}Promise.resolve(m).then(f).catch(p=>{var E;if(r)return;const h=e.retry??(es?0:3),v=e.retryDelay??_S,S=typeof v=="function"?v(n,p):v,b=h===!0||typeof h=="number"&&n<h||typeof h=="function"&&h(n,p);if(t||!b){y(p);return}n++,(E=e.onFail)==null||E.call(e,n,p),SS(S).then(()=>c()?void 0:x()).then(()=>{t?y(p):k()})})};return{promise:o,cancel:i,continue:()=>(s==null||s(),o),cancelRetry:a,continueRetry:l,canStart:d,start:()=>(d()?k():x().then(k),o)}}var NS=e=>setTimeout(e,0);function TS(){let e=[],t=0,n=a=>{a()},r=a=>{a()},s=NS;const o=a=>{t?e.push(a):s(()=>{n(a)})},i=()=>{const a=e;e=[],a.length&&s(()=>{r(()=>{a.forEach(l=>{n(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||i()}return l},batchCalls:a=>(...l)=>{o(()=>{a(...l)})},schedule:o,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{s=a}}}var Be=TS(),zr,Pm,iv=(Pm=class{constructor(){G(this,zr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),$u(this.gcTime)&&F(this,zr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(es?1/0:5*60*1e3))}clearGcTimeout(){C(this,zr)&&(clearTimeout(C(this,zr)),F(this,zr,void 0))}},zr=new WeakMap,Pm),Hs,$r,Pt,Ur,tt,_i,Br,Wt,wn,Dm,jS=(Dm=class extends iv{constructor(t){super();G(this,Wt);G(this,Hs);G(this,$r);G(this,Pt);G(this,Ur);G(this,tt);G(this,_i);G(this,Br);F(this,Br,!1),F(this,_i,t.defaultOptions),this.setOptions(t.options),this.observers=[],F(this,Ur,t.client),F(this,Pt,C(this,Ur).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,F(this,Hs,RS(this.options)),this.state=t.state??C(this,Hs),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=C(this,tt))==null?void 0:t.promise}setOptions(t){this.options={...C(this,_i),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&C(this,Pt).remove(this)}setData(t,n){const r=Bu(this.state.data,t,this.options);return ie(this,Wt,wn).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){ie(this,Wt,wn).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,s;const n=(r=C(this,tt))==null?void 0:r.promise;return(s=C(this,tt))==null||s.cancel(t),n?n.then(ct).catch(ct):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(C(this,Hs))}isActive(){return this.observers.some(t=>qt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===kf||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>mr(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Jy(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=C(this,tt))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=C(this,tt))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),C(this,Pt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(C(this,tt)&&(C(this,Br)?C(this,tt).cancel({revert:!0}):C(this,tt).cancelRetry()),this.scheduleGc()),C(this,Pt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||ie(this,Wt,wn).call(this,{type:"invalidate"})}fetch(t,n){var c,d,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(C(this,tt))return C(this,tt).continueRetry(),C(this,tt).promise}if(t&&this.setOptions(t),!this.options.queryFn){const y=this.observers.find(x=>x.options.queryFn);y&&this.setOptions(y.options)}const r=new AbortController,s=y=>{Object.defineProperty(y,"signal",{enumerable:!0,get:()=>(F(this,Br,!0),r.signal)})},o=()=>{const y=tv(this.options,n),k=(()=>{const m={client:C(this,Ur),queryKey:this.queryKey,meta:this.meta};return s(m),m})();return F(this,Br,!1),this.options.persister?this.options.persister(y,k,this):y(k)},a=(()=>{const y={fetchOptions:n,options:this.options,queryKey:this.queryKey,client:C(this,Ur),state:this.state,fetchFn:o};return s(y),y})();(c=this.options.behavior)==null||c.onFetch(a,this),F(this,$r,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((d=a.fetchOptions)==null?void 0:d.meta))&&ie(this,Wt,wn).call(this,{type:"fetch",meta:(f=a.fetchOptions)==null?void 0:f.meta});const l=y=>{var x,k,m,w;Ic(y)&&y.silent||ie(this,Wt,wn).call(this,{type:"error",error:y}),Ic(y)||((k=(x=C(this,Pt).config).onError)==null||k.call(x,y,this),(w=(m=C(this,Pt).config).onSettled)==null||w.call(m,this.state.data,y,this)),this.scheduleGc()};return F(this,tt,ov({initialPromise:n==null?void 0:n.initialPromise,fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:y=>{var x,k,m,w;if(y===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(y)}catch(p){l(p);return}(k=(x=C(this,Pt).config).onSuccess)==null||k.call(x,y,this),(w=(m=C(this,Pt).config).onSettled)==null||w.call(m,y,this.state.error,this),this.scheduleGc()},onError:l,onFail:(y,x)=>{ie(this,Wt,wn).call(this,{type:"failed",failureCount:y,error:x})},onPause:()=>{ie(this,Wt,wn).call(this,{type:"pause"})},onContinue:()=>{ie(this,Wt,wn).call(this,{type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0})),C(this,tt).start()}},Hs=new WeakMap,$r=new WeakMap,Pt=new WeakMap,Ur=new WeakMap,tt=new WeakMap,_i=new WeakMap,Br=new WeakMap,Wt=new WeakSet,wn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...av(r.data,this.options),fetchMeta:t.meta??null};case"success":return F(this,$r,void 0),{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return Ic(s)&&s.revert&&C(this,$r)?{...C(this,$r),fetchStatus:"idle"}:{...r,error:s,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Be.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),C(this,Pt).notify({query:this,type:"updated",action:t})})},Dm);function av(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:rv(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function RS(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var on,Om,IS=(Om=class extends wo{constructor(t={}){super();G(this,on);this.config=t,F(this,on,new Map)}build(t,n,r){const s=n.queryKey,o=n.queryHash??Sf(s,n);let i=this.get(o);return i||(i=new jS({client:t,queryKey:s,queryHash:o,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(s)}),this.add(i)),i}add(t){C(this,on).has(t.queryHash)||(C(this,on).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=C(this,on).get(t.queryHash);n&&(t.destroy(),n===t&&C(this,on).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Be.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return C(this,on).get(t)}getAll(){return[...C(this,on).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Sp(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>Sp(t,r)):n}notify(t){Be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Be.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Be.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},on=new WeakMap,Om),an,it,Vr,ln,Un,Am,PS=(Am=class extends iv{constructor(t){super();G(this,ln);G(this,an);G(this,it);G(this,Vr);this.mutationId=t.mutationId,F(this,it,t.mutationCache),F(this,an,[]),this.state=t.state||lv(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){C(this,an).includes(t)||(C(this,an).push(t),this.clearGcTimeout(),C(this,it).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){F(this,an,C(this,an).filter(n=>n!==t)),this.scheduleGc(),C(this,it).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){C(this,an).length||(this.state.status==="pending"?this.scheduleGc():C(this,it).remove(this))}continue(){var t;return((t=C(this,Vr))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,a,l,c,d,f,y,x,k,m,w,p,h,v,S,b,E,_,N;const n=()=>{ie(this,ln,Un).call(this,{type:"continue"})};F(this,Vr,ov({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(j,R)=>{ie(this,ln,Un).call(this,{type:"failed",failureCount:j,error:R})},onPause:()=>{ie(this,ln,Un).call(this,{type:"pause"})},onContinue:n,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>C(this,it).canRun(this)}));const r=this.state.status==="pending",s=!C(this,Vr).canStart();try{if(r)n();else{ie(this,ln,Un).call(this,{type:"pending",variables:t,isPaused:s}),await((i=(o=C(this,it).config).onMutate)==null?void 0:i.call(o,t,this));const R=await((l=(a=this.options).onMutate)==null?void 0:l.call(a,t));R!==this.state.context&&ie(this,ln,Un).call(this,{type:"pending",context:R,variables:t,isPaused:s})}const j=await C(this,Vr).start();return await((d=(c=C(this,it).config).onSuccess)==null?void 0:d.call(c,j,t,this.state.context,this)),await((y=(f=this.options).onSuccess)==null?void 0:y.call(f,j,t,this.state.context)),await((k=(x=C(this,it).config).onSettled)==null?void 0:k.call(x,j,null,this.state.variables,this.state.context,this)),await((w=(m=this.options).onSettled)==null?void 0:w.call(m,j,null,t,this.state.context)),ie(this,ln,Un).call(this,{type:"success",data:j}),j}catch(j){try{throw await((h=(p=C(this,it).config).onError)==null?void 0:h.call(p,j,t,this.state.context,this)),await((S=(v=this.options).onError)==null?void 0:S.call(v,j,t,this.state.context)),await((E=(b=C(this,it).config).onSettled)==null?void 0:E.call(b,void 0,j,this.state.variables,this.state.context,this)),await((N=(_=this.options).onSettled)==null?void 0:N.call(_,void 0,j,t,this.state.context)),j}finally{ie(this,ln,Un).call(this,{type:"error",error:j})}}finally{C(this,it).runNext(this)}}},an=new WeakMap,it=new WeakMap,Vr=new WeakMap,ln=new WeakSet,Un=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Be.batch(()=>{C(this,an).forEach(r=>{r.onMutationUpdate(t)}),C(this,it).notify({mutation:this,type:"updated",action:t})})},Am);function lv(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var bn,Qt,Ni,Mm,DS=(Mm=class extends wo{constructor(t={}){super();G(this,bn);G(this,Qt);G(this,Ni);this.config=t,F(this,bn,new Set),F(this,Qt,new Map),F(this,Ni,0)}build(t,n,r){const s=new PS({mutationCache:this,mutationId:++Vi(this,Ni)._,options:t.defaultMutationOptions(n),state:r});return this.add(s),s}add(t){C(this,bn).add(t);const n=aa(t);if(typeof n=="string"){const r=C(this,Qt).get(n);r?r.push(t):C(this,Qt).set(n,[t])}this.notify({type:"added",mutation:t})}remove(t){if(C(this,bn).delete(t)){const n=aa(t);if(typeof n=="string"){const r=C(this,Qt).get(n);if(r)if(r.length>1){const s=r.indexOf(t);s!==-1&&r.splice(s,1)}else r[0]===t&&C(this,Qt).delete(n)}}this.notify({type:"removed",mutation:t})}canRun(t){const n=aa(t);if(typeof n=="string"){const r=C(this,Qt).get(n),s=r==null?void 0:r.find(o=>o.state.status==="pending");return!s||s===t}else return!0}runNext(t){var r;const n=aa(t);if(typeof n=="string"){const s=(r=C(this,Qt).get(n))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Be.batch(()=>{C(this,bn).forEach(t=>{this.notify({type:"removed",mutation:t})}),C(this,bn).clear(),C(this,Qt).clear()})}getAll(){return Array.from(C(this,bn))}find(t){const n={exact:!0,...t};return this.getAll().find(r=>kp(n,r))}findAll(t={}){return this.getAll().filter(n=>kp(t,n))}notify(t){Be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Be.batch(()=>Promise.all(t.map(n=>n.continue().catch(ct))))}},bn=new WeakMap,Qt=new WeakMap,Ni=new WeakMap,Mm);function aa(e){var t;return(t=e.options.scope)==null?void 0:t.id}function Ep(e){return{onFetch:(t,n)=>{var d,f,y,x,k;const r=t.options,s=(y=(f=(d=t.fetchOptions)==null?void 0:d.meta)==null?void 0:f.fetchMore)==null?void 0:y.direction,o=((x=t.state.data)==null?void 0:x.pages)||[],i=((k=t.state.data)==null?void 0:k.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const c=async()=>{let m=!1;const w=v=>{Object.defineProperty(v,"signal",{enumerable:!0,get:()=>(t.signal.aborted?m=!0:t.signal.addEventListener("abort",()=>{m=!0}),t.signal)})},p=tv(t.options,t.fetchOptions),h=async(v,S,b)=>{if(m)return Promise.reject();if(S==null&&v.pages.length)return Promise.resolve(v);const _=(()=>{const L={client:t.client,queryKey:t.queryKey,pageParam:S,direction:b?"backward":"forward",meta:t.options.meta};return w(L),L})(),N=await p(_),{maxPages:j}=t.options,R=b?bS:kS;return{pages:R(v.pages,N,j),pageParams:R(v.pageParams,S,j)}};if(s&&o.length){const v=s==="backward",S=v?OS:_p,b={pages:o,pageParams:i},E=S(r,b);a=await h(b,E,v)}else{const v=e??o.length;do{const S=l===0?i[0]??r.initialPageParam:_p(r,a);if(l>0&&S==null)break;a=await h(a,S),l++}while(l<v)}return a};t.options.persister?t.fetchFn=()=>{var m,w;return(w=(m=t.options).persister)==null?void 0:w.call(m,c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=c}}}function _p(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function OS(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var Me,Gn,Yn,Ws,Qs,Xn,Zs,Ks,Lm,AS=(Lm=class{constructor(e={}){G(this,Me);G(this,Gn);G(this,Yn);G(this,Ws);G(this,Qs);G(this,Xn);G(this,Zs);G(this,Ks);F(this,Me,e.queryCache||new IS),F(this,Gn,e.mutationCache||new DS),F(this,Yn,e.defaultOptions||{}),F(this,Ws,new Map),F(this,Qs,new Map),F(this,Xn,0)}mount(){Vi(this,Xn)._++,C(this,Xn)===1&&(F(this,Zs,bf.subscribe(async e=>{e&&(await this.resumePausedMutations(),C(this,Me).onFocus())})),F(this,Ks,sl.subscribe(async e=>{e&&(await this.resumePausedMutations(),C(this,Me).onOnline())})))}unmount(){var e,t;Vi(this,Xn)._--,C(this,Xn)===0&&((e=C(this,Zs))==null||e.call(this),F(this,Zs,void 0),(t=C(this,Ks))==null||t.call(this),F(this,Ks,void 0))}isFetching(e){return C(this,Me).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return C(this,Gn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=C(this,Me).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),n=C(this,Me).build(this,t),r=n.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(mr(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return C(this,Me).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),s=C(this,Me).get(r.queryHash),o=s==null?void 0:s.state.data,i=wS(t,o);if(i!==void 0)return C(this,Me).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return Be.batch(()=>C(this,Me).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=C(this,Me).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=C(this,Me);Be.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=C(this,Me);return Be.batch(()=>(n.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const n={revert:!0,...t},r=Be.batch(()=>C(this,Me).findAll(e).map(s=>s.cancel(n)));return Promise.all(r).then(ct).catch(ct)}invalidateQueries(e,t={}){return Be.batch(()=>(C(this,Me).findAll(e).forEach(n=>{n.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const n={...t,cancelRefetch:t.cancelRefetch??!0},r=Be.batch(()=>C(this,Me).findAll(e).filter(s=>!s.isDisabled()&&!s.isStatic()).map(s=>{let o=s.fetch(void 0,n);return n.throwOnError||(o=o.catch(ct)),s.state.fetchStatus==="paused"?Promise.resolve():o}));return Promise.all(r).then(ct)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=C(this,Me).build(this,t);return n.isStaleByTime(mr(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(ct).catch(ct)}fetchInfiniteQuery(e){return e.behavior=Ep(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(ct).catch(ct)}ensureInfiniteQueryData(e){return e.behavior=Ep(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return sl.isOnline()?C(this,Gn).resumePausedMutations():Promise.resolve()}getQueryCache(){return C(this,Me)}getMutationCache(){return C(this,Gn)}getDefaultOptions(){return C(this,Yn)}setDefaultOptions(e){F(this,Yn,e)}setQueryDefaults(e,t){C(this,Ws).set(ts(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...C(this,Ws).values()],n={};return t.forEach(r=>{xi(e,r.queryKey)&&Object.assign(n,r.defaultOptions)}),n}setMutationDefaults(e,t){C(this,Qs).set(ts(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...C(this,Qs).values()],n={};return t.forEach(r=>{xi(e,r.mutationKey)&&Object.assign(n,r.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...C(this,Yn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Sf(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===kf&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...C(this,Yn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){C(this,Me).clear(),C(this,Gn).clear()}},Me=new WeakMap,Gn=new WeakMap,Yn=new WeakMap,Ws=new WeakMap,Qs=new WeakMap,Xn=new WeakMap,Zs=new WeakMap,Ks=new WeakMap,Lm),ht,de,Ti,at,Hr,qs,Jn,er,ji,Gs,Ys,Wr,Qr,tr,Xs,we,Bo,Hu,Wu,Qu,Zu,Ku,qu,Gu,cv,Fm,MS=(Fm=class extends wo{constructor(t,n){super();G(this,we);G(this,ht);G(this,de);G(this,Ti);G(this,at);G(this,Hr);G(this,qs);G(this,Jn);G(this,er);G(this,ji);G(this,Gs);G(this,Ys);G(this,Wr);G(this,Qr);G(this,tr);G(this,Xs,new Set);this.options=n,F(this,ht,t),F(this,er,null),F(this,Jn,Vu()),this.options.experimental_prefetchInRender||C(this,Jn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(C(this,de).addObserver(this),Np(C(this,de),this.options)?ie(this,we,Bo).call(this):this.updateResult(),ie(this,we,Zu).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Yu(C(this,de),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Yu(C(this,de),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,ie(this,we,Ku).call(this),ie(this,we,qu).call(this),C(this,de).removeObserver(this)}setOptions(t){const n=this.options,r=C(this,de);if(this.options=C(this,ht).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof qt(this.options.enabled,C(this,de))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");ie(this,we,Gu).call(this),C(this,de).setOptions(this.options),n._defaulted&&!rl(this.options,n)&&C(this,ht).getQueryCache().notify({type:"observerOptionsUpdated",query:C(this,de),observer:this});const s=this.hasListeners();s&&Tp(C(this,de),r,this.options,n)&&ie(this,we,Bo).call(this),this.updateResult(),s&&(C(this,de)!==r||qt(this.options.enabled,C(this,de))!==qt(n.enabled,C(this,de))||mr(this.options.staleTime,C(this,de))!==mr(n.staleTime,C(this,de)))&&ie(this,we,Hu).call(this);const o=ie(this,we,Wu).call(this);s&&(C(this,de)!==r||qt(this.options.enabled,C(this,de))!==qt(n.enabled,C(this,de))||o!==C(this,tr))&&ie(this,we,Qu).call(this,o)}getOptimisticResult(t){const n=C(this,ht).getQueryCache().build(C(this,ht),t),r=this.createResult(n,t);return FS(this,r)&&(F(this,at,r),F(this,qs,this.options),F(this,Hr,C(this,de).state)),r}getCurrentResult(){return C(this,at)}trackResult(t,n){return new Proxy(t,{get:(r,s)=>(this.trackProp(s),n==null||n(s),Reflect.get(r,s))})}trackProp(t){C(this,Xs).add(t)}getCurrentQuery(){return C(this,de)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=C(this,ht).defaultQueryOptions(t),r=C(this,ht).getQueryCache().build(C(this,ht),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){return ie(this,we,Bo).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),C(this,at)))}createResult(t,n){var j;const r=C(this,de),s=this.options,o=C(this,at),i=C(this,Hr),a=C(this,qs),c=t!==r?t.state:C(this,Ti),{state:d}=t;let f={...d},y=!1,x;if(n._optimisticResults){const R=this.hasListeners(),L=!R&&Np(t,n),A=R&&Tp(t,r,n,s);(L||A)&&(f={...f,...av(d.data,t.options)}),n._optimisticResults==="isRestoring"&&(f.fetchStatus="idle")}let{error:k,errorUpdatedAt:m,status:w}=f;x=f.data;let p=!1;if(n.placeholderData!==void 0&&x===void 0&&w==="pending"){let R;o!=null&&o.isPlaceholderData&&n.placeholderData===(a==null?void 0:a.placeholderData)?(R=o.data,p=!0):R=typeof n.placeholderData=="function"?n.placeholderData((j=C(this,Ys))==null?void 0:j.state.data,C(this,Ys)):n.placeholderData,R!==void 0&&(w="success",x=Bu(o==null?void 0:o.data,R,n),y=!0)}if(n.select&&x!==void 0&&!p)if(o&&x===(i==null?void 0:i.data)&&n.select===C(this,ji))x=C(this,Gs);else try{F(this,ji,n.select),x=n.select(x),x=Bu(o==null?void 0:o.data,x,n),F(this,Gs,x),F(this,er,null)}catch(R){F(this,er,R)}C(this,er)&&(k=C(this,er),x=C(this,Gs),m=Date.now(),w="error");const h=f.fetchStatus==="fetching",v=w==="pending",S=w==="error",b=v&&h,E=x!==void 0,N={status:w,fetchStatus:f.fetchStatus,isPending:v,isSuccess:w==="success",isError:S,isInitialLoading:b,isLoading:b,data:x,dataUpdatedAt:f.dataUpdatedAt,error:k,errorUpdatedAt:m,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>c.dataUpdateCount||f.errorUpdateCount>c.errorUpdateCount,isFetching:h,isRefetching:h&&!v,isLoadingError:S&&!E,isPaused:f.fetchStatus==="paused",isPlaceholderData:y,isRefetchError:S&&E,isStale:Cf(t,n),refetch:this.refetch,promise:C(this,Jn)};if(this.options.experimental_prefetchInRender){const R=Z=>{N.status==="error"?Z.reject(N.error):N.data!==void 0&&Z.resolve(N.data)},L=()=>{const Z=F(this,Jn,N.promise=Vu());R(Z)},A=C(this,Jn);switch(A.status){case"pending":t.queryHash===r.queryHash&&R(A);break;case"fulfilled":(N.status==="error"||N.data!==A.value)&&L();break;case"rejected":(N.status!=="error"||N.error!==A.reason)&&L();break}}return N}updateResult(){const t=C(this,at),n=this.createResult(C(this,de),this.options);if(F(this,Hr,C(this,de).state),F(this,qs,this.options),C(this,Hr).data!==void 0&&F(this,Ys,C(this,de)),rl(n,t))return;F(this,at,n);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:s}=this.options,o=typeof s=="function"?s():s;if(o==="all"||!o&&!C(this,Xs).size)return!0;const i=new Set(o??C(this,Xs));return this.options.throwOnError&&i.add("error"),Object.keys(C(this,at)).some(a=>{const l=a;return C(this,at)[l]!==t[l]&&i.has(l)})};ie(this,we,cv).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&ie(this,we,Zu).call(this)}},ht=new WeakMap,de=new WeakMap,Ti=new WeakMap,at=new WeakMap,Hr=new WeakMap,qs=new WeakMap,Jn=new WeakMap,er=new WeakMap,ji=new WeakMap,Gs=new WeakMap,Ys=new WeakMap,Wr=new WeakMap,Qr=new WeakMap,tr=new WeakMap,Xs=new WeakMap,we=new WeakSet,Bo=function(t){ie(this,we,Gu).call(this);let n=C(this,de).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(ct)),n},Hu=function(){ie(this,we,Ku).call(this);const t=mr(this.options.staleTime,C(this,de));if(es||C(this,at).isStale||!$u(t))return;const r=Jy(C(this,at).dataUpdatedAt,t)+1;F(this,Wr,setTimeout(()=>{C(this,at).isStale||this.updateResult()},r))},Wu=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(C(this,de)):this.options.refetchInterval)??!1},Qu=function(t){ie(this,we,qu).call(this),F(this,tr,t),!(es||qt(this.options.enabled,C(this,de))===!1||!$u(C(this,tr))||C(this,tr)===0)&&F(this,Qr,setInterval(()=>{(this.options.refetchIntervalInBackground||bf.isFocused())&&ie(this,we,Bo).call(this)},C(this,tr)))},Zu=function(){ie(this,we,Hu).call(this),ie(this,we,Qu).call(this,ie(this,we,Wu).call(this))},Ku=function(){C(this,Wr)&&(clearTimeout(C(this,Wr)),F(this,Wr,void 0))},qu=function(){C(this,Qr)&&(clearInterval(C(this,Qr)),F(this,Qr,void 0))},Gu=function(){const t=C(this,ht).getQueryCache().build(C(this,ht),this.options);if(t===C(this,de))return;const n=C(this,de);F(this,de,t),F(this,Ti,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},cv=function(t){Be.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(C(this,at))}),C(this,ht).getQueryCache().notify({query:C(this,de),type:"observerResultsUpdated"})})},Fm);function LS(e,t){return qt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Np(e,t){return LS(e,t)||e.state.data!==void 0&&Yu(e,t,t.refetchOnMount)}function Yu(e,t,n){if(qt(t.enabled,e)!==!1&&mr(t.staleTime,e)!=="static"){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&Cf(e,t)}return!1}function Tp(e,t,n,r){return(e!==t||qt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&Cf(e,n)}function Cf(e,t){return qt(t.enabled,e)!==!1&&e.isStaleByTime(mr(t.staleTime,e))}function FS(e,t){return!rl(e.getCurrentResult(),t)}var nr,rr,pt,Cn,Tn,Ta,Xu,zm,zS=(zm=class extends wo{constructor(n,r){super();G(this,Tn);G(this,nr);G(this,rr);G(this,pt);G(this,Cn);F(this,nr,n),this.setOptions(r),this.bindMethods(),ie(this,Tn,Ta).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(n){var s;const r=this.options;this.options=C(this,nr).defaultMutationOptions(n),rl(this.options,r)||C(this,nr).getMutationCache().notify({type:"observerOptionsUpdated",mutation:C(this,pt),observer:this}),r!=null&&r.mutationKey&&this.options.mutationKey&&ts(r.mutationKey)!==ts(this.options.mutationKey)?this.reset():((s=C(this,pt))==null?void 0:s.state.status)==="pending"&&C(this,pt).setOptions(this.options)}onUnsubscribe(){var n;this.hasListeners()||(n=C(this,pt))==null||n.removeObserver(this)}onMutationUpdate(n){ie(this,Tn,Ta).call(this),ie(this,Tn,Xu).call(this,n)}getCurrentResult(){return C(this,rr)}reset(){var n;(n=C(this,pt))==null||n.removeObserver(this),F(this,pt,void 0),ie(this,Tn,Ta).call(this),ie(this,Tn,Xu).call(this)}mutate(n,r){var s;return F(this,Cn,r),(s=C(this,pt))==null||s.removeObserver(this),F(this,pt,C(this,nr).getMutationCache().build(C(this,nr),this.options)),C(this,pt).addObserver(this),C(this,pt).execute(n)}},nr=new WeakMap,rr=new WeakMap,pt=new WeakMap,Cn=new WeakMap,Tn=new WeakSet,Ta=function(){var r;const n=((r=C(this,pt))==null?void 0:r.state)??lv();F(this,rr,{...n,isPending:n.status==="pending",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset})},Xu=function(n){Be.batch(()=>{var r,s,o,i,a,l,c,d;if(C(this,Cn)&&this.hasListeners()){const f=C(this,rr).variables,y=C(this,rr).context;(n==null?void 0:n.type)==="success"?((s=(r=C(this,Cn)).onSuccess)==null||s.call(r,n.data,f,y),(i=(o=C(this,Cn)).onSettled)==null||i.call(o,n.data,null,f,y)):(n==null?void 0:n.type)==="error"&&((l=(a=C(this,Cn)).onError)==null||l.call(a,n.error,f,y),(d=(c=C(this,Cn)).onSettled)==null||d.call(c,void 0,n.error,f,y))}this.listeners.forEach(f=>{f(C(this,rr))})})},zm),uv=g.createContext(void 0),Tr=e=>{const t=g.useContext(uv);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},$S=({client:e,children:t})=>(g.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),u.jsx(uv.Provider,{value:e,children:t})),dv=g.createContext(!1),US=()=>g.useContext(dv);dv.Provider;function BS(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var VS=g.createContext(BS()),HS=()=>g.useContext(VS),WS=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},QS=e=>{g.useEffect(()=>{e.clearReset()},[e])},ZS=({result:e,errorResetBoundary:t,throwOnError:n,query:r,suspense:s})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(s&&e.data===void 0||nv(n,[e.error,r])),KS=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),n=e.staleTime;e.staleTime=typeof n=="function"?(...r)=>t(n(...r)):t(n),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},qS=(e,t)=>e.isLoading&&e.isFetching&&!t,GS=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,jp=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function YS(e,t,n){var f,y,x,k,m;const r=US(),s=HS(),o=Tr(),i=o.defaultQueryOptions(e);(y=(f=o.getDefaultOptions().queries)==null?void 0:f._experimental_beforeQuery)==null||y.call(f,i),i._optimisticResults=r?"isRestoring":"optimistic",KS(i),WS(i,s),QS(s);const a=!o.getQueryCache().get(i.queryHash),[l]=g.useState(()=>new t(o,i)),c=l.getOptimisticResult(i),d=!r&&e.subscribed!==!1;if(g.useSyncExternalStore(g.useCallback(w=>{const p=d?l.subscribe(Be.batchCalls(w)):ct;return l.updateResult(),p},[l,d]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),g.useEffect(()=>{l.setOptions(i)},[i,l]),GS(i,c))throw jp(i,l,s);if(ZS({result:c,errorResetBoundary:s,throwOnError:i.throwOnError,query:o.getQueryCache().get(i.queryHash),suspense:i.suspense}))throw c.error;if((k=(x=o.getDefaultOptions().queries)==null?void 0:x._experimental_afterQuery)==null||k.call(x,i,c),i.experimental_prefetchInRender&&!es&&qS(c,r)){const w=a?jp(i,l,s):(m=o.getQueryCache().get(i.queryHash))==null?void 0:m.promise;w==null||w.catch(ct).finally(()=>{l.updateResult()})}return i.notifyOnChangeProps?c:l.trackResult(c)}function Ef(e,t){return YS(e,MS)}function Ai(e,t){const n=Tr(),[r]=g.useState(()=>new zS(n,e));g.useEffect(()=>{r.setOptions(e)},[r,e]);const s=g.useSyncExternalStore(g.useCallback(i=>r.subscribe(Be.batchCalls(i)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),o=g.useCallback((i,a)=>{r.mutate(i,a).catch(ct)},[r]);if(s.error&&nv(r.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:o,mutateAsync:s.mutate}}var XS=function(){return null};const JS=new AS({defaultOptions:{queries:{staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}),xe={tasks:["tasks"],taskStats:["tasks","stats"]};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var ek={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tk=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),fe=(e,t)=>{const n=g.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:a="",children:l,...c},d)=>g.createElement("svg",{ref:d,...ek,width:s,height:s,stroke:r,strokeWidth:i?Number(o)*24/Number(s):o,className:["lucide",`lucide-${tk(e)}`,a].join(" "),...c},[...t.map(([f,y])=>g.createElement(f,y)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=fe("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _f=fe("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nk=fe("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=fe("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nf=fe("CheckCircle2",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fv=fe("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ju=fe("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hv=fe("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tf=fe("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pv=fe("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rk=fe("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jf=fe("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=fe("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sk=fe("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ok=fe("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ik=fe("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ol=fe("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ak=fe("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mv=fe("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lk=fe("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ck=fe("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uk=fe("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dk=fe("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rf=fe("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=fe("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ed=fe("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fk=fe("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hk=fe("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pk=fe("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mk=fe("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const io=fe("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rp=fe("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wr=fe("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const td=fe("Undo2",[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5v0a5.5 5.5 0 0 1-5.5 5.5H11",key:"llx8ln"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dn=fe("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function Ip(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function gv(...e){return t=>{let n=!1;const r=e.map(s=>{const o=Ip(s,t);return!n&&typeof o=="function"&&(n=!0),o});if(n)return()=>{for(let s=0;s<r.length;s++){const o=r[s];typeof o=="function"?o():Ip(e[s],null)}}}}function Ae(...e){return g.useCallback(gv(...e),e)}function ao(e){const t=yk(e),n=g.forwardRef((r,s)=>{const{children:o,...i}=r,a=g.Children.toArray(o),l=a.find(xk);if(l){const c=l.props.children,d=a.map(f=>f===l?g.Children.count(c)>1?g.Children.only(null):g.isValidElement(c)?c.props.children:null:f);return u.jsx(t,{...i,ref:s,children:g.isValidElement(c)?g.cloneElement(c,void 0,d):null})}return u.jsx(t,{...i,ref:s,children:o})});return n.displayName=`${e}.Slot`,n}var gk=ao("Slot");function yk(e){const t=g.forwardRef((n,r)=>{const{children:s,...o}=n;if(g.isValidElement(s)){const i=Sk(s),a=wk(o,s.props);return s.type!==g.Fragment&&(a.ref=r?gv(r,i):i),g.cloneElement(s,a)}return g.Children.count(s)>1?g.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var vk=Symbol("radix.slottable");function xk(e){return g.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===vk}function wk(e,t){const n={...t};for(const r in t){const s=e[r],o=t[r];/^on[A-Z]/.test(r)?s&&o?n[r]=(...a)=>{const l=o(...a);return s(...a),l}:s&&(n[r]=s):r==="style"?n[r]={...s,...o}:r==="className"&&(n[r]=[s,o].filter(Boolean).join(" "))}return{...e,...n}}function Sk(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function yv(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=yv(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function vv(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=yv(e))&&(r&&(r+=" "),r+=t);return r}const Pp=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,Dp=vv,kk=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return Dp(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:s,defaultVariants:o}=t,i=Object.keys(s).map(c=>{const d=n==null?void 0:n[c],f=o==null?void 0:o[c];if(d===null)return null;const y=Pp(d)||Pp(f);return s[c][y]}),a=n&&Object.entries(n).reduce((c,d)=>{let[f,y]=d;return y===void 0||(c[f]=y),c},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,d)=>{let{class:f,className:y,...x}=d;return Object.entries(x).every(k=>{let[m,w]=k;return Array.isArray(w)?w.includes({...o,...a}[m]):{...o,...a}[m]===w})?[...c,f,y]:c},[]);return Dp(e,i,l,n==null?void 0:n.class,n==null?void 0:n.className)},If="-",bk=e=>{const t=Ek(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const a=i.split(If);return a[0]===""&&a.length!==1&&a.shift(),xv(a,t)||Ck(i)},getConflictingClassGroupIds:(i,a)=>{const l=n[i]||[];return a&&r[i]?[...l,...r[i]]:l}}},xv=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?xv(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const o=e.join(If);return(i=t.validators.find(({validator:a})=>a(o)))==null?void 0:i.classGroupId},Op=/^\[(.+)\]$/,Ck=e=>{if(Op.test(e)){const t=Op.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Ek=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Nk(Object.entries(e.classGroups),n).forEach(([o,i])=>{nd(i,r,o,t)}),r},nd=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const o=s===""?t:Ap(t,s);o.classGroupId=n;return}if(typeof s=="function"){if(_k(s)){nd(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([o,i])=>{nd(i,Ap(t,o),n,r)})})},Ap=(e,t)=>{let n=e;return t.split(If).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},_k=e=>e.isThemeGetter,Nk=(e,t)=>t?e.map(([n,r])=>{const s=r.map(o=>typeof o=="string"?t+o:typeof o=="object"?Object.fromEntries(Object.entries(o).map(([i,a])=>[t+i,a])):o);return[n,s]}):e,Tk=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(o,i)=>{n.set(o,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(o){let i=n.get(o);if(i!==void 0)return i;if((i=r.get(o))!==void 0)return s(o,i),i},set(o,i){n.has(o)?n.set(o,i):s(o,i)}}},wv="!",jk=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,s=t[0],o=t.length,i=a=>{const l=[];let c=0,d=0,f;for(let w=0;w<a.length;w++){let p=a[w];if(c===0){if(p===s&&(r||a.slice(w,w+o)===t)){l.push(a.slice(d,w)),d=w+o;continue}if(p==="/"){f=w;continue}}p==="["?c++:p==="]"&&c--}const y=l.length===0?a:a.substring(d),x=y.startsWith(wv),k=x?y.substring(1):y,m=f&&f>d?f-d:void 0;return{modifiers:l,hasImportantModifier:x,baseClassName:k,maybePostfixModifierPosition:m}};return n?a=>n({className:a,parseClassName:i}):i},Rk=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Ik=e=>({cache:Tk(e.cacheSize),parseClassName:jk(e),...bk(e)}),Pk=/\s+/,Dk=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s}=t,o=[],i=e.trim().split(Pk);let a="";for(let l=i.length-1;l>=0;l-=1){const c=i[l],{modifiers:d,hasImportantModifier:f,baseClassName:y,maybePostfixModifierPosition:x}=n(c);let k=!!x,m=r(k?y.substring(0,x):y);if(!m){if(!k){a=c+(a.length>0?" "+a:a);continue}if(m=r(y),!m){a=c+(a.length>0?" "+a:a);continue}k=!1}const w=Rk(d).join(":"),p=f?w+wv:w,h=p+m;if(o.includes(h))continue;o.push(h);const v=s(m,k);for(let S=0;S<v.length;++S){const b=v[S];o.push(p+b)}a=c+(a.length>0?" "+a:a)}return a};function Ok(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Sv(t))&&(r&&(r+=" "),r+=n);return r}const Sv=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Sv(e[r]))&&(n&&(n+=" "),n+=t);return n};function Ak(e,...t){let n,r,s,o=i;function i(l){const c=t.reduce((d,f)=>f(d),e());return n=Ik(c),r=n.cache.get,s=n.cache.set,o=a,a(l)}function a(l){const c=r(l);if(c)return c;const d=Dk(l,n);return s(l,d),d}return function(){return o(Ok.apply(null,arguments))}}const Ne=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},kv=/^\[(?:([a-z-]+):)?(.+)\]$/i,Mk=/^\d+\/\d+$/,Lk=new Set(["px","full","screen"]),Fk=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,zk=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,$k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Uk=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Bk=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,xn=e=>Ls(e)||Lk.has(e)||Mk.test(e),zn=e=>So(e,"length",Gk),Ls=e=>!!e&&!Number.isNaN(Number(e)),Pc=e=>So(e,"number",Ls),Mo=e=>!!e&&Number.isInteger(Number(e)),Vk=e=>e.endsWith("%")&&Ls(e.slice(0,-1)),re=e=>kv.test(e),$n=e=>Fk.test(e),Hk=new Set(["length","size","percentage"]),Wk=e=>So(e,Hk,bv),Qk=e=>So(e,"position",bv),Zk=new Set(["image","url"]),Kk=e=>So(e,Zk,Xk),qk=e=>So(e,"",Yk),Lo=()=>!0,So=(e,t,n)=>{const r=kv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},Gk=e=>zk.test(e)&&!$k.test(e),bv=()=>!1,Yk=e=>Uk.test(e),Xk=e=>Bk.test(e),Jk=()=>{const e=Ne("colors"),t=Ne("spacing"),n=Ne("blur"),r=Ne("brightness"),s=Ne("borderColor"),o=Ne("borderRadius"),i=Ne("borderSpacing"),a=Ne("borderWidth"),l=Ne("contrast"),c=Ne("grayscale"),d=Ne("hueRotate"),f=Ne("invert"),y=Ne("gap"),x=Ne("gradientColorStops"),k=Ne("gradientColorStopPositions"),m=Ne("inset"),w=Ne("margin"),p=Ne("opacity"),h=Ne("padding"),v=Ne("saturate"),S=Ne("scale"),b=Ne("sepia"),E=Ne("skew"),_=Ne("space"),N=Ne("translate"),j=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],L=()=>["auto",re,t],A=()=>[re,t],Z=()=>["",xn,zn],J=()=>["auto",Ls,re],ae=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],K=()=>["solid","dashed","dotted","double","none"],ee=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],T=()=>["start","end","center","between","around","evenly","stretch"],D=()=>["","0",re],$=()=>["auto","avoid","all","avoid-page","page","left","right","column"],O=()=>[Ls,re];return{cacheSize:500,separator:":",theme:{colors:[Lo],spacing:[xn,zn],blur:["none","",$n,re],brightness:O(),borderColor:[e],borderRadius:["none","","full",$n,re],borderSpacing:A(),borderWidth:Z(),contrast:O(),grayscale:D(),hueRotate:O(),invert:D(),gap:A(),gradientColorStops:[e],gradientColorStopPositions:[Vk,zn],inset:L(),margin:L(),opacity:O(),padding:A(),saturate:O(),scale:O(),sepia:D(),skew:O(),space:A(),translate:A()},classGroups:{aspect:[{aspect:["auto","square","video",re]}],container:["container"],columns:[{columns:[$n]}],"break-after":[{"break-after":$()}],"break-before":[{"break-before":$()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...ae(),re]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:j()}],"overscroll-x":[{"overscroll-x":j()}],"overscroll-y":[{"overscroll-y":j()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Mo,re]}],basis:[{basis:L()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",re]}],grow:[{grow:D()}],shrink:[{shrink:D()}],order:[{order:["first","last","none",Mo,re]}],"grid-cols":[{"grid-cols":[Lo]}],"col-start-end":[{col:["auto",{span:["full",Mo,re]},re]}],"col-start":[{"col-start":J()}],"col-end":[{"col-end":J()}],"grid-rows":[{"grid-rows":[Lo]}],"row-start-end":[{row:["auto",{span:[Mo,re]},re]}],"row-start":[{"row-start":J()}],"row-end":[{"row-end":J()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",re]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",re]}],gap:[{gap:[y]}],"gap-x":[{"gap-x":[y]}],"gap-y":[{"gap-y":[y]}],"justify-content":[{justify:["normal",...T()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...T(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...T(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[w]}],mx:[{mx:[w]}],my:[{my:[w]}],ms:[{ms:[w]}],me:[{me:[w]}],mt:[{mt:[w]}],mr:[{mr:[w]}],mb:[{mb:[w]}],ml:[{ml:[w]}],"space-x":[{"space-x":[_]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[_]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",re,t]}],"min-w":[{"min-w":[re,t,"min","max","fit"]}],"max-w":[{"max-w":[re,t,"none","full","min","max","fit","prose",{screen:[$n]},$n]}],h:[{h:[re,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[re,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[re,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[re,t,"auto","min","max","fit"]}],"font-size":[{text:["base",$n,zn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Pc]}],"font-family":[{font:[Lo]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",re]}],"line-clamp":[{"line-clamp":["none",Ls,Pc]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",xn,re]}],"list-image":[{"list-image":["none",re]}],"list-style-type":[{list:["none","disc","decimal",re]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[p]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[p]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...K(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",xn,zn]}],"underline-offset":[{"underline-offset":["auto",xn,re]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:A()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",re]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",re]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[p]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...ae(),Qk]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Wk]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Kk]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[k]}],"gradient-via-pos":[{via:[k]}],"gradient-to-pos":[{to:[k]}],"gradient-from":[{from:[x]}],"gradient-via":[{via:[x]}],"gradient-to":[{to:[x]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[p]}],"border-style":[{border:[...K(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[p]}],"divide-style":[{divide:K()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...K()]}],"outline-offset":[{"outline-offset":[xn,re]}],"outline-w":[{outline:[xn,zn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[p]}],"ring-offset-w":[{"ring-offset":[xn,zn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",$n,qk]}],"shadow-color":[{shadow:[Lo]}],opacity:[{opacity:[p]}],"mix-blend":[{"mix-blend":[...ee(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":ee()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",$n,re]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[f]}],saturate:[{saturate:[v]}],sepia:[{sepia:[b]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[p]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[b]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",re]}],duration:[{duration:O()}],ease:[{ease:["linear","in","out","in-out",re]}],delay:[{delay:O()}],animate:[{animate:["none","spin","ping","pulse","bounce",re]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[Mo,re]}],"translate-x":[{"translate-x":[N]}],"translate-y":[{"translate-y":[N]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",re]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",re]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":A()}],"scroll-mx":[{"scroll-mx":A()}],"scroll-my":[{"scroll-my":A()}],"scroll-ms":[{"scroll-ms":A()}],"scroll-me":[{"scroll-me":A()}],"scroll-mt":[{"scroll-mt":A()}],"scroll-mr":[{"scroll-mr":A()}],"scroll-mb":[{"scroll-mb":A()}],"scroll-ml":[{"scroll-ml":A()}],"scroll-p":[{"scroll-p":A()}],"scroll-px":[{"scroll-px":A()}],"scroll-py":[{"scroll-py":A()}],"scroll-ps":[{"scroll-ps":A()}],"scroll-pe":[{"scroll-pe":A()}],"scroll-pt":[{"scroll-pt":A()}],"scroll-pr":[{"scroll-pr":A()}],"scroll-pb":[{"scroll-pb":A()}],"scroll-pl":[{"scroll-pl":A()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",re]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[xn,zn,Pc]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},eb=Ak(Jk);function B(...e){return eb(vv(e))}function Cv(e){const t=new Date(e),r=e-new Date().getTime(),s=Math.ceil(r/(1e3*60*60*24));return s<0?`逾期 ${Math.abs(s)} 天`:s===0?"今天到期":s===1?"明天到期":s<7?`${s}天后到期`:t.toLocaleDateString("zh-CN",{month:"short",day:"numeric"})}function Pf(e){return e?e<Date.now():!1}const tb=kk("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),X=g.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...s},o)=>{const i=r?gk:"button";return u.jsx(i,{className:B(tb({variant:t,size:n,className:e})),ref:o,...s})});X.displayName="Button";const gn=g.forwardRef(({className:e,type:t,...n},r)=>u.jsx("input",{type:t,className:B("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));gn.displayName="Input";var ve;(function(e){e.assertEqual=s=>{};function t(s){}e.assertIs=t;function n(s){throw new Error}e.assertNever=n,e.arrayToEnum=s=>{const o={};for(const i of s)o[i]=i;return o},e.getValidEnumValues=s=>{const o=e.objectKeys(s).filter(a=>typeof s[s[a]]!="number"),i={};for(const a of o)i[a]=s[a];return e.objectValues(i)},e.objectValues=s=>e.objectKeys(s).map(function(o){return s[o]}),e.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{const o=[];for(const i in s)Object.prototype.hasOwnProperty.call(s,i)&&o.push(i);return o},e.find=(s,o)=>{for(const i of s)if(o(i))return i},e.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&Number.isFinite(s)&&Math.floor(s)===s;function r(s,o=" | "){return s.map(i=>typeof i=="string"?`'${i}'`:i).join(o)}e.joinValues=r,e.jsonStringifyReplacer=(s,o)=>typeof o=="bigint"?o.toString():o})(ve||(ve={}));var Mp;(function(e){e.mergeShapes=(t,n)=>({...t,...n})})(Mp||(Mp={}));const W=ve.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Wn=e=>{switch(typeof e){case"undefined":return W.undefined;case"string":return W.string;case"number":return Number.isNaN(e)?W.nan:W.number;case"boolean":return W.boolean;case"function":return W.function;case"bigint":return W.bigint;case"symbol":return W.symbol;case"object":return Array.isArray(e)?W.array:e===null?W.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?W.promise:typeof Map<"u"&&e instanceof Map?W.map:typeof Set<"u"&&e instanceof Set?W.set:typeof Date<"u"&&e instanceof Date?W.date:W.object;default:return W.unknown}},P=ve.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class On extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(o){return o.message},r={_errors:[]},s=o=>{for(const i of o.issues)if(i.code==="invalid_union")i.unionErrors.map(s);else if(i.code==="invalid_return_type")s(i.returnTypeError);else if(i.code==="invalid_arguments")s(i.argumentsError);else if(i.path.length===0)r._errors.push(n(i));else{let a=r,l=0;for(;l<i.path.length;){const c=i.path[l];l===i.path.length-1?(a[c]=a[c]||{_errors:[]},a[c]._errors.push(n(i))):a[c]=a[c]||{_errors:[]},a=a[c],l++}}};return s(this),r}static assert(t){if(!(t instanceof On))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ve.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const s of this.issues)s.path.length>0?(n[s.path[0]]=n[s.path[0]]||[],n[s.path[0]].push(t(s))):r.push(t(s));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}On.create=e=>new On(e);const rd=(e,t)=>{let n;switch(e.code){case P.invalid_type:e.received===W.undefined?n="Required":n=`Expected ${e.expected}, received ${e.received}`;break;case P.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,ve.jsonStringifyReplacer)}`;break;case P.unrecognized_keys:n=`Unrecognized key(s) in object: ${ve.joinValues(e.keys,", ")}`;break;case P.invalid_union:n="Invalid input";break;case P.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${ve.joinValues(e.options)}`;break;case P.invalid_enum_value:n=`Invalid enum value. Expected ${ve.joinValues(e.options)}, received '${e.received}'`;break;case P.invalid_arguments:n="Invalid function arguments";break;case P.invalid_return_type:n="Invalid function return type";break;case P.invalid_date:n="Invalid date";break;case P.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:ve.assertNever(e.validation):e.validation!=="regex"?n=`Invalid ${e.validation}`:n="Invalid";break;case P.too_small:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:n="Invalid input";break;case P.too_big:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?n=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:n="Invalid input";break;case P.custom:n="Invalid input";break;case P.invalid_intersection_types:n="Intersection results could not be merged";break;case P.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case P.not_finite:n="Number must be finite";break;default:n=t.defaultError,ve.assertNever(e)}return{message:n}};let nb=rd;function rb(){return nb}const sb=e=>{const{data:t,path:n,errorMaps:r,issueData:s}=e,o=[...n,...s.path||[]],i={...s,path:o};if(s.message!==void 0)return{...s,path:o,message:s.message};let a="";const l=r.filter(c=>!!c).slice().reverse();for(const c of l)a=c(i,{data:t,defaultError:a}).message;return{...s,path:o,message:a}};function z(e,t){const n=rb(),r=sb({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===rd?void 0:rd].filter(s=>!!s)});e.common.issues.push(r)}class Nt{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const s of n){if(s.status==="aborted")return te;s.status==="dirty"&&t.dirty(),r.push(s.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const s of n){const o=await s.key,i=await s.value;r.push({key:o,value:i})}return Nt.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const s of n){const{key:o,value:i}=s;if(o.status==="aborted"||i.status==="aborted")return te;o.status==="dirty"&&t.dirty(),i.status==="dirty"&&t.dirty(),o.value!=="__proto__"&&(typeof i.value<"u"||s.alwaysSet)&&(r[o.value]=i.value)}return{status:t.value,value:r}}}const te=Object.freeze({status:"aborted"}),Vo=e=>({status:"dirty",value:e}),$t=e=>({status:"valid",value:e}),Lp=e=>e.status==="aborted",Fp=e=>e.status==="dirty",lo=e=>e.status==="valid",il=e=>typeof Promise<"u"&&e instanceof Promise;var Q;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(Q||(Q={}));class Sr{constructor(t,n,r,s){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const zp=(e,t)=>{if(lo(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new On(e.common.issues);return this._error=n,this._error}}};function ce(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:s}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(i,a)=>{const{message:l}=e;return i.code==="invalid_enum_value"?{message:l??a.defaultError}:typeof a.data>"u"?{message:l??r??a.defaultError}:i.code!=="invalid_type"?{message:a.defaultError}:{message:l??n??a.defaultError}},description:s}}class ye{get description(){return this._def.description}_getType(t){return Wn(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:Wn(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new Nt,ctx:{common:t.parent.common,data:t.data,parsedType:Wn(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if(il(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){const r={common:{issues:[],async:(n==null?void 0:n.async)??!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Wn(t)},s=this._parseSync({data:t,path:r.path,parent:r});return zp(r,s)}"~validate"(t){var r,s;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Wn(t)};if(!this["~standard"].async)try{const o=this._parseSync({data:t,path:[],parent:n});return lo(o)?{value:o.value}:{issues:n.common.issues}}catch(o){(s=(r=o==null?void 0:o.message)==null?void 0:r.toLowerCase())!=null&&s.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:n}).then(o=>lo(o)?{value:o.value}:{issues:n.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:Wn(t)},s=this._parse({data:t,path:r.path,parent:r}),o=await(il(s)?s:Promise.resolve(s));return zp(r,o)}refine(t,n){const r=s=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(s):n;return this._refinement((s,o)=>{const i=t(s),a=()=>o.addIssue({code:P.custom,...r(s)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(a(),!1)):i?!0:(a(),!1)})}refinement(t,n){return this._refinement((r,s)=>t(r)?!0:(s.addIssue(typeof n=="function"?n(r,s):n),!1))}_refinement(t){return new fo({schema:this,typeName:ne.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return gr.create(this,this._def)}nullable(){return ho.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return pn.create(this)}promise(){return ul.create(this,this._def)}or(t){return ll.create([this,t],this._def)}and(t){return cl.create(this,t,this._def)}transform(t){return new fo({...ce(this._def),schema:this,typeName:ne.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new ad({...ce(this._def),innerType:this,defaultValue:n,typeName:ne.ZodDefault})}brand(){return new Nb({typeName:ne.ZodBranded,type:this,...ce(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new ld({...ce(this._def),innerType:this,catchValue:n,typeName:ne.ZodCatch})}describe(t){const n=this.constructor;return new n({...this._def,description:t})}pipe(t){return Df.create(this,t)}readonly(){return cd.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const ob=/^c[^\s-]{8,}$/i,ib=/^[0-9a-z]+$/,ab=/^[0-9A-HJKMNP-TV-Z]{26}$/i,lb=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,cb=/^[a-z0-9_-]{21}$/i,ub=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,db=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,fb=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,hb="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Dc;const pb=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,mb=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,gb=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,yb=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,vb=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,xb=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ev="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",wb=new RegExp(`^${Ev}$`);function _v(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`);const n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}function Sb(e){return new RegExp(`^${_v(e)}$`)}function kb(e){let t=`${Ev}T${_v(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function bb(e,t){return!!((t==="v4"||!t)&&pb.test(e)||(t==="v6"||!t)&&gb.test(e))}function Cb(e,t){if(!ub.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),s=JSON.parse(atob(r));return!(typeof s!="object"||s===null||"typ"in s&&(s==null?void 0:s.typ)!=="JWT"||!s.alg||t&&s.alg!==t)}catch{return!1}}function Eb(e,t){return!!((t==="v4"||!t)&&mb.test(e)||(t==="v6"||!t)&&yb.test(e))}class ir extends ye{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==W.string){const o=this._getOrReturnCtx(t);return z(o,{code:P.invalid_type,expected:W.string,received:o.parsedType}),te}const r=new Nt;let s;for(const o of this._def.checks)if(o.kind==="min")t.data.length<o.value&&(s=this._getOrReturnCtx(t,s),z(s,{code:P.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),r.dirty());else if(o.kind==="max")t.data.length>o.value&&(s=this._getOrReturnCtx(t,s),z(s,{code:P.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!1,message:o.message}),r.dirty());else if(o.kind==="length"){const i=t.data.length>o.value,a=t.data.length<o.value;(i||a)&&(s=this._getOrReturnCtx(t,s),i?z(s,{code:P.too_big,maximum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}):a&&z(s,{code:P.too_small,minimum:o.value,type:"string",inclusive:!0,exact:!0,message:o.message}),r.dirty())}else if(o.kind==="email")fb.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"email",code:P.invalid_string,message:o.message}),r.dirty());else if(o.kind==="emoji")Dc||(Dc=new RegExp(hb,"u")),Dc.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"emoji",code:P.invalid_string,message:o.message}),r.dirty());else if(o.kind==="uuid")lb.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"uuid",code:P.invalid_string,message:o.message}),r.dirty());else if(o.kind==="nanoid")cb.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"nanoid",code:P.invalid_string,message:o.message}),r.dirty());else if(o.kind==="cuid")ob.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"cuid",code:P.invalid_string,message:o.message}),r.dirty());else if(o.kind==="cuid2")ib.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"cuid2",code:P.invalid_string,message:o.message}),r.dirty());else if(o.kind==="ulid")ab.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"ulid",code:P.invalid_string,message:o.message}),r.dirty());else if(o.kind==="url")try{new URL(t.data)}catch{s=this._getOrReturnCtx(t,s),z(s,{validation:"url",code:P.invalid_string,message:o.message}),r.dirty()}else o.kind==="regex"?(o.regex.lastIndex=0,o.regex.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"regex",code:P.invalid_string,message:o.message}),r.dirty())):o.kind==="trim"?t.data=t.data.trim():o.kind==="includes"?t.data.includes(o.value,o.position)||(s=this._getOrReturnCtx(t,s),z(s,{code:P.invalid_string,validation:{includes:o.value,position:o.position},message:o.message}),r.dirty()):o.kind==="toLowerCase"?t.data=t.data.toLowerCase():o.kind==="toUpperCase"?t.data=t.data.toUpperCase():o.kind==="startsWith"?t.data.startsWith(o.value)||(s=this._getOrReturnCtx(t,s),z(s,{code:P.invalid_string,validation:{startsWith:o.value},message:o.message}),r.dirty()):o.kind==="endsWith"?t.data.endsWith(o.value)||(s=this._getOrReturnCtx(t,s),z(s,{code:P.invalid_string,validation:{endsWith:o.value},message:o.message}),r.dirty()):o.kind==="datetime"?kb(o).test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{code:P.invalid_string,validation:"datetime",message:o.message}),r.dirty()):o.kind==="date"?wb.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{code:P.invalid_string,validation:"date",message:o.message}),r.dirty()):o.kind==="time"?Sb(o).test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{code:P.invalid_string,validation:"time",message:o.message}),r.dirty()):o.kind==="duration"?db.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"duration",code:P.invalid_string,message:o.message}),r.dirty()):o.kind==="ip"?bb(t.data,o.version)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"ip",code:P.invalid_string,message:o.message}),r.dirty()):o.kind==="jwt"?Cb(t.data,o.alg)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"jwt",code:P.invalid_string,message:o.message}),r.dirty()):o.kind==="cidr"?Eb(t.data,o.version)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"cidr",code:P.invalid_string,message:o.message}),r.dirty()):o.kind==="base64"?vb.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"base64",code:P.invalid_string,message:o.message}),r.dirty()):o.kind==="base64url"?xb.test(t.data)||(s=this._getOrReturnCtx(t,s),z(s,{validation:"base64url",code:P.invalid_string,message:o.message}),r.dirty()):ve.assertNever(o);return{status:r.value,value:t.data}}_regex(t,n,r){return this.refinement(s=>t.test(s),{validation:n,code:P.invalid_string,...Q.errToObj(r)})}_addCheck(t){return new ir({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...Q.errToObj(t)})}url(t){return this._addCheck({kind:"url",...Q.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...Q.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...Q.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...Q.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...Q.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...Q.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...Q.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...Q.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...Q.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...Q.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...Q.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...Q.errToObj(t)})}datetime(t){return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(t==null?void 0:t.offset)??!1,local:(t==null?void 0:t.local)??!1,...Q.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...Q.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...Q.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...Q.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...Q.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...Q.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...Q.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...Q.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...Q.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...Q.errToObj(n)})}nonempty(t){return this.min(1,Q.errToObj(t))}trim(){return new ir({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new ir({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new ir({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}ir.create=e=>new ir({checks:[],typeName:ne.ZodString,coerce:(e==null?void 0:e.coerce)??!1,...ce(e)});function _b(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=n>r?n:r,o=Number.parseInt(e.toFixed(s).replace(".","")),i=Number.parseInt(t.toFixed(s).replace(".",""));return o%i/10**s}class co extends ye{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==W.number){const o=this._getOrReturnCtx(t);return z(o,{code:P.invalid_type,expected:W.number,received:o.parsedType}),te}let r;const s=new Nt;for(const o of this._def.checks)o.kind==="int"?ve.isInteger(t.data)||(r=this._getOrReturnCtx(t,r),z(r,{code:P.invalid_type,expected:"integer",received:"float",message:o.message}),s.dirty()):o.kind==="min"?(o.inclusive?t.data<o.value:t.data<=o.value)&&(r=this._getOrReturnCtx(t,r),z(r,{code:P.too_small,minimum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),s.dirty()):o.kind==="max"?(o.inclusive?t.data>o.value:t.data>=o.value)&&(r=this._getOrReturnCtx(t,r),z(r,{code:P.too_big,maximum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),s.dirty()):o.kind==="multipleOf"?_b(t.data,o.value)!==0&&(r=this._getOrReturnCtx(t,r),z(r,{code:P.not_multiple_of,multipleOf:o.value,message:o.message}),s.dirty()):o.kind==="finite"?Number.isFinite(t.data)||(r=this._getOrReturnCtx(t,r),z(r,{code:P.not_finite,message:o.message}),s.dirty()):ve.assertNever(o);return{status:s.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,Q.toString(n))}gt(t,n){return this.setLimit("min",t,!1,Q.toString(n))}lte(t,n){return this.setLimit("max",t,!0,Q.toString(n))}lt(t,n){return this.setLimit("max",t,!1,Q.toString(n))}setLimit(t,n,r,s){return new co({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:Q.toString(s)}]})}_addCheck(t){return new co({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:Q.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:Q.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:Q.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:Q.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:Q.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:Q.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:Q.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Q.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Q.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&ve.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}co.create=e=>new co({checks:[],typeName:ne.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...ce(e)});class wi extends ye{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==W.bigint)return this._getInvalidInput(t);let r;const s=new Nt;for(const o of this._def.checks)o.kind==="min"?(o.inclusive?t.data<o.value:t.data<=o.value)&&(r=this._getOrReturnCtx(t,r),z(r,{code:P.too_small,type:"bigint",minimum:o.value,inclusive:o.inclusive,message:o.message}),s.dirty()):o.kind==="max"?(o.inclusive?t.data>o.value:t.data>=o.value)&&(r=this._getOrReturnCtx(t,r),z(r,{code:P.too_big,type:"bigint",maximum:o.value,inclusive:o.inclusive,message:o.message}),s.dirty()):o.kind==="multipleOf"?t.data%o.value!==BigInt(0)&&(r=this._getOrReturnCtx(t,r),z(r,{code:P.not_multiple_of,multipleOf:o.value,message:o.message}),s.dirty()):ve.assertNever(o);return{status:s.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return z(n,{code:P.invalid_type,expected:W.bigint,received:n.parsedType}),te}gte(t,n){return this.setLimit("min",t,!0,Q.toString(n))}gt(t,n){return this.setLimit("min",t,!1,Q.toString(n))}lte(t,n){return this.setLimit("max",t,!0,Q.toString(n))}lt(t,n){return this.setLimit("max",t,!1,Q.toString(n))}setLimit(t,n,r,s){return new wi({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:Q.toString(s)}]})}_addCheck(t){return new wi({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:Q.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:Q.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:Q.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:Q.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:Q.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}wi.create=e=>new wi({checks:[],typeName:ne.ZodBigInt,coerce:(e==null?void 0:e.coerce)??!1,...ce(e)});class sd extends ye{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==W.boolean){const r=this._getOrReturnCtx(t);return z(r,{code:P.invalid_type,expected:W.boolean,received:r.parsedType}),te}return $t(t.data)}}sd.create=e=>new sd({typeName:ne.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...ce(e)});class al extends ye{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==W.date){const o=this._getOrReturnCtx(t);return z(o,{code:P.invalid_type,expected:W.date,received:o.parsedType}),te}if(Number.isNaN(t.data.getTime())){const o=this._getOrReturnCtx(t);return z(o,{code:P.invalid_date}),te}const r=new Nt;let s;for(const o of this._def.checks)o.kind==="min"?t.data.getTime()<o.value&&(s=this._getOrReturnCtx(t,s),z(s,{code:P.too_small,message:o.message,inclusive:!0,exact:!1,minimum:o.value,type:"date"}),r.dirty()):o.kind==="max"?t.data.getTime()>o.value&&(s=this._getOrReturnCtx(t,s),z(s,{code:P.too_big,message:o.message,inclusive:!0,exact:!1,maximum:o.value,type:"date"}),r.dirty()):ve.assertNever(o);return{status:r.value,value:new Date(t.data.getTime())}}_addCheck(t){return new al({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:Q.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:Q.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}al.create=e=>new al({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:ne.ZodDate,...ce(e)});class $p extends ye{_parse(t){if(this._getType(t)!==W.symbol){const r=this._getOrReturnCtx(t);return z(r,{code:P.invalid_type,expected:W.symbol,received:r.parsedType}),te}return $t(t.data)}}$p.create=e=>new $p({typeName:ne.ZodSymbol,...ce(e)});class Up extends ye{_parse(t){if(this._getType(t)!==W.undefined){const r=this._getOrReturnCtx(t);return z(r,{code:P.invalid_type,expected:W.undefined,received:r.parsedType}),te}return $t(t.data)}}Up.create=e=>new Up({typeName:ne.ZodUndefined,...ce(e)});class Bp extends ye{_parse(t){if(this._getType(t)!==W.null){const r=this._getOrReturnCtx(t);return z(r,{code:P.invalid_type,expected:W.null,received:r.parsedType}),te}return $t(t.data)}}Bp.create=e=>new Bp({typeName:ne.ZodNull,...ce(e)});class Vp extends ye{constructor(){super(...arguments),this._any=!0}_parse(t){return $t(t.data)}}Vp.create=e=>new Vp({typeName:ne.ZodAny,...ce(e)});class Hp extends ye{constructor(){super(...arguments),this._unknown=!0}_parse(t){return $t(t.data)}}Hp.create=e=>new Hp({typeName:ne.ZodUnknown,...ce(e)});class kr extends ye{_parse(t){const n=this._getOrReturnCtx(t);return z(n,{code:P.invalid_type,expected:W.never,received:n.parsedType}),te}}kr.create=e=>new kr({typeName:ne.ZodNever,...ce(e)});class Wp extends ye{_parse(t){if(this._getType(t)!==W.undefined){const r=this._getOrReturnCtx(t);return z(r,{code:P.invalid_type,expected:W.void,received:r.parsedType}),te}return $t(t.data)}}Wp.create=e=>new Wp({typeName:ne.ZodVoid,...ce(e)});class pn extends ye{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),s=this._def;if(n.parsedType!==W.array)return z(n,{code:P.invalid_type,expected:W.array,received:n.parsedType}),te;if(s.exactLength!==null){const i=n.data.length>s.exactLength.value,a=n.data.length<s.exactLength.value;(i||a)&&(z(n,{code:i?P.too_big:P.too_small,minimum:a?s.exactLength.value:void 0,maximum:i?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(s.minLength!==null&&n.data.length<s.minLength.value&&(z(n,{code:P.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),s.maxLength!==null&&n.data.length>s.maxLength.value&&(z(n,{code:P.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((i,a)=>s.type._parseAsync(new Sr(n,i,n.path,a)))).then(i=>Nt.mergeArray(r,i));const o=[...n.data].map((i,a)=>s.type._parseSync(new Sr(n,i,n.path,a)));return Nt.mergeArray(r,o)}get element(){return this._def.type}min(t,n){return new pn({...this._def,minLength:{value:t,message:Q.toString(n)}})}max(t,n){return new pn({...this._def,maxLength:{value:t,message:Q.toString(n)}})}length(t,n){return new pn({...this._def,exactLength:{value:t,message:Q.toString(n)}})}nonempty(t){return this.min(1,t)}}pn.create=(e,t)=>new pn({type:e,minLength:null,maxLength:null,exactLength:null,typeName:ne.ZodArray,...ce(t)});function ys(e){if(e instanceof ze){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=gr.create(ys(r))}return new ze({...e._def,shape:()=>t})}else return e instanceof pn?new pn({...e._def,type:ys(e.element)}):e instanceof gr?gr.create(ys(e.unwrap())):e instanceof ho?ho.create(ys(e.unwrap())):e instanceof ss?ss.create(e.items.map(t=>ys(t))):e}class ze extends ye{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=ve.objectKeys(t);return this._cached={shape:t,keys:n},this._cached}_parse(t){if(this._getType(t)!==W.object){const c=this._getOrReturnCtx(t);return z(c,{code:P.invalid_type,expected:W.object,received:c.parsedType}),te}const{status:r,ctx:s}=this._processInputParams(t),{shape:o,keys:i}=this._getCached(),a=[];if(!(this._def.catchall instanceof kr&&this._def.unknownKeys==="strip"))for(const c in s.data)i.includes(c)||a.push(c);const l=[];for(const c of i){const d=o[c],f=s.data[c];l.push({key:{status:"valid",value:c},value:d._parse(new Sr(s,f,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof kr){const c=this._def.unknownKeys;if(c==="passthrough")for(const d of a)l.push({key:{status:"valid",value:d},value:{status:"valid",value:s.data[d]}});else if(c==="strict")a.length>0&&(z(s,{code:P.unrecognized_keys,keys:a}),r.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const d of a){const f=s.data[d];l.push({key:{status:"valid",value:d},value:c._parse(new Sr(s,f,s.path,d)),alwaysSet:d in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const d of l){const f=await d.key,y=await d.value;c.push({key:f,value:y,alwaysSet:d.alwaysSet})}return c}).then(c=>Nt.mergeObjectSync(r,c)):Nt.mergeObjectSync(r,l)}get shape(){return this._def.shape()}strict(t){return Q.errToObj,new ze({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var o,i;const s=((i=(o=this._def).errorMap)==null?void 0:i.call(o,n,r).message)??r.defaultError;return n.code==="unrecognized_keys"?{message:Q.errToObj(t).message??s}:{message:s}}}:{}})}strip(){return new ze({...this._def,unknownKeys:"strip"})}passthrough(){return new ze({...this._def,unknownKeys:"passthrough"})}extend(t){return new ze({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new ze({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:ne.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new ze({...this._def,catchall:t})}pick(t){const n={};for(const r of ve.objectKeys(t))t[r]&&this.shape[r]&&(n[r]=this.shape[r]);return new ze({...this._def,shape:()=>n})}omit(t){const n={};for(const r of ve.objectKeys(this.shape))t[r]||(n[r]=this.shape[r]);return new ze({...this._def,shape:()=>n})}deepPartial(){return ys(this)}partial(t){const n={};for(const r of ve.objectKeys(this.shape)){const s=this.shape[r];t&&!t[r]?n[r]=s:n[r]=s.optional()}return new ze({...this._def,shape:()=>n})}required(t){const n={};for(const r of ve.objectKeys(this.shape))if(t&&!t[r])n[r]=this.shape[r];else{let o=this.shape[r];for(;o instanceof gr;)o=o._def.innerType;n[r]=o}return new ze({...this._def,shape:()=>n})}keyof(){return Nv(ve.objectKeys(this.shape))}}ze.create=(e,t)=>new ze({shape:()=>e,unknownKeys:"strip",catchall:kr.create(),typeName:ne.ZodObject,...ce(t)});ze.strictCreate=(e,t)=>new ze({shape:()=>e,unknownKeys:"strict",catchall:kr.create(),typeName:ne.ZodObject,...ce(t)});ze.lazycreate=(e,t)=>new ze({shape:e,unknownKeys:"strip",catchall:kr.create(),typeName:ne.ZodObject,...ce(t)});class ll extends ye{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;function s(o){for(const a of o)if(a.result.status==="valid")return a.result;for(const a of o)if(a.result.status==="dirty")return n.common.issues.push(...a.ctx.common.issues),a.result;const i=o.map(a=>new On(a.ctx.common.issues));return z(n,{code:P.invalid_union,unionErrors:i}),te}if(n.common.async)return Promise.all(r.map(async o=>{const i={...n,common:{...n.common,issues:[]},parent:null};return{result:await o._parseAsync({data:n.data,path:n.path,parent:i}),ctx:i}})).then(s);{let o;const i=[];for(const l of r){const c={...n,common:{...n.common,issues:[]},parent:null},d=l._parseSync({data:n.data,path:n.path,parent:c});if(d.status==="valid")return d;d.status==="dirty"&&!o&&(o={result:d,ctx:c}),c.common.issues.length&&i.push(c.common.issues)}if(o)return n.common.issues.push(...o.ctx.common.issues),o.result;const a=i.map(l=>new On(l));return z(n,{code:P.invalid_union,unionErrors:a}),te}}get options(){return this._def.options}}ll.create=(e,t)=>new ll({options:e,typeName:ne.ZodUnion,...ce(t)});function od(e,t){const n=Wn(e),r=Wn(t);if(e===t)return{valid:!0,data:e};if(n===W.object&&r===W.object){const s=ve.objectKeys(t),o=ve.objectKeys(e).filter(a=>s.indexOf(a)!==-1),i={...e,...t};for(const a of o){const l=od(e[a],t[a]);if(!l.valid)return{valid:!1};i[a]=l.data}return{valid:!0,data:i}}else if(n===W.array&&r===W.array){if(e.length!==t.length)return{valid:!1};const s=[];for(let o=0;o<e.length;o++){const i=e[o],a=t[o],l=od(i,a);if(!l.valid)return{valid:!1};s.push(l.data)}return{valid:!0,data:s}}else return n===W.date&&r===W.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class cl extends ye{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=(o,i)=>{if(Lp(o)||Lp(i))return te;const a=od(o.value,i.value);return a.valid?((Fp(o)||Fp(i))&&n.dirty(),{status:n.value,value:a.data}):(z(r,{code:P.invalid_intersection_types}),te)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([o,i])=>s(o,i)):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}cl.create=(e,t,n)=>new cl({left:e,right:t,typeName:ne.ZodIntersection,...ce(n)});class ss extends ye{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==W.array)return z(r,{code:P.invalid_type,expected:W.array,received:r.parsedType}),te;if(r.data.length<this._def.items.length)return z(r,{code:P.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),te;!this._def.rest&&r.data.length>this._def.items.length&&(z(r,{code:P.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const o=[...r.data].map((i,a)=>{const l=this._def.items[a]||this._def.rest;return l?l._parse(new Sr(r,i,r.path,a)):null}).filter(i=>!!i);return r.common.async?Promise.all(o).then(i=>Nt.mergeArray(n,i)):Nt.mergeArray(n,o)}get items(){return this._def.items}rest(t){return new ss({...this._def,rest:t})}}ss.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ss({items:e,typeName:ne.ZodTuple,rest:null,...ce(t)})};class Qp extends ye{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==W.map)return z(r,{code:P.invalid_type,expected:W.map,received:r.parsedType}),te;const s=this._def.keyType,o=this._def.valueType,i=[...r.data.entries()].map(([a,l],c)=>({key:s._parse(new Sr(r,a,r.path,[c,"key"])),value:o._parse(new Sr(r,l,r.path,[c,"value"]))}));if(r.common.async){const a=new Map;return Promise.resolve().then(async()=>{for(const l of i){const c=await l.key,d=await l.value;if(c.status==="aborted"||d.status==="aborted")return te;(c.status==="dirty"||d.status==="dirty")&&n.dirty(),a.set(c.value,d.value)}return{status:n.value,value:a}})}else{const a=new Map;for(const l of i){const c=l.key,d=l.value;if(c.status==="aborted"||d.status==="aborted")return te;(c.status==="dirty"||d.status==="dirty")&&n.dirty(),a.set(c.value,d.value)}return{status:n.value,value:a}}}}Qp.create=(e,t,n)=>new Qp({valueType:t,keyType:e,typeName:ne.ZodMap,...ce(n)});class Si extends ye{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==W.set)return z(r,{code:P.invalid_type,expected:W.set,received:r.parsedType}),te;const s=this._def;s.minSize!==null&&r.data.size<s.minSize.value&&(z(r,{code:P.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),n.dirty()),s.maxSize!==null&&r.data.size>s.maxSize.value&&(z(r,{code:P.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),n.dirty());const o=this._def.valueType;function i(l){const c=new Set;for(const d of l){if(d.status==="aborted")return te;d.status==="dirty"&&n.dirty(),c.add(d.value)}return{status:n.value,value:c}}const a=[...r.data.values()].map((l,c)=>o._parse(new Sr(r,l,r.path,c)));return r.common.async?Promise.all(a).then(l=>i(l)):i(a)}min(t,n){return new Si({...this._def,minSize:{value:t,message:Q.toString(n)}})}max(t,n){return new Si({...this._def,maxSize:{value:t,message:Q.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}Si.create=(e,t)=>new Si({valueType:e,minSize:null,maxSize:null,typeName:ne.ZodSet,...ce(t)});class id extends ye{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}id.create=(e,t)=>new id({getter:e,typeName:ne.ZodLazy,...ce(t)});class Zp extends ye{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return z(n,{received:n.data,code:P.invalid_literal,expected:this._def.value}),te}return{status:"valid",value:t.data}}get value(){return this._def.value}}Zp.create=(e,t)=>new Zp({value:e,typeName:ne.ZodLiteral,...ce(t)});function Nv(e,t){return new uo({values:e,typeName:ne.ZodEnum,...ce(t)})}class uo extends ye{_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return z(n,{expected:ve.joinValues(r),received:n.parsedType,code:P.invalid_type}),te}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return z(n,{received:n.data,code:P.invalid_enum_value,options:r}),te}return $t(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return uo.create(t,{...this._def,...n})}exclude(t,n=this._def){return uo.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}uo.create=Nv;class Kp extends ye{_parse(t){const n=ve.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==W.string&&r.parsedType!==W.number){const s=ve.objectValues(n);return z(r,{expected:ve.joinValues(s),received:r.parsedType,code:P.invalid_type}),te}if(this._cache||(this._cache=new Set(ve.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const s=ve.objectValues(n);return z(r,{received:r.data,code:P.invalid_enum_value,options:s}),te}return $t(t.data)}get enum(){return this._def.values}}Kp.create=(e,t)=>new Kp({values:e,typeName:ne.ZodNativeEnum,...ce(t)});class ul extends ye{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==W.promise&&n.common.async===!1)return z(n,{code:P.invalid_type,expected:W.promise,received:n.parsedType}),te;const r=n.parsedType===W.promise?n.data:Promise.resolve(n.data);return $t(r.then(s=>this._def.type.parseAsync(s,{path:n.path,errorMap:n.common.contextualErrorMap})))}}ul.create=(e,t)=>new ul({type:e,typeName:ne.ZodPromise,...ce(t)});class fo extends ye{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ne.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=this._def.effect||null,o={addIssue:i=>{z(r,i),i.fatal?n.abort():n.dirty()},get path(){return r.path}};if(o.addIssue=o.addIssue.bind(o),s.type==="preprocess"){const i=s.transform(r.data,o);if(r.common.async)return Promise.resolve(i).then(async a=>{if(n.value==="aborted")return te;const l=await this._def.schema._parseAsync({data:a,path:r.path,parent:r});return l.status==="aborted"?te:l.status==="dirty"||n.value==="dirty"?Vo(l.value):l});{if(n.value==="aborted")return te;const a=this._def.schema._parseSync({data:i,path:r.path,parent:r});return a.status==="aborted"?te:a.status==="dirty"||n.value==="dirty"?Vo(a.value):a}}if(s.type==="refinement"){const i=a=>{const l=s.refinement(a,o);if(r.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(r.common.async===!1){const a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?te:(a.status==="dirty"&&n.dirty(),i(a.value),{status:n.value,value:a.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(a=>a.status==="aborted"?te:(a.status==="dirty"&&n.dirty(),i(a.value).then(()=>({status:n.value,value:a.value}))))}if(s.type==="transform")if(r.common.async===!1){const i=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!lo(i))return te;const a=s.transform(i.value,o);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:a}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(i=>lo(i)?Promise.resolve(s.transform(i.value,o)).then(a=>({status:n.value,value:a})):te);ve.assertNever(s)}}fo.create=(e,t,n)=>new fo({schema:e,typeName:ne.ZodEffects,effect:t,...ce(n)});fo.createWithPreprocess=(e,t,n)=>new fo({schema:t,effect:{type:"preprocess",transform:e},typeName:ne.ZodEffects,...ce(n)});class gr extends ye{_parse(t){return this._getType(t)===W.undefined?$t(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}gr.create=(e,t)=>new gr({innerType:e,typeName:ne.ZodOptional,...ce(t)});class ho extends ye{_parse(t){return this._getType(t)===W.null?$t(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}ho.create=(e,t)=>new ho({innerType:e,typeName:ne.ZodNullable,...ce(t)});class ad extends ye{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===W.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}ad.create=(e,t)=>new ad({innerType:e,typeName:ne.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...ce(t)});class ld extends ye{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return il(s)?s.then(o=>({status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new On(r.common.issues)},input:r.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new On(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}ld.create=(e,t)=>new ld({innerType:e,typeName:ne.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...ce(t)});class qp extends ye{_parse(t){if(this._getType(t)!==W.nan){const r=this._getOrReturnCtx(t);return z(r,{code:P.invalid_type,expected:W.nan,received:r.parsedType}),te}return{status:"valid",value:t.data}}}qp.create=e=>new qp({typeName:ne.ZodNaN,...ce(e)});class Nb extends ye{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class Df extends ye{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const o=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return o.status==="aborted"?te:o.status==="dirty"?(n.dirty(),Vo(o.value)):this._def.out._parseAsync({data:o.value,path:r.path,parent:r})})();{const s=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?te:s.status==="dirty"?(n.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:r.path,parent:r})}}static create(t,n){return new Df({in:t,out:n,typeName:ne.ZodPipeline})}}class cd extends ye{_parse(t){const n=this._def.innerType._parse(t),r=s=>(lo(s)&&(s.value=Object.freeze(s.value)),s);return il(n)?n.then(s=>r(s)):r(n)}unwrap(){return this._def.innerType}}cd.create=(e,t)=>new cd({innerType:e,typeName:ne.ZodReadonly,...ce(t)});var ne;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(ne||(ne={}));const Y=ir.create,Se=co.create,os=sd.create;kr.create;const dl=pn.create,Tt=ze.create;ll.create;cl.create;ss.create;const Tb=id.create,Mi=uo.create;ul.create;gr.create;ho.create;const jb=Tt({background:Y(),foreground:Y(),card:Y(),cardForeground:Y(),popover:Y(),popoverForeground:Y(),primary:Y(),primaryForeground:Y(),secondary:Y(),secondaryForeground:Y(),muted:Y(),mutedForeground:Y(),accent:Y(),accentForeground:Y(),destructive:Y(),destructiveForeground:Y(),border:Y(),input:Y(),ring:Y()});Tt({id:Y(),name:Y(),mode:Mi(["light","dark"]),colors:jb,isBuiltIn:os(),createdAt:Se()});Tt({mode:Mi(["light","dark","system"]),activeThemeId:Y(),autoSwitchEnabled:os(),autoSwitchTimes:Tt({lightModeStart:Y(),darkModeStart:Y()}),eyeCareMode:os(),transitionDuration:Se()});const fl=[{id:"light-default",name:"浅色默认",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"222.2 47.4% 11.2%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"210 40% 96%",accentForeground:"222.2 47.4% 11.2%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"222.2 84% 4.9%"},isBuiltIn:!0,createdAt:Date.now()},{id:"dark-default",name:"深色默认",mode:"dark",colors:{background:"222.2 84% 4.9%",foreground:"210 40% 98%",card:"222.2 84% 4.9%",cardForeground:"210 40% 98%",popover:"222.2 84% 4.9%",popoverForeground:"210 40% 98%",primary:"210 40% 98%",primaryForeground:"222.2 47.4% 11.2%",secondary:"217.2 32.6% 17.5%",secondaryForeground:"210 40% 98%",muted:"217.2 32.6% 17.5%",mutedForeground:"215 20.2% 65.1%",accent:"217.2 32.6% 17.5%",accentForeground:"210 40% 98%",destructive:"0 62.8% 30.6%",destructiveForeground:"210 40% 98%",border:"217.2 32.6% 17.5%",input:"217.2 32.6% 17.5%",ring:"212.7 26.8% 83.9%"},isBuiltIn:!0,createdAt:Date.now()},{id:"blue-theme",name:"蓝色主题",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"221.2 83.2% 53.3%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"221.2 83.2% 53.3%",accentForeground:"210 40% 98%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"221.2 83.2% 53.3%"},isBuiltIn:!0,createdAt:Date.now()},{id:"green-theme",name:"绿色主题",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"142.1 76.2% 36.3%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"142.1 76.2% 36.3%",accentForeground:"210 40% 98%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"142.1 76.2% 36.3%"},isBuiltIn:!0,createdAt:Date.now()},{id:"purple-theme",name:"紫色主题",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"262.1 83.3% 57.8%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"262.1 83.3% 57.8%",accentForeground:"210 40% 98%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"262.1 83.3% 57.8%"},isBuiltIn:!0,createdAt:Date.now()},{id:"eye-care",name:"护眼模式",mode:"light",colors:{background:"60 9.1% 97.8%",foreground:"24 9.8% 10%",card:"60 9.1% 97.8%",cardForeground:"24 9.8% 10%",popover:"60 9.1% 97.8%",popoverForeground:"24 9.8% 10%",primary:"25 95% 53%",primaryForeground:"60 9.1% 97.8%",secondary:"60 4.8% 95.9%",secondaryForeground:"24 9.8% 10%",muted:"60 4.8% 95.9%",mutedForeground:"25 5.3% 44.7%",accent:"60 4.8% 95.9%",accentForeground:"24 9.8% 10%",destructive:"0 84.2% 60.2%",destructiveForeground:"60 9.1% 97.8%",border:"20 5.9% 90%",input:"20 5.9% 90%",ring:"25 95% 53%"},isBuiltIn:!0,createdAt:Date.now()}],Gp={mode:"system",activeThemeId:"light-default",autoSwitchEnabled:!1,autoSwitchTimes:{lightModeStart:"06:00",darkModeStart:"18:00"},eyeCareMode:!1,transitionDuration:300},Rb=e=>fl.find(t=>t.id===e),Yp=e=>fl.filter(t=>t.mode===e),Tv=()=>typeof window<"u"&&window.electronAPI!==void 0,Ke=async(e,t)=>{if(!Tv()){if(console.warn("Electron API not available, using fallback or throwing error"),t!==void 0)return t;throw new Error("Electron API not available in current environment")}try{return await e()}catch(n){if(console.error("API call failed:",n),t!==void 0)return t;throw n}},qe=Tv()?window.electronAPI:null,jr={getAll:()=>Ke(()=>qe.task.getAll(),[]),getHierarchical:()=>Ke(()=>qe.task.getHierarchical(),[]),create:e=>Ke(()=>qe.task.create(e)),update:(e,t)=>Ke(()=>qe.task.update(e,t)),delete:e=>Ke(()=>qe.task.delete(e)),reorder:e=>Ke(()=>qe.task.reorder(e)),getStats:()=>Ke(()=>qe.task.getStats(),{total:0,completed:0,pending:0,important:0,today:0,planned:0}),softDelete:e=>Ke(()=>qe.task.softDelete(e)),restore:e=>Ke(()=>qe.task.restore(e)),getById:e=>Ke(()=>qe.task.getById(e)),batchSoftDelete:e=>Ke(()=>qe.task.batchSoftDelete(e)),batchRestore:e=>Ke(()=>qe.task.batchRestore(e)),cleanupDeleted:e=>Ke(()=>qe.task.cleanupDeleted(e)),getDeletedStats:()=>Ke(()=>qe.task.getDeletedStats()),getUndoable:()=>Ke(()=>qe.task.getUndoable())},Xp={get:e=>Ke(()=>qe.settings.get(e),null),set:(e,t)=>Ke(()=>qe.settings.set(e,t),!0)},jv=g.createContext(void 0),Ib=()=>{const[e,t]=g.useState("light");return g.useEffect(()=>{const n=window.matchMedia("(prefers-color-scheme: dark)"),r=s=>{t(s.matches?"dark":"light")};return t(n.matches?"dark":"light"),n.addEventListener("change",r),()=>n.removeEventListener("change",r)},[]),e},Pb=(e,t=300)=>{const n=document.documentElement;n.style.setProperty("--theme-transition-duration",`${t}ms`),Object.entries(e.colors).forEach(([r,s])=>{const o=`--${r.replace(/([A-Z])/g,"-$1").toLowerCase()}`;n.style.setProperty(o,s)}),e.mode==="dark"?n.classList.add("dark"):n.classList.remove("dark"),n.setAttribute("data-theme",e.id),n.setAttribute("data-theme-mode",e.mode)},Db=({children:e})=>{const[t,n]=g.useState(Gp),[r,s]=g.useState([]),[o,i]=g.useState(!0),a=Ib(),l=[...fl,...r],c=g.useCallback(()=>{const b=Rb(t.activeThemeId);return b||fl[0]},[t.activeThemeId]),d=g.useCallback(()=>{if(t.mode==="system"){const b=Yp(a),E=c();return E.mode===a?E:b.find(_=>_.id.includes("default"))||b[0]||E}return c()},[t.mode,a,c]),f=d(),y=g.useCallback(b=>{Pb(b,t.transitionDuration)},[t.transitionDuration]),x=g.useCallback(async b=>{try{await Xp.set("theme_config",JSON.stringify(b)),n(b)}catch(E){throw console.error("Failed to save theme config:",E),E}},[]),k=g.useCallback(async b=>{const E={...t,mode:b};await x(E)},[t,x]),m=g.useCallback(async b=>{if(!l.find(N=>N.id===b))throw new Error(`Theme with id "${b}" not found`);const _={...t,activeThemeId:b};await x(_)},[t,l,x]),w=g.useCallback(async b=>{const E={...t,...b};await x(E)},[t,x]),p=g.useCallback(async()=>{const b=["light","dark","system"],E=b.indexOf(t.mode),_=b[(E+1)%b.length];await k(_)},[t.mode,k]),h=g.useCallback(async b=>{console.log("Creating custom theme:",b)},[]),v=g.useCallback(async b=>{console.log("Deleting custom theme:",b)},[]);g.useEffect(()=>{(async()=>{try{i(!0);const E=await Xp.get("theme_config");if(E){const _=JSON.parse(E);n(_)}}catch(E){console.error("Failed to load theme config:",E),n(Gp)}finally{i(!1)}})()},[]),g.useEffect(()=>{o||y(f)},[f,o,y]),g.useEffect(()=>{if(!t.autoSwitchEnabled)return;const b=()=>{const _=new Date,N=`${_.getHours().toString().padStart(2,"0")}:${_.getMinutes().toString().padStart(2,"0")}`,{lightModeStart:j,darkModeStart:R}=t.autoSwitchTimes;let L;if(N>=j&&N<R?L="light":L="dark",f.mode!==L){const A=Yp(L),Z=A.find(J=>J.id.includes("default"))||A[0];Z&&m(Z.id)}};b();const E=setInterval(b,6e4);return()=>clearInterval(E)},[t.autoSwitchEnabled,t.autoSwitchTimes,f.mode,m]);const S={config:t,currentTheme:f,availableThemes:l,systemTheme:a,isLoading:o,setMode:k,setTheme:m,updateConfig:w,toggleMode:p,createCustomTheme:h,deleteCustomTheme:v,getEffectiveTheme:d,applyTheme:y};return u.jsx(jv.Provider,{value:S,children:e})},Ob=()=>{const e=g.useContext(jv);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e},Ab={},Jp=e=>{let t;const n=new Set,r=(d,f)=>{const y=typeof d=="function"?d(t):d;if(!Object.is(y,t)){const x=t;t=f??(typeof y!="object"||y===null)?y:Object.assign({},t,y),n.forEach(k=>k(t,x))}},s=()=>t,l={setState:r,getState:s,getInitialState:()=>c,subscribe:d=>(n.add(d),()=>n.delete(d)),destroy:()=>{(Ab?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},c=t=e(r,s,l);return l},Mb=e=>e?Jp(e):Jp;var Rv={exports:{}},Iv={},Pv={exports:{}},Dv={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var po=g;function Lb(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Fb=typeof Object.is=="function"?Object.is:Lb,zb=po.useState,$b=po.useEffect,Ub=po.useLayoutEffect,Bb=po.useDebugValue;function Vb(e,t){var n=t(),r=zb({inst:{value:n,getSnapshot:t}}),s=r[0].inst,o=r[1];return Ub(function(){s.value=n,s.getSnapshot=t,Oc(s)&&o({inst:s})},[e,n,t]),$b(function(){return Oc(s)&&o({inst:s}),e(function(){Oc(s)&&o({inst:s})})},[e]),Bb(n),n}function Oc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Fb(e,n)}catch{return!0}}function Hb(e,t){return t()}var Wb=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Hb:Vb;Dv.useSyncExternalStore=po.useSyncExternalStore!==void 0?po.useSyncExternalStore:Wb;Pv.exports=Dv;var Qb=Pv.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fl=g,Zb=Qb;function Kb(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qb=typeof Object.is=="function"?Object.is:Kb,Gb=Zb.useSyncExternalStore,Yb=Fl.useRef,Xb=Fl.useEffect,Jb=Fl.useMemo,eC=Fl.useDebugValue;Iv.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var o=Yb(null);if(o.current===null){var i={hasValue:!1,value:null};o.current=i}else i=o.current;o=Jb(function(){function l(x){if(!c){if(c=!0,d=x,x=r(x),s!==void 0&&i.hasValue){var k=i.value;if(s(k,x))return f=k}return f=x}if(k=f,qb(d,x))return k;var m=r(x);return s!==void 0&&s(k,m)?(d=x,k):(d=x,f=m)}var c=!1,d,f,y=n===void 0?null:n;return[function(){return l(t())},y===null?void 0:function(){return l(y())}]},[t,n,r,s]);var a=Gb(e,o[0],o[1]);return Xb(function(){i.hasValue=!0,i.value=a},[a]),eC(a),a};Rv.exports=Iv;var tC=Rv.exports;const nC=Ed(tC),Ov={},{useDebugValue:rC}=We,{useSyncExternalStoreWithSelector:sC}=nC;let em=!1;const oC=e=>e;function iC(e,t=oC,n){(Ov?"production":void 0)!=="production"&&n&&!em&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),em=!0);const r=sC(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return rC(r),r}const tm=e=>{(Ov?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?Mb(e):e,n=(r,s)=>iC(t,r,s);return Object.assign(n,t),n},Of=e=>e?tm(e):tm,ja={BASE_URL:"./",DEV:!1,MODE:"production",PROD:!0,SSR:!1},ud=new Map,la=e=>{const t=ud.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([n,r])=>[n,r.getState()])):{}},aC=(e,t,n)=>{if(e===void 0)return{type:"untracked",connection:t.connect(n)};const r=ud.get(n.name);if(r)return{type:"tracked",store:e,...r};const s={connection:t.connect(n),stores:{}};return ud.set(n.name,s),{type:"tracked",store:e,...s}},lC=(e,t={})=>(n,r,s)=>{const{enabled:o,anonymousActionType:i,store:a,...l}=t;let c;try{c=(o??(ja?"production":void 0)!=="production")&&window.__REDUX_DEVTOOLS_EXTENSION__}catch{}if(!c)return(ja?"production":void 0)!=="production"&&o&&console.warn("[zustand devtools middleware] Please install/enable Redux devtools extension"),e(n,r,s);const{connection:d,...f}=aC(a,c,l);let y=!0;s.setState=(m,w,p)=>{const h=n(m,w);if(!y)return h;const v=p===void 0?{type:i||"anonymous"}:typeof p=="string"?{type:p}:p;return a===void 0?(d==null||d.send(v,r()),h):(d==null||d.send({...v,type:`${a}/${v.type}`},{...la(l.name),[a]:s.getState()}),h)};const x=(...m)=>{const w=y;y=!1,n(...m),y=w},k=e(s.setState,r,s);if(f.type==="untracked"?d==null||d.init(k):(f.stores[f.store]=s,d==null||d.init(Object.fromEntries(Object.entries(f.stores).map(([m,w])=>[m,m===f.store?k:w.getState()])))),s.dispatchFromDevtools&&typeof s.dispatch=="function"){let m=!1;const w=s.dispatch;s.dispatch=(...p)=>{(ja?"production":void 0)!=="production"&&p[0].type==="__setState"&&!m&&(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),m=!0),w(...p)}}return d.subscribe(m=>{var w;switch(m.type){case"ACTION":if(typeof m.payload!="string"){console.error("[zustand devtools middleware] Unsupported action format");return}return Ac(m.payload,p=>{if(p.type==="__setState"){if(a===void 0){x(p.state);return}Object.keys(p.state).length!==1&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format. 
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);const h=p.state[a];if(h==null)return;JSON.stringify(s.getState())!==JSON.stringify(h)&&x(h);return}s.dispatchFromDevtools&&typeof s.dispatch=="function"&&s.dispatch(p)});case"DISPATCH":switch(m.payload.type){case"RESET":return x(k),a===void 0?d==null?void 0:d.init(s.getState()):d==null?void 0:d.init(la(l.name));case"COMMIT":if(a===void 0){d==null||d.init(s.getState());return}return d==null?void 0:d.init(la(l.name));case"ROLLBACK":return Ac(m.state,p=>{if(a===void 0){x(p),d==null||d.init(s.getState());return}x(p[a]),d==null||d.init(la(l.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return Ac(m.state,p=>{if(a===void 0){x(p);return}JSON.stringify(s.getState())!==JSON.stringify(p[a])&&x(p[a])});case"IMPORT_STATE":{const{nextLiftedState:p}=m.payload,h=(w=p.computedStates.slice(-1)[0])==null?void 0:w.state;if(!h)return;x(a===void 0?h:h[a]),d==null||d.send(null,p);return}case"PAUSE_RECORDING":return y=!y}return}}),k},cC=lC,Ac=(e,t)=>{let n;try{n=JSON.parse(e)}catch(r){console.error("[zustand devtools middleware] Could not parse the received json",r)}n!==void 0&&t(n)};function uC(e,t){let n;try{n=e()}catch{return}return{getItem:s=>{var o;const i=l=>l===null?null:JSON.parse(l,void 0),a=(o=n.getItem(s))!=null?o:null;return a instanceof Promise?a.then(i):i(a)},setItem:(s,o)=>n.setItem(s,JSON.stringify(o,void 0)),removeItem:s=>n.removeItem(s)}}const ki=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return ki(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return ki(r)(n)}}}},dC=(e,t)=>(n,r,s)=>{let o={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:w=>w,version:0,merge:(w,p)=>({...p,...w}),...t},i=!1;const a=new Set,l=new Set;let c;try{c=o.getStorage()}catch{}if(!c)return e((...w)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...w)},r,s);const d=ki(o.serialize),f=()=>{const w=o.partialize({...r()});let p;const h=d({state:w,version:o.version}).then(v=>c.setItem(o.name,v)).catch(v=>{p=v});if(p)throw p;return h},y=s.setState;s.setState=(w,p)=>{y(w,p),f()};const x=e((...w)=>{n(...w),f()},r,s);let k;const m=()=>{var w;if(!c)return;i=!1,a.forEach(h=>h(r()));const p=((w=o.onRehydrateStorage)==null?void 0:w.call(o,r()))||void 0;return ki(c.getItem.bind(c))(o.name).then(h=>{if(h)return o.deserialize(h)}).then(h=>{if(h)if(typeof h.version=="number"&&h.version!==o.version){if(o.migrate)return o.migrate(h.state,h.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return h.state}).then(h=>{var v;return k=o.merge(h,(v=r())!=null?v:x),n(k,!0),f()}).then(()=>{p==null||p(k,void 0),i=!0,l.forEach(h=>h(k))}).catch(h=>{p==null||p(void 0,h)})};return s.persist={setOptions:w=>{o={...o,...w},w.getStorage&&(c=w.getStorage())},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>m(),hasHydrated:()=>i,onHydrate:w=>(a.add(w),()=>{a.delete(w)}),onFinishHydration:w=>(l.add(w),()=>{l.delete(w)})},m(),k||x},fC=(e,t)=>(n,r,s)=>{let o={storage:uC(()=>localStorage),partialize:m=>m,version:0,merge:(m,w)=>({...w,...m}),...t},i=!1;const a=new Set,l=new Set;let c=o.storage;if(!c)return e((...m)=>{console.warn(`[zustand persist middleware] Unable to update item '${o.name}', the given storage is currently unavailable.`),n(...m)},r,s);const d=()=>{const m=o.partialize({...r()});return c.setItem(o.name,{state:m,version:o.version})},f=s.setState;s.setState=(m,w)=>{f(m,w),d()};const y=e((...m)=>{n(...m),d()},r,s);s.getInitialState=()=>y;let x;const k=()=>{var m,w;if(!c)return;i=!1,a.forEach(h=>{var v;return h((v=r())!=null?v:y)});const p=((w=o.onRehydrateStorage)==null?void 0:w.call(o,(m=r())!=null?m:y))||void 0;return ki(c.getItem.bind(c))(o.name).then(h=>{if(h)if(typeof h.version=="number"&&h.version!==o.version){if(o.migrate)return[!0,o.migrate(h.state,h.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,h.state];return[!1,void 0]}).then(h=>{var v;const[S,b]=h;if(x=o.merge(b,(v=r())!=null?v:y),n(x,!0),S)return d()}).then(()=>{p==null||p(x,void 0),x=r(),i=!0,l.forEach(h=>h(x))}).catch(h=>{p==null||p(void 0,h)})};return s.persist={setOptions:m=>{o={...o,...m},m.storage&&(c=m.storage)},clearStorage:()=>{c==null||c.removeItem(o.name)},getOptions:()=>o,rehydrate:()=>k(),hasHydrated:()=>i,onHydrate:m=>(a.add(m),()=>{a.delete(m)}),onFinishHydration:m=>(l.add(m),()=>{l.delete(m)})},o.skipHydration||k(),x||y},hC=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((ja?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),dC(e,t)):fC(e,t),pC=hC,zl=Of()(pC(e=>({activeView:"today",setActiveView:t=>e({activeView:t}),searchQuery:"",setSearchQuery:t=>e({searchQuery:t}),sortBy:"custom",setSortBy:t=>e({sortBy:t}),showCompleted:!0,setShowCompleted:t=>e({showCompleted:t}),isTaskInputFocused:!1,setTaskInputFocused:t=>e({isTaskInputFocused:t})}),{name:"lingan-view-store",partialize:e=>({activeView:e.activeView,sortBy:e.sortBy,showCompleted:e.showCompleted})}));function $l(){return Ef({queryKey:xe.tasks,queryFn:jr.getAll})}function mC(){return Ef({queryKey:xe.taskStats,queryFn:jr.getStats})}function gC(){return Ef({queryKey:[...xe.tasks,"hierarchical"],queryFn:jr.getHierarchical})}function Ul(){const e=Tr();return Ai({mutationFn:t=>jr.create(t),onSuccess:t=>{e.setQueryData(xe.tasks,n=>n?[...n,t]:[t]),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]}),e.invalidateQueries({queryKey:xe.taskStats})},onError:()=>{e.invalidateQueries({queryKey:xe.tasks}),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]})}})}function Li(){const e=Tr();return Ai({mutationFn:({id:t,input:n})=>jr.update(t,n),onSuccess:t=>{e.setQueryData(xe.tasks,n=>n?n.map(r=>r.id===t.id?t:r):[t]),e.setQueryData([...xe.tasks,"hierarchical"],n=>{if(!n)return n;const r=s=>s.task.id===t.id?{...s,task:t}:{...s,children:s.children.map(r)};return n.map(r)}),e.invalidateQueries({queryKey:xe.taskStats})},onError:()=>{e.invalidateQueries({queryKey:xe.tasks}),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]})}})}function Av(){const e=Tr();return Ai({mutationFn:t=>jr.delete(t),onSuccess:(t,n)=>{e.setQueryData(xe.tasks,r=>r?r.filter(s=>s.id!==n):[]),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]}),e.invalidateQueries({queryKey:xe.taskStats})},onError:()=>{e.invalidateQueries({queryKey:xe.tasks}),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]})}})}function Mv(){const e=Tr();return Ai({mutationFn:t=>jr.softDelete(t),onSuccess:(t,n)=>{e.setQueryData(xe.tasks,r=>r?r.filter(s=>s.id!==n):[]),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]}),e.invalidateQueries({queryKey:xe.taskStats})},onError:()=>{e.invalidateQueries({queryKey:xe.tasks}),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]})}})}function Af(){const e=Tr();return Ai({mutationFn:t=>jr.restore(t),onSuccess:()=>{e.invalidateQueries({queryKey:xe.tasks}),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]}),e.invalidateQueries({queryKey:xe.taskStats})},onError:()=>{e.invalidateQueries({queryKey:xe.tasks}),e.invalidateQueries({queryKey:[...xe.tasks,"hierarchical"]})}})}function yC({children:e,onAddTaskClick:t}){var v;const[n,r]=g.useState(!1),{currentTheme:s,toggleMode:o}=Ob(),{data:i=[]}=$l(),{activeView:a,setActiveView:l,searchQuery:c,setSearchQuery:d,sortBy:f,setSortBy:y,showCompleted:x,setShowCompleted:k,setTaskInputFocused:m}=zl(),w=g.useMemo(()=>{const S=Date.now(),b=new Date;b.setHours(0,0,0,0);const E=b.getTime(),_=E+24*60*60*1e3,N=i.filter(j=>!j.parentTaskId&&(!j.taskType||j.taskType!=="subtask"));return{today:N.filter(j=>!j.isCompleted&&j.dueDate&&j.dueDate>=E&&j.dueDate<_).length,important:N.filter(j=>!j.isCompleted&&(j.priority===3||j.isImportant)).length,planned:N.filter(j=>!j.isCompleted&&j.dueDate&&j.dueDate>S).length,all:N.filter(j=>!j.isCompleted).length,completed:N.filter(j=>j.isCompleted).length}},[i]),p=[{id:"today",label:"我的一天",icon:Rp,count:w.today,active:a==="today"},{id:"important",label:"重要",icon:io,count:w.important,active:a==="important"},{id:"planned",label:"已计划",icon:ns,count:w.planned,active:a==="planned"},{id:"all",label:"全部",icon:mv,count:w.all,active:a==="all"},{id:"completed",label:"已完成",icon:Ju,count:w.completed,active:a==="completed"}],h=()=>{m(!0),t==null||t()};return u.jsxs("div",{className:"flex h-screen bg-background",children:[u.jsxs("div",{className:B("flex flex-col bg-white border-r border-gray-200 transition-all duration-300 shadow-sm",n?"w-16":"w-80"),children:[u.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50/50",children:[u.jsxs("div",{className:B("flex items-center gap-3",n&&"justify-center"),children:[u.jsx(X,{variant:"ghost",size:"sm",onClick:()=>r(!n),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200",children:u.jsx(ck,{className:"h-5 w-5 text-gray-600"})}),!n&&u.jsx("h1",{className:"text-xl font-bold text-gray-800 tracking-tight",children:"灵感 APP"})]}),!n&&u.jsx(X,{variant:"ghost",size:"sm",onClick:o,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200",title:`当前主题: ${s.name} (${s.mode==="dark"?"深色":"浅色"})`,children:s.mode==="dark"?u.jsx(Rp,{className:"h-4 w-4 text-gray-600"}):u.jsx(uk,{className:"h-4 w-4 text-gray-600"})})]}),!n&&u.jsx("div",{className:"p-4",children:u.jsxs("div",{className:"relative",children:[u.jsx(fk,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),u.jsx(gn,{value:c,onChange:S=>d(S.target.value),placeholder:"搜索任务...",className:B("ms-input pl-10 bg-gray-50 border-gray-200 rounded-lg","focus:bg-white focus:border-blue-400 focus:shadow-sm","placeholder:text-gray-400 text-gray-700")})]})}),u.jsx("div",{className:"flex-1 px-3 py-2",children:u.jsx("nav",{className:"space-y-1",children:p.map(S=>u.jsxs(X,{variant:"ghost",className:B("ms-nav-item w-full justify-start gap-3 h-12 px-4 rounded-lg transition-all duration-200",n&&"justify-center px-2",S.active?"ms-nav-item active bg-blue-50 text-blue-600 hover:bg-blue-100 border-r-3 border-blue-500 font-semibold shadow-sm":"text-gray-700 hover:bg-blue-50 hover:text-blue-600"),onClick:()=>l(S.id),children:[u.jsx(S.icon,{className:B("h-5 w-5 flex-shrink-0 transition-colors duration-200",S.active?"text-blue-600":"text-gray-500")}),!n&&u.jsxs(u.Fragment,{children:[u.jsx("span",{className:"flex-1 text-left font-medium",children:S.label}),S.count!==void 0&&u.jsx("span",{className:B("text-xs px-2 py-1 rounded-full min-w-[20px] text-center font-medium transition-colors duration-200",S.active?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-600"),children:S.count})]})]},S.id))})}),u.jsx("div",{className:"p-3 border-t border-gray-200 bg-gray-50/30",children:u.jsxs(X,{variant:"ghost",className:B("w-full justify-start gap-3 h-12 px-4 rounded-lg transition-all duration-200","text-gray-700 hover:bg-gray-100 hover:text-gray-800",n&&"justify-center px-2"),children:[u.jsx(pk,{className:"h-5 w-5 flex-shrink-0 text-gray-500"}),!n&&u.jsx("span",{className:"font-medium",children:"设置"})]})})]}),u.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[u.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border bg-card/50",children:[u.jsxs("div",{className:"flex items-center gap-4",children:[u.jsx("h2",{className:"text-2xl font-semibold text-foreground",children:((v=p.find(S=>S.id===a))==null?void 0:v.label)||"我的一天"}),u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(X,{variant:"ghost",size:"sm",onClick:()=>{const S=["custom","priority","dueDate","created","alphabetical"],E=(S.indexOf(f)+1)%S.length;y(S[E])},title:`当前排序: ${f==="custom"?"自定义":f==="priority"?"优先级":f==="dueDate"?"截止日期":f==="created"?"创建时间":"字母顺序"}`,children:u.jsx(nk,{className:"h-4 w-4"})}),u.jsx(X,{variant:"ghost",size:"sm",onClick:()=>k(!x),title:x?"隐藏已完成任务":"显示已完成任务",children:x?u.jsx(ok,{className:"h-4 w-4"}):u.jsx(sk,{className:"h-4 w-4"})}),u.jsx(X,{variant:"ghost",size:"sm",title:"更多选项",children:u.jsx(dk,{className:"h-4 w-4"})})]})]}),u.jsxs(X,{onClick:h,className:"gap-2",children:[u.jsx(rs,{className:"h-4 w-4"}),"添加任务"]})]}),u.jsx("div",{className:"flex-1 overflow-auto",children:u.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e})})]})]})}const Re={HIGH:1,MEDIUM:2,LOW:3},vC=Tt({id:Y(),content:Y().min(1,"任务内容不能为空"),isCompleted:os(),priority:Se().int().min(1).max(3),dueDate:Se().int().nullable(),orderIndex:Se().int(),createdAt:Se().int(),deletedAt:Se().int().nullable().optional(),parentTaskId:Y().nullable().optional(),taskType:Mi(["task","subtask","template"]).default("task"),description:Y().nullable().optional(),estimatedDuration:Se().int().nullable().optional(),actualDuration:Se().int().nullable().optional(),progress:Se().int().min(0).max(100).default(0)});Tt({content:Y().min(1,"任务内容不能为空"),priority:Se().int().min(1).max(3).default(Re.MEDIUM),dueDate:Se().int().nullable().optional(),parentTaskId:Y().nullable().optional(),taskType:Mi(["task","subtask","template"]).default("task"),description:Y().nullable().optional(),estimatedDuration:Se().int().nullable().optional(),progress:Se().int().min(0).max(100).default(0)});Tt({content:Y().min(1,"任务内容不能为空").optional(),isCompleted:os().optional(),priority:Se().int().min(1).max(3).optional(),dueDate:Se().int().nullable().optional(),parentTaskId:Y().nullable().optional(),taskType:Mi(["task","subtask","template"]).optional(),description:Y().nullable().optional(),estimatedDuration:Se().int().nullable().optional(),actualDuration:Se().int().nullable().optional(),progress:Se().int().min(0).max(100).optional()});Tt({id:Y(),orderIndex:Se().int()});const xC=Tb(()=>Tt({task:vC,children:dl(xC),depth:Se().int(),path:dl(Y())}));Tt({id:Y(),name:Y().min(1,"标签名称不能为空"),color:Y().regex(/^#[0-9A-Fa-f]{6}$/,"颜色格式不正确"),description:Y().nullable().optional(),createdAt:Se().int(),updatedAt:Se().int()});Tt({name:Y().min(1,"标签名称不能为空"),color:Y().regex(/^#[0-9A-Fa-f]{6}$/,"颜色格式不正确").default("#3b82f6"),description:Y().nullable().optional()});Tt({id:Y(),name:Y().min(1,"模板名称不能为空"),content:Y().min(1,"模板内容不能为空"),description:Y().nullable().optional(),priority:Se().int().min(1).max(3),estimatedDuration:Se().int().nullable().optional(),tags:dl(Y()),isPublic:os(),createdBy:Y().nullable().optional(),createdAt:Se().int(),updatedAt:Se().int()});Tt({name:Y().min(1,"模板名称不能为空"),content:Y().min(1,"模板内容不能为空"),description:Y().nullable().optional(),priority:Se().int().min(1).max(3).default(Re.MEDIUM),estimatedDuration:Se().int().nullable().optional(),tags:dl(Y()).default([]),isPublic:os().default(!1)});const Ho={[Re.HIGH]:"高",[Re.MEDIUM]:"中",[Re.LOW]:"低"},Mf={[Re.HIGH]:"text-red-600 bg-red-50 border-red-200",[Re.MEDIUM]:"text-yellow-600 bg-yellow-50 border-yellow-200",[Re.LOW]:"text-green-600 bg-green-50 border-green-200"};function wC(){const[e,t]=g.useState(""),[n,r]=g.useState(!1),[s,o]=g.useState(Re.MEDIUM),[i,a]=g.useState(""),[l,c]=g.useState(!1),d=g.useRef(null),f=Ul(),{isTaskInputFocused:y,setTaskInputFocused:x,activeView:k}=zl();g.useEffect(()=>{var S;y&&((S=d.current)==null||S.focus(),r(!0),x(!1))},[y,x]);const m=async S=>{var b;if(S.preventDefault(),!!e.trim())try{let E=i?new Date(i).getTime():void 0,_=l;if(k==="today"&&!i){const N=new Date;N.setHours(23,59,59,999),E=N.getTime()}k==="important"&&(_=!0),await f.mutateAsync({content:e.trim(),priority:s,dueDate:E,isImportant:_}),t(""),a(""),c(!1),o(Re.MEDIUM),r(!1),(b=d.current)==null||b.focus()}catch(E){console.error("Failed to create task:",E)}},w=()=>{r(!0)},p=()=>{var S;t(""),a(""),c(!1),o(Re.MEDIUM),r(!1),(S=d.current)==null||S.blur()},h=S=>{S.key==="Escape"?p():S.key==="Enter"&&(S.ctrlKey||S.metaKey)&&m(S)},v=S=>{switch(S){case Re.HIGH:return"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-950/30";case Re.LOW:return"text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-950/30";default:return"text-muted-foreground bg-muted"}};return u.jsx("div",{className:B("ms-card ms-fade-in transition-all duration-300","focus-within:shadow-lg focus-within:border-blue-300",n&&"ms-card-elevated"),children:u.jsxs("form",{onSubmit:m,children:[u.jsxs("div",{className:"flex items-center gap-3 p-4",children:[u.jsx("div",{className:"flex-shrink-0",children:u.jsx("div",{className:B("w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200",e.trim()?"border-blue-500 bg-blue-50 text-blue-600":"border-gray-300 text-gray-400 hover:border-blue-400 hover:text-blue-500"),children:u.jsx(rs,{className:"h-4 w-4"})})}),u.jsx(gn,{ref:d,value:e,onChange:S=>t(S.target.value),onFocus:w,onKeyDown:h,placeholder:`添加任务${k==="today"?"到我的一天":k==="important"?"（重要）":""}`,className:B("ms-input border-0 bg-transparent text-base px-0","placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0","transition-all duration-200")}),e.trim()&&u.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0 ms-slide-up",children:[n&&u.jsx(X,{type:"button",variant:"ghost",size:"sm",onClick:p,className:"ms-button-ghost p-2 hover:bg-gray-100",children:u.jsx(Dn,{className:"h-4 w-4"})}),u.jsxs(X,{type:"submit",size:"sm",disabled:f.isPending,className:B("ms-button-primary gap-2 shadow-sm",f.isPending&&"opacity-80 cursor-not-allowed"),children:[f.isPending?u.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}):u.jsx(hk,{className:"h-4 w-4"}),n&&(f.isPending?"添加中...":"添加")]})]})]}),n&&u.jsxs("div",{className:"border-t border-gray-200 bg-gray-50/50 p-4 space-y-4 ms-slide-up",children:[u.jsxs("div",{className:"flex items-center gap-3 flex-wrap",children:[u.jsxs("div",{className:"flex items-center gap-2 ms-scale-in",children:[u.jsx(ns,{className:"h-4 w-4 text-gray-500"}),u.jsx("input",{type:"date",value:i,onChange:S=>a(S.target.value),className:B("ms-input text-sm px-2 py-1 min-w-[140px]","border-gray-300 rounded-md bg-white","hover:border-blue-400 focus:border-blue-500")})]}),u.jsxs(X,{type:"button",variant:"ghost",size:"sm",onClick:()=>c(!l),className:B("ms-button-ghost gap-2 transition-all duration-200",l?"text-yellow-600 bg-yellow-100 hover:bg-yellow-200 border-yellow-300":"text-gray-600 hover:text-yellow-600 hover:bg-yellow-50"),children:[u.jsx(io,{className:B("h-4 w-4 transition-all duration-200",l&&"fill-current text-yellow-500")}),"重要"]}),u.jsxs("div",{className:"flex items-center gap-2 ms-scale-in",children:[u.jsx(ol,{className:"h-4 w-4 text-gray-500"}),u.jsx("div",{className:"flex gap-1",children:[Re.LOW,Re.MEDIUM,Re.HIGH].map(S=>u.jsx(X,{type:"button",variant:"ghost",size:"sm",onClick:()=>o(S),className:B("w-8 h-8 p-0 text-xs font-medium rounded-full transition-all duration-200","border border-transparent hover:scale-110",s===S?B(v(S),"border-current shadow-sm"):"text-gray-400 hover:text-gray-600 hover:bg-gray-100"),children:S},S))})]})]}),u.jsxs("div",{className:"flex items-center justify-between pt-2",children:[u.jsxs("div",{className:"text-xs text-gray-500 space-y-1",children:[u.jsxs("div",{className:"flex items-center gap-2 flex-wrap",children:[i&&u.jsxs("span",{className:"inline-flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-md",children:[u.jsx(ns,{className:"h-3 w-3"}),new Date(i).toLocaleDateString()]}),l&&u.jsxs("span",{className:"inline-flex items-center gap-1 px-2 py-1 bg-yellow-50 text-yellow-700 rounded-md",children:[u.jsx(io,{className:"h-3 w-3 fill-current"}),"重要"]}),s!==Re.MEDIUM&&u.jsxs("span",{className:B("inline-flex items-center gap-1 px-2 py-1 rounded-md",s===Re.HIGH?"bg-red-50 text-red-700":"bg-blue-50 text-blue-700"),children:[u.jsx(ol,{className:"h-3 w-3"}),"优先级: ",s===Re.HIGH?"高":"低"]})]}),u.jsx("div",{className:"text-gray-400 text-xs",children:"Ctrl+Enter 提交 • Esc 取消"})]}),u.jsxs("div",{className:"flex gap-2",children:[u.jsx(X,{type:"button",variant:"ghost",size:"sm",onClick:p,className:"ms-button-secondary",children:"取消"}),u.jsx(X,{type:"submit",size:"sm",disabled:!e.trim()||f.isPending,className:B("ms-button-primary",(!e.trim()||f.isPending)&&"opacity-50 cursor-not-allowed"),children:f.isPending?"添加中...":"添加任务"})]})]})]})]})})}const SC=Of((e,t)=>({selectedTaskIds:new Set,isSelectionMode:!1,recentlyDeletedTasks:new Map,undoTimeouts:new Map,toggleTaskSelection:n=>{e(r=>{const s=new Set(r.selectedTaskIds);return s.has(n)?s.delete(n):s.add(n),{selectedTaskIds:s}})},selectAllTasks:n=>{e({selectedTaskIds:new Set(n)})},clearSelection:()=>{e({selectedTaskIds:new Set})},enterSelectionMode:()=>{e({isSelectionMode:!0})},exitSelectionMode:()=>{e({isSelectionMode:!1,selectedTaskIds:new Set})},toggleSelectionMode:()=>{const{isSelectionMode:n}=t();e(n?{isSelectionMode:!1,selectedTaskIds:new Set}:{isSelectionMode:!0})},addDeletedTask:n=>{e(r=>{const s=new Map(r.recentlyDeletedTasks);s.set(n.id,n);const o=r.undoTimeouts.get(n.id);o&&clearTimeout(o);const i=new Map(r.undoTimeouts),a=setTimeout(()=>{e(l=>{const c=new Map(l.recentlyDeletedTasks),d=new Map(l.undoTimeouts);return c.delete(n.id),d.delete(n.id),{recentlyDeletedTasks:c,undoTimeouts:d}})},3e4);return i.set(n.id,a),{recentlyDeletedTasks:s,undoTimeouts:i}})},removeDeletedTask:n=>{e(r=>{const s=new Map(r.recentlyDeletedTasks),o=new Map(r.undoTimeouts);s.delete(n);const i=o.get(n);return i&&(clearTimeout(i),o.delete(n)),{recentlyDeletedTasks:s,undoTimeouts:o}})},clearDeletedTasks:()=>{const{undoTimeouts:n}=t();n.forEach(r=>clearTimeout(r)),e({recentlyDeletedTasks:new Map,undoTimeouts:new Map})},getUndoableTask:n=>{const{recentlyDeletedTasks:r}=t();return r.get(n)}}));function Fi(){const e=SC();return{selectedTaskIds:Array.from(e.selectedTaskIds),selectedCount:e.selectedTaskIds.size,isSelectionMode:e.isSelectionMode,isTaskSelected:t=>e.selectedTaskIds.has(t),recentlyDeletedTasks:Array.from(e.recentlyDeletedTasks.values()),hasUndoableTasks:e.recentlyDeletedTasks.size>0,toggleTaskSelection:e.toggleTaskSelection,selectAllTasks:e.selectAllTasks,clearSelection:e.clearSelection,enterSelectionMode:e.enterSelectionMode,exitSelectionMode:e.exitSelectionMode,toggleSelectionMode:e.toggleSelectionMode,addDeletedTask:e.addDeletedTask,removeDeletedTask:e.removeDeletedTask,clearDeletedTasks:e.clearDeletedTasks,getUndoableTask:e.getUndoableTask}}function kC({onLongPress:e,onClick:t,delay:n=500}){const r=g.useRef(null),s=g.useRef(!1),o=g.useCallback(()=>{s.current=!1,r.current=setTimeout(()=>{s.current=!0,e()},n)},[e,n]),i=g.useCallback(()=>{r.current&&(clearTimeout(r.current),r.current=null)},[]),a=g.useCallback(()=>{!s.current&&t&&t(),s.current=!1},[t]);return{onMouseDown:o,onMouseUp:i,onMouseLeave:i,onTouchStart:o,onTouchEnd:i,onClick:a}}const nt={fast:150,normal:300,slow:500},mt={easeOut:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",easeIn:"cubic-bezier(0.55, 0.055, 0.675, 0.19)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)"},bC=(e,t)=>{e.style.transition=`all ${nt.fast}ms ${mt.easeOut}`,t?(e.style.transform="scale(0.98)",e.style.boxShadow="0 0 0 2px rgb(59 130 246 / 0.5)"):(e.style.transform="scale(1)",e.style.boxShadow="")},CC=e=>{e.style.transform="translateY(100%)",e.style.opacity="0",e.offsetHeight,e.style.transition=`all ${nt.normal}ms ${mt.easeOut}`,e.style.transform="translateY(0)",e.style.opacity="1"},EC=e=>new Promise(t=>{e.style.transition=`all ${nt.normal}ms ${mt.easeIn}`,e.style.transform="translateY(100%)",e.style.opacity="0",setTimeout(()=>{t()},nt.normal)}),_C=()=>{if(document.getElementById("custom-animations"))return;const e=document.createElement("style");e.id="custom-animations",e.textContent=`
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
      20%, 40%, 60%, 80% { transform: translateX(2px); }
    }
    
    @keyframes bounce {
      0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
      40%, 43% { transform: translate3d(0, -8px, 0); }
      70% { transform: translate3d(0, -4px, 0); }
      90% { transform: translate3d(0, -2px, 0); }
    }
    
    @keyframes slideInFromRight {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideInFromBottom {
      from { transform: translateY(100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes slideInFromTop {
      from { transform: translateY(-100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.9); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    .animate-slide-in-right {
      animation: slideInFromRight ${nt.normal}ms ${mt.easeOut};
    }
    
    .animate-slide-in-bottom {
      animation: slideInFromBottom ${nt.normal}ms ${mt.easeOut};
    }
    
    .animate-slide-in-top {
      animation: slideInFromTop ${nt.normal}ms ${mt.bounce};
    }
    
    .animate-fade-in {
      animation: fadeIn ${nt.normal}ms ${mt.easeOut};
    }
    
    .animate-scale-in {
      animation: scaleIn ${nt.normal}ms ${mt.bounce};
    }
    
    .transition-all-fast {
      transition: all ${nt.fast}ms ${mt.easeOut};
    }
    
    .transition-all-normal {
      transition: all ${nt.normal}ms ${mt.easeOut};
    }
    
    .transition-all-slow {
      transition: all ${nt.slow}ms ${mt.easeOut};
    }
  `,document.head.appendChild(e)},NC=()=>{_C()};class Mc{static buttonClick(t){t.style.transform="scale(0.95)",t.style.transition=`transform ${nt.fast}ms ${mt.easeOut}`,setTimeout(()=>{t.style.transform="scale(1)"},nt.fast)}static hover(t,n){t.style.transition=`transform ${nt.fast}ms ${mt.easeOut}`,t.style.transform=n?"translateY(-1px)":"translateY(0)"}static focus(t,n){t.style.transition=`box-shadow ${nt.fast}ms ${mt.easeOut}`,t.style.boxShadow=n?"0 0 0 2px rgb(59 130 246 / 0.5)":""}}const Lv=g.createContext(void 0);function TC({children:e}){const[t,n]=g.useState([]),r=g.useCallback(o=>{const i=Math.random().toString(36).substr(2,9),a={...o,id:i};n(c=>[...c,a]);const l=o.duration||5e3;setTimeout(()=>{n(c=>c.filter(d=>d.id!==i))},l)},[]),s=g.useCallback(o=>{n(i=>i.filter(a=>a.id!==o))},[]);return u.jsxs(Lv.Provider,{value:{toasts:t,addToast:r,removeToast:s},children:[e,u.jsx(jC,{})]})}function Lf(){const e=g.useContext(Lv);if(!e)throw new Error("useToast must be used within a ToastProvider");return e}function jC(){const{toasts:e}=Lf();return u.jsx("div",{className:"fixed bottom-4 right-4 z-50 flex flex-col gap-2 max-w-sm",children:e.map(t=>u.jsx(RC,{toast:t},t.id))})}function RC({toast:e}){const{removeToast:t}=Lf(),n={success:fv,error:Ml,warning:_f,info:lk},r={success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"},s={success:"text-green-600",error:"text-red-600",warning:"text-yellow-600",info:"text-blue-600"},o=n[e.type];return u.jsxs("div",{className:B("flex items-start gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm","animate-in slide-in-from-right-full duration-300",r[e.type]),children:[u.jsx(o,{className:B("h-5 w-5 mt-0.5 flex-shrink-0",s[e.type])}),u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsx("div",{className:"font-medium text-sm",children:e.title}),e.description&&u.jsx("div",{className:"text-sm opacity-90 mt-1",children:e.description}),e.action&&u.jsxs("button",{onClick:e.action.onClick,className:"inline-flex items-center gap-1 mt-2 text-sm font-medium underline hover:no-underline",children:[u.jsx(td,{className:"h-3 w-3"}),e.action.label]})]}),u.jsx("button",{onClick:()=>t(e.id),className:"flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity",children:u.jsx(Dn,{className:"h-4 w-4"})})]})}function ko(){const{addToast:e}=Lf(),t=g.useCallback((c,d,f)=>{e({type:"success",title:c,description:d,action:f})},[e]),n=g.useCallback((c,d)=>{e({type:"error",title:c,description:d})},[e]),r=g.useCallback((c,d)=>{e({type:"warning",title:c,description:d})},[e]),s=g.useCallback((c,d)=>{e({type:"info",title:c,description:d})},[e]),o=g.useCallback((c,d)=>{e({type:"success",title:"删除成功",description:`"${c}" 已被删除`,action:{label:"撤销",onClick:d},duration:8e3})},[e]),i=g.useCallback((c,d)=>{e({type:"success",title:"批量删除成功",description:`已删除 ${c} 个任务`,action:{label:"撤销",onClick:d},duration:8e3})},[e]),a=g.useCallback(c=>{e({type:"success",title:"批量恢复成功",description:`已恢复 ${c} 个任务`,duration:5e3})},[e]),l=g.useCallback(c=>{e({type:"info",title:"清理完成",description:`已清理 ${c} 个过期的已删除任务`,duration:5e3})},[e]);return{showSuccess:t,showError:n,showWarning:r,showInfo:s,showDeleteSuccess:o,showBatchDeleteSuccess:i,showBatchRestoreSuccess:a,showCleanupSuccess:l}}function IC(e,t){const n=g.createContext(t),r=o=>{const{children:i,...a}=o,l=g.useMemo(()=>a,Object.values(a));return u.jsx(n.Provider,{value:l,children:i})};r.displayName=e+"Provider";function s(o){const i=g.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${o}\` must be used within \`${e}\``)}return[r,s]}function zi(e,t=[]){let n=[];function r(o,i){const a=g.createContext(i),l=n.length;n=[...n,i];const c=f=>{var p;const{scope:y,children:x,...k}=f,m=((p=y==null?void 0:y[e])==null?void 0:p[l])||a,w=g.useMemo(()=>k,Object.values(k));return u.jsx(m.Provider,{value:w,children:x})};c.displayName=o+"Provider";function d(f,y){var m;const x=((m=y==null?void 0:y[e])==null?void 0:m[l])||a,k=g.useContext(x);if(k)return k;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${o}\``)}return[c,d]}const s=()=>{const o=n.map(i=>g.createContext(i));return function(a){const l=(a==null?void 0:a[e])||o;return g.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return s.scopeName=e,[r,PC(s,...t)]}function PC(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(o){const i=r.reduce((a,{useScope:l,scopeName:c})=>{const f=l(o)[`__scope${c}`];return{...a,...f}},{});return g.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Ce(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e==null||e(s),n===!1||!s.defaultPrevented)return t==null?void 0:t(s)}}var ot=globalThis!=null&&globalThis.document?g.useLayoutEffect:()=>{},DC=Gm[" useInsertionEffect ".trim().toString()]||ot;function hl({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[s,o,i]=OC({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:s;{const d=g.useRef(e!==void 0);g.useEffect(()=>{const f=d.current;f!==a&&console.warn(`${r} is changing from ${f?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),d.current=a},[a,r])}const c=g.useCallback(d=>{var f;if(a){const y=AC(d)?d(e):d;y!==e&&((f=i.current)==null||f.call(i,y))}else o(d)},[a,e,o,i]);return[l,c]}function OC({defaultProp:e,onChange:t}){const[n,r]=g.useState(e),s=g.useRef(n),o=g.useRef(t);return DC(()=>{o.current=t},[t]),g.useEffect(()=>{var i;s.current!==n&&((i=o.current)==null||i.call(o,n),s.current=n)},[n,s]),[n,r,o]}function AC(e){return typeof e=="function"}function Fv(e){const t=g.useRef({value:e,previous:e});return g.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function zv(e){const[t,n]=g.useState(void 0);return ot(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const o=s[0];let i,a;if("borderBoxSize"in o){const l=o.borderBoxSize,c=Array.isArray(l)?l[0]:l;i=c.inlineSize,a=c.blockSize}else i=e.offsetWidth,a=e.offsetHeight;n({width:i,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}function MC(e,t){return g.useReducer((n,r)=>t[n][r]??n,e)}var $i=e=>{const{present:t,children:n}=e,r=LC(t),s=typeof n=="function"?n({present:r.isPresent}):g.Children.only(n),o=Ae(r.ref,FC(s));return typeof n=="function"||r.isPresent?g.cloneElement(s,{ref:o}):null};$i.displayName="Presence";function LC(e){const[t,n]=g.useState(),r=g.useRef(null),s=g.useRef(e),o=g.useRef("none"),i=e?"mounted":"unmounted",[a,l]=MC(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const c=ca(r.current);o.current=a==="mounted"?c:"none"},[a]),ot(()=>{const c=r.current,d=s.current;if(d!==e){const y=o.current,x=ca(c);e?l("MOUNT"):x==="none"||(c==null?void 0:c.display)==="none"?l("UNMOUNT"):l(d&&y!==x?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,l]),ot(()=>{if(t){let c;const d=t.ownerDocument.defaultView??window,f=x=>{const m=ca(r.current).includes(x.animationName);if(x.target===t&&m&&(l("ANIMATION_END"),!s.current)){const w=t.style.animationFillMode;t.style.animationFillMode="forwards",c=d.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=w)})}},y=x=>{x.target===t&&(o.current=ca(r.current))};return t.addEventListener("animationstart",y),t.addEventListener("animationcancel",f),t.addEventListener("animationend",f),()=>{d.clearTimeout(c),t.removeEventListener("animationstart",y),t.removeEventListener("animationcancel",f),t.removeEventListener("animationend",f)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:g.useCallback(c=>{r.current=c?getComputedStyle(c):null,n(c)},[])}}function ca(e){return(e==null?void 0:e.animationName)||"none"}function FC(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var zC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],be=zC.reduce((e,t)=>{const n=ao(`Primitive.${t}`),r=g.forwardRef((s,o)=>{const{asChild:i,...a}=s,l=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(l,{...a,ref:o})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function $C(e,t){e&&xo.flushSync(()=>e.dispatchEvent(t))}var Bl="Checkbox",[UC,XN]=zi(Bl),[BC,Ff]=UC(Bl);function VC(e){const{__scopeCheckbox:t,checked:n,children:r,defaultChecked:s,disabled:o,form:i,name:a,onCheckedChange:l,required:c,value:d="on",internal_do_not_use_render:f}=e,[y,x]=hl({prop:n,defaultProp:s??!1,onChange:l,caller:Bl}),[k,m]=g.useState(null),[w,p]=g.useState(null),h=g.useRef(!1),v=k?!!i||!!k.closest("form"):!0,S={checked:y,disabled:o,setChecked:x,control:k,setControl:m,name:a,form:i,value:d,hasConsumerStoppedPropagationRef:h,required:c,defaultChecked:yr(s)?!1:s,isFormControl:v,bubbleInput:w,setBubbleInput:p};return u.jsx(BC,{scope:t,...S,children:HC(f)?f(S):r})}var $v="CheckboxTrigger",Uv=g.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...r},s)=>{const{control:o,value:i,disabled:a,checked:l,required:c,setControl:d,setChecked:f,hasConsumerStoppedPropagationRef:y,isFormControl:x,bubbleInput:k}=Ff($v,e),m=Ae(s,d),w=g.useRef(l);return g.useEffect(()=>{const p=o==null?void 0:o.form;if(p){const h=()=>f(w.current);return p.addEventListener("reset",h),()=>p.removeEventListener("reset",h)}},[o,f]),u.jsx(be.button,{type:"button",role:"checkbox","aria-checked":yr(l)?"mixed":l,"aria-required":c,"data-state":Qv(l),"data-disabled":a?"":void 0,disabled:a,value:i,...r,ref:m,onKeyDown:Ce(t,p=>{p.key==="Enter"&&p.preventDefault()}),onClick:Ce(n,p=>{f(h=>yr(h)?!0:!h),k&&x&&(y.current=p.isPropagationStopped(),y.current||p.stopPropagation())})})});Uv.displayName=$v;var zf=g.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:r,checked:s,defaultChecked:o,required:i,disabled:a,value:l,onCheckedChange:c,form:d,...f}=e;return u.jsx(VC,{__scopeCheckbox:n,checked:s,defaultChecked:o,disabled:a,required:i,onCheckedChange:c,name:r,form:d,value:l,internal_do_not_use_render:({isFormControl:y})=>u.jsxs(u.Fragment,{children:[u.jsx(Uv,{...f,ref:t,__scopeCheckbox:n}),y&&u.jsx(Wv,{__scopeCheckbox:n})]})})});zf.displayName=Bl;var Bv="CheckboxIndicator",Vv=g.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...s}=e,o=Ff(Bv,n);return u.jsx($i,{present:r||yr(o.checked)||o.checked===!0,children:u.jsx(be.span,{"data-state":Qv(o.checked),"data-disabled":o.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});Vv.displayName=Bv;var Hv="CheckboxBubbleInput",Wv=g.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:r,hasConsumerStoppedPropagationRef:s,checked:o,defaultChecked:i,required:a,disabled:l,name:c,value:d,form:f,bubbleInput:y,setBubbleInput:x}=Ff(Hv,e),k=Ae(n,x),m=Fv(o),w=zv(r);g.useEffect(()=>{const h=y;if(!h)return;const v=window.HTMLInputElement.prototype,b=Object.getOwnPropertyDescriptor(v,"checked").set,E=!s.current;if(m!==o&&b){const _=new Event("click",{bubbles:E});h.indeterminate=yr(o),b.call(h,yr(o)?!1:o),h.dispatchEvent(_)}},[y,m,o,s]);const p=g.useRef(yr(o)?!1:o);return u.jsx(be.input,{type:"checkbox","aria-hidden":!0,defaultChecked:i??p.current,required:a,disabled:l,name:c,value:d,form:f,...t,tabIndex:-1,ref:k,style:{...t.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});Wv.displayName=Hv;function HC(e){return typeof e=="function"}function yr(e){return e==="indeterminate"}function Qv(e){return yr(e)?"indeterminate":e?"checked":"unchecked"}const $f=g.forwardRef(({className:e,...t},n)=>u.jsx(zf,{ref:n,className:B("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:u.jsx(Vv,{className:B("flex items-center justify-center text-current"),children:u.jsx(hv,{className:"h-4 w-4"})})}));$f.displayName=zf.displayName;var WC=Gm[" useId ".trim().toString()]||(()=>{}),QC=0;function Fs(e){const[t,n]=g.useState(WC());return ot(()=>{n(r=>r??String(QC++))},[e]),t?`radix-${t}`:""}function is(e){const t=g.useRef(e);return g.useEffect(()=>{t.current=e}),g.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function ZC(e,t=globalThis==null?void 0:globalThis.document){const n=is(e);g.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var KC="DismissableLayer",dd="dismissableLayer.update",qC="dismissableLayer.pointerDownOutside",GC="dismissableLayer.focusOutside",nm,Zv=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Uf=g.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:o,onInteractOutside:i,onDismiss:a,...l}=e,c=g.useContext(Zv),[d,f]=g.useState(null),y=(d==null?void 0:d.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,x]=g.useState({}),k=Ae(t,_=>f(_)),m=Array.from(c.layers),[w]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),p=m.indexOf(w),h=d?m.indexOf(d):-1,v=c.layersWithOutsidePointerEventsDisabled.size>0,S=h>=p,b=JC(_=>{const N=_.target,j=[...c.branches].some(R=>R.contains(N));!S||j||(s==null||s(_),i==null||i(_),_.defaultPrevented||a==null||a())},y),E=e2(_=>{const N=_.target;[...c.branches].some(R=>R.contains(N))||(o==null||o(_),i==null||i(_),_.defaultPrevented||a==null||a())},y);return ZC(_=>{h===c.layers.size-1&&(r==null||r(_),!_.defaultPrevented&&a&&(_.preventDefault(),a()))},y),g.useEffect(()=>{if(d)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(nm=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(d)),c.layers.add(d),rm(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(y.body.style.pointerEvents=nm)}},[d,y,n,c]),g.useEffect(()=>()=>{d&&(c.layers.delete(d),c.layersWithOutsidePointerEventsDisabled.delete(d),rm())},[d,c]),g.useEffect(()=>{const _=()=>x({});return document.addEventListener(dd,_),()=>document.removeEventListener(dd,_)},[]),u.jsx(be.div,{...l,ref:k,style:{pointerEvents:v?S?"auto":"none":void 0,...e.style},onFocusCapture:Ce(e.onFocusCapture,E.onFocusCapture),onBlurCapture:Ce(e.onBlurCapture,E.onBlurCapture),onPointerDownCapture:Ce(e.onPointerDownCapture,b.onPointerDownCapture)})});Uf.displayName=KC;var YC="DismissableLayerBranch",XC=g.forwardRef((e,t)=>{const n=g.useContext(Zv),r=g.useRef(null),s=Ae(t,r);return g.useEffect(()=>{const o=r.current;if(o)return n.branches.add(o),()=>{n.branches.delete(o)}},[n.branches]),u.jsx(be.div,{...e,ref:s})});XC.displayName=YC;function JC(e,t=globalThis==null?void 0:globalThis.document){const n=is(e),r=g.useRef(!1),s=g.useRef(()=>{});return g.useEffect(()=>{const o=a=>{if(a.target&&!r.current){let l=function(){Kv(qC,n,c,{discrete:!0})};const c={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=l,t.addEventListener("click",s.current,{once:!0})):l()}else t.removeEventListener("click",s.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",o)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",o),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function e2(e,t=globalThis==null?void 0:globalThis.document){const n=is(e),r=g.useRef(!1);return g.useEffect(()=>{const s=o=>{o.target&&!r.current&&Kv(GC,n,{originalEvent:o},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function rm(){const e=new CustomEvent(dd);document.dispatchEvent(e)}function Kv(e,t,n,{discrete:r}){const s=n.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?$C(s,o):s.dispatchEvent(o)}var Lc="focusScope.autoFocusOnMount",Fc="focusScope.autoFocusOnUnmount",sm={bubbles:!1,cancelable:!0},t2="FocusScope",Bf=g.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:o,...i}=e,[a,l]=g.useState(null),c=is(s),d=is(o),f=g.useRef(null),y=Ae(t,m=>l(m)),x=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(r){let m=function(v){if(x.paused||!a)return;const S=v.target;a.contains(S)?f.current=S:Bn(f.current,{select:!0})},w=function(v){if(x.paused||!a)return;const S=v.relatedTarget;S!==null&&(a.contains(S)||Bn(f.current,{select:!0}))},p=function(v){if(document.activeElement===document.body)for(const b of v)b.removedNodes.length>0&&Bn(a)};document.addEventListener("focusin",m),document.addEventListener("focusout",w);const h=new MutationObserver(p);return a&&h.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",w),h.disconnect()}}},[r,a,x.paused]),g.useEffect(()=>{if(a){im.add(x);const m=document.activeElement;if(!a.contains(m)){const p=new CustomEvent(Lc,sm);a.addEventListener(Lc,c),a.dispatchEvent(p),p.defaultPrevented||(n2(a2(qv(a)),{select:!0}),document.activeElement===m&&Bn(a))}return()=>{a.removeEventListener(Lc,c),setTimeout(()=>{const p=new CustomEvent(Fc,sm);a.addEventListener(Fc,d),a.dispatchEvent(p),p.defaultPrevented||Bn(m??document.body,{select:!0}),a.removeEventListener(Fc,d),im.remove(x)},0)}}},[a,c,d,x]);const k=g.useCallback(m=>{if(!n&&!r||x.paused)return;const w=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,p=document.activeElement;if(w&&p){const h=m.currentTarget,[v,S]=r2(h);v&&S?!m.shiftKey&&p===S?(m.preventDefault(),n&&Bn(v,{select:!0})):m.shiftKey&&p===v&&(m.preventDefault(),n&&Bn(S,{select:!0})):p===h&&m.preventDefault()}},[n,r,x.paused]);return u.jsx(be.div,{tabIndex:-1,...i,ref:y,onKeyDown:k})});Bf.displayName=t2;function n2(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Bn(r,{select:t}),document.activeElement!==n)return}function r2(e){const t=qv(e),n=om(t,e),r=om(t.reverse(),e);return[n,r]}function qv(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function om(e,t){for(const n of e)if(!s2(n,{upTo:t}))return n}function s2(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function o2(e){return e instanceof HTMLInputElement&&"select"in e}function Bn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&o2(e)&&t&&e.select()}}var im=i2();function i2(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=am(e,t),e.unshift(t)},remove(t){var n;e=am(e,t),(n=e[0])==null||n.resume()}}}function am(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function a2(e){return e.filter(t=>t.tagName!=="A")}var l2="Portal",Vf=g.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[s,o]=g.useState(!1);ot(()=>o(!0),[]);const i=n||s&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return i?xS.createPortal(u.jsx(be.div,{...r,ref:t}),i):null});Vf.displayName=l2;var zc=0;function Gv(){g.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??lm()),document.body.insertAdjacentElement("beforeend",e[1]??lm()),zc++,()=>{zc===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),zc--}},[])}function lm(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var un=function(){return un=Object.assign||function(t){for(var n,r=1,s=arguments.length;r<s;r++){n=arguments[r];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},un.apply(this,arguments)};function Yv(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}function c2(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,o;r<s;r++)(o||!(r in t))&&(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))}var Ra="right-scroll-bar-position",Ia="width-before-scroll-bar",u2="with-scroll-bars-hidden",d2="--removed-body-scroll-bar-size";function $c(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function f2(e,t){var n=g.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var s=n.value;s!==r&&(n.value=r,n.callback(r,s))}}}})[0];return n.callback=t,n.facade}var h2=typeof window<"u"?g.useLayoutEffect:g.useEffect,cm=new WeakMap;function p2(e,t){var n=f2(null,function(r){return e.forEach(function(s){return $c(s,r)})});return h2(function(){var r=cm.get(n);if(r){var s=new Set(r),o=new Set(e),i=n.current;s.forEach(function(a){o.has(a)||$c(a,null)}),o.forEach(function(a){s.has(a)||$c(a,i)})}cm.set(n,e)},[e]),n}function m2(e){return e}function g2(e,t){t===void 0&&(t=m2);var n=[],r=!1,s={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(o){var i=t(o,r);return n.push(i),function(){n=n.filter(function(a){return a!==i})}},assignSyncMedium:function(o){for(r=!0;n.length;){var i=n;n=[],i.forEach(o)}n={push:function(a){return o(a)},filter:function(){return n}}},assignMedium:function(o){r=!0;var i=[];if(n.length){var a=n;n=[],a.forEach(o),i=n}var l=function(){var d=i;i=[],d.forEach(o)},c=function(){return Promise.resolve().then(l)};c(),n={push:function(d){i.push(d),c()},filter:function(d){return i=i.filter(d),n}}}};return s}function y2(e){e===void 0&&(e={});var t=g2(null);return t.options=un({async:!0,ssr:!1},e),t}var Xv=function(e){var t=e.sideCar,n=Yv(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return g.createElement(r,un({},n))};Xv.isSideCarExport=!0;function v2(e,t){return e.useMedium(t),Xv}var Jv=y2(),Uc=function(){},Vl=g.forwardRef(function(e,t){var n=g.useRef(null),r=g.useState({onScrollCapture:Uc,onWheelCapture:Uc,onTouchMoveCapture:Uc}),s=r[0],o=r[1],i=e.forwardProps,a=e.children,l=e.className,c=e.removeScrollBar,d=e.enabled,f=e.shards,y=e.sideCar,x=e.noRelative,k=e.noIsolation,m=e.inert,w=e.allowPinchZoom,p=e.as,h=p===void 0?"div":p,v=e.gapMode,S=Yv(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=y,E=p2([n,t]),_=un(un({},S),s);return g.createElement(g.Fragment,null,d&&g.createElement(b,{sideCar:Jv,removeScrollBar:c,shards:f,noRelative:x,noIsolation:k,inert:m,setCallbacks:o,allowPinchZoom:!!w,lockRef:n,gapMode:v}),i?g.cloneElement(g.Children.only(a),un(un({},_),{ref:E})):g.createElement(h,un({},_,{className:l,ref:E}),a))});Vl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Vl.classNames={fullWidth:Ia,zeroRight:Ra};var x2=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function w2(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=x2();return t&&e.setAttribute("nonce",t),e}function S2(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function k2(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var b2=function(){var e=0,t=null;return{add:function(n){e==0&&(t=w2())&&(S2(t,n),k2(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},C2=function(){var e=b2();return function(t,n){g.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},e0=function(){var e=C2(),t=function(n){var r=n.styles,s=n.dynamic;return e(r,s),null};return t},E2={left:0,top:0,right:0,gap:0},Bc=function(e){return parseInt(e||"",10)||0},_2=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],s=t[e==="padding"?"paddingRight":"marginRight"];return[Bc(n),Bc(r),Bc(s)]},N2=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return E2;var t=_2(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},T2=e0(),zs="data-scroll-locked",j2=function(e,t,n,r){var s=e.left,o=e.top,i=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(u2,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(zs,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(o,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ra,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Ia,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Ra," .").concat(Ra,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Ia," .").concat(Ia,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(zs,`] {
    `).concat(d2,": ").concat(a,`px;
  }
`)},um=function(){var e=parseInt(document.body.getAttribute(zs)||"0",10);return isFinite(e)?e:0},R2=function(){g.useEffect(function(){return document.body.setAttribute(zs,(um()+1).toString()),function(){var e=um()-1;e<=0?document.body.removeAttribute(zs):document.body.setAttribute(zs,e.toString())}},[])},I2=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,s=r===void 0?"margin":r;R2();var o=g.useMemo(function(){return N2(s)},[s]);return g.createElement(T2,{styles:j2(o,!t,s,n?"":"!important")})},fd=!1;if(typeof window<"u")try{var ua=Object.defineProperty({},"passive",{get:function(){return fd=!0,!0}});window.addEventListener("test",ua,ua),window.removeEventListener("test",ua,ua)}catch{fd=!1}var ps=fd?{passive:!1}:!1,P2=function(e){return e.tagName==="TEXTAREA"},t0=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!P2(e)&&n[t]==="visible")},D2=function(e){return t0(e,"overflowY")},O2=function(e){return t0(e,"overflowX")},dm=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var s=n0(e,r);if(s){var o=r0(e,r),i=o[1],a=o[2];if(i>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},A2=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},M2=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},n0=function(e,t){return e==="v"?D2(t):O2(t)},r0=function(e,t){return e==="v"?A2(t):M2(t)},L2=function(e,t){return e==="h"&&t==="rtl"?-1:1},F2=function(e,t,n,r,s){var o=L2(e,window.getComputedStyle(t).direction),i=o*r,a=n.target,l=t.contains(a),c=!1,d=i>0,f=0,y=0;do{if(!a)break;var x=r0(e,a),k=x[0],m=x[1],w=x[2],p=m-w-o*k;(k||p)&&n0(e,a)&&(f+=p,y+=k);var h=a.parentNode;a=h&&h.nodeType===Node.DOCUMENT_FRAGMENT_NODE?h.host:h}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(d&&Math.abs(f)<1||!d&&Math.abs(y)<1)&&(c=!0),c},da=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},fm=function(e){return[e.deltaX,e.deltaY]},hm=function(e){return e&&"current"in e?e.current:e},z2=function(e,t){return e[0]===t[0]&&e[1]===t[1]},$2=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},U2=0,ms=[];function B2(e){var t=g.useRef([]),n=g.useRef([0,0]),r=g.useRef(),s=g.useState(U2++)[0],o=g.useState(e0)[0],i=g.useRef(e);g.useEffect(function(){i.current=e},[e]),g.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var m=c2([e.lockRef.current],(e.shards||[]).map(hm),!0).filter(Boolean);return m.forEach(function(w){return w.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),m.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var a=g.useCallback(function(m,w){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!i.current.allowPinchZoom;var p=da(m),h=n.current,v="deltaX"in m?m.deltaX:h[0]-p[0],S="deltaY"in m?m.deltaY:h[1]-p[1],b,E=m.target,_=Math.abs(v)>Math.abs(S)?"h":"v";if("touches"in m&&_==="h"&&E.type==="range")return!1;var N=dm(_,E);if(!N)return!0;if(N?b=_:(b=_==="v"?"h":"v",N=dm(_,E)),!N)return!1;if(!r.current&&"changedTouches"in m&&(v||S)&&(r.current=b),!b)return!0;var j=r.current||b;return F2(j,w,m,j==="h"?v:S)},[]),l=g.useCallback(function(m){var w=m;if(!(!ms.length||ms[ms.length-1]!==o)){var p="deltaY"in w?fm(w):da(w),h=t.current.filter(function(b){return b.name===w.type&&(b.target===w.target||w.target===b.shadowParent)&&z2(b.delta,p)})[0];if(h&&h.should){w.cancelable&&w.preventDefault();return}if(!h){var v=(i.current.shards||[]).map(hm).filter(Boolean).filter(function(b){return b.contains(w.target)}),S=v.length>0?a(w,v[0]):!i.current.noIsolation;S&&w.cancelable&&w.preventDefault()}}},[]),c=g.useCallback(function(m,w,p,h){var v={name:m,delta:w,target:p,should:h,shadowParent:V2(p)};t.current.push(v),setTimeout(function(){t.current=t.current.filter(function(S){return S!==v})},1)},[]),d=g.useCallback(function(m){n.current=da(m),r.current=void 0},[]),f=g.useCallback(function(m){c(m.type,fm(m),m.target,a(m,e.lockRef.current))},[]),y=g.useCallback(function(m){c(m.type,da(m),m.target,a(m,e.lockRef.current))},[]);g.useEffect(function(){return ms.push(o),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:y}),document.addEventListener("wheel",l,ps),document.addEventListener("touchmove",l,ps),document.addEventListener("touchstart",d,ps),function(){ms=ms.filter(function(m){return m!==o}),document.removeEventListener("wheel",l,ps),document.removeEventListener("touchmove",l,ps),document.removeEventListener("touchstart",d,ps)}},[]);var x=e.removeScrollBar,k=e.inert;return g.createElement(g.Fragment,null,k?g.createElement(o,{styles:$2(s)}):null,x?g.createElement(I2,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function V2(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const H2=v2(Jv,B2);var Hf=g.forwardRef(function(e,t){return g.createElement(Vl,un({},e,{ref:t,sideCar:H2}))});Hf.classNames=Vl.classNames;var W2=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},gs=new WeakMap,fa=new WeakMap,ha={},Vc=0,s0=function(e){return e&&(e.host||s0(e.parentNode))},Q2=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=s0(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Z2=function(e,t,n,r){var s=Q2(t,Array.isArray(e)?e:[e]);ha[n]||(ha[n]=new WeakMap);var o=ha[n],i=[],a=new Set,l=new Set(s),c=function(f){!f||a.has(f)||(a.add(f),c(f.parentNode))};s.forEach(c);var d=function(f){!f||l.has(f)||Array.prototype.forEach.call(f.children,function(y){if(a.has(y))d(y);else try{var x=y.getAttribute(r),k=x!==null&&x!=="false",m=(gs.get(y)||0)+1,w=(o.get(y)||0)+1;gs.set(y,m),o.set(y,w),i.push(y),m===1&&k&&fa.set(y,!0),w===1&&y.setAttribute(n,"true"),k||y.setAttribute(r,"true")}catch(p){console.error("aria-hidden: cannot operate on ",y,p)}})};return d(t),a.clear(),Vc++,function(){i.forEach(function(f){var y=gs.get(f)-1,x=o.get(f)-1;gs.set(f,y),o.set(f,x),y||(fa.has(f)||f.removeAttribute(r),fa.delete(f)),x||f.removeAttribute(n)}),Vc--,Vc||(gs=new WeakMap,gs=new WeakMap,fa=new WeakMap,ha={})}},o0=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),s=W2(e);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live], script"))),Z2(r,s,n,"aria-hidden")):function(){return null}},Hl="Dialog",[i0,JN]=zi(Hl),[K2,nn]=i0(Hl),a0=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:s,onOpenChange:o,modal:i=!0}=e,a=g.useRef(null),l=g.useRef(null),[c,d]=hl({prop:r,defaultProp:s??!1,onChange:o,caller:Hl});return u.jsx(K2,{scope:t,triggerRef:a,contentRef:l,contentId:Fs(),titleId:Fs(),descriptionId:Fs(),open:c,onOpenChange:d,onOpenToggle:g.useCallback(()=>d(f=>!f),[d]),modal:i,children:n})};a0.displayName=Hl;var l0="DialogTrigger",q2=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=nn(l0,n),o=Ae(t,s.triggerRef);return u.jsx(be.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Zf(s.open),...r,ref:o,onClick:Ce(e.onClick,s.onOpenToggle)})});q2.displayName=l0;var Wf="DialogPortal",[G2,c0]=i0(Wf,{forceMount:void 0}),u0=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:s}=e,o=nn(Wf,t);return u.jsx(G2,{scope:t,forceMount:n,children:g.Children.map(r,i=>u.jsx($i,{present:n||o.open,children:u.jsx(Vf,{asChild:!0,container:s,children:i})}))})};u0.displayName=Wf;var pl="DialogOverlay",d0=g.forwardRef((e,t)=>{const n=c0(pl,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,o=nn(pl,e.__scopeDialog);return o.modal?u.jsx($i,{present:r||o.open,children:u.jsx(X2,{...s,ref:t})}):null});d0.displayName=pl;var Y2=ao("DialogOverlay.RemoveScroll"),X2=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=nn(pl,n);return u.jsx(Hf,{as:Y2,allowPinchZoom:!0,shards:[s.contentRef],children:u.jsx(be.div,{"data-state":Zf(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),as="DialogContent",f0=g.forwardRef((e,t)=>{const n=c0(as,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,o=nn(as,e.__scopeDialog);return u.jsx($i,{present:r||o.open,children:o.modal?u.jsx(J2,{...s,ref:t}):u.jsx(eE,{...s,ref:t})})});f0.displayName=as;var J2=g.forwardRef((e,t)=>{const n=nn(as,e.__scopeDialog),r=g.useRef(null),s=Ae(t,n.contentRef,r);return g.useEffect(()=>{const o=r.current;if(o)return o0(o)},[]),u.jsx(h0,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Ce(e.onCloseAutoFocus,o=>{var i;o.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:Ce(e.onPointerDownOutside,o=>{const i=o.detail.originalEvent,a=i.button===0&&i.ctrlKey===!0;(i.button===2||a)&&o.preventDefault()}),onFocusOutside:Ce(e.onFocusOutside,o=>o.preventDefault())})}),eE=g.forwardRef((e,t)=>{const n=nn(as,e.__scopeDialog),r=g.useRef(!1),s=g.useRef(!1);return u.jsx(h0,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:o=>{var i,a;(i=e.onCloseAutoFocus)==null||i.call(e,o),o.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),o.preventDefault()),r.current=!1,s.current=!1},onInteractOutside:o=>{var l,c;(l=e.onInteractOutside)==null||l.call(e,o),o.defaultPrevented||(r.current=!0,o.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const i=o.target;((c=n.triggerRef.current)==null?void 0:c.contains(i))&&o.preventDefault(),o.detail.originalEvent.type==="focusin"&&s.current&&o.preventDefault()}})}),h0=g.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:o,...i}=e,a=nn(as,n),l=g.useRef(null),c=Ae(t,l);return Gv(),u.jsxs(u.Fragment,{children:[u.jsx(Bf,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:o,children:u.jsx(Uf,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Zf(a.open),...i,ref:c,onDismiss:()=>a.onOpenChange(!1)})}),u.jsxs(u.Fragment,{children:[u.jsx(tE,{titleId:a.titleId}),u.jsx(rE,{contentRef:l,descriptionId:a.descriptionId})]})]})}),Qf="DialogTitle",p0=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=nn(Qf,n);return u.jsx(be.h2,{id:s.titleId,...r,ref:t})});p0.displayName=Qf;var m0="DialogDescription",g0=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=nn(m0,n);return u.jsx(be.p,{id:s.descriptionId,...r,ref:t})});g0.displayName=m0;var y0="DialogClose",v0=g.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=nn(y0,n);return u.jsx(be.button,{type:"button",...r,ref:t,onClick:Ce(e.onClick,()=>s.onOpenChange(!1))})});v0.displayName=y0;function Zf(e){return e?"open":"closed"}var x0="DialogTitleWarning",[eT,w0]=IC(x0,{contentName:as,titleName:Qf,docsSlug:"dialog"}),tE=({titleId:e})=>{const t=w0(x0),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return g.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},nE="DialogDescriptionWarning",rE=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${w0(nE).contentName}}.`;return g.useEffect(()=>{var o;const s=(o=e.current)==null?void 0:o.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},sE=a0,oE=u0,S0=d0,k0=f0,b0=p0,C0=g0,iE=v0;const Kf=sE,aE=oE,E0=g.forwardRef(({className:e,...t},n)=>u.jsx(S0,{ref:n,className:B("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));E0.displayName=S0.displayName;const Wl=g.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(aE,{children:[u.jsx(E0,{}),u.jsxs(k0,{ref:r,className:B("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,u.jsxs(iE,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[u.jsx(Dn,{className:"h-4 w-4"}),u.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Wl.displayName=k0.displayName;const Ql=({className:e,...t})=>u.jsx("div",{className:B("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});Ql.displayName="DialogHeader";const qf=({className:e,...t})=>u.jsx("div",{className:B("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});qf.displayName="DialogFooter";const Zl=g.forwardRef(({className:e,...t},n)=>u.jsx(b0,{ref:n,className:B("text-lg font-semibold leading-none tracking-tight",e),...t}));Zl.displayName=b0.displayName;const Kl=g.forwardRef(({className:e,...t},n)=>u.jsx(C0,{ref:n,className:B("text-sm text-muted-foreground",e),...t}));Kl.displayName=C0.displayName;function Gf({open:e,onOpenChange:t,onConfirm:n,title:r="确认删除",description:s,itemName:o,isLoading:i=!1,variant:a="danger"}){const l=()=>{n(),t(!1)},c=()=>{t(!1)},d=o?`确定要删除"${o}"吗？此操作无法撤销。`:"确定要删除这个项目吗？此操作无法撤销。";return u.jsx(Kf,{open:e,onOpenChange:t,children:u.jsxs(Wl,{className:"sm:max-w-[425px]",children:[u.jsxs(Ql,{className:"text-center",children:[u.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:u.jsx(_f,{className:"h-6 w-6 text-red-600"})}),u.jsx(Zl,{className:"text-lg font-semibold text-gray-900",children:r}),u.jsx(Kl,{className:"text-sm text-gray-500 mt-2",children:s||d})]}),u.jsxs(qf,{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3 mt-6",children:[u.jsxs(X,{variant:"outline",onClick:c,disabled:i,className:"sm:w-auto w-full",children:[u.jsx(Dn,{className:"h-4 w-4 mr-2"}),"取消"]}),u.jsxs(X,{variant:"destructive",onClick:l,disabled:i,className:"sm:w-auto w-full",children:[u.jsx(wr,{className:"h-4 w-4 mr-2"}),i?"删除中...":"确认删除"]})]})]})})}function lE({open:e,onOpenChange:t,onConfirm:n,count:r,isLoading:s=!1}){const o=()=>{n(),t(!1)},i=()=>{t(!1)};return u.jsx(Kf,{open:e,onOpenChange:t,children:u.jsxs(Wl,{className:"sm:max-w-[425px]",children:[u.jsxs(Ql,{className:"text-center",children:[u.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:u.jsx(wr,{className:"h-6 w-6 text-red-600"})}),u.jsx(Zl,{className:"text-lg font-semibold text-gray-900",children:"批量删除确认"}),u.jsxs(Kl,{className:"text-sm text-gray-500 mt-2",children:["确定要删除选中的 ",u.jsx("span",{className:"font-medium text-red-600",children:r})," 个任务吗？",u.jsx("br",{}),"此操作无法撤销。"]})]}),u.jsxs(qf,{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3 mt-6",children:[u.jsxs(X,{variant:"outline",onClick:i,disabled:s,className:"sm:w-auto w-full",children:[u.jsx(Dn,{className:"h-4 w-4 mr-2"}),"取消"]}),u.jsxs(X,{variant:"destructive",onClick:o,disabled:s,className:"sm:w-auto w-full",children:[u.jsx(wr,{className:"h-4 w-4 mr-2"}),s?"删除中...":`删除 ${r} 个任务`]})]})]})})}function _0({hierarchy:e,onToggleComplete:t,onAddSubtask:n,onEdit:r,onTaskClick:s,className:o}){const{task:i,children:a,depth:l}=e,[c,d]=g.useState(!0),[f,y]=g.useState(!1),[x,k]=g.useState(!1),[m,w]=g.useState(!1),p=g.useRef(null);Li(),Av();const h=Mv(),v=Af(),{isSelectionMode:S,isTaskSelected:b,toggleTaskSelection:E,enterSelectionMode:_,addDeletedTask:N,removeDeletedTask:j}=Fi(),{showDeleteSuccess:R,showError:L}=ko(),A=Pf(i.dueDate),Z=a.length>0,J=a.filter(q=>q.task.isCompleted).length,ae=Z?J/a.length*100:0,K=b(i.id),ee=q=>{const M=["","ml-4 md:ml-6","ml-8 md:ml-12","ml-12 md:ml-18","ml-16 md:ml-24"];return M[q]||M[4]},T=kC({onLongPress:O,onClick:$});function D(){S?E(i.id):t==null||t(i.id)}function $(){S?(E(i.id),p.current&&bC(p.current,!K)):s==null||s(i)}function O(){S||(_(),E(i.id))}function se(){n==null||n(i.id)}function oe(){w(!0)}async function he(){k(!0);try{p.current&&Mc.buttonClick(p.current),await h.mutateAsync(i.id),N({id:i.id,content:i.content,deletedAt:Date.now()}),R(i.content,async()=>{try{j(i.id),await v.mutateAsync(i.id),R("撤销成功",()=>{})}catch(q){console.error("Failed to restore task:",q);let M="无法恢复已删除的任务";q instanceof Error&&(q.message.includes("任务不存在或未被删除")?M="该任务可能已经被恢复":M=q.message),L("撤销失败",M)}})}catch(q){console.error("Failed to delete task:",q),L("删除失败",q instanceof Error?q.message:"删除任务时发生错误")}finally{k(!1),w(!1)}}function _e(q){return Mf[q]||"bg-gray-100"}return u.jsxs("div",{className:B("w-full",ee(l),o),children:[u.jsx("div",{ref:p,...T,className:B("group bg-card rounded-xl border border-border/50 transition-all duration-200 hover:border-border hover:shadow-md hover:shadow-primary/5 hover:-translate-y-0.5",i.isCompleted&&"opacity-75",A&&!i.isCompleted&&"border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20",S&&"cursor-pointer",K&&"border-primary bg-primary/5 shadow-md transform scale-[0.98]",x&&"opacity-50 pointer-events-none",l>0&&"bg-muted/30 border-muted hover:bg-muted/40"),onMouseEnter:()=>{y(!0),p.current&&!S&&Mc.hover(p.current,!0)},onMouseLeave:()=>{y(!1),p.current&&!S&&Mc.hover(p.current,!1)},children:u.jsxs("div",{className:"flex items-start gap-3 md:gap-4 p-3 md:p-4",children:[u.jsx("div",{className:"flex-shrink-0 w-6 h-6 flex items-center justify-center",children:Z?u.jsx("button",{onClick:()=>d(!c),className:"w-full h-full flex items-center justify-center hover:bg-gray-100 rounded transition-all duration-200",title:c?"收起子任务":"展开子任务",children:u.jsx(pv,{className:B("w-4 h-4 text-gray-500 transition-transform duration-200",c&&"rotate-90")})}):u.jsx("div",{className:"w-4 h-4"})}),S?u.jsx($f,{checked:K,onCheckedChange:()=>E(i.id),className:"mt-1"}):u.jsx(X,{variant:"ghost",size:"sm",onClick:D,className:"p-0 h-6 w-6 rounded-full hover:bg-transparent",children:i.isCompleted?u.jsx(Nf,{className:"h-6 w-6 text-green-600 fill-green-100"}):u.jsx(jf,{className:"h-6 w-6 text-muted-foreground hover:text-primary transition-colors"})}),u.jsx("div",{className:"flex-1 min-w-0",children:u.jsxs("div",{className:"flex items-start justify-between gap-2",children:[u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsx("h3",{className:B("text-sm md:text-base font-medium text-foreground cursor-pointer hover:text-primary transition-colors",i.isCompleted&&"line-through text-muted-foreground",l>0&&"text-xs md:text-sm"),onClick:$,children:i.content}),u.jsxs("div",{className:"flex items-center gap-3 mt-2",children:[i.priority!==Re.MEDIUM&&u.jsx("div",{className:B("w-2 h-2 rounded-full",_e(i.priority))}),i.dueDate&&u.jsxs("div",{className:B("flex items-center gap-1 text-xs px-2 py-1 rounded-md",A&&!i.isCompleted?"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-950/30":"text-muted-foreground bg-muted/50"),children:[u.jsx(ns,{className:"h-3 w-3"}),u.jsx("span",{children:Cv(i.dueDate)})]}),Z&&u.jsx("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:u.jsxs("div",{className:"flex items-center gap-1",children:[u.jsx("div",{className:"w-12 h-1.5 bg-muted rounded-full overflow-hidden",children:u.jsx("div",{className:"h-full bg-primary transition-all duration-300",style:{width:`${ae}%`}})}),u.jsxs("span",{children:[J,"/",a.length]})]})})]})]}),u.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[n&&l<4&&u.jsx(X,{variant:"ghost",size:"sm",onClick:se,className:"p-1 h-6 w-6",title:"添加子任务",children:u.jsx(rs,{className:"h-3 w-3"})}),u.jsx(X,{variant:"ghost",size:"sm",className:"p-1 h-6 w-6",onClick:()=>r==null?void 0:r(i),title:"编辑任务",children:u.jsx(Rf,{className:"h-3 w-3"})}),u.jsx(X,{variant:"ghost",size:"sm",className:"p-1 h-6 w-6 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20",onClick:oe,title:"删除任务",disabled:x,children:u.jsx(wr,{className:"h-3 w-3"})})]})]})})]})}),Z&&u.jsx("div",{className:B("overflow-hidden transition-all duration-300 ease-in-out",c?"max-h-[2000px] opacity-100 mt-3":"max-h-0 opacity-0 mt-0"),children:u.jsx("div",{className:"space-y-3",children:a.map(q=>u.jsx(_0,{hierarchy:q,onToggleComplete:t,onAddSubtask:n,onEdit:r,onTaskClick:s},q.task.id))})}),u.jsx(Gf,{open:m,onOpenChange:w,onConfirm:he,itemName:i.content,isLoading:x,description:Z?`确定要删除"${i.content}"吗？这将同时删除 ${a.length} 个子任务。此操作可在30秒内撤销。`:`确定要删除"${i.content}"吗？此操作可在30秒内撤销。`})]})}function cE(){const[e,t]=g.useState(!1),[n,r]=g.useState(!1),[s,o]=g.useState(!1),i=g.useRef(null),{selectedCount:a,isSelectionMode:l,exitSelectionMode:c,clearSelection:d,selectedTaskIds:f,selectAllTasks:y,addDeletedTask:x}=Fi(),{data:k=[]}=$l(),{showBatchDeleteSuccess:m,showBatchRestoreSuccess:w,showError:p}=ko();if(g.useEffect(()=>{const E=l&&a>0;E&&!s?(o(!0),setTimeout(()=>{i.current&&CC(i.current)},10)):!E&&s&&(i.current?EC(i.current).then(()=>{o(!1)}):o(!1))},[l,a,s]),!s)return null;const h=k.filter(E=>!E.deletedAt),v=a===h.length,S=()=>{v?d():y(h.map(E=>E.id))},b=async()=>{if(!n){r(!0);try{if(f.length===0){p("批量删除失败","没有选中任何任务");return}const E=k.filter(N=>f.includes(N.id)&&!N.deletedAt);if(E.length===0){p("批量删除失败","选中的任务已被删除或不存在");return}console.log("Attempting to batch delete tasks:",E.map(N=>N.id));const _=await window.electronAPI.task.batchSoftDelete(E.map(N=>N.id));console.log("Batch delete result:",_),_&&_.deletedCount>0?(E.forEach(N=>{_.deletedTaskIds.includes(N.id)&&x({id:N.id,content:N.content,deletedAt:Date.now()})}),m(_.deletedCount,async()=>{try{const N=await window.electronAPI.task.batchRestore(_.deletedTaskIds);w(N.restoredCount||_.deletedTaskIds.length)}catch(N){console.error("Failed to restore tasks:",N);const j=N instanceof Error?N.message:"未知错误";p("撤销失败",`无法恢复已删除的任务: ${j}`)}}),c()):p("批量删除失败","没有任务被成功删除")}catch(E){console.error("Batch delete failed:",E);const _=E instanceof Error?E.message:"未知错误";p("批量删除失败",`删除操作失败: ${_}`)}finally{r(!1)}}};return u.jsxs(u.Fragment,{children:[u.jsxs("div",{ref:i,className:B("fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40","bg-white border border-gray-200 rounded-lg shadow-lg p-3","flex items-center gap-3 min-w-[300px]","transition-all duration-300"),children:[u.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[u.jsx(Ju,{className:"h-4 w-4"}),"已选择 ",a," 个任务"]}),u.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[u.jsx(X,{size:"sm",variant:"outline",onClick:S,children:v?u.jsxs(u.Fragment,{children:[u.jsx(mk,{className:"h-4 w-4 mr-1"}),"取消全选"]}):u.jsxs(u.Fragment,{children:[u.jsx(Ju,{className:"h-4 w-4 mr-1"}),"全选"]})}),u.jsxs(X,{size:"sm",variant:"outline",onClick:d,children:[u.jsx(Dn,{className:"h-4 w-4 mr-1"}),"取消选择"]}),u.jsxs(X,{size:"sm",variant:"destructive",onClick:()=>t(!0),disabled:n,children:[u.jsx(wr,{className:"h-4 w-4 mr-1"}),n?"删除中...":"删除选中"]}),u.jsx(X,{size:"sm",variant:"ghost",onClick:c,children:u.jsx(Dn,{className:"h-4 w-4"})})]})]}),u.jsx(lE,{open:e,onOpenChange:t,onConfirm:b,count:a,isLoading:n})]})}const Yf=g.forwardRef(({className:e,...t},n)=>u.jsx("textarea",{className:B("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...t}));Yf.displayName="Textarea";function pm(e,[t,n]){return Math.min(n,Math.max(t,e))}function uE(e){const t=e+"CollectionProvider",[n,r]=zi(t),[s,o]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=m=>{const{scope:w,children:p}=m,h=We.useRef(null),v=We.useRef(new Map).current;return u.jsx(s,{scope:w,itemMap:v,collectionRef:h,children:p})};i.displayName=t;const a=e+"CollectionSlot",l=ao(a),c=We.forwardRef((m,w)=>{const{scope:p,children:h}=m,v=o(a,p),S=Ae(w,v.collectionRef);return u.jsx(l,{ref:S,children:h})});c.displayName=a;const d=e+"CollectionItemSlot",f="data-radix-collection-item",y=ao(d),x=We.forwardRef((m,w)=>{const{scope:p,children:h,...v}=m,S=We.useRef(null),b=Ae(w,S),E=o(d,p);return We.useEffect(()=>(E.itemMap.set(S,{ref:S,...v}),()=>void E.itemMap.delete(S))),u.jsx(y,{[f]:"",ref:b,children:h})});x.displayName=d;function k(m){const w=o(e+"CollectionConsumer",m);return We.useCallback(()=>{const h=w.collectionRef.current;if(!h)return[];const v=Array.from(h.querySelectorAll(`[${f}]`));return Array.from(w.itemMap.values()).sort((E,_)=>v.indexOf(E.ref.current)-v.indexOf(_.ref.current))},[w.collectionRef,w.itemMap])}return[{Provider:i,Slot:c,ItemSlot:x},k,r]}var dE=g.createContext(void 0);function fE(e){const t=g.useContext(dE);return e||t||"ltr"}const hE=["top","right","bottom","left"],br=Math.min,kt=Math.max,ml=Math.round,pa=Math.floor,mn=e=>({x:e,y:e}),pE={left:"right",right:"left",bottom:"top",top:"bottom"},mE={start:"end",end:"start"};function hd(e,t,n){return kt(e,br(t,n))}function An(e,t){return typeof e=="function"?e(t):e}function Mn(e){return e.split("-")[0]}function bo(e){return e.split("-")[1]}function Xf(e){return e==="x"?"y":"x"}function Jf(e){return e==="y"?"height":"width"}function dn(e){return["top","bottom"].includes(Mn(e))?"y":"x"}function eh(e){return Xf(dn(e))}function gE(e,t,n){n===void 0&&(n=!1);const r=bo(e),s=eh(e),o=Jf(s);let i=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(i=gl(i)),[i,gl(i)]}function yE(e){const t=gl(e);return[pd(e),t,pd(t)]}function pd(e){return e.replace(/start|end/g,t=>mE[t])}function vE(e,t,n){const r=["left","right"],s=["right","left"],o=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:r:t?r:s;case"left":case"right":return t?o:i;default:return[]}}function xE(e,t,n,r){const s=bo(e);let o=vE(Mn(e),n==="start",r);return s&&(o=o.map(i=>i+"-"+s),t&&(o=o.concat(o.map(pd)))),o}function gl(e){return e.replace(/left|right|bottom|top/g,t=>pE[t])}function wE(e){return{top:0,right:0,bottom:0,left:0,...e}}function N0(e){return typeof e!="number"?wE(e):{top:e,right:e,bottom:e,left:e}}function yl(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function mm(e,t,n){let{reference:r,floating:s}=e;const o=dn(t),i=eh(t),a=Jf(i),l=Mn(t),c=o==="y",d=r.x+r.width/2-s.width/2,f=r.y+r.height/2-s.height/2,y=r[a]/2-s[a]/2;let x;switch(l){case"top":x={x:d,y:r.y-s.height};break;case"bottom":x={x:d,y:r.y+r.height};break;case"right":x={x:r.x+r.width,y:f};break;case"left":x={x:r.x-s.width,y:f};break;default:x={x:r.x,y:r.y}}switch(bo(t)){case"start":x[i]-=y*(n&&c?-1:1);break;case"end":x[i]+=y*(n&&c?-1:1);break}return x}const SE=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:o=[],platform:i}=n,a=o.filter(Boolean),l=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:s}),{x:d,y:f}=mm(c,r,l),y=r,x={},k=0;for(let m=0;m<a.length;m++){const{name:w,fn:p}=a[m],{x:h,y:v,data:S,reset:b}=await p({x:d,y:f,initialPlacement:r,placement:y,strategy:s,middlewareData:x,rects:c,platform:i,elements:{reference:e,floating:t}});d=h??d,f=v??f,x={...x,[w]:{...x[w],...S}},b&&k<=50&&(k++,typeof b=="object"&&(b.placement&&(y=b.placement),b.rects&&(c=b.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:s}):b.rects),{x:d,y:f}=mm(c,y,l)),m=-1)}return{x:d,y:f,placement:y,strategy:s,middlewareData:x}};async function bi(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:o,rects:i,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:y=!1,padding:x=0}=An(t,e),k=N0(x),w=a[y?f==="floating"?"reference":"floating":f],p=yl(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(w)))==null||n?w:w.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:c,rootBoundary:d,strategy:l})),h=f==="floating"?{x:r,y:s,width:i.floating.width,height:i.floating.height}:i.reference,v=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),S=await(o.isElement==null?void 0:o.isElement(v))?await(o.getScale==null?void 0:o.getScale(v))||{x:1,y:1}:{x:1,y:1},b=yl(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:h,offsetParent:v,strategy:l}):h);return{top:(p.top-b.top+k.top)/S.y,bottom:(b.bottom-p.bottom+k.bottom)/S.y,left:(p.left-b.left+k.left)/S.x,right:(b.right-p.right+k.right)/S.x}}const kE=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:o,platform:i,elements:a,middlewareData:l}=t,{element:c,padding:d=0}=An(e,t)||{};if(c==null)return{};const f=N0(d),y={x:n,y:r},x=eh(s),k=Jf(x),m=await i.getDimensions(c),w=x==="y",p=w?"top":"left",h=w?"bottom":"right",v=w?"clientHeight":"clientWidth",S=o.reference[k]+o.reference[x]-y[x]-o.floating[k],b=y[x]-o.reference[x],E=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let _=E?E[v]:0;(!_||!await(i.isElement==null?void 0:i.isElement(E)))&&(_=a.floating[v]||o.floating[k]);const N=S/2-b/2,j=_/2-m[k]/2-1,R=br(f[p],j),L=br(f[h],j),A=R,Z=_-m[k]-L,J=_/2-m[k]/2+N,ae=hd(A,J,Z),K=!l.arrow&&bo(s)!=null&&J!==ae&&o.reference[k]/2-(J<A?R:L)-m[k]/2<0,ee=K?J<A?J-A:J-Z:0;return{[x]:y[x]+ee,data:{[x]:ae,centerOffset:J-ae-ee,...K&&{alignmentOffset:ee}},reset:K}}}),bE=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:o,rects:i,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:y,fallbackStrategy:x="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:m=!0,...w}=An(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const p=Mn(s),h=dn(a),v=Mn(a)===a,S=await(l.isRTL==null?void 0:l.isRTL(c.floating)),b=y||(v||!m?[gl(a)]:yE(a)),E=k!=="none";!y&&E&&b.push(...xE(a,m,k,S));const _=[a,...b],N=await bi(t,w),j=[];let R=((r=o.flip)==null?void 0:r.overflows)||[];if(d&&j.push(N[p]),f){const J=gE(s,i,S);j.push(N[J[0]],N[J[1]])}if(R=[...R,{placement:s,overflows:j}],!j.every(J=>J<=0)){var L,A;const J=(((L=o.flip)==null?void 0:L.index)||0)+1,ae=_[J];if(ae&&(!(f==="alignment"?h!==dn(ae):!1)||R.every(T=>T.overflows[0]>0&&dn(T.placement)===h)))return{data:{index:J,overflows:R},reset:{placement:ae}};let K=(A=R.filter(ee=>ee.overflows[0]<=0).sort((ee,T)=>ee.overflows[1]-T.overflows[1])[0])==null?void 0:A.placement;if(!K)switch(x){case"bestFit":{var Z;const ee=(Z=R.filter(T=>{if(E){const D=dn(T.placement);return D===h||D==="y"}return!0}).map(T=>[T.placement,T.overflows.filter(D=>D>0).reduce((D,$)=>D+$,0)]).sort((T,D)=>T[1]-D[1])[0])==null?void 0:Z[0];ee&&(K=ee);break}case"initialPlacement":K=a;break}if(s!==K)return{reset:{placement:K}}}return{}}}};function gm(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function ym(e){return hE.some(t=>e[t]>=0)}const CE=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=An(e,t);switch(r){case"referenceHidden":{const o=await bi(t,{...s,elementContext:"reference"}),i=gm(o,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:ym(i)}}}case"escaped":{const o=await bi(t,{...s,altBoundary:!0}),i=gm(o,n.floating);return{data:{escapedOffsets:i,escaped:ym(i)}}}default:return{}}}}};async function EE(e,t){const{placement:n,platform:r,elements:s}=e,o=await(r.isRTL==null?void 0:r.isRTL(s.floating)),i=Mn(n),a=bo(n),l=dn(n)==="y",c=["left","top"].includes(i)?-1:1,d=o&&l?-1:1,f=An(t,e);let{mainAxis:y,crossAxis:x,alignmentAxis:k}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof k=="number"&&(x=a==="end"?k*-1:k),l?{x:x*d,y:y*c}:{x:y*c,y:x*d}}const _E=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:o,placement:i,middlewareData:a}=t,l=await EE(t,e);return i===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:s+l.x,y:o+l.y,data:{...l,placement:i}}}}},NE=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:o=!0,crossAxis:i=!1,limiter:a={fn:w=>{let{x:p,y:h}=w;return{x:p,y:h}}},...l}=An(e,t),c={x:n,y:r},d=await bi(t,l),f=dn(Mn(s)),y=Xf(f);let x=c[y],k=c[f];if(o){const w=y==="y"?"top":"left",p=y==="y"?"bottom":"right",h=x+d[w],v=x-d[p];x=hd(h,x,v)}if(i){const w=f==="y"?"top":"left",p=f==="y"?"bottom":"right",h=k+d[w],v=k-d[p];k=hd(h,k,v)}const m=a.fn({...t,[y]:x,[f]:k});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[y]:o,[f]:i}}}}}},TE=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:o,middlewareData:i}=t,{offset:a=0,mainAxis:l=!0,crossAxis:c=!0}=An(e,t),d={x:n,y:r},f=dn(s),y=Xf(f);let x=d[y],k=d[f];const m=An(a,t),w=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){const v=y==="y"?"height":"width",S=o.reference[y]-o.floating[v]+w.mainAxis,b=o.reference[y]+o.reference[v]-w.mainAxis;x<S?x=S:x>b&&(x=b)}if(c){var p,h;const v=y==="y"?"width":"height",S=["top","left"].includes(Mn(s)),b=o.reference[f]-o.floating[v]+(S&&((p=i.offset)==null?void 0:p[f])||0)+(S?0:w.crossAxis),E=o.reference[f]+o.reference[v]+(S?0:((h=i.offset)==null?void 0:h[f])||0)-(S?w.crossAxis:0);k<b?k=b:k>E&&(k=E)}return{[y]:x,[f]:k}}}},jE=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:o,platform:i,elements:a}=t,{apply:l=()=>{},...c}=An(e,t),d=await bi(t,c),f=Mn(s),y=bo(s),x=dn(s)==="y",{width:k,height:m}=o.floating;let w,p;f==="top"||f==="bottom"?(w=f,p=y===(await(i.isRTL==null?void 0:i.isRTL(a.floating))?"start":"end")?"left":"right"):(p=f,w=y==="end"?"top":"bottom");const h=m-d.top-d.bottom,v=k-d.left-d.right,S=br(m-d[w],h),b=br(k-d[p],v),E=!t.middlewareData.shift;let _=S,N=b;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(N=v),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(_=h),E&&!y){const R=kt(d.left,0),L=kt(d.right,0),A=kt(d.top,0),Z=kt(d.bottom,0);x?N=k-2*(R!==0||L!==0?R+L:kt(d.left,d.right)):_=m-2*(A!==0||Z!==0?A+Z:kt(d.top,d.bottom))}await l({...t,availableWidth:N,availableHeight:_});const j=await i.getDimensions(a.floating);return k!==j.width||m!==j.height?{reset:{rects:!0}}:{}}}};function ql(){return typeof window<"u"}function Co(e){return T0(e)?(e.nodeName||"").toLowerCase():"#document"}function Et(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function vn(e){var t;return(t=(T0(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function T0(e){return ql()?e instanceof Node||e instanceof Et(e).Node:!1}function en(e){return ql()?e instanceof Element||e instanceof Et(e).Element:!1}function yn(e){return ql()?e instanceof HTMLElement||e instanceof Et(e).HTMLElement:!1}function vm(e){return!ql()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Et(e).ShadowRoot}function Ui(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=tn(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(s)}function RE(e){return["table","td","th"].includes(Co(e))}function Gl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function th(e){const t=nh(),n=en(e)?tn(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function IE(e){let t=Cr(e);for(;yn(t)&&!mo(t);){if(th(t))return t;if(Gl(t))return null;t=Cr(t)}return null}function nh(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function mo(e){return["html","body","#document"].includes(Co(e))}function tn(e){return Et(e).getComputedStyle(e)}function Yl(e){return en(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Cr(e){if(Co(e)==="html")return e;const t=e.assignedSlot||e.parentNode||vm(e)&&e.host||vn(e);return vm(t)?t.host:t}function j0(e){const t=Cr(e);return mo(t)?e.ownerDocument?e.ownerDocument.body:e.body:yn(t)&&Ui(t)?t:j0(t)}function Ci(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=j0(e),o=s===((r=e.ownerDocument)==null?void 0:r.body),i=Et(s);if(o){const a=md(i);return t.concat(i,i.visualViewport||[],Ui(s)?s:[],a&&n?Ci(a):[])}return t.concat(s,Ci(s,[],n))}function md(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function R0(e){const t=tn(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=yn(e),o=s?e.offsetWidth:n,i=s?e.offsetHeight:r,a=ml(n)!==o||ml(r)!==i;return a&&(n=o,r=i),{width:n,height:r,$:a}}function rh(e){return en(e)?e:e.contextElement}function $s(e){const t=rh(e);if(!yn(t))return mn(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:o}=R0(t);let i=(o?ml(n.width):n.width)/r,a=(o?ml(n.height):n.height)/s;return(!i||!Number.isFinite(i))&&(i=1),(!a||!Number.isFinite(a))&&(a=1),{x:i,y:a}}const PE=mn(0);function I0(e){const t=Et(e);return!nh()||!t.visualViewport?PE:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function DE(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Et(e)?!1:t}function ls(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),o=rh(e);let i=mn(1);t&&(r?en(r)&&(i=$s(r)):i=$s(e));const a=DE(o,n,r)?I0(o):mn(0);let l=(s.left+a.x)/i.x,c=(s.top+a.y)/i.y,d=s.width/i.x,f=s.height/i.y;if(o){const y=Et(o),x=r&&en(r)?Et(r):r;let k=y,m=md(k);for(;m&&r&&x!==k;){const w=$s(m),p=m.getBoundingClientRect(),h=tn(m),v=p.left+(m.clientLeft+parseFloat(h.paddingLeft))*w.x,S=p.top+(m.clientTop+parseFloat(h.paddingTop))*w.y;l*=w.x,c*=w.y,d*=w.x,f*=w.y,l+=v,c+=S,k=Et(m),m=md(k)}}return yl({width:d,height:f,x:l,y:c})}function sh(e,t){const n=Yl(e).scrollLeft;return t?t.left+n:ls(vn(e)).left+n}function P0(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:sh(e,r)),o=r.top+t.scrollTop;return{x:s,y:o}}function OE(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const o=s==="fixed",i=vn(r),a=t?Gl(t.floating):!1;if(r===i||a&&o)return n;let l={scrollLeft:0,scrollTop:0},c=mn(1);const d=mn(0),f=yn(r);if((f||!f&&!o)&&((Co(r)!=="body"||Ui(i))&&(l=Yl(r)),yn(r))){const x=ls(r);c=$s(r),d.x=x.x+r.clientLeft,d.y=x.y+r.clientTop}const y=i&&!f&&!o?P0(i,l,!0):mn(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+y.x,y:n.y*c.y-l.scrollTop*c.y+d.y+y.y}}function AE(e){return Array.from(e.getClientRects())}function ME(e){const t=vn(e),n=Yl(e),r=e.ownerDocument.body,s=kt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=kt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+sh(e);const a=-n.scrollTop;return tn(r).direction==="rtl"&&(i+=kt(t.clientWidth,r.clientWidth)-s),{width:s,height:o,x:i,y:a}}function LE(e,t){const n=Et(e),r=vn(e),s=n.visualViewport;let o=r.clientWidth,i=r.clientHeight,a=0,l=0;if(s){o=s.width,i=s.height;const c=nh();(!c||c&&t==="fixed")&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:i,x:a,y:l}}function FE(e,t){const n=ls(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,o=yn(e)?$s(e):mn(1),i=e.clientWidth*o.x,a=e.clientHeight*o.y,l=s*o.x,c=r*o.y;return{width:i,height:a,x:l,y:c}}function xm(e,t,n){let r;if(t==="viewport")r=LE(e,n);else if(t==="document")r=ME(vn(e));else if(en(t))r=FE(t,n);else{const s=I0(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return yl(r)}function D0(e,t){const n=Cr(e);return n===t||!en(n)||mo(n)?!1:tn(n).position==="fixed"||D0(n,t)}function zE(e,t){const n=t.get(e);if(n)return n;let r=Ci(e,[],!1).filter(a=>en(a)&&Co(a)!=="body"),s=null;const o=tn(e).position==="fixed";let i=o?Cr(e):e;for(;en(i)&&!mo(i);){const a=tn(i),l=th(i);!l&&a.position==="fixed"&&(s=null),(o?!l&&!s:!l&&a.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||Ui(i)&&!l&&D0(e,i))?r=r.filter(d=>d!==i):s=a,i=Cr(i)}return t.set(e,r),r}function $E(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const i=[...n==="clippingAncestors"?Gl(t)?[]:zE(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((c,d)=>{const f=xm(t,d,s);return c.top=kt(f.top,c.top),c.right=br(f.right,c.right),c.bottom=br(f.bottom,c.bottom),c.left=kt(f.left,c.left),c},xm(t,a,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function UE(e){const{width:t,height:n}=R0(e);return{width:t,height:n}}function BE(e,t,n){const r=yn(t),s=vn(t),o=n==="fixed",i=ls(e,!0,o,t);let a={scrollLeft:0,scrollTop:0};const l=mn(0);function c(){l.x=sh(s)}if(r||!r&&!o)if((Co(t)!=="body"||Ui(s))&&(a=Yl(t)),r){const x=ls(t,!0,o,t);l.x=x.x+t.clientLeft,l.y=x.y+t.clientTop}else s&&c();o&&!r&&s&&c();const d=s&&!r&&!o?P0(s,a):mn(0),f=i.left+a.scrollLeft-l.x-d.x,y=i.top+a.scrollTop-l.y-d.y;return{x:f,y,width:i.width,height:i.height}}function Hc(e){return tn(e).position==="static"}function wm(e,t){if(!yn(e)||tn(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return vn(e)===n&&(n=n.ownerDocument.body),n}function O0(e,t){const n=Et(e);if(Gl(e))return n;if(!yn(e)){let s=Cr(e);for(;s&&!mo(s);){if(en(s)&&!Hc(s))return s;s=Cr(s)}return n}let r=wm(e,t);for(;r&&RE(r)&&Hc(r);)r=wm(r,t);return r&&mo(r)&&Hc(r)&&!th(r)?n:r||IE(e)||n}const VE=async function(e){const t=this.getOffsetParent||O0,n=this.getDimensions,r=await n(e.floating);return{reference:BE(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function HE(e){return tn(e).direction==="rtl"}const WE={convertOffsetParentRelativeRectToViewportRelativeRect:OE,getDocumentElement:vn,getClippingRect:$E,getOffsetParent:O0,getElementRects:VE,getClientRects:AE,getDimensions:UE,getScale:$s,isElement:en,isRTL:HE};function A0(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function QE(e,t){let n=null,r;const s=vn(e);function o(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function i(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),o();const c=e.getBoundingClientRect(),{left:d,top:f,width:y,height:x}=c;if(a||t(),!y||!x)return;const k=pa(f),m=pa(s.clientWidth-(d+y)),w=pa(s.clientHeight-(f+x)),p=pa(d),v={rootMargin:-k+"px "+-m+"px "+-w+"px "+-p+"px",threshold:kt(0,br(1,l))||1};let S=!0;function b(E){const _=E[0].intersectionRatio;if(_!==l){if(!S)return i();_?i(!1,_):r=setTimeout(()=>{i(!1,1e-7)},1e3)}_===1&&!A0(c,e.getBoundingClientRect())&&i(),S=!1}try{n=new IntersectionObserver(b,{...v,root:s.ownerDocument})}catch{n=new IntersectionObserver(b,v)}n.observe(e)}return i(!0),o}function ZE(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,c=rh(e),d=s||o?[...c?Ci(c):[],...Ci(t)]:[];d.forEach(p=>{s&&p.addEventListener("scroll",n,{passive:!0}),o&&p.addEventListener("resize",n)});const f=c&&a?QE(c,n):null;let y=-1,x=null;i&&(x=new ResizeObserver(p=>{let[h]=p;h&&h.target===c&&x&&(x.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var v;(v=x)==null||v.observe(t)})),n()}),c&&!l&&x.observe(c),x.observe(t));let k,m=l?ls(e):null;l&&w();function w(){const p=ls(e);m&&!A0(m,p)&&n(),m=p,k=requestAnimationFrame(w)}return n(),()=>{var p;d.forEach(h=>{s&&h.removeEventListener("scroll",n),o&&h.removeEventListener("resize",n)}),f==null||f(),(p=x)==null||p.disconnect(),x=null,l&&cancelAnimationFrame(k)}}const KE=_E,qE=NE,GE=bE,YE=jE,XE=CE,Sm=kE,JE=TE,e_=(e,t,n)=>{const r=new Map,s={platform:WE,...n},o={...s.platform,_c:r};return SE(e,t,{...s,platform:o})};var t_=typeof document<"u",n_=function(){},Pa=t_?g.useLayoutEffect:n_;function vl(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!vl(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const o=s[r];if(!(o==="_owner"&&e.$$typeof)&&!vl(e[o],t[o]))return!1}return!0}return e!==e&&t!==t}function M0(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function km(e,t){const n=M0(e);return Math.round(t*n)/n}function Wc(e){const t=g.useRef(e);return Pa(()=>{t.current=e}),t}function r_(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:o,floating:i}={},transform:a=!0,whileElementsMounted:l,open:c}=e,[d,f]=g.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[y,x]=g.useState(r);vl(y,r)||x(r);const[k,m]=g.useState(null),[w,p]=g.useState(null),h=g.useCallback(T=>{T!==E.current&&(E.current=T,m(T))},[]),v=g.useCallback(T=>{T!==_.current&&(_.current=T,p(T))},[]),S=o||k,b=i||w,E=g.useRef(null),_=g.useRef(null),N=g.useRef(d),j=l!=null,R=Wc(l),L=Wc(s),A=Wc(c),Z=g.useCallback(()=>{if(!E.current||!_.current)return;const T={placement:t,strategy:n,middleware:y};L.current&&(T.platform=L.current),e_(E.current,_.current,T).then(D=>{const $={...D,isPositioned:A.current!==!1};J.current&&!vl(N.current,$)&&(N.current=$,xo.flushSync(()=>{f($)}))})},[y,t,n,L,A]);Pa(()=>{c===!1&&N.current.isPositioned&&(N.current.isPositioned=!1,f(T=>({...T,isPositioned:!1})))},[c]);const J=g.useRef(!1);Pa(()=>(J.current=!0,()=>{J.current=!1}),[]),Pa(()=>{if(S&&(E.current=S),b&&(_.current=b),S&&b){if(R.current)return R.current(S,b,Z);Z()}},[S,b,Z,R,j]);const ae=g.useMemo(()=>({reference:E,floating:_,setReference:h,setFloating:v}),[h,v]),K=g.useMemo(()=>({reference:S,floating:b}),[S,b]),ee=g.useMemo(()=>{const T={position:n,left:0,top:0};if(!K.floating)return T;const D=km(K.floating,d.x),$=km(K.floating,d.y);return a?{...T,transform:"translate("+D+"px, "+$+"px)",...M0(K.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:D,top:$}},[n,a,K.floating,d.x,d.y]);return g.useMemo(()=>({...d,update:Z,refs:ae,elements:K,floatingStyles:ee}),[d,Z,ae,K,ee])}const s_=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Sm({element:r.current,padding:s}).fn(n):{}:r?Sm({element:r,padding:s}).fn(n):{}}}},o_=(e,t)=>({...KE(e),options:[e,t]}),i_=(e,t)=>({...qE(e),options:[e,t]}),a_=(e,t)=>({...JE(e),options:[e,t]}),l_=(e,t)=>({...GE(e),options:[e,t]}),c_=(e,t)=>({...YE(e),options:[e,t]}),u_=(e,t)=>({...XE(e),options:[e,t]}),d_=(e,t)=>({...s_(e),options:[e,t]});var f_="Arrow",L0=g.forwardRef((e,t)=>{const{children:n,width:r=10,height:s=5,...o}=e;return u.jsx(be.svg,{...o,ref:t,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:u.jsx("polygon",{points:"0,0 30,0 15,10"})})});L0.displayName=f_;var h_=L0,oh="Popper",[F0,z0]=zi(oh),[p_,$0]=F0(oh),U0=e=>{const{__scopePopper:t,children:n}=e,[r,s]=g.useState(null);return u.jsx(p_,{scope:t,anchor:r,onAnchorChange:s,children:n})};U0.displayName=oh;var B0="PopperAnchor",V0=g.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...s}=e,o=$0(B0,n),i=g.useRef(null),a=Ae(t,i);return g.useEffect(()=>{o.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:u.jsx(be.div,{...s,ref:a})});V0.displayName=B0;var ih="PopperContent",[m_,g_]=F0(ih),H0=g.forwardRef((e,t)=>{var M,le,V,H,pe,me;const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:o="center",alignOffset:i=0,arrowPadding:a=0,avoidCollisions:l=!0,collisionBoundary:c=[],collisionPadding:d=0,sticky:f="partial",hideWhenDetached:y=!1,updatePositionStrategy:x="optimized",onPlaced:k,...m}=e,w=$0(ih,n),[p,h]=g.useState(null),v=Ae(t,Ze=>h(Ze)),[S,b]=g.useState(null),E=zv(S),_=(E==null?void 0:E.width)??0,N=(E==null?void 0:E.height)??0,j=r+(o!=="center"?"-"+o:""),R=typeof d=="number"?d:{top:0,right:0,bottom:0,left:0,...d},L=Array.isArray(c)?c:[c],A=L.length>0,Z={padding:R,boundary:L.filter(v_),altBoundary:A},{refs:J,floatingStyles:ae,placement:K,isPositioned:ee,middlewareData:T}=r_({strategy:"fixed",placement:j,whileElementsMounted:(...Ze)=>ZE(...Ze,{animationFrame:x==="always"}),elements:{reference:w.anchor},middleware:[o_({mainAxis:s+N,alignmentAxis:i}),l&&i_({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?a_():void 0,...Z}),l&&l_({...Z}),c_({...Z,apply:({elements:Ze,rects:It,availableWidth:Pr,availableHeight:_o})=>{const{width:No,height:Hx}=It.reference,Bi=Ze.floating.style;Bi.setProperty("--radix-popper-available-width",`${Pr}px`),Bi.setProperty("--radix-popper-available-height",`${_o}px`),Bi.setProperty("--radix-popper-anchor-width",`${No}px`),Bi.setProperty("--radix-popper-anchor-height",`${Hx}px`)}}),S&&d_({element:S,padding:a}),x_({arrowWidth:_,arrowHeight:N}),y&&u_({strategy:"referenceHidden",...Z})]}),[D,$]=Z0(K),O=is(k);ot(()=>{ee&&(O==null||O())},[ee,O]);const se=(M=T.arrow)==null?void 0:M.x,oe=(le=T.arrow)==null?void 0:le.y,he=((V=T.arrow)==null?void 0:V.centerOffset)!==0,[_e,q]=g.useState();return ot(()=>{p&&q(window.getComputedStyle(p).zIndex)},[p]),u.jsx("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...ae,transform:ee?ae.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:_e,"--radix-popper-transform-origin":[(H=T.transformOrigin)==null?void 0:H.x,(pe=T.transformOrigin)==null?void 0:pe.y].join(" "),...((me=T.hide)==null?void 0:me.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:u.jsx(m_,{scope:n,placedSide:D,onArrowChange:b,arrowX:se,arrowY:oe,shouldHideArrow:he,children:u.jsx(be.div,{"data-side":D,"data-align":$,...m,ref:v,style:{...m.style,animation:ee?void 0:"none"}})})})});H0.displayName=ih;var W0="PopperArrow",y_={top:"bottom",right:"left",bottom:"top",left:"right"},Q0=g.forwardRef(function(t,n){const{__scopePopper:r,...s}=t,o=g_(W0,r),i=y_[o.placedSide];return u.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:u.jsx(h_,{...s,ref:n,style:{...s.style,display:"block"}})})});Q0.displayName=W0;function v_(e){return e!==null}var x_=e=>({name:"transformOrigin",options:e,fn(t){var w,p,h;const{placement:n,rects:r,middlewareData:s}=t,i=((w=s.arrow)==null?void 0:w.centerOffset)!==0,a=i?0:e.arrowWidth,l=i?0:e.arrowHeight,[c,d]=Z0(n),f={start:"0%",center:"50%",end:"100%"}[d],y=(((p=s.arrow)==null?void 0:p.x)??0)+a/2,x=(((h=s.arrow)==null?void 0:h.y)??0)+l/2;let k="",m="";return c==="bottom"?(k=i?f:`${y}px`,m=`${-l}px`):c==="top"?(k=i?f:`${y}px`,m=`${r.floating.height+l}px`):c==="right"?(k=`${-l}px`,m=i?f:`${x}px`):c==="left"&&(k=`${r.floating.width+l}px`,m=i?f:`${x}px`),{data:{x:k,y:m}}}});function Z0(e){const[t,n="center"]=e.split("-");return[t,n]}var w_=U0,S_=V0,k_=H0,b_=Q0,K0=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),C_="VisuallyHidden",E_=g.forwardRef((e,t)=>u.jsx(be.span,{...e,ref:t,style:{...K0,...e.style}}));E_.displayName=C_;var __=[" ","Enter","ArrowUp","ArrowDown"],N_=[" ","Enter"],cs="Select",[Xl,Jl,T_]=uE(cs),[Eo,tT]=zi(cs,[T_,z0]),ec=z0(),[j_,Rr]=Eo(cs),[R_,I_]=Eo(cs),q0=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:s,onOpenChange:o,value:i,defaultValue:a,onValueChange:l,dir:c,name:d,autoComplete:f,disabled:y,required:x,form:k}=e,m=ec(t),[w,p]=g.useState(null),[h,v]=g.useState(null),[S,b]=g.useState(!1),E=fE(c),[_,N]=hl({prop:r,defaultProp:s??!1,onChange:o,caller:cs}),[j,R]=hl({prop:i,defaultProp:a,onChange:l,caller:cs}),L=g.useRef(null),A=w?k||!!w.closest("form"):!0,[Z,J]=g.useState(new Set),ae=Array.from(Z).map(K=>K.props.value).join(";");return u.jsx(w_,{...m,children:u.jsxs(j_,{required:x,scope:t,trigger:w,onTriggerChange:p,valueNode:h,onValueNodeChange:v,valueNodeHasChildren:S,onValueNodeHasChildrenChange:b,contentId:Fs(),value:j,onValueChange:R,open:_,onOpenChange:N,dir:E,triggerPointerDownPosRef:L,disabled:y,children:[u.jsx(Xl.Provider,{scope:t,children:u.jsx(R_,{scope:e.__scopeSelect,onNativeOptionAdd:g.useCallback(K=>{J(ee=>new Set(ee).add(K))},[]),onNativeOptionRemove:g.useCallback(K=>{J(ee=>{const T=new Set(ee);return T.delete(K),T})},[]),children:n})}),A?u.jsxs(xx,{"aria-hidden":!0,required:x,tabIndex:-1,name:d,autoComplete:f,value:j,onChange:K=>R(K.target.value),disabled:y,form:k,children:[j===void 0?u.jsx("option",{value:""}):null,Array.from(Z)]},ae):null]})})};q0.displayName=cs;var G0="SelectTrigger",Y0=g.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...s}=e,o=ec(n),i=Rr(G0,n),a=i.disabled||r,l=Ae(t,i.onTriggerChange),c=Jl(n),d=g.useRef("touch"),[f,y,x]=Sx(m=>{const w=c().filter(v=>!v.disabled),p=w.find(v=>v.value===i.value),h=kx(w,m,p);h!==void 0&&i.onValueChange(h.value)}),k=m=>{a||(i.onOpenChange(!0),x()),m&&(i.triggerPointerDownPosRef.current={x:Math.round(m.pageX),y:Math.round(m.pageY)})};return u.jsx(S_,{asChild:!0,...o,children:u.jsx(be.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:a,"data-disabled":a?"":void 0,"data-placeholder":wx(i.value)?"":void 0,...s,ref:l,onClick:Ce(s.onClick,m=>{m.currentTarget.focus(),d.current!=="mouse"&&k(m)}),onPointerDown:Ce(s.onPointerDown,m=>{d.current=m.pointerType;const w=m.target;w.hasPointerCapture(m.pointerId)&&w.releasePointerCapture(m.pointerId),m.button===0&&m.ctrlKey===!1&&m.pointerType==="mouse"&&(k(m),m.preventDefault())}),onKeyDown:Ce(s.onKeyDown,m=>{const w=f.current!=="";!(m.ctrlKey||m.altKey||m.metaKey)&&m.key.length===1&&y(m.key),!(w&&m.key===" ")&&__.includes(m.key)&&(k(),m.preventDefault())})})})});Y0.displayName=G0;var X0="SelectValue",J0=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:s,children:o,placeholder:i="",...a}=e,l=Rr(X0,n),{onValueNodeHasChildrenChange:c}=l,d=o!==void 0,f=Ae(t,l.onValueNodeChange);return ot(()=>{c(d)},[c,d]),u.jsx(be.span,{...a,ref:f,style:{pointerEvents:"none"},children:wx(l.value)?u.jsx(u.Fragment,{children:i}):o})});J0.displayName=X0;var P_="SelectIcon",ex=g.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...s}=e;return u.jsx(be.span,{"aria-hidden":!0,...s,ref:t,children:r||"▼"})});ex.displayName=P_;var D_="SelectPortal",tx=e=>u.jsx(Vf,{asChild:!0,...e});tx.displayName=D_;var us="SelectContent",nx=g.forwardRef((e,t)=>{const n=Rr(us,e.__scopeSelect),[r,s]=g.useState();if(ot(()=>{s(new DocumentFragment)},[]),!n.open){const o=r;return o?xo.createPortal(u.jsx(rx,{scope:e.__scopeSelect,children:u.jsx(Xl.Slot,{scope:e.__scopeSelect,children:u.jsx("div",{children:e.children})})}),o):null}return u.jsx(sx,{...e,ref:t})});nx.displayName=us;var Vt=10,[rx,Ir]=Eo(us),O_="SelectContentImpl",A_=ao("SelectContent.RemoveScroll"),sx=g.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:o,onPointerDownOutside:i,side:a,sideOffset:l,align:c,alignOffset:d,arrowPadding:f,collisionBoundary:y,collisionPadding:x,sticky:k,hideWhenDetached:m,avoidCollisions:w,...p}=e,h=Rr(us,n),[v,S]=g.useState(null),[b,E]=g.useState(null),_=Ae(t,M=>S(M)),[N,j]=g.useState(null),[R,L]=g.useState(null),A=Jl(n),[Z,J]=g.useState(!1),ae=g.useRef(!1);g.useEffect(()=>{if(v)return o0(v)},[v]),Gv();const K=g.useCallback(M=>{const[le,...V]=A().map(me=>me.ref.current),[H]=V.slice(-1),pe=document.activeElement;for(const me of M)if(me===pe||(me==null||me.scrollIntoView({block:"nearest"}),me===le&&b&&(b.scrollTop=0),me===H&&b&&(b.scrollTop=b.scrollHeight),me==null||me.focus(),document.activeElement!==pe))return},[A,b]),ee=g.useCallback(()=>K([N,v]),[K,N,v]);g.useEffect(()=>{Z&&ee()},[Z,ee]);const{onOpenChange:T,triggerPointerDownPosRef:D}=h;g.useEffect(()=>{if(v){let M={x:0,y:0};const le=H=>{var pe,me;M={x:Math.abs(Math.round(H.pageX)-(((pe=D.current)==null?void 0:pe.x)??0)),y:Math.abs(Math.round(H.pageY)-(((me=D.current)==null?void 0:me.y)??0))}},V=H=>{M.x<=10&&M.y<=10?H.preventDefault():v.contains(H.target)||T(!1),document.removeEventListener("pointermove",le),D.current=null};return D.current!==null&&(document.addEventListener("pointermove",le),document.addEventListener("pointerup",V,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",le),document.removeEventListener("pointerup",V,{capture:!0})}}},[v,T,D]),g.useEffect(()=>{const M=()=>T(!1);return window.addEventListener("blur",M),window.addEventListener("resize",M),()=>{window.removeEventListener("blur",M),window.removeEventListener("resize",M)}},[T]);const[$,O]=Sx(M=>{const le=A().filter(pe=>!pe.disabled),V=le.find(pe=>pe.ref.current===document.activeElement),H=kx(le,M,V);H&&setTimeout(()=>H.ref.current.focus())}),se=g.useCallback((M,le,V)=>{const H=!ae.current&&!V;(h.value!==void 0&&h.value===le||H)&&(j(M),H&&(ae.current=!0))},[h.value]),oe=g.useCallback(()=>v==null?void 0:v.focus(),[v]),he=g.useCallback((M,le,V)=>{const H=!ae.current&&!V;(h.value!==void 0&&h.value===le||H)&&L(M)},[h.value]),_e=r==="popper"?gd:ox,q=_e===gd?{side:a,sideOffset:l,align:c,alignOffset:d,arrowPadding:f,collisionBoundary:y,collisionPadding:x,sticky:k,hideWhenDetached:m,avoidCollisions:w}:{};return u.jsx(rx,{scope:n,content:v,viewport:b,onViewportChange:E,itemRefCallback:se,selectedItem:N,onItemLeave:oe,itemTextRefCallback:he,focusSelectedItem:ee,selectedItemText:R,position:r,isPositioned:Z,searchRef:$,children:u.jsx(Hf,{as:A_,allowPinchZoom:!0,children:u.jsx(Bf,{asChild:!0,trapped:h.open,onMountAutoFocus:M=>{M.preventDefault()},onUnmountAutoFocus:Ce(s,M=>{var le;(le=h.trigger)==null||le.focus({preventScroll:!0}),M.preventDefault()}),children:u.jsx(Uf,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:o,onPointerDownOutside:i,onFocusOutside:M=>M.preventDefault(),onDismiss:()=>h.onOpenChange(!1),children:u.jsx(_e,{role:"listbox",id:h.contentId,"data-state":h.open?"open":"closed",dir:h.dir,onContextMenu:M=>M.preventDefault(),...p,...q,onPlaced:()=>J(!0),ref:_,style:{display:"flex",flexDirection:"column",outline:"none",...p.style},onKeyDown:Ce(p.onKeyDown,M=>{const le=M.ctrlKey||M.altKey||M.metaKey;if(M.key==="Tab"&&M.preventDefault(),!le&&M.key.length===1&&O(M.key),["ArrowUp","ArrowDown","Home","End"].includes(M.key)){let H=A().filter(pe=>!pe.disabled).map(pe=>pe.ref.current);if(["ArrowUp","End"].includes(M.key)&&(H=H.slice().reverse()),["ArrowUp","ArrowDown"].includes(M.key)){const pe=M.target,me=H.indexOf(pe);H=H.slice(me+1)}setTimeout(()=>K(H)),M.preventDefault()}})})})})})})});sx.displayName=O_;var M_="SelectItemAlignedPosition",ox=g.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...s}=e,o=Rr(us,n),i=Ir(us,n),[a,l]=g.useState(null),[c,d]=g.useState(null),f=Ae(t,_=>d(_)),y=Jl(n),x=g.useRef(!1),k=g.useRef(!0),{viewport:m,selectedItem:w,selectedItemText:p,focusSelectedItem:h}=i,v=g.useCallback(()=>{if(o.trigger&&o.valueNode&&a&&c&&m&&w&&p){const _=o.trigger.getBoundingClientRect(),N=c.getBoundingClientRect(),j=o.valueNode.getBoundingClientRect(),R=p.getBoundingClientRect();if(o.dir!=="rtl"){const pe=R.left-N.left,me=j.left-pe,Ze=_.left-me,It=_.width+Ze,Pr=Math.max(It,N.width),_o=window.innerWidth-Vt,No=pm(me,[Vt,Math.max(Vt,_o-Pr)]);a.style.minWidth=It+"px",a.style.left=No+"px"}else{const pe=N.right-R.right,me=window.innerWidth-j.right-pe,Ze=window.innerWidth-_.right-me,It=_.width+Ze,Pr=Math.max(It,N.width),_o=window.innerWidth-Vt,No=pm(me,[Vt,Math.max(Vt,_o-Pr)]);a.style.minWidth=It+"px",a.style.right=No+"px"}const L=y(),A=window.innerHeight-Vt*2,Z=m.scrollHeight,J=window.getComputedStyle(c),ae=parseInt(J.borderTopWidth,10),K=parseInt(J.paddingTop,10),ee=parseInt(J.borderBottomWidth,10),T=parseInt(J.paddingBottom,10),D=ae+K+Z+T+ee,$=Math.min(w.offsetHeight*5,D),O=window.getComputedStyle(m),se=parseInt(O.paddingTop,10),oe=parseInt(O.paddingBottom,10),he=_.top+_.height/2-Vt,_e=A-he,q=w.offsetHeight/2,M=w.offsetTop+q,le=ae+K+M,V=D-le;if(le<=he){const pe=L.length>0&&w===L[L.length-1].ref.current;a.style.bottom="0px";const me=c.clientHeight-m.offsetTop-m.offsetHeight,Ze=Math.max(_e,q+(pe?oe:0)+me+ee),It=le+Ze;a.style.height=It+"px"}else{const pe=L.length>0&&w===L[0].ref.current;a.style.top="0px";const Ze=Math.max(he,ae+m.offsetTop+(pe?se:0)+q)+V;a.style.height=Ze+"px",m.scrollTop=le-he+m.offsetTop}a.style.margin=`${Vt}px 0`,a.style.minHeight=$+"px",a.style.maxHeight=A+"px",r==null||r(),requestAnimationFrame(()=>x.current=!0)}},[y,o.trigger,o.valueNode,a,c,m,w,p,o.dir,r]);ot(()=>v(),[v]);const[S,b]=g.useState();ot(()=>{c&&b(window.getComputedStyle(c).zIndex)},[c]);const E=g.useCallback(_=>{_&&k.current===!0&&(v(),h==null||h(),k.current=!1)},[v,h]);return u.jsx(F_,{scope:n,contentWrapper:a,shouldExpandOnScrollRef:x,onScrollButtonChange:E,children:u.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:u.jsx(be.div,{...s,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});ox.displayName=M_;var L_="SelectPopperPosition",gd=g.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:s=Vt,...o}=e,i=ec(n);return u.jsx(k_,{...i,...o,ref:t,align:r,collisionPadding:s,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});gd.displayName=L_;var[F_,ah]=Eo(us,{}),yd="SelectViewport",ix=g.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...s}=e,o=Ir(yd,n),i=ah(yd,n),a=Ae(t,o.onViewportChange),l=g.useRef(0);return u.jsxs(u.Fragment,{children:[u.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),u.jsx(Xl.Slot,{scope:n,children:u.jsx(be.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:a,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:Ce(s.onScroll,c=>{const d=c.currentTarget,{contentWrapper:f,shouldExpandOnScrollRef:y}=i;if(y!=null&&y.current&&f){const x=Math.abs(l.current-d.scrollTop);if(x>0){const k=window.innerHeight-Vt*2,m=parseFloat(f.style.minHeight),w=parseFloat(f.style.height),p=Math.max(m,w);if(p<k){const h=p+x,v=Math.min(k,h),S=h-v;f.style.height=v+"px",f.style.bottom==="0px"&&(d.scrollTop=S>0?S:0,f.style.justifyContent="flex-end")}}}l.current=d.scrollTop})})})]})});ix.displayName=yd;var ax="SelectGroup",[z_,$_]=Eo(ax),U_=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=Fs();return u.jsx(z_,{scope:n,id:s,children:u.jsx(be.div,{role:"group","aria-labelledby":s,...r,ref:t})})});U_.displayName=ax;var lx="SelectLabel",cx=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=$_(lx,n);return u.jsx(be.div,{id:s.id,...r,ref:t})});cx.displayName=lx;var xl="SelectItem",[B_,ux]=Eo(xl),dx=g.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:s=!1,textValue:o,...i}=e,a=Rr(xl,n),l=Ir(xl,n),c=a.value===r,[d,f]=g.useState(o??""),[y,x]=g.useState(!1),k=Ae(t,h=>{var v;return(v=l.itemRefCallback)==null?void 0:v.call(l,h,r,s)}),m=Fs(),w=g.useRef("touch"),p=()=>{s||(a.onValueChange(r),a.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return u.jsx(B_,{scope:n,value:r,disabled:s,textId:m,isSelected:c,onItemTextChange:g.useCallback(h=>{f(v=>v||((h==null?void 0:h.textContent)??"").trim())},[]),children:u.jsx(Xl.ItemSlot,{scope:n,value:r,disabled:s,textValue:d,children:u.jsx(be.div,{role:"option","aria-labelledby":m,"data-highlighted":y?"":void 0,"aria-selected":c&&y,"data-state":c?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...i,ref:k,onFocus:Ce(i.onFocus,()=>x(!0)),onBlur:Ce(i.onBlur,()=>x(!1)),onClick:Ce(i.onClick,()=>{w.current!=="mouse"&&p()}),onPointerUp:Ce(i.onPointerUp,()=>{w.current==="mouse"&&p()}),onPointerDown:Ce(i.onPointerDown,h=>{w.current=h.pointerType}),onPointerMove:Ce(i.onPointerMove,h=>{var v;w.current=h.pointerType,s?(v=l.onItemLeave)==null||v.call(l):w.current==="mouse"&&h.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Ce(i.onPointerLeave,h=>{var v;h.currentTarget===document.activeElement&&((v=l.onItemLeave)==null||v.call(l))}),onKeyDown:Ce(i.onKeyDown,h=>{var S;((S=l.searchRef)==null?void 0:S.current)!==""&&h.key===" "||(N_.includes(h.key)&&p(),h.key===" "&&h.preventDefault())})})})})});dx.displayName=xl;var Wo="SelectItemText",fx=g.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:s,...o}=e,i=Rr(Wo,n),a=Ir(Wo,n),l=ux(Wo,n),c=I_(Wo,n),[d,f]=g.useState(null),y=Ae(t,p=>f(p),l.onItemTextChange,p=>{var h;return(h=a.itemTextRefCallback)==null?void 0:h.call(a,p,l.value,l.disabled)}),x=d==null?void 0:d.textContent,k=g.useMemo(()=>u.jsx("option",{value:l.value,disabled:l.disabled,children:x},l.value),[l.disabled,l.value,x]),{onNativeOptionAdd:m,onNativeOptionRemove:w}=c;return ot(()=>(m(k),()=>w(k)),[m,w,k]),u.jsxs(u.Fragment,{children:[u.jsx(be.span,{id:l.textId,...o,ref:y}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?xo.createPortal(o.children,i.valueNode):null]})});fx.displayName=Wo;var hx="SelectItemIndicator",px=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return ux(hx,n).isSelected?u.jsx(be.span,{"aria-hidden":!0,...r,ref:t}):null});px.displayName=hx;var vd="SelectScrollUpButton",mx=g.forwardRef((e,t)=>{const n=Ir(vd,e.__scopeSelect),r=ah(vd,e.__scopeSelect),[s,o]=g.useState(!1),i=Ae(t,r.onScrollButtonChange);return ot(()=>{if(n.viewport&&n.isPositioned){let a=function(){const c=l.scrollTop>0;o(c)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),s?u.jsx(yx,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop-l.offsetHeight)}}):null});mx.displayName=vd;var xd="SelectScrollDownButton",gx=g.forwardRef((e,t)=>{const n=Ir(xd,e.__scopeSelect),r=ah(xd,e.__scopeSelect),[s,o]=g.useState(!1),i=Ae(t,r.onScrollButtonChange);return ot(()=>{if(n.viewport&&n.isPositioned){let a=function(){const c=l.scrollHeight-l.clientHeight,d=Math.ceil(l.scrollTop)<c;o(d)};const l=n.viewport;return a(),l.addEventListener("scroll",a),()=>l.removeEventListener("scroll",a)}},[n.viewport,n.isPositioned]),s?u.jsx(yx,{...e,ref:i,onAutoScroll:()=>{const{viewport:a,selectedItem:l}=n;a&&l&&(a.scrollTop=a.scrollTop+l.offsetHeight)}}):null});gx.displayName=xd;var yx=g.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...s}=e,o=Ir("SelectScrollButton",n),i=g.useRef(null),a=Jl(n),l=g.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return g.useEffect(()=>()=>l(),[l]),ot(()=>{var d;const c=a().find(f=>f.ref.current===document.activeElement);(d=c==null?void 0:c.ref.current)==null||d.scrollIntoView({block:"nearest"})},[a]),u.jsx(be.div,{"aria-hidden":!0,...s,ref:t,style:{flexShrink:0,...s.style},onPointerDown:Ce(s.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(r,50))}),onPointerMove:Ce(s.onPointerMove,()=>{var c;(c=o.onItemLeave)==null||c.call(o),i.current===null&&(i.current=window.setInterval(r,50))}),onPointerLeave:Ce(s.onPointerLeave,()=>{l()})})}),V_="SelectSeparator",vx=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return u.jsx(be.div,{"aria-hidden":!0,...r,ref:t})});vx.displayName=V_;var wd="SelectArrow",H_=g.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=ec(n),o=Rr(wd,n),i=Ir(wd,n);return o.open&&i.position==="popper"?u.jsx(b_,{...s,...r,ref:t}):null});H_.displayName=wd;var W_="SelectBubbleInput",xx=g.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{const s=g.useRef(null),o=Ae(r,s),i=Fv(t);return g.useEffect(()=>{const a=s.current;if(!a)return;const l=window.HTMLSelectElement.prototype,d=Object.getOwnPropertyDescriptor(l,"value").set;if(i!==t&&d){const f=new Event("change",{bubbles:!0});d.call(a,t),a.dispatchEvent(f)}},[i,t]),u.jsx(be.select,{...n,style:{...K0,...n.style},ref:o,defaultValue:t})});xx.displayName=W_;function wx(e){return e===""||e===void 0}function Sx(e){const t=is(e),n=g.useRef(""),r=g.useRef(0),s=g.useCallback(i=>{const a=n.current+i;t(a),function l(c){n.current=c,window.clearTimeout(r.current),c!==""&&(r.current=window.setTimeout(()=>l(""),1e3))}(a)},[t]),o=g.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return g.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,s,o]}function kx(e,t,n){const s=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,o=n?e.indexOf(n):-1;let i=Q_(e,Math.max(o,0));s.length===1&&(i=i.filter(c=>c!==n));const l=i.find(c=>c.textValue.toLowerCase().startsWith(s.toLowerCase()));return l!==n?l:void 0}function Q_(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Z_=q0,bx=Y0,K_=J0,q_=ex,G_=tx,Cx=nx,Y_=ix,Ex=cx,_x=dx,X_=fx,J_=px,Nx=mx,Tx=gx,jx=vx;const lh=Z_,ch=K_,tc=g.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(bx,{ref:r,className:B("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,u.jsx(q_,{asChild:!0,children:u.jsx(Tf,{className:"h-4 w-4 opacity-50"})})]}));tc.displayName=bx.displayName;const Rx=g.forwardRef(({className:e,...t},n)=>u.jsx(Nx,{ref:n,className:B("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(rk,{className:"h-4 w-4"})}));Rx.displayName=Nx.displayName;const Ix=g.forwardRef(({className:e,...t},n)=>u.jsx(Tx,{ref:n,className:B("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(Tf,{className:"h-4 w-4"})}));Ix.displayName=Tx.displayName;const nc=g.forwardRef(({className:e,children:t,position:n="popper",...r},s)=>u.jsx(G_,{children:u.jsxs(Cx,{ref:s,className:B("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[u.jsx(Rx,{}),u.jsx(Y_,{className:B("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),u.jsx(Ix,{})]})}));nc.displayName=Cx.displayName;const eN=g.forwardRef(({className:e,...t},n)=>u.jsx(Ex,{ref:n,className:B("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));eN.displayName=Ex.displayName;const Gt=g.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(_x,{ref:r,className:B("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(J_,{children:u.jsx(hv,{className:"h-4 w-4"})})}),u.jsx(X_,{children:t})]}));Gt.displayName=_x.displayName;const tN=g.forwardRef(({className:e,...t},n)=>u.jsx(jx,{ref:n,className:B("-mx-1 my-1 h-px bg-muted",e),...t}));tN.displayName=jx.displayName;function nN({task:e,isOpen:t,onClose:n}){const[r,s]=g.useState(""),[o,i]=g.useState(""),[a,l]=g.useState(2),[c,d]=g.useState(""),[f,y]=g.useState(!1),x=Li(),{showSuccess:k,showError:m}=ko();g.useEffect(()=>{e&&(s(e.content),i(e.description||""),l(e.priority),d(e.dueDate?new Date(e.dueDate).toISOString().split("T")[0]:""),y(e.isImportant||!1))},[e]);const w=async()=>{if(!(!e||!r.trim()))try{await x.mutateAsync({id:e.id,input:{content:r.trim(),description:o.trim()||void 0,priority:a,dueDate:c?new Date(c).getTime():null,isImportant:f}}),k("任务已更新","任务信息已成功保存"),n()}catch(S){console.error("Failed to update task:",S),m("更新失败","无法保存任务信息")}},p=()=>{n()},h=S=>{switch(S){case 1:return"低";case 2:return"中";case 3:return"高";case 4:return"紧急";default:return"中"}},v=S=>{switch(S){case 1:return"text-blue-600";case 2:return"text-green-600";case 3:return"text-orange-600";case 4:return"text-red-600";default:return"text-green-600"}};return e?u.jsx(Kf,{open:t,onOpenChange:p,children:u.jsxs(Wl,{className:"sm:max-w-[500px] max-h-[80vh] overflow-y-auto",children:[u.jsxs(Ql,{children:[u.jsxs(Zl,{className:"flex items-center justify-between",children:[u.jsx("span",{children:"编辑任务"}),e.parentTaskId&&u.jsx("span",{className:"text-sm text-muted-foreground bg-muted px-2 py-1 rounded",children:"子任务"})]}),u.jsx(Kl,{children:"编辑任务的详细信息，包括标题、描述、优先级和截止日期等。"})]}),u.jsxs("div",{className:"space-y-4 py-4",children:[u.jsxs("div",{className:"space-y-2",children:[u.jsx("label",{className:"text-sm font-medium",children:"任务标题"}),u.jsx(gn,{value:r,onChange:S=>s(S.target.value),placeholder:"输入任务标题...",className:"w-full"})]}),u.jsxs("div",{className:"space-y-2",children:[u.jsx("label",{className:"text-sm font-medium",children:"描述"}),u.jsx(Yf,{value:o,onChange:S=>i(S.target.value),placeholder:"添加任务描述...",className:"w-full min-h-[80px] resize-none"})]}),u.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[u.jsxs("div",{className:"space-y-2",children:[u.jsxs("label",{className:"text-sm font-medium flex items-center gap-1",children:[u.jsx(ol,{className:"h-4 w-4"}),"优先级"]}),u.jsxs(lh,{value:a.toString(),onValueChange:S=>l(Number(S)),children:[u.jsx(tc,{children:u.jsx(ch,{children:u.jsx("span",{className:v(a),children:h(a)})})}),u.jsxs(nc,{children:[u.jsx(Gt,{value:"1",children:u.jsx("span",{className:"text-blue-600",children:"低"})}),u.jsx(Gt,{value:"2",children:u.jsx("span",{className:"text-green-600",children:"中"})}),u.jsx(Gt,{value:"3",children:u.jsx("span",{className:"text-orange-600",children:"高"})}),u.jsx(Gt,{value:"4",children:u.jsx("span",{className:"text-red-600",children:"紧急"})})]})]})]}),u.jsxs("div",{className:"space-y-2",children:[u.jsxs("label",{className:"text-sm font-medium flex items-center gap-1",children:[u.jsx(io,{className:"h-4 w-4"}),"重要性"]}),u.jsxs(X,{variant:f?"default":"outline",onClick:()=>y(!f),className:"w-full justify-start",children:[u.jsx(io,{className:`h-4 w-4 mr-2 ${f?"fill-current":""}`}),f?"重要":"普通"]})]})]}),u.jsxs("div",{className:"space-y-2",children:[u.jsxs("label",{className:"text-sm font-medium flex items-center gap-1",children:[u.jsx(ns,{className:"h-4 w-4"}),"截止日期"]}),u.jsx(gn,{type:"date",value:c,onChange:S=>d(S.target.value),className:"w-full"})]})]}),u.jsxs("div",{className:"flex justify-end gap-2 pt-4 border-t",children:[u.jsx(X,{variant:"outline",onClick:p,children:"取消"}),u.jsx(X,{onClick:w,disabled:!r.trim()||x.isPending,children:x.isPending?"保存中...":"保存"})]})]})}):null}function rN(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return g.useMemo(()=>r=>{t.forEach(s=>s(r))},t)}const sN=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function oN(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function iN(e){return"nodeType"in e}function Px(e){var t,n;return e?oN(e)?e:iN(e)&&(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window:window}const rc=sN?g.useLayoutEffect:g.useEffect;function Dx(e){const t=g.useRef(e);return rc(()=>{t.current=e}),g.useCallback(function(){for(var n=arguments.length,r=new Array(n),s=0;s<n;s++)r[s]=arguments[s];return t.current==null?void 0:t.current(...r)},[])}function Sd(e,t){t===void 0&&(t=[e]);const n=g.useRef(e);return rc(()=>{n.current!==e&&(n.current=e)},t),n}function kd(e){const t=Dx(e),n=g.useRef(null),r=g.useCallback(s=>{s!==n.current&&(t==null||t(s,n.current)),n.current=s},[]);return[n,r]}let Qc={};function Ox(e,t){return g.useMemo(()=>{const n=Qc[e]==null?0:Qc[e]+1;return Qc[e]=n,e+"-"+n},[e,t])}function aN(e){if(!e)return!1;const{KeyboardEvent:t}=Px(e.target);return t&&e instanceof t}const Ei=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[Ei.Translate.toString(e),Ei.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}});var ti;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(ti||(ti={}));function bm(){}const lN=Object.freeze({x:0,y:0});function cN(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function uN(e,t,n){const r=cN(t);if(!r)return e;const{scaleX:s,scaleY:o,x:i,y:a}=r,l=e.left-i-(1-s)*parseFloat(n),c=e.top-a-(1-o)*parseFloat(n.slice(n.indexOf(" ")+1)),d=s?e.width/s:e.width,f=o?e.height/o:e.height;return{width:d,height:f,top:c,right:l+d,bottom:c+f,left:l}}const dN={ignoreTransform:!1};function fN(e,t){t===void 0&&(t=dN);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:c,transformOrigin:d}=Px(e).getComputedStyle(e);c&&(n=uN(n,c,d))}const{top:r,left:s,width:o,height:i,bottom:a,right:l}=n;return{top:r,left:s,width:o,height:i,bottom:a,right:l}}var Rs;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(Rs||(Rs={}));var Cm;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(Cm||(Cm={}));var At;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"})(At||(At={}));At.Space,At.Enter,At.Esc,At.Space,At.Enter,At.Tab;var Em;(function(e){e[e.RightClick=2]="RightClick"})(Em||(Em={}));var _m;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(_m||(_m={}));var Nm;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(Nm||(Nm={}));Rs.Backward+"",Rs.Forward+"",Rs.Backward+"",Rs.Forward+"";var bd;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(bd||(bd={}));var Cd;(function(e){e.Optimized="optimized"})(Cd||(Cd={}));function hN(e){let{callback:t,disabled:n}=e;const r=Dx(t),s=g.useMemo(()=>{if(n||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:o}=window;return new o(r)},[n]);return g.useEffect(()=>()=>s==null?void 0:s.disconnect(),[s]),s}function pN(e,t){return g.useMemo(()=>e.reduce((n,r)=>{let{eventName:s,handler:o}=r;return n[s]=i=>{o(i,t)},n},{}),[e,t])}bd.WhileDragging,Cd.Optimized;const mN={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:bm,draggableNodes:new Map,over:null,measureDroppableContainers:bm},Ax=g.createContext(mN),gN=g.createContext({...lN,scaleX:1,scaleY:1});var Tm;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(Tm||(Tm={}));const yN=g.createContext(null),jm="button",vN="Draggable";function xN(e){let{id:t,data:n,disabled:r=!1,attributes:s}=e;const o=Ox(vN),{activators:i,activatorEvent:a,active:l,activeNodeRect:c,ariaDescribedById:d,draggableNodes:f,over:y}=g.useContext(Ax),{role:x=jm,roleDescription:k="draggable",tabIndex:m=0}=s??{},w=(l==null?void 0:l.id)===t,p=g.useContext(w?gN:yN),[h,v]=kd(),[S,b]=kd(),E=pN(i,t),_=Sd(n);rc(()=>(f.set(t,{id:t,key:o,node:h,activatorNode:S,data:_}),()=>{const j=f.get(t);j&&j.key===o&&f.delete(t)}),[f,t]);const N=g.useMemo(()=>({role:x,tabIndex:m,"aria-disabled":r,"aria-pressed":w&&x===jm?!0:void 0,"aria-roledescription":k,"aria-describedby":d.draggable}),[r,x,m,w,k,d.draggable]);return{active:l,activatorEvent:a,activeNodeRect:c,attributes:N,isDragging:w,listeners:r?void 0:E,node:h,over:y,setNodeRef:v,setActivatorNodeRef:b,transform:p}}const wN="Droppable",SN={timeout:25};function kN(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:s}=e;const o=Ox(wN),{active:i,dispatch:a,over:l,measureDroppableContainers:c}=g.useContext(Ax),d=g.useRef({disabled:n}),f=g.useRef(!1),y=g.useRef(null),x=g.useRef(null),{disabled:k,updateMeasurementsFor:m,timeout:w}={...SN,...s},p=Sd(m??r),h=g.useCallback(()=>{if(!f.current){f.current=!0;return}x.current!=null&&clearTimeout(x.current),x.current=setTimeout(()=>{c(Array.isArray(p.current)?p.current:[p.current]),x.current=null},w)},[w]),v=hN({callback:h,disabled:k||!i}),S=g.useCallback((N,j)=>{v&&(j&&(v.unobserve(j),f.current=!1),N&&v.observe(N))},[v]),[b,E]=kd(S),_=Sd(t);return g.useEffect(()=>{!v||!b.current||(v.disconnect(),f.current=!1,v.observe(b.current))},[b,v]),g.useEffect(()=>(a({type:ti.RegisterDroppable,element:{id:r,key:o,disabled:n,node:b,rect:y,data:_}}),()=>a({type:ti.UnregisterDroppable,key:o,id:r})),[r]),g.useEffect(()=>{n!==d.current.disabled&&(a({type:ti.SetDroppableDisabled,id:r,key:o,disabled:n}),d.current.disabled=n)},[r,o,n,a]),{active:i,rect:y,isOver:(l==null?void 0:l.id)===r,node:b,over:l,setNodeRef:E}}function Mx(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function ma(e){return e!==null&&e>=0}const bN=e=>{let{rects:t,activeIndex:n,overIndex:r,index:s}=e;const o=Mx(t,r,n),i=t[s],a=o[s];return!a||!i?null:{x:a.left-i.left,y:a.top-i.top,scaleX:a.width/i.width,scaleY:a.height/i.height}},CN="Sortable",EN=We.createContext({activeIndex:-1,containerId:CN,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:bN,disabled:{draggable:!1,droppable:!1}}),_N=e=>{let{id:t,items:n,activeIndex:r,overIndex:s}=e;return Mx(n,r,s).indexOf(t)},NN=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:s,items:o,newIndex:i,previousItems:a,previousContainerId:l,transition:c}=e;return!c||!r||a!==o&&s===i?!1:n?!0:i!==s&&t===l},TN={duration:200,easing:"ease"},Lx="transform",jN=Ei.Transition.toString({property:Lx,duration:0,easing:"linear"}),RN={roleDescription:"sortable"};function IN(e){let{disabled:t,index:n,node:r,rect:s}=e;const[o,i]=g.useState(null),a=g.useRef(n);return rc(()=>{if(!t&&n!==a.current&&r.current){const l=s.current;if(l){const c=fN(r.current,{ignoreTransform:!0}),d={x:l.left-c.left,y:l.top-c.top,scaleX:l.width/c.width,scaleY:l.height/c.height};(d.x||d.y)&&i(d)}}n!==a.current&&(a.current=n)},[t,n,r,s]),g.useEffect(()=>{o&&i(null)},[o]),o}function PN(e){let{animateLayoutChanges:t=NN,attributes:n,disabled:r,data:s,getNewIndex:o=_N,id:i,strategy:a,resizeObserverConfig:l,transition:c=TN}=e;const{items:d,containerId:f,activeIndex:y,disabled:x,disableTransforms:k,sortedRects:m,overIndex:w,useDragOverlay:p,strategy:h}=g.useContext(EN),v=DN(r,x),S=d.indexOf(i),b=g.useMemo(()=>({sortable:{containerId:f,index:S,items:d},...s}),[f,s,S,d]),E=g.useMemo(()=>d.slice(d.indexOf(i)),[d,i]),{rect:_,node:N,isOver:j,setNodeRef:R}=kN({id:i,data:b,disabled:v.droppable,resizeObserverConfig:{updateMeasurementsFor:E,...l}}),{active:L,activatorEvent:A,activeNodeRect:Z,attributes:J,setNodeRef:ae,listeners:K,isDragging:ee,over:T,setActivatorNodeRef:D,transform:$}=xN({id:i,data:b,attributes:{...RN,...n},disabled:v.draggable}),O=rN(R,ae),se=!!L,oe=se&&!k&&ma(y)&&ma(w),he=!p&&ee,_e=he&&oe?$:null,M=oe?_e??(a??h)({rects:m,activeNodeRect:Z,activeIndex:y,overIndex:w,index:S}):null,le=ma(y)&&ma(w)?o({id:i,items:d,activeIndex:y,overIndex:w}):S,V=L==null?void 0:L.id,H=g.useRef({activeId:V,items:d,newIndex:le,containerId:f}),pe=d!==H.current.items,me=t({active:L,containerId:f,isDragging:ee,isSorting:se,id:i,index:S,items:d,newIndex:H.current.newIndex,previousItems:H.current.items,previousContainerId:H.current.containerId,transition:c,wasDragging:H.current.activeId!=null}),Ze=IN({disabled:!me,index:S,node:N,rect:_});return g.useEffect(()=>{se&&H.current.newIndex!==le&&(H.current.newIndex=le),f!==H.current.containerId&&(H.current.containerId=f),d!==H.current.items&&(H.current.items=d)},[se,le,f,d]),g.useEffect(()=>{if(V===H.current.activeId)return;if(V&&!H.current.activeId){H.current.activeId=V;return}const Pr=setTimeout(()=>{H.current.activeId=V},50);return()=>clearTimeout(Pr)},[V]),{active:L,activeIndex:y,attributes:J,data:b,rect:_,index:S,newIndex:le,items:d,isOver:j,isSorting:se,isDragging:ee,listeners:K,node:N,overIndex:w,over:T,setNodeRef:O,setActivatorNodeRef:D,setDroppableNodeRef:R,setDraggableNodeRef:ae,transform:Ze??M,transition:It()};function It(){if(Ze||pe&&H.current.newIndex===S)return jN;if(!(he&&!aN(A)||!c)&&(se||me))return Ei.Transition.toString({...c,property:Lx})}}function DN(e,t){var n,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}At.Down,At.Right,At.Up,At.Left;const ON=Of()(cC((e,t)=>({taskFilter:"all",setTaskFilter:n=>e({taskFilter:n}),sidebarOpen:!0,setSidebarOpen:n=>e({sidebarOpen:n}),toggleSidebar:()=>e(n=>({sidebarOpen:!n.sidebarOpen})),theme:"system",setTheme:n=>e({theme:n}),isAddingTask:!1,setIsAddingTask:n=>e({isAddingTask:n}),editingTaskId:null,setEditingTaskId:n=>e({editingTaskId:n}),searchQuery:"",setSearchQuery:n=>e({searchQuery:n}),sortBy:"custom",setSortBy:n=>e({sortBy:n}),viewMode:"list",setViewMode:n=>e({viewMode:n})}),{name:"ui-store"})),AN=()=>ON(e=>({editingId:e.editingTaskId,setEditingId:e.setEditingTaskId}));function MN({task:e,isSubtask:t=!1,className:n="",onAddSubtask:r}){const[s,o]=g.useState(e.content),[i,a]=g.useState(e.priority.toString()),[l,c]=g.useState(e.dueDate?new Date(e.dueDate).toISOString().split("T")[0]:""),[d,f]=g.useState(!1),[y,x]=g.useState(!1),{editingId:k,setEditingId:m}=AN(),w=Li(),p=Av(),{showDeleteSuccess:h}=ko(),{isSelectionMode:v,isTaskSelected:S,toggleTaskSelection:b}=Fi(),E=k===e.id,_=Pf(e.dueDate),N=S(e.id),{attributes:j,listeners:R,setNodeRef:L,transform:A,transition:Z,isDragging:J}=PN({id:e.id}),ae={transform:Ei.Transform.toString(A),transition:Z},K=()=>{v?b(e.id):w.mutate({id:e.id,input:{isCompleted:!e.isCompleted}})},ee=()=>{m(e.id),o(e.content),a(e.priority.toString()),c(e.dueDate?new Date(e.dueDate).toISOString().split("T")[0]:"")},T=()=>{s.trim()&&(w.mutate({id:e.id,input:{content:s.trim(),priority:parseInt(i),dueDate:l?new Date(l).getTime():null}}),m(null))},D=()=>{m(null),o(e.content),a(e.priority.toString()),c(e.dueDate?new Date(e.dueDate).toISOString().split("T")[0]:"")},$=()=>{f(!0)},O=()=>{x(!0),setTimeout(()=>{p.mutate(e.id,{onSuccess:()=>{h(e.content,()=>{console.log("撤销删除:",e.id)})},onSettled:()=>{x(!1)}})},200)},se=oe=>{oe.key==="Enter"?T():oe.key==="Escape"&&D()};return u.jsxs("div",{ref:L,style:ae,className:B("group flex items-center gap-3 p-3 bg-card rounded-lg border transition-all duration-200",t?"bg-muted/50 border-muted text-sm":"bg-card border-border hover:border-border/80",J&&"opacity-50 shadow-lg",e.isCompleted&&"opacity-60",_&&!e.isCompleted&&"border-destructive/50 bg-destructive/5",y&&"animate-out slide-out-to-right-full duration-200 opacity-0 scale-95",N&&"ring-2 ring-primary border-primary/50 bg-primary/5",n),children:[u.jsx("div",{...j,...R,className:"cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity",children:u.jsx(ak,{className:"h-4 w-4 text-gray-400"})}),u.jsx($f,{checked:v?N:e.isCompleted,onCheckedChange:K,className:"flex-shrink-0"}),u.jsx("div",{className:"flex-1 min-w-0",children:E?u.jsxs("div",{className:"space-y-2",children:[u.jsx(gn,{value:s,onChange:oe=>o(oe.target.value),onKeyDown:se,placeholder:"任务内容...",autoFocus:!0}),u.jsxs("div",{className:"flex gap-2",children:[u.jsxs(lh,{value:i,onValueChange:a,children:[u.jsx(tc,{className:"w-24",children:u.jsx(ch,{})}),u.jsxs(nc,{children:[u.jsx(Gt,{value:"1",children:Ho[Re.HIGH]}),u.jsx(Gt,{value:"2",children:Ho[Re.MEDIUM]}),u.jsx(Gt,{value:"3",children:Ho[Re.LOW]})]})]}),u.jsx(gn,{type:"date",value:l,onChange:oe=>c(oe.target.value),className:"w-40"})]})]}):u.jsxs("div",{children:[u.jsx("div",{className:B("text-sm font-medium",e.isCompleted&&"line-through text-gray-500"),children:e.content}),u.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[u.jsx("span",{className:B("px-2 py-0.5 text-xs rounded-full border",Mf[e.priority]),children:Ho[e.priority]}),e.dueDate&&u.jsxs("div",{className:B("flex items-center gap-1 text-xs",_?"text-red-600":"text-gray-500"),children:[_?u.jsx(Ml,{className:"h-3 w-3"}):u.jsx(ns,{className:"h-3 w-3"}),Cv(e.dueDate)]}),u.jsxs("div",{className:"flex items-center gap-1 text-xs text-gray-400",children:[u.jsx(Ll,{className:"h-3 w-3"}),new Date(e.createdAt).toLocaleDateString("zh-CN")]})]})]})}),u.jsx("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:E?u.jsxs(u.Fragment,{children:[u.jsx(X,{size:"sm",variant:"ghost",onClick:T,disabled:!s.trim(),children:"保存"}),u.jsx(X,{size:"sm",variant:"ghost",onClick:D,children:"取消"})]}):u.jsxs(u.Fragment,{children:[!t&&r&&u.jsx(X,{size:"sm",variant:"ghost",onClick:()=>r(e.id),title:"添加子任务",className:"opacity-0 group-hover:opacity-100 transition-opacity",children:u.jsx(rs,{className:"h-4 w-4"})}),u.jsx(X,{size:"sm",variant:"ghost",onClick:ee,children:u.jsx(Rf,{className:"h-4 w-4"})}),u.jsx(X,{size:"sm",variant:"ghost",onClick:$,className:"text-red-600 hover:text-red-700",children:u.jsx(wr,{className:"h-4 w-4"})})]})}),u.jsx(Gf,{open:d,onOpenChange:f,onConfirm:O,itemName:e.content,isLoading:p.isPending})]})}const LN=({parentTask:e,subtasks:t,className:n=""})=>{const[r,s]=g.useState(!0),[o,i]=g.useState(!1),[a,l]=g.useState(""),c=Ul(),d=async()=>{if(a.trim())try{await c.mutateAsync({content:a.trim(),priority:Re.MEDIUM,parentTaskId:e.id,taskType:"subtask",progress:0}),l(""),i(!1)}catch(k){console.error("Failed to create subtask:",k)}},f=k=>{k.key==="Enter"?d():k.key==="Escape"&&(i(!1),l(""))},y=t.filter(k=>k.isCompleted).length,x=t.length;return x===0&&!o?u.jsx("div",{className:`ml-6 mt-3 ${n}`,children:u.jsxs(X,{variant:"ghost",size:"sm",onClick:()=>i(!0),className:"text-gray-500 hover:text-blue-600 hover:bg-blue-50 transition-colors duration-200 h-8 px-3 rounded-md",children:[u.jsx(rs,{className:"h-4 w-4 mr-2"}),"添加子任务"]})}):u.jsxs("div",{className:`ml-6 mt-3 ${n}`,children:[x>0&&u.jsxs("div",{className:"flex items-center gap-3 mb-3 py-2",children:[u.jsx(X,{variant:"ghost",size:"sm",onClick:()=>s(!r),className:"p-1 h-auto text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors duration-200",children:r?u.jsx(Tf,{className:"h-4 w-4"}):u.jsx(pv,{className:"h-4 w-4"})}),u.jsx("span",{className:"text-sm font-medium text-gray-700",children:"子任务"}),u.jsxs("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full",children:[y,"/",x]}),u.jsx(X,{variant:"ghost",size:"sm",onClick:()=>i(!0),className:"p-1 h-auto text-gray-500 hover:text-blue-600 hover:bg-blue-50 ml-auto rounded transition-colors duration-200",title:"添加子任务",children:u.jsx(rs,{className:"h-4 w-4"})})]}),r&&u.jsx("div",{className:"space-y-1",children:t.map((k,m)=>u.jsxs("div",{className:"relative",children:[u.jsx("div",{className:"absolute left-0 top-0 w-5 h-7 border-l-2 border-b-2 border-gray-300 rounded-bl-lg",style:{borderColor:"#e5e7eb"}}),u.jsx("div",{className:"ml-6 pl-1",children:u.jsx(MN,{task:k,isSubtask:!0,className:"bg-gray-50/80 border-gray-200 hover:bg-gray-100/80 transition-colors duration-200 shadow-sm"})})]},k.id))}),o&&u.jsxs("div",{className:"mt-3 relative",children:[u.jsx("div",{className:"absolute left-0 top-0 w-5 h-7 border-l-2 border-b-2 border-gray-300 rounded-bl-lg",style:{borderColor:"#e5e7eb"}}),u.jsx("div",{className:"ml-6 pl-1",children:u.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-3 shadow-sm",children:[u.jsx(gn,{value:a,onChange:k=>l(k.target.value),onKeyDown:f,placeholder:"输入子任务内容...",className:"border-0 bg-transparent text-sm px-0 focus-visible:ring-0 focus-visible:ring-offset-0 placeholder:text-gray-400",autoFocus:!0}),u.jsxs("div",{className:"flex gap-2 mt-3 pt-2 border-t border-gray-100",children:[u.jsx(X,{size:"sm",onClick:d,disabled:!a.trim()||c.isPending,className:"ms-button-primary h-7 px-3 text-xs",children:c.isPending?"添加中...":"添加"}),u.jsx(X,{size:"sm",variant:"ghost",onClick:()=>{i(!1),l("")},className:"h-7 px-3 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100",children:"取消"})]})]})})]})]})};function FN({task:e,isOpen:t,onClose:n}){var $;const[r,s]=g.useState(!1),[o,i]=g.useState({}),[a,l]=g.useState(!0),[c,d]=g.useState(""),[f,y]=g.useState(!1),[x,k]=g.useState(!1),[m,w]=g.useState(!1),{data:p=[]}=$l(),h=Li();Ul();const v=Mv(),S=Af(),{showSuccess:b,showError:E,showDeleteSuccess:_}=ko(),{addDeletedTask:N,removeDeletedTask:j}=Fi(),R=p.filter(O=>O.parentTaskId===(e==null?void 0:e.id));if(R.filter(O=>O.isCompleted).length,g.useEffect(()=>{e&&(i({content:e.content,description:e.description||"",priority:e.priority,dueDate:e.dueDate,isImportant:e.isImportant||!1}),s(!1))},[e]),!t||!e)return null;const L=Pf(e.dueDate),A=async()=>{try{await h.mutateAsync({id:e.id,input:o}),s(!1),b("任务已更新")}catch{E("更新任务失败")}},Z=()=>{i({content:e.content,description:e.description||"",priority:e.priority,dueDate:e.dueDate,isImportant:e.isImportant||!1}),s(!1)},J=async()=>{try{await h.mutateAsync({id:e.id,input:{isCompleted:!e.isCompleted}})}catch{E("更新任务状态失败")}},ae=async()=>{try{await h.mutateAsync({id:e.id,input:{isImportant:!e.isImportant}})}catch{E("更新任务重要性失败")}},K=()=>{k(!0)},ee=async()=>{w(!0);try{await v.mutateAsync(e.id),N({id:e.id,content:e.content,deletedAt:Date.now()}),_(e.content,async()=>{try{j(e.id),await S.mutateAsync(e.id),_("撤销成功",()=>{})}catch(O){console.error("Failed to restore task:",O);let se="无法恢复已删除的任务";O instanceof Error&&(O.message.includes("任务不存在或未被删除")?se="该任务可能已经被恢复":se=O.message),E("撤销失败",se)}}),n()}catch(O){console.error("Failed to delete task:",O),E("删除失败",O instanceof Error?O.message:"删除任务时发生错误")}finally{w(!1),k(!1)}},T=O=>Mf[O]||"bg-gray-100",D=O=>O?new Date(O).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric"}):"";return u.jsxs(u.Fragment,{children:[t&&u.jsx("div",{className:"fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden animate-in fade-in duration-300",onClick:n}),u.jsx("div",{className:B("fixed inset-y-0 right-0 bg-background border-l border-border shadow-xl transform transition-all duration-300 ease-out z-50","w-full md:w-96",t?"translate-x-0 opacity-100":"translate-x-full opacity-0"),children:u.jsxs("div",{className:"flex flex-col h-full",children:[u.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[u.jsx("h2",{className:"text-lg font-semibold text-foreground",children:"任务详情"}),u.jsx(X,{variant:"ghost",size:"sm",onClick:n,className:"p-1 h-8 w-8",children:u.jsx(Dn,{className:"h-4 w-4"})})]}),u.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-6",children:[u.jsxs("div",{className:"flex items-start gap-3",children:[u.jsx(X,{variant:"ghost",size:"sm",onClick:J,className:"p-0 h-6 w-6 rounded-full hover:bg-transparent mt-1",children:e.isCompleted?u.jsx(Nf,{className:"h-6 w-6 text-green-600 fill-green-100"}):u.jsx(jf,{className:"h-6 w-6 text-muted-foreground hover:text-primary transition-colors"})}),u.jsx("div",{className:"flex-1 min-w-0",children:r?u.jsx(gn,{value:o.content||"",onChange:O=>i({...o,content:O.target.value}),className:"text-lg font-medium",placeholder:"任务标题"}):u.jsx("h3",{className:B("text-lg font-medium text-foreground",e.isCompleted&&"line-through text-muted-foreground"),children:e.content})}),u.jsx(X,{variant:"ghost",size:"sm",onClick:ae,className:"p-1 h-8 w-8",children:u.jsx(io,{className:B("h-4 w-4",e.isImportant?"text-yellow-500 fill-current":"text-muted-foreground")})})]}),u.jsx("div",{className:"flex gap-2",children:r?u.jsxs(u.Fragment,{children:[u.jsx(X,{onClick:A,size:"sm",children:"保存"}),u.jsx(X,{onClick:Z,variant:"outline",size:"sm",children:"取消"})]}):u.jsxs(u.Fragment,{children:[u.jsxs(X,{onClick:()=>s(!0),variant:"outline",size:"sm",className:"gap-2",children:[u.jsx(Rf,{className:"h-4 w-4"}),"编辑"]}),u.jsxs(X,{onClick:K,variant:"outline",size:"sm",className:"gap-2 text-red-600 hover:text-red-700 hover:border-red-300 hover:bg-red-50 dark:hover:bg-red-950/20",disabled:m,children:[u.jsx(wr,{className:"h-4 w-4"}),"删除"]})]})}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsx(ol,{className:"h-4 w-4 text-muted-foreground"}),u.jsx("span",{className:"text-sm text-muted-foreground w-16",children:"优先级"}),r?u.jsxs(lh,{value:($=o.priority)==null?void 0:$.toString(),onValueChange:O=>i({...o,priority:parseInt(O)}),children:[u.jsx(tc,{className:"w-32",children:u.jsx(ch,{})}),u.jsxs(nc,{children:[u.jsx(Gt,{value:"1",children:"低"}),u.jsx(Gt,{value:"2",children:"中"}),u.jsx(Gt,{value:"3",children:"高"})]})]}):u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("div",{className:B("w-2 h-2 rounded-full",T(e.priority))}),u.jsx("span",{className:"text-sm",children:Ho[e.priority]||"中"})]})]}),u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsx(ns,{className:"h-4 w-4 text-muted-foreground"}),u.jsx("span",{className:"text-sm text-muted-foreground w-16",children:"截止日期"}),r?u.jsx(gn,{type:"date",value:o.dueDate?new Date(o.dueDate).toISOString().split("T")[0]:"",onChange:O=>i({...o,dueDate:O.target.value?new Date(O.target.value).getTime():null}),className:"w-40"}):u.jsx("span",{className:B("text-sm",L&&!e.isCompleted?"text-red-600":"text-foreground"),children:e.dueDate?D(e.dueDate):"未设置"})]}),u.jsxs("div",{className:"flex items-center gap-3",children:[u.jsx(Ll,{className:"h-4 w-4 text-muted-foreground"}),u.jsx("span",{className:"text-sm text-muted-foreground w-16",children:"创建时间"}),u.jsx("span",{className:"text-sm text-foreground",children:D(e.createdAt)})]})]}),u.jsxs("div",{className:"space-y-2",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(ik,{className:"h-4 w-4 text-muted-foreground"}),u.jsx("span",{className:"text-sm font-medium text-foreground",children:"描述"})]}),r?u.jsx(Yf,{value:o.description||"",onChange:O=>i({...o,description:O.target.value}),placeholder:"添加描述...",className:"min-h-[80px]"}):u.jsx("div",{className:"text-sm text-muted-foreground bg-muted/30 rounded-md p-3 min-h-[80px]",children:e.description||"暂无描述"})]}),u.jsx("div",{className:"border-t border-border pt-4",children:u.jsx(LN,{parentTask:e,subtasks:R,className:"ml-0"})})]})]})}),u.jsx(Gf,{open:x,onOpenChange:k,onConfirm:ee,itemName:e.content,isLoading:m,description:R.length>0?`确定要删除"${e.content}"吗？这将同时删除 ${R.length} 个子任务。此操作可在30秒内撤销。`:`确定要删除"${e.content}"吗？此操作可在30秒内撤销。`})]})}const Fx=({title:e="出现了一些问题",message:t="请稍后重试或刷新页面",onRetry:n,showRetry:r=!0,className:s=""})=>u.jsxs("div",{className:`flex flex-col items-center justify-center p-8 text-center ${s}`,children:[u.jsx("div",{className:"mb-4 p-3 rounded-full bg-red-50 dark:bg-red-900/20",children:u.jsx(Ml,{className:"w-8 h-8 text-red-500"})}),u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:e}),u.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6 max-w-md",children:t}),r&&u.jsxs("button",{onClick:n,className:"inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200",children:[u.jsx(ed,{className:"w-4 h-4"}),"重试"]})]}),zN=({message:e="加载中...",className:t=""})=>u.jsxs("div",{className:`flex flex-col items-center justify-center p-8 text-center ${t}`,children:[u.jsx("div",{className:"mb-4",children:u.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),u.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:e})]}),$N=({title:e="暂无数据",message:t="还没有任何内容",action:n,icon:r,className:s=""})=>u.jsxs("div",{className:`flex flex-col items-center justify-center p-8 text-center ${s}`,children:[r&&u.jsx("div",{className:"mb-4 p-3 rounded-full bg-gray-50 dark:bg-gray-800",children:r}),u.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2",children:e}),u.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6 max-w-md",children:t}),n]});let nT=class extends We.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,n){console.error("Error caught by boundary:",t,n)}render(){return this.state.hasError?u.jsx(Fx,{title:"应用出现错误",message:"页面遇到了意外错误，请刷新页面重试",onRetry:()=>window.location.reload(),className:"min-h-[400px]"}):this.props.children}};function UN(){const{data:e=[],isLoading:t,error:n,refetch:r}=$l(),{data:s=[],isLoading:o,error:i,refetch:a}=gC(),l=Li(),c=Ul(),d=Tr(),{activeView:f,searchQuery:y,sortBy:x,showCompleted:k,setTaskInputFocused:m}=zl(),w=!0,[p,h]=g.useState(null),[v,S]=g.useState(!1),[b,E]=g.useState(null),[_,N]=g.useState(!1),{parentTasks:j,taskMap:R}=g.useMemo(()=>{const oe=Date.now(),he=new Date;he.setHours(0,0,0,0);const _e=he.getTime(),q=_e+24*60*60*1e3;let M=e.filter(V=>f==="completed"||!V.parentTaskId);switch(f){case"today":M=M.filter(V=>!V.isCompleted&&V.dueDate&&V.dueDate>=_e&&V.dueDate<q);break;case"important":M=M.filter(V=>!V.isCompleted&&(V.priority===3||V.isImportant));break;case"planned":M=M.filter(V=>!V.isCompleted&&V.dueDate&&V.dueDate>oe);break;case"completed":M=M.filter(V=>V.isCompleted);break;case"all":default:k||(M=M.filter(V=>!V.isCompleted));break}if(y.trim()){const V=y.toLowerCase();M=M.filter(H=>H.content.toLowerCase().includes(V))}M.sort((V,H)=>{switch(x){case"priority":return H.priority-V.priority;case"dueDate":return!V.dueDate&&!H.dueDate?0:V.dueDate?H.dueDate?V.dueDate-H.dueDate:-1:1;case"created":return H.createdAt-V.createdAt;case"alphabetical":return V.content.localeCompare(H.content);case"custom":default:return V.orderIndex-H.orderIndex}});const le=new Map;return e.forEach(V=>{V.parentTaskId&&(le.has(V.parentTaskId)||le.set(V.parentTaskId,[]),le.get(V.parentTaskId).push(V))}),le.forEach(V=>{V.sort((H,pe)=>H.orderIndex-pe.orderIndex)}),{parentTasks:M,taskMap:le}},[e,f,y,x,k]),L=g.useMemo(()=>s.filter(oe=>{const he=_e=>{const q=_e.task,M=Date.now(),le=new Date().setHours(0,0,0,0),V=new Date().setHours(23,59,59,999);let H=!0;switch(f){case"today":H=!q.isCompleted&&q.dueDate&&q.dueDate>=le&&q.dueDate<=V;break;case"important":H=!q.isCompleted&&(q.priority===3||q.isImportant);break;case"planned":H=!q.isCompleted&&q.dueDate&&q.dueDate>M;break;case"completed":H=q.isCompleted;break;case"all":default:k||(H=!q.isCompleted);break}const pe=!y.trim()||q.content.toLowerCase().includes(y.toLowerCase())||q.description&&q.description.toLowerCase().includes(y.toLowerCase());return H&&pe?!0:_e.children.some(me=>he(me))};return he(oe)}),[s,f,y,k,w]),A=oe=>{const he=e.find(_e=>_e.id===oe);he&&l.mutate({id:oe,input:{isCompleted:!he.isCompleted}})},Z=async oe=>{try{await c.mutateAsync({content:"新子任务",priority:2,parentTaskId:oe,taskType:"subtask"})}catch(he){console.error("Failed to create subtask:",he)}},J=oe=>{h(oe),S(!0)},ae=()=>{S(!1),h(null)},K=oe=>{E(oe),N(!0)},ee=()=>{N(!1),E(null)},T=o,D=i,$=a,O=()=>{$(),d.invalidateQueries({queryKey:xe.tasks}),d.invalidateQueries({queryKey:xe.taskStats})};if(T)return u.jsx(zN,{message:"正在加载任务...",className:"min-h-[300px]"});if(D)return u.jsx(Fx,{title:"任务加载失败",message:"无法获取任务数据，请检查网络连接或稍后重试",onRetry:O,className:"min-h-[300px]"});if(L.length===0){const{title:oe,message:he,showAction:_e}=(()=>{if(y.trim())return{title:"没有找到匹配的任务",message:`没有包含 "${y}" 的任务`,showAction:!1};switch(f){case"today":return{title:"今天还没有任务",message:"为今天添加一些任务来保持高效",showAction:!0};case"important":return{title:"没有重要任务",message:"标记重要任务来优先处理",showAction:!1};case"planned":return{title:"没有已计划的任务",message:"为任务设置截止日期来更好地规划",showAction:!1};case"completed":return{title:"还没有完成任何任务",message:"完成任务后会在这里显示",showAction:!1};case"all":default:return{title:"开始你的第一个任务",message:"创建任务来管理你的待办事项",showAction:!0}}})(),q=()=>{m(!0)};return u.jsx($N,{title:oe,message:he,icon:u.jsx(Nf,{className:"w-8 h-8 text-muted-foreground"}),action:_e?u.jsxs("button",{onClick:q,className:"inline-flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg transition-colors duration-200",children:[u.jsx(rs,{className:"w-4 h-4"}),"添加任务"]}):void 0,className:"min-h-[300px]"})}return u.jsxs(u.Fragment,{children:[u.jsx("div",{className:"space-y-4",children:L.map(oe=>u.jsx(_0,{hierarchy:oe,onToggleComplete:A,onAddSubtask:Z,onEdit:J,onTaskClick:K},oe.task.id))}),u.jsx(cE,{}),u.jsx(nN,{task:p,isOpen:v,onClose:ae}),u.jsx(FN,{task:b,isOpen:_,onClose:ee})]})}function BN({className:e}){const[t,n]=g.useState(!1),{recentlyDeletedTasks:r,hasUndoableTasks:s,removeDeletedTask:o,clearDeletedTasks:i,addDeletedTask:a}=Fi(),{showSuccess:l,showError:c}=ko(),d=Af();g.useEffect(()=>{n(s)},[s]);const f=async m=>{try{o(m.id),await d.mutateAsync(m.id),l("撤销成功",`"${m.content}" 已恢复`)}catch(w){console.error("Failed to restore task:",w);let p="无法恢复已删除的任务";w instanceof Error&&(w.message.includes("任务不存在或未被删除")?p="该任务可能已经被恢复或不存在":p=w.message),c("撤销失败",p),w instanceof Error&&w.message.includes("任务不存在或未被删除")||a(m)}},y=async()=>{const m=[...r],w=m.map(p=>p.id);try{i(),await window.electronAPI.task.batchRestore(w),l("批量撤销成功",`已恢复 ${w.length} 个任务`)}catch(p){console.error("Failed to batch restore tasks:",p),m.forEach(v=>a(v));let h="无法恢复部分任务";p instanceof Error&&(h=p.message),c("批量撤销失败",h)}},x=()=>{i()},k=m=>{const w=3e4-(Date.now()-m);return`${Math.max(0,Math.ceil(w/1e3))}s`};return!t||r.length===0?null:u.jsx("div",{className:B("fixed top-4 right-4 z-50 max-w-sm","animate-in slide-in-from-top-full duration-300",e),children:u.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 space-y-3",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx(wr,{className:"h-4 w-4 text-red-600"}),u.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["最近删除 (",r.length,")"]})]}),u.jsx(X,{variant:"ghost",size:"sm",onClick:x,className:"p-1 h-6 w-6",children:u.jsx(Dn,{className:"h-4 w-4"})})]}),u.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:r.map(m=>u.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded-md",children:[u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsx("p",{className:"text-sm text-gray-900 truncate",children:m.content}),u.jsxs("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[u.jsx(Ll,{className:"h-3 w-3"}),k(m.deletedAt)]})]}),u.jsxs(X,{variant:"outline",size:"sm",onClick:()=>f(m),className:"ml-2 h-7 px-2 text-xs",children:[u.jsx(td,{className:"h-3 w-3 mr-1"}),"撤销"]})]},m.id))}),r.length>1&&u.jsx("div",{className:"pt-2 border-t border-gray-200",children:u.jsxs(X,{variant:"outline",size:"sm",onClick:y,className:"w-full",children:[u.jsx(td,{className:"h-4 w-4 mr-2"}),"撤销全部 (",r.length,")"]})})]})})}function VN(){const{data:e,isLoading:t}=mC();if(t||!e)return u.jsx("div",{className:"grid grid-cols-4 gap-4",children:Array.from({length:4}).map((s,o)=>u.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[u.jsx("div",{className:"h-4 bg-gray-200 rounded animate-pulse mb-2"}),u.jsx("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]},o))});const n=e.total>0?Math.round(e.completed/e.total*100):0,r=[{label:"总任务",value:e.total,icon:jf,color:"text-gray-600",bgColor:"bg-gray-50"},{label:"已完成",value:e.completed,icon:fv,color:"text-green-600",bgColor:"bg-green-50"},{label:"待完成",value:e.pending,icon:Ll,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"已逾期",value:e.overdue,icon:Ml,color:"text-red-600",bgColor:"bg-red-50"}];return u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm font-medium text-gray-700",children:"完成率"}),u.jsxs("span",{className:"text-lg font-bold text-gray-900",children:[n,"%"]})]}),u.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:u.jsx("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:`${n}%`}})})]}),u.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:r.map(s=>{const o=s.icon;return u.jsx("div",{className:`p-4 rounded-lg border border-gray-200 ${s.bgColor}`,children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("p",{className:"text-sm font-medium text-gray-600",children:s.label}),u.jsx("p",{className:`text-2xl font-bold ${s.color}`,children:s.value})]}),u.jsx(o,{className:`h-8 w-8 ${s.color}`})]})},s.label)})})]})}function HN(){const e=g.useRef(null),{setTaskInputFocused:t}=zl(),n=()=>{var r;t(!0),(r=e.current)==null||r.scrollIntoView({behavior:"smooth",block:"center"})};return u.jsxs(yC,{onAddTaskClick:n,children:[u.jsxs("div",{className:"space-y-6",children:[u.jsx("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6 border border-primary/20",children:u.jsx(VN,{})}),u.jsx("div",{ref:e,children:u.jsx(wC,{})}),u.jsx(UN,{})]}),u.jsx(BN,{})]})}const zx=We.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:B("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));zx.displayName="Card";const $x=We.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:B("flex flex-col space-y-1.5 p-6",e),...t}));$x.displayName="CardHeader";const Ux=We.forwardRef(({className:e,...t},n)=>u.jsx("h3",{ref:n,className:B("text-2xl font-semibold leading-none tracking-tight",e),...t}));Ux.displayName="CardTitle";const Bx=We.forwardRef(({className:e,...t},n)=>u.jsx("p",{ref:n,className:B("text-sm text-muted-foreground",e),...t}));Bx.displayName="CardDescription";const Vx=We.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:B("p-6 pt-0",e),...t}));Vx.displayName="CardContent";const WN=We.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:B("flex items-center p-6 pt-0",e),...t}));WN.displayName="CardFooter";class uh extends g.Component{constructor(n){super(n);Ut(this,"retryCount",0);Ut(this,"maxRetries",3);Ut(this,"reportError",(n,r)=>{var o;const s={id:this.state.errorId,message:n.message,stack:n.stack,componentStack:r.componentStack,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href,retryCount:this.retryCount};try{console.error("Error Report:",s),(o=window.electronAPI)!=null&&o.app&&console.log("Error logged to main process")}catch(i){console.error("Failed to report error:",i)}});Ut(this,"handleReset",()=>{this.retryCount++,this.setState({hasError:!1,error:void 0,errorInfo:void 0,errorId:this.generateErrorId()})});Ut(this,"handleRestart",()=>{var n,r;(r=(n=window.electronAPI)==null?void 0:n.app)!=null&&r.quit?window.electronAPI.app.quit():window.location.reload()});Ut(this,"handleGoHome",()=>{this.handleReset()});Ut(this,"copyErrorDetails",()=>{var r,s,o;const n={id:this.state.errorId,message:(r=this.state.error)==null?void 0:r.message,stack:(s=this.state.error)==null?void 0:s.stack,componentStack:(o=this.state.errorInfo)==null?void 0:o.componentStack,timestamp:new Date().toISOString()};navigator.clipboard.writeText(JSON.stringify(n,null,2)).then(()=>{console.log("Error details copied to clipboard")}).catch(i=>{console.error("Failed to copy error details:",i)})});this.state={hasError:!1,errorId:this.generateErrorId()}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorId:uh.prototype.generateErrorId()}}componentDidCatch(n,r){var s,o;console.error("ErrorBoundary caught an error:",n,r),this.setState({errorInfo:r}),(o=(s=this.props).onError)==null||o.call(s,n,r),this.reportError(n,r)}generateErrorId(){return`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}render(){var n,r;if(this.state.hasError){if(this.props.fallback)return this.props.fallback;const s=this.retryCount<this.maxRetries,o=((n=this.state.error)==null?void 0:n.name)!=="ChunkLoadError";return u.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:u.jsxs(zx,{className:"max-w-lg w-full",children:[u.jsxs($x,{className:"text-center",children:[u.jsx("div",{className:"mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center",children:u.jsx(_f,{className:"h-6 w-6 text-destructive"})}),u.jsx(Ux,{className:"text-xl font-semibold",children:"应用出现错误"}),u.jsx(Bx,{children:"很抱歉，应用遇到了意外错误。您可以尝试以下操作来恢复。"})]}),u.jsxs(Vx,{className:"space-y-4",children:[u.jsxs("div",{className:"bg-muted p-3 rounded-md",children:[u.jsx("p",{className:"text-sm font-medium text-muted-foreground mb-1",children:"错误信息:"}),u.jsx("p",{className:"text-sm text-foreground",children:((r=this.state.error)==null?void 0:r.message)||"未知错误"}),u.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["错误ID: ",this.state.errorId]})]}),u.jsxs("div",{className:"space-y-2",children:[s&&o&&u.jsxs(X,{onClick:this.handleReset,className:"w-full",variant:"default",children:[u.jsx(ed,{className:"mr-2 h-4 w-4"}),"重试 (",this.maxRetries-this.retryCount," 次机会)"]}),u.jsxs(X,{onClick:this.handleGoHome,variant:"outline",className:"w-full",children:[u.jsx(mv,{className:"mr-2 h-4 w-4"}),"返回主页"]}),u.jsxs(X,{onClick:this.handleRestart,variant:"outline",className:"w-full",children:[u.jsx(ed,{className:"mr-2 h-4 w-4"}),"重启应用"]})]}),!1,u.jsx("div",{className:"text-center text-xs text-muted-foreground",children:"如果问题持续存在，请联系技术支持"})]})]})})}return this.props.children}}class QN{constructor(t={}){Ut(this,"config");Ut(this,"sessionId");Ut(this,"reportQueue",[]);this.config={enableConsoleLogging:!0,enableLocalStorage:!0,enableRemoteReporting:!1,maxLocalReports:100,...t},this.sessionId=this.generateSessionId(),this.setupGlobalErrorHandlers(),this.loadStoredReports()}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateErrorId(){return`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}setupGlobalErrorHandlers(){window.addEventListener("error",t=>{var n;this.reportError({type:"javascript",message:t.message,stack:(n=t.error)==null?void 0:n.stack,severity:"high",context:{filename:t.filename,lineno:t.lineno,colno:t.colno}})}),window.addEventListener("unhandledrejection",t=>{var n,r;this.reportError({type:"promise",message:((n=t.reason)==null?void 0:n.message)||"Unhandled Promise Rejection",stack:(r=t.reason)==null?void 0:r.stack,severity:"medium",context:{reason:t.reason}})})}loadStoredReports(){if(this.config.enableLocalStorage)try{const t=localStorage.getItem("errorReports");t&&(this.reportQueue=JSON.parse(t))}catch(t){console.warn("Failed to load stored error reports:",t)}}saveReportsToStorage(){if(this.config.enableLocalStorage)try{const t=this.reportQueue.slice(-this.config.maxLocalReports);localStorage.setItem("errorReports",JSON.stringify(t))}catch(t){console.warn("Failed to save error reports to storage:",t)}}reportError(t){var r,s;const n={id:this.generateErrorId(),type:"javascript",message:"Unknown error",timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href,sessionId:this.sessionId,environment:"production",severity:"medium",...t};return(s=(r=window.electronAPI)==null?void 0:r.app)!=null&&s.getVersion&&window.electronAPI.app.getVersion().then(o=>{n.buildVersion=o}).catch(()=>{}),this.reportQueue.push(n),this.config.enableConsoleLogging&&console.error("Error reported:",n),this.saveReportsToStorage(),this.config.enableRemoteReporting&&this.sendToRemoteService(n),this.sendToMainProcess(n),n.id}reportReactError(t,n,r){return this.reportError({type:"react",message:t.message,stack:t.stack,componentStack:n.componentStack,severity:"high",context:r})}async sendToRemoteService(t){if(this.config.reportingEndpoint)try{const n=await fetch(this.config.reportingEndpoint,{method:"POST",headers:{"Content-Type":"application/json",...this.config.apiKey&&{Authorization:`Bearer ${this.config.apiKey}`}},body:JSON.stringify(t)});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);console.log("Error report sent successfully:",t.id)}catch(n){console.warn("Failed to send error report to remote service:",n)}}sendToMainProcess(t){var n;try{(n=window.electronAPI)!=null&&n.invoke&&window.electronAPI.invoke("error:report",t).catch(r=>{console.warn("Failed to send error report to main process:",r)})}catch(r){console.warn("Failed to communicate with main process:",r)}}getReports(){return[...this.reportQueue]}getReportById(t){return this.reportQueue.find(n=>n.id===t)}clearReports(){this.reportQueue=[],this.saveReportsToStorage()}getReportsByType(t){return this.reportQueue.filter(n=>n.type===t)}getReportsBySeverity(t){return this.reportQueue.filter(n=>n.severity===t)}exportReports(){return JSON.stringify(this.reportQueue,null,2)}updateConfig(t){this.config={...this.config,...t}}getStats(){const t=this.reportQueue,n=Date.now(),r=60*60*1e3,s=24*r;return{total:t.length,lastHour:t.filter(o=>n-new Date(o.timestamp).getTime()<r).length,lastDay:t.filter(o=>n-new Date(o.timestamp).getTime()<s).length,byType:{react:t.filter(o=>o.type==="react").length,javascript:t.filter(o=>o.type==="javascript").length,promise:t.filter(o=>o.type==="promise").length,network:t.filter(o=>o.type==="network").length},bySeverity:{low:t.filter(o=>o.severity==="low").length,medium:t.filter(o=>o.severity==="medium").length,high:t.filter(o=>o.severity==="high").length,critical:t.filter(o=>o.severity==="critical").length}}}}const ZN=new QN({enableConsoleLogging:!0,enableLocalStorage:!0,enableRemoteReporting:!0,maxLocalReports:100}),KN=(e,t,n)=>ZN.reportReactError(e,t,n);function qN(){return g.useEffect(()=>{NC()},[]),u.jsx(uh,{onError:KN,children:u.jsx($S,{client:JS,children:u.jsx(Db,{children:u.jsxs(TC,{children:[u.jsx("div",{className:"min-h-screen bg-background text-foreground theme-transition",children:u.jsx(HN,{})}),u.jsx(XS,{initialIsOpen:!1})]})})})})}Zc.createRoot(document.getElementById("root")).render(u.jsx(We.StrictMode,{children:u.jsx(qN,{})}));
