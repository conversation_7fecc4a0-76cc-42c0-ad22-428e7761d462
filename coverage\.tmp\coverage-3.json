{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 5}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 3}], "isBlockCoverage": true}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3603, "endOffset": 3761, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 3}], "isBlockCoverage": true}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 0}], "isBlockCoverage": false}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 5}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/hooks/__tests__/useTasks.simple.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18696, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 406, "endOffset": 2348, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 449, "endOffset": 506, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 574, "endOffset": 1687, "count": 1}, {"startOffset": 1598, "endOffset": 1683, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1755, "endOffset": 2344, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2402, "endOffset": 4197, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2473, "endOffset": 3283, "count": 1}, {"startOffset": 3189, "endOffset": 3279, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3351, "endOffset": 4193, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4248, "endOffset": 6197, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4316, "endOffset": 5328, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5393, "endOffset": 6193, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6251, "endOffset": 6822, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6324, "endOffset": 6818, "count": 1}, {"startOffset": 6724, "endOffset": 6814, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1416", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/hooks/useTasks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "useTasks", "ranges": [{"startOffset": 612, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 884, "endOffset": 908, "count": 3}], "isBlockCoverage": true}, {"functionName": "useTaskStats", "ranges": [{"startOffset": 912, "endOffset": 1097, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1198, "endOffset": 1226, "count": 3}], "isBlockCoverage": true}, {"functionName": "useCreateTask", "ranges": [{"startOffset": 1230, "endOffset": 1870, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1972, "endOffset": 2001, "count": 3}], "isBlockCoverage": true}, {"functionName": "useUpdateTask", "ranges": [{"startOffset": 2005, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2836, "endOffset": 2865, "count": 3}], "isBlockCoverage": true}, {"functionName": "useDeleteTask", "ranges": [{"startOffset": 2869, "endOffset": 3527, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3629, "endOffset": 3658, "count": 3}], "isBlockCoverage": true}, {"functionName": "useReorderTasks", "ranges": [{"startOffset": 3662, "endOffset": 4873, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4977, "endOffset": 5008, "count": 3}], "isBlockCoverage": true}]}, {"scriptId": "1469", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 309, "endOffset": 328, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAll", "ranges": [{"startOffset": 360, "endOffset": 383, "count": 0}], "isBlockCoverage": false}, {"functionName": "create", "ranges": [{"startOffset": 395, "endOffset": 428, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 440, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 493, "endOffset": 520, "count": 0}], "isBlockCoverage": false}, {"functionName": "reorder", "ranges": [{"startOffset": 533, "endOffset": 565, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStats", "ranges": [{"startOffset": 579, "endOffset": 604, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 703, "endOffset": 726, "count": 0}], "isBlockCoverage": false}, {"functionName": "getVersion", "ranges": [{"startOffset": 761, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "quit", "ranges": [{"startOffset": 797, "endOffset": 817, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 915, "endOffset": 937, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 970, "endOffset": 1000, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 1009, "endOffset": 1053, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1156, "endOffset": 1183, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1470", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/queryClient.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "retry<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 597, "endOffset": 653, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 964, "endOffset": 991, "count": 0}], "isBlockCoverage": false}, {"functionName": "settings", "ranges": [{"startOffset": 1081, "endOffset": 1128, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1264, "endOffset": 1290, "count": 0}], "isBlockCoverage": false}]}]}