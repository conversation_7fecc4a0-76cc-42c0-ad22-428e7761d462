import React, { useState, useRef, useEffect } from 'react'
import {
  Plus,
  Calendar,
  Star,
  Flag,
  Send,
  X
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { cn } from '../../lib/utils'
import { useCreateTask } from '../../hooks/useTasks'
import { useViewStore } from '../../stores/viewStore'
import { TaskPriority } from '../../../shared/types/task'

export function ModernTaskInput() {
  const [content, setContent] = useState('')
  const [isExpanded, setIsExpanded] = useState(false)
  const [priority, setPriority] = useState<TaskPriority>(TaskPriority.MEDIUM)
  const [dueDate, setDueDate] = useState<string>('')
  const [isImportant, setIsImportant] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)
  const createTask = useCreateTask()
  const { isTaskInputFocused, setTaskInputFocused, activeView } = useViewStore()

  // 监听外部聚焦请求
  useEffect(() => {
    if (isTaskInputFocused) {
      inputRef.current?.focus()
      setIsExpanded(true)
      setTaskInputFocused(false)
    }
  }, [isTaskInputFocused, setTaskInputFocused])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!content.trim()) return

    try {
      // 根据当前视图设置默认值
      let defaultDueDate = dueDate ? new Date(dueDate).getTime() : undefined
      let defaultImportant = isImportant

      // 如果在"我的一天"视图，默认设置今天的截止日期
      if (activeView === 'today' && !dueDate) {
        const today = new Date()
        today.setHours(23, 59, 59, 999) // 设置为今天结束
        defaultDueDate = today.getTime()
      }

      // 如果在"重要"视图，默认标记为重要
      if (activeView === 'important') {
        defaultImportant = true
      }

      await createTask.mutateAsync({
        content: content.trim(),
        priority,
        dueDate: defaultDueDate,
        isImportant: defaultImportant
      })

      // 重置表单
      setContent('')
      setDueDate('')
      setIsImportant(false)
      setPriority(TaskPriority.MEDIUM)
      setIsExpanded(false)

      // 重新聚焦输入框
      inputRef.current?.focus()
    } catch (error) {
      console.error('Failed to create task:', error)
    }
  }

  const handleFocus = () => {
    setIsExpanded(true)
  }

  const handleCancel = () => {
    setContent('')
    setDueDate('')
    setIsImportant(false)
    setPriority(TaskPriority.MEDIUM)
    setIsExpanded(false)
    inputRef.current?.blur()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel()
    } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleSubmit(e as any)
    }
  }

  const getPriorityColor = (p: TaskPriority) => {
    switch (p) {
      case TaskPriority.HIGH:
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-950/30'
      case TaskPriority.LOW:
        return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-950/30'
      default:
        return 'text-muted-foreground bg-muted'
    }
  }

  return (
    <div className={cn(
      "ms-card ms-fade-in transition-all duration-300",
      "focus-within:shadow-lg focus-within:border-blue-300",
      isExpanded && "ms-card-elevated"
    )}>
      <form onSubmit={handleSubmit}>
        {/* 主输入区域 */}
        <div className="flex items-center gap-3 p-4">
          <div className="flex-shrink-0">
            <div className={cn(
              "w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200",
              content.trim()
                ? "border-blue-500 bg-blue-50 text-blue-600"
                : "border-gray-300 text-gray-400 hover:border-blue-400 hover:text-blue-500"
            )}>
              <Plus className="h-4 w-4" />
            </div>
          </div>

          <Input
            ref={inputRef}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            placeholder={`添加任务${activeView === 'today' ? '到我的一天' : activeView === 'important' ? '（重要）' : ''}`}
            className={cn(
              "ms-input border-0 bg-transparent text-base px-0",
              "placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0",
              "transition-all duration-200"
            )}
          />

          {content.trim() && (
            <div className="flex items-center gap-2 flex-shrink-0 ms-slide-up">
              {isExpanded && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="ms-button-ghost p-2 hover:bg-gray-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              <Button
                type="submit"
                size="sm"
                disabled={createTask.isPending}
                className={cn(
                  "ms-button-primary gap-2 shadow-sm",
                  createTask.isPending && "opacity-80 cursor-not-allowed"
                )}
              >
                {createTask.isPending ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                {isExpanded && (createTask.isPending ? '添加中...' : '添加')}
              </Button>
            </div>
          )}
        </div>

        {/* 扩展选项 */}
        {isExpanded && (
          <div className="border-t border-gray-200 bg-gray-50/50 p-4 space-y-4 ms-slide-up">
            {/* 工具栏 */}
            <div className="flex items-center gap-3 flex-wrap">
              {/* 截止日期 */}
              <div className="flex items-center gap-2 ms-scale-in">
                <Calendar className="h-4 w-4 text-gray-500" />
                <input
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  className={cn(
                    "ms-input text-sm px-2 py-1 min-w-[140px]",
                    "border-gray-300 rounded-md bg-white",
                    "hover:border-blue-400 focus:border-blue-500"
                  )}
                />
              </div>

              {/* 重要标记 */}
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setIsImportant(!isImportant)}
                className={cn(
                  "ms-button-ghost gap-2 transition-all duration-200",
                  isImportant
                    ? "text-yellow-600 bg-yellow-100 hover:bg-yellow-200 border-yellow-300"
                    : "text-gray-600 hover:text-yellow-600 hover:bg-yellow-50"
                )}
              >
                <Star className={cn(
                  "h-4 w-4 transition-all duration-200",
                  isImportant && "fill-current text-yellow-500"
                )} />
                重要
              </Button>

              {/* 优先级 */}
              <div className="flex items-center gap-2 ms-scale-in">
                <Flag className="h-4 w-4 text-gray-500" />
                <div className="flex gap-1">
                  {[TaskPriority.LOW, TaskPriority.MEDIUM, TaskPriority.HIGH].map((p) => (
                    <Button
                      key={p}
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setPriority(p)}
                      className={cn(
                        "w-8 h-8 p-0 text-xs font-medium rounded-full transition-all duration-200",
                        "border border-transparent hover:scale-110",
                        priority === p
                          ? cn(getPriorityColor(p), "border-current shadow-sm")
                          : "text-gray-400 hover:text-gray-600 hover:bg-gray-100"
                      )}
                    >
                      {p}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center justify-between pt-2">
              <div className="text-xs text-gray-500 space-y-1">
                <div className="flex items-center gap-2 flex-wrap">
                  {dueDate && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-md">
                      <Calendar className="h-3 w-3" />
                      {new Date(dueDate).toLocaleDateString()}
                    </span>
                  )}
                  {isImportant && (
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-yellow-50 text-yellow-700 rounded-md">
                      <Star className="h-3 w-3 fill-current" />
                      重要
                    </span>
                  )}
                  {priority !== TaskPriority.MEDIUM && (
                    <span className={cn(
                      "inline-flex items-center gap-1 px-2 py-1 rounded-md",
                      priority === TaskPriority.HIGH
                        ? "bg-red-50 text-red-700"
                        : "bg-blue-50 text-blue-700"
                    )}>
                      <Flag className="h-3 w-3" />
                      优先级: {priority === TaskPriority.HIGH ? '高' : '低'}
                    </span>
                  )}
                </div>
                <div className="text-gray-400 text-xs">
                  Ctrl+Enter 提交 • Esc 取消
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={handleCancel}
                  className="ms-button-secondary"
                >
                  取消
                </Button>
                <Button
                  type="submit"
                  size="sm"
                  disabled={!content.trim() || createTask.isPending}
                  className={cn(
                    "ms-button-primary",
                    (!content.trim() || createTask.isPending) && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {createTask.isPending ? '添加中...' : '添加任务'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  )
}
