{"version": 3, "sources": ["../../../src/singlestore-core/columns/json.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreJsonBuilderInitial<TName extends string> = SingleStoreJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'SingleStoreJson';\n\tdata: unknown;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'SingleStoreJson'>>\n\textends SingleStoreColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'SingleStoreJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreJson<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreJson<T extends ColumnBaseConfig<'json', 'SingleStoreJson'>> extends SingleStoreColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreJson';\n\n\tgetSQLType(): string {\n\t\treturn 'json';\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n}\n\nexport function json(): SingleStoreJsonBuilderInitial<''>;\nexport function json<TName extends string>(name: TName): SingleStoreJsonBuilderInitial<TName>;\nexport function json(name?: string) {\n\treturn new SingleStoreJsonBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,mBAAmB,gCAAgC;AAYrD,MAAM,+BACJ,yBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,iBAAiB;AAAA,EACtC;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAA+E,kBAAqB;AAAA,EAChH,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,iBAAiB,OAA0B;AACnD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,uBAAuB,QAAQ,EAAE;AAC7C;", "names": []}