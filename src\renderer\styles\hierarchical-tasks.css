/* 层级任务样式 */

/* 层级任务容器 */
.hierarchical-task-container {
  position: relative;
}

/* 层级缩进 */
.task-level-0 { margin-left: 0; }
.task-level-1 { margin-left: 20px; }
.task-level-2 { margin-left: 40px; }
.task-level-3 { margin-left: 60px; }
.task-level-4 { margin-left: 80px; }

/* 连接线样式 */
.hierarchy-connector {
  position: absolute;
  left: -12px;
  top: 0;
  width: 12px;
  height: 24px;
  border-left: 2px solid #e5e7eb;
  border-bottom: 2px solid #e5e7eb;
  border-bottom-left-radius: 6px;
}

.hierarchy-connector::before {
  content: '';
  position: absolute;
  left: -2px;
  top: -100vh;
  width: 2px;
  height: 100vh;
  background-color: #e5e7eb;
}

/* 最后一个子任务的连接线 */
.hierarchy-connector.last-child::before {
  display: none;
}

/* 展开/收起按钮 */
.hierarchy-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #d1d5db;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hierarchy-toggle:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

.hierarchy-toggle.expanded {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

/* 子任务项样式 */
.subtask-item {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 8px 12px;
  margin: 4px 0;
  position: relative;
}

.subtask-item:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

.subtask-item.completed {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
}

/* 子任务进度条 */
.subtask-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 0 12px;
}

.subtask-progress-bar {
  flex: 1;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.subtask-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.subtask-progress-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 35px;
  text-align: right;
}

/* 添加子任务按钮 */
.add-subtask-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 4px;
  cursor: pointer;
}

.hierarchical-task-container:hover .add-subtask-btn {
  opacity: 1;
}

.add-subtask-btn:hover {
  background-color: #e5e7eb;
  border-color: #9ca3af;
}

/* 层级深度指示器 */
.depth-indicator {
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #9ca3af;
}

.depth-indicator.depth-1 { background-color: #3b82f6; }
.depth-indicator.depth-2 { background-color: #10b981; }
.depth-indicator.depth-3 { background-color: #f59e0b; }
.depth-indicator.depth-4 { background-color: #ef4444; }

/* 拖拽状态 */
.hierarchical-task-container.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 拖拽目标区域 */
.drop-zone {
  min-height: 40px;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #9ca3af;
  font-size: 14px;
  margin: 4px 0;
  transition: all 0.2s ease;
}

.drop-zone.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #3b82f6;
}

/* 任务类型标识 */
.task-type-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.task-type-badge.main-task {
  background-color: #dbeafe;
  color: #1e40af;
}

.task-type-badge.subtask {
  background-color: #d1fae5;
  color: #065f46;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-level-1 { margin-left: 16px; }
  .task-level-2 { margin-left: 32px; }
  .task-level-3 { margin-left: 48px; }
  .task-level-4 { margin-left: 64px; }
  
  .hierarchy-connector {
    left: -10px;
    width: 10px;
  }
  
  .subtask-progress {
    padding: 0 8px;
  }
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.subtask-item {
  animation: slideIn 0.2s ease-out;
}

/* 折叠/展开动画 */
.hierarchy-children {
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.hierarchy-children.collapsed {
  max-height: 0;
}

.hierarchy-children.expanded {
  max-height: 1000px; /* 足够大的值 */
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .hierarchy-connector {
    border-color: #000;
  }
  
  .subtask-item {
    border-color: #000;
  }
  
  .subtask-progress-bar {
    background-color: #000;
  }
}
