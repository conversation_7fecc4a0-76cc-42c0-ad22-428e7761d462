{"version": 3, "sources": ["../../src/gel/session.ts"], "sourcesContent": ["import type { Client } from 'gel';\nimport type { Transaction } from 'gel/dist/transaction';\nimport { type Cache, NoopCache } from '~/cache/core/index.ts';\nimport type { WithCacheConfig } from '~/cache/core/types.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { GelDialect } from '~/gel-core/dialect.ts';\nimport type { SelectedFieldsOrdered } from '~/gel-core/query-builders/select.types.ts';\nimport { GelPreparedQuery, GelSession, GelTransaction, type PreparedQueryConfig } from '~/gel-core/session.ts';\nimport { type Logger, NoopLogger } from '~/logger.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { fillPlaceholders, type Query, type SQL } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport { mapResultRow } from '~/utils.ts';\n\nexport type GelClient = Client | Transaction;\n\nexport class GelDbPreparedQuery<T extends PreparedQueryConfig> extends GelPreparedQuery<T> {\n\tstatic override readonly [entityKind]: string = 'GelPreparedQuery';\n\n\tconstructor(\n\t\tprivate client: GelClient,\n\t\tprivate queryString: string,\n\t\tprivate params: unknown[],\n\t\tprivate logger: Logger,\n\t\tcache: Cache,\n\t\tqueryMetadata: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t} | undefined,\n\t\tcacheConfig: WithCacheConfig | undefined,\n\t\tprivate fields: SelectedFieldsOrdered | undefined,\n\t\tprivate _isResponseInArrayMode: boolean,\n\t\tprivate customResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tprivate transaction: boolean = false,\n\t) {\n\t\tsuper({ sql: queryString, params }, cache, queryMetadata, cacheConfig);\n\t}\n\n\tasync execute(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['execute']> {\n\t\treturn tracer.startActiveSpan('drizzle.execute', async () => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\t\t\tconst { fields, queryString: query, client, joinsNotNullableMap, customResultMapper } = this;\n\t\t\tif (!fields && !customResultMapper) {\n\t\t\t\treturn tracer.startActiveSpan('drizzle.driver.execute', async (span) => {\n\t\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t\t'drizzle.query.text': query,\n\t\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t\t});\n\n\t\t\t\t\treturn await this.queryWithCache(query, params, async () => {\n\t\t\t\t\t\treturn await client.querySQL(query, params.length ? params : undefined);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tconst result = (await tracer.startActiveSpan('drizzle.driver.execute', async (span) => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': query,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\n\t\t\t\treturn await this.queryWithCache(query, params, async () => {\n\t\t\t\t\treturn await client.withSQLRowMode('array').querySQL(query, params.length ? params : undefined);\n\t\t\t\t});\n\t\t\t})) as unknown[][];\n\n\t\t\treturn tracer.startActiveSpan('drizzle.mapResponse', () => {\n\t\t\t\treturn customResultMapper\n\t\t\t\t\t? customResultMapper(result)\n\t\t\t\t\t: result.map((row) => mapResultRow<T['execute']>(fields!, row, joinsNotNullableMap));\n\t\t\t});\n\t\t});\n\t}\n\n\tasync all(placeholderValues: Record<string, unknown> | undefined = {}): Promise<T['all']> {\n\t\treturn await tracer.startActiveSpan('drizzle.execute', async () => {\n\t\t\tconst params = fillPlaceholders(this.params, placeholderValues);\n\t\t\tthis.logger.logQuery(this.queryString, params);\n\t\t\treturn await tracer.startActiveSpan('drizzle.driver.execute', async (span) => {\n\t\t\t\tspan?.setAttributes({\n\t\t\t\t\t'drizzle.query.text': this.queryString,\n\t\t\t\t\t'drizzle.query.params': JSON.stringify(params),\n\t\t\t\t});\n\t\t\t\treturn await this.queryWithCache(this.queryString, params, async () => {\n\t\t\t\t\treturn await this.client.withSQLRowMode('array').querySQL(\n\t\t\t\t\t\tthis.queryString,\n\t\t\t\t\t\tparams.length ? params : undefined,\n\t\t\t\t\t).then((\n\t\t\t\t\t\tresult,\n\t\t\t\t\t) => result);\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode(): boolean {\n\t\treturn this._isResponseInArrayMode;\n\t}\n}\n\nexport interface GelSessionOptions {\n\tlogger?: Logger;\n\tcache?: Cache;\n}\n\nexport class GelDbSession<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends GelSession<GelQueryResultHKT, TFullSchema, TSchema>\n{\n\tstatic override readonly [entityKind]: string = 'GelDbSession';\n\n\tprivate logger: Logger;\n\tprivate cache: Cache;\n\n\tconstructor(\n\t\tprivate client: GelClient,\n\t\tdialect: GelDialect,\n\t\tprivate schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprivate options: GelSessionOptions = {},\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t\tthis.cache = options.cache ?? new NoopCache();\n\t}\n\n\tprepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tqueryMetadata?: {\n\t\t\ttype: 'select' | 'update' | 'delete' | 'insert';\n\t\t\ttables: string[];\n\t\t},\n\t\tcacheConfig?: WithCacheConfig,\n\t): GelDbPreparedQuery<T> {\n\t\treturn new GelDbPreparedQuery(\n\t\t\tthis.client,\n\t\t\tquery.sql,\n\t\t\tquery.params,\n\t\t\tthis.logger,\n\t\t\tthis.cache,\n\t\t\tqueryMetadata,\n\t\t\tcacheConfig,\n\t\t\tfields,\n\t\t\tisResponseInArrayMode,\n\t\t\tcustomResultMapper,\n\t\t);\n\t}\n\n\toverride async transaction<T>(\n\t\ttransaction: (tx: GelTransaction<GelQueryResultHKT, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T> {\n\t\treturn await (this.client as Client).transaction(async (clientTx) => {\n\t\t\tconst session = new GelDbSession(clientTx, this.dialect, this.schema, this.options);\n\t\t\tconst tx = new GelDbTransaction<TFullSchema, TSchema>(this.dialect, session, this.schema);\n\t\t\treturn await transaction(tx);\n\t\t});\n\t}\n\n\toverride async count(sql: SQL): Promise<number> {\n\t\tconst res = await this.execute<[{ count: string }]>(sql);\n\t\treturn Number(res[0]['count']);\n\t}\n}\n\nexport class GelDbTransaction<TFullSchema extends Record<string, unknown>, TSchema extends TablesRelationalConfig>\n\textends GelTransaction<GelQueryResultHKT, TFullSchema, TSchema>\n{\n\tstatic override readonly [entityKind]: string = 'GelDbTransaction';\n\n\toverride async transaction<T>(transaction: (tx: GelDbTransaction<TFullSchema, TSchema>) => Promise<T>): Promise<T> {\n\t\tconst tx = new GelDbTransaction<TFullSchema, TSchema>(\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tthis.schema,\n\t\t);\n\t\treturn await transaction(tx);\n\t}\n}\n\n// TODO fix this\nexport interface GelQueryResultHKT {\n\treadonly $brand: 'GelQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n"], "mappings": "AAEA,SAAqB,iBAAiB;AAEtC,SAAS,kBAAkB;AAG3B,SAAS,kBAAkB,YAAY,sBAAgD;AACvF,SAAsB,kBAAkB;AAExC,SAAS,wBAA8C;AACvD,SAAS,cAAc;AACvB,SAAS,oBAAoB;AAItB,MAAM,2BAA0D,iBAAoB;AAAA,EAG1F,YACS,QACA,aACA,QACA,QACR,OACA,eAIA,aACQ,QACA,wBACA,oBACA,cAAuB,OAC9B;AACD,UAAM,EAAE,KAAK,aAAa,OAAO,GAAG,OAAO,eAAe,WAAW;AAf7D;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAAA,EAGT;AAAA,EAnBA,QAA0B,UAAU,IAAY;AAAA,EAqBhD,MAAM,QAAQ,oBAAyD,CAAC,GAA0B;AACjG,WAAO,OAAO,gBAAgB,mBAAmB,YAAY;AAC5D,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAE9D,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAC7C,YAAM,EAAE,QAAQ,aAAa,OAAO,QAAQ,qBAAqB,mBAAmB,IAAI;AACxF,UAAI,CAAC,UAAU,CAAC,oBAAoB;AACnC,eAAO,OAAO,gBAAgB,0BAA0B,OAAO,SAAS;AACvE,gBAAM,cAAc;AAAA,YACnB,sBAAsB;AAAA,YACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,UAC9C,CAAC;AAED,iBAAO,MAAM,KAAK,eAAe,OAAO,QAAQ,YAAY;AAC3D,mBAAO,MAAM,OAAO,SAAS,OAAO,OAAO,SAAS,SAAS,MAAS;AAAA,UACvE,CAAC;AAAA,QACF,CAAC;AAAA,MACF;AAEA,YAAM,SAAU,MAAM,OAAO,gBAAgB,0BAA0B,OAAO,SAAS;AACtF,cAAM,cAAc;AAAA,UACnB,sBAAsB;AAAA,UACtB,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AAED,eAAO,MAAM,KAAK,eAAe,OAAO,QAAQ,YAAY;AAC3D,iBAAO,MAAM,OAAO,eAAe,OAAO,EAAE,SAAS,OAAO,OAAO,SAAS,SAAS,MAAS;AAAA,QAC/F,CAAC;AAAA,MACF,CAAC;AAED,aAAO,OAAO,gBAAgB,uBAAuB,MAAM;AAC1D,eAAO,qBACJ,mBAAmB,MAAM,IACzB,OAAO,IAAI,CAAC,QAAQ,aAA2B,QAAS,KAAK,mBAAmB,CAAC;AAAA,MACrF,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,IAAI,oBAAyD,CAAC,GAAsB;AACzF,WAAO,MAAM,OAAO,gBAAgB,mBAAmB,YAAY;AAClE,YAAM,SAAS,iBAAiB,KAAK,QAAQ,iBAAiB;AAC9D,WAAK,OAAO,SAAS,KAAK,aAAa,MAAM;AAC7C,aAAO,MAAM,OAAO,gBAAgB,0BAA0B,OAAO,SAAS;AAC7E,cAAM,cAAc;AAAA,UACnB,sBAAsB,KAAK;AAAA,UAC3B,wBAAwB,KAAK,UAAU,MAAM;AAAA,QAC9C,CAAC;AACD,eAAO,MAAM,KAAK,eAAe,KAAK,aAAa,QAAQ,YAAY;AACtE,iBAAO,MAAM,KAAK,OAAO,eAAe,OAAO,EAAE;AAAA,YAChD,KAAK;AAAA,YACL,OAAO,SAAS,SAAS;AAAA,UAC1B,EAAE,KAAK,CACN,WACI,MAAM;AAAA,QACZ,CAAC;AAAA,MACF,CAAC;AAAA,IACF,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,wBAAiC;AAChC,WAAO,KAAK;AAAA,EACb;AACD;AAOO,MAAM,qBACJ,WACT;AAAA,EAMC,YACS,QACR,SACQ,QACA,UAA6B,CAAC,GACrC;AACD,UAAM,OAAO;AALL;AAEA;AACA;AAGR,SAAK,SAAS,QAAQ,UAAU,IAAI,WAAW;AAC/C,SAAK,QAAQ,QAAQ,SAAS,IAAI,UAAU;AAAA,EAC7C;AAAA,EAdA,QAA0B,UAAU,IAAY;AAAA,EAExC;AAAA,EACA;AAAA,EAaR,aACC,OACA,QACA,MACA,uBACA,oBACA,eAIA,aACwB;AACxB,WAAO,IAAI;AAAA,MACV,KAAK;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAe,YACd,aACa;AACb,WAAO,MAAO,KAAK,OAAkB,YAAY,OAAO,aAAa;AACpE,YAAM,UAAU,IAAI,aAAa,UAAU,KAAK,SAAS,KAAK,QAAQ,KAAK,OAAO;AAClF,YAAM,KAAK,IAAI,iBAAuC,KAAK,SAAS,SAAS,KAAK,MAAM;AACxF,aAAO,MAAM,YAAY,EAAE;AAAA,IAC5B,CAAC;AAAA,EACF;AAAA,EAEA,MAAe,MAAM,KAA2B;AAC/C,UAAM,MAAM,MAAM,KAAK,QAA6B,GAAG;AACvD,WAAO,OAAO,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAC9B;AACD;AAEO,MAAM,yBACJ,eACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,MAAe,YAAe,aAAqF;AAClH,UAAM,KAAK,IAAI;AAAA,MACd,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACN;AACA,WAAO,MAAM,YAAY,EAAE;AAAA,EAC5B;AACD;", "names": []}