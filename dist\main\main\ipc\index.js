"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupIpcHandlers = setupIpcHandlers;
const electron_1 = require("electron");
const taskService_1 = require("../services/taskService");
const settingsService_1 = require("../services/settingsService");
const utils_1 = require("../utils");
const channels_1 = require("../../shared/ipc/channels");
// 服务实例
const taskService = new taskService_1.TaskService();
const settingsService = new settingsService_1.SettingsService();
// 安全的 IPC 处理器包装器
function createIpcHandler(channel, handler) {
    return async (event, ...args) => {
        try {
            // 验证输入参数
            const validatedInput = (0, channels_1.validateIpcInput)(channel, args);
            // 执行处理器
            const result = await handler(...validatedInput);
            // 验证输出结果
            const validatedOutput = (0, channels_1.validateIpcOutput)(channel, result);
            return validatedOutput;
        }
        catch (error) {
            (0, utils_1.log)('error', `IPC handler error for ${channel}:`, error);
            throw error;
        }
    };
}
function setupIpcHandlers() {
    // 任务相关处理器
    electron_1.ipcMain.handle('task:getAll', createIpcHandler('task:getAll', async () => {
        return await taskService.getAllTasks();
    }));
    electron_1.ipcMain.handle('task:getHierarchical', createIpcHandler('task:getHierarchical', async () => {
        return await taskService.getHierarchicalTasks();
    }));
    electron_1.ipcMain.handle('task:create', createIpcHandler('task:create', async (input) => {
        return await taskService.createTask(input);
    }));
    electron_1.ipcMain.handle('task:update', createIpcHandler('task:update', async (id, input) => {
        return await taskService.updateTask(id, input);
    }));
    electron_1.ipcMain.handle('task:delete', createIpcHandler('task:delete', async (id) => {
        return await taskService.deleteTask(id);
    }));
    electron_1.ipcMain.handle('task:reorder', createIpcHandler('task:reorder', async (reorderData) => {
        return await taskService.reorderTasks(reorderData);
    }));
    electron_1.ipcMain.handle('task:getStats', createIpcHandler('task:getStats', async () => {
        return await taskService.getTaskStats();
    }));
    // 单个任务软删除和恢复处理器
    electron_1.ipcMain.handle('task:softDelete', createIpcHandler('task:softDelete', async (id) => {
        return await taskService.softDeleteTask(id);
    }));
    electron_1.ipcMain.handle('task:restore', createIpcHandler('task:restore', async (id) => {
        return await taskService.restoreTask(id);
    }));
    electron_1.ipcMain.handle('task:getById', createIpcHandler('task:getById', async (id) => {
        return await taskService.getTaskById(id);
    }));
    // 批量操作处理器
    electron_1.ipcMain.handle('task:batchSoftDelete', createIpcHandler('task:batchSoftDelete', async (taskIds) => {
        return await taskService.batchSoftDeleteTasks(taskIds);
    }));
    electron_1.ipcMain.handle('task:batchRestore', createIpcHandler('task:batchRestore', async (taskIds) => {
        return await taskService.batchRestoreTasks(taskIds);
    }));
    // 自动清理处理器
    electron_1.ipcMain.handle('task:cleanupDeleted', createIpcHandler('task:cleanupDeleted', async (retentionPeriod) => {
        return await taskService.cleanupDeletedTasks(retentionPeriod);
    }));
    electron_1.ipcMain.handle('task:getDeletedStats', createIpcHandler('task:getDeletedStats', async () => {
        return await taskService.getDeletedTasksStats();
    }));
    electron_1.ipcMain.handle('task:getUndoable', createIpcHandler('task:getUndoable', async () => {
        return await taskService.getUndoableTasks();
    }));
    // 应用相关处理器
    electron_1.ipcMain.handle('app:getVersion', createIpcHandler('app:getVersion', async () => {
        return (0, utils_1.getAppVersion)();
    }));
    electron_1.ipcMain.handle('app:quit', createIpcHandler('app:quit', async () => {
        (0, utils_1.quitApp)();
    }));
    // 设置相关处理器
    electron_1.ipcMain.handle('settings:get', createIpcHandler('settings:get', async (key) => {
        return await settingsService.getSetting(key);
    }));
    electron_1.ipcMain.handle('settings:set', createIpcHandler('settings:set', async (key, value) => {
        return await settingsService.setSetting(key, value);
    }));
    // 错误报告处理器
    electron_1.ipcMain.handle('error:report', createIpcHandler('error:report', async (errorReport) => {
        try {
            // 记录错误到主进程日志
            (0, utils_1.log)('error', 'Error reported from renderer:', {
                id: errorReport.id,
                type: errorReport.type,
                message: errorReport.message,
                timestamp: errorReport.timestamp,
                severity: errorReport.severity,
            });
            // 可以在这里添加更多的错误处理逻辑
            // 例如：保存到文件、发送到远程服务等
            return { success: true, id: errorReport.id };
        }
        catch (error) {
            (0, utils_1.log)('error', 'Failed to process error report:', error);
            throw error;
        }
    }));
    (0, utils_1.log)('info', 'IPC handlers setup completed');
}
