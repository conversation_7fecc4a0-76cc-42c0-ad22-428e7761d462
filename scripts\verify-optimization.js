#!/usr/bin/env node

/**
 * 验证数据库优化效果的简单脚本
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const dbPath = path.join(dataDir, 'app.db');

console.log('🔍 验证数据库优化效果...\n');

if (!fs.existsSync(dbPath)) {
  console.error('❌ 数据库文件不存在:', dbPath);
  process.exit(1);
}

const db = new Database(dbPath);

try {
  // 1. 检查数据库基本信息
  console.log('📊 数据库基本信息:');
  const dbSize = fs.statSync(dbPath).size;
  console.log(`   💾 数据库大小: ${(dbSize / 1024 / 1024).toFixed(2)} MB`);
  
  // 2. 检查任务数据
  const taskCount = db.prepare("SELECT COUNT(*) as count FROM tasks").get();
  console.log(`   📝 总任务数量: ${taskCount.count}`);
  
  const activeTasks = db.prepare("SELECT COUNT(*) as count FROM tasks WHERE deleted_at IS NULL").get();
  console.log(`   📝 活跃任务数量: ${activeTasks.count}`);
  
  // 3. 检查现有索引
  console.log('\n📋 当前数据库索引:');
  const indexes = db.prepare(
    "SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%' ORDER BY name"
  ).all();
  
  console.log(`   📊 索引总数: ${indexes.length}`);
  indexes.forEach(index => {
    console.log(`   - ${index.name}`);
  });
  
  // 4. 简单性能测试
  console.log('\n⚡ 简单性能测试:');
  
  const queries = [
    {
      name: '全部任务查询',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL ORDER BY order_index LIMIT 10'
    },
    {
      name: '已完成任务查询',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL AND is_completed = 1 LIMIT 10'
    },
    {
      name: '高优先级任务查询',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL AND priority = 1 LIMIT 10'
    },
    {
      name: '任务统计查询',
      sql: 'SELECT COUNT(*) as total, SUM(is_completed) as completed FROM tasks WHERE deleted_at IS NULL'
    }
  ];
  
  queries.forEach(({ name, sql }) => {
    const start = Date.now();
    const result = db.prepare(sql).all();
    const duration = Date.now() - start;
    
    console.log(`   ${name}: ${duration}ms (${result.length} 条记录)`);
    
    if (duration > 50) {
      console.log(`     ⚠️  查询较慢，可能需要进一步优化`);
    } else {
      console.log(`     ✅ 查询性能良好`);
    }
  });
  
  // 5. 检查查询计划（如果有索引）
  console.log('\n🔍 查询计划分析:');
  const explainResult = db.prepare('EXPLAIN QUERY PLAN SELECT * FROM tasks WHERE is_completed = 0 ORDER BY order_index').all();
  
  const usesIndex = explainResult.some(row => 
    row.detail && row.detail.toLowerCase().includes('index')
  );
  
  if (usesIndex) {
    console.log('   ✅ 查询使用了索引优化');
  } else {
    console.log('   ⚠️  查询可能未使用索引，建议应用索引优化');
  }
  
  // 6. 数据库配置检查
  console.log('\n⚙️  数据库配置:');
  const journalMode = db.pragma('journal_mode');
  const cacheSize = db.pragma('cache_size');
  const pageSize = db.pragma('page_size');
  
  console.log(`   📝 日志模式: ${journalMode}`);
  console.log(`   💾 缓存大小: ${cacheSize} 页`);
  console.log(`   📄 页面大小: ${pageSize} 字节`);
  
  // 7. 优化建议
  console.log('\n💡 优化建议:');
  
  if (indexes.length < 10) {
    console.log('   🔧 建议运行索引优化脚本: node scripts/apply-indexes-simple.js');
  } else {
    console.log('   ✅ 索引配置良好');
  }
  
  if (journalMode !== 'wal') {
    console.log('   🔧 建议启用WAL模式以提高并发性能');
  } else {
    console.log('   ✅ WAL模式已启用');
  }
  
  if (cacheSize < 5000) {
    console.log('   🔧 建议增加缓存大小以提高查询性能');
  } else {
    console.log('   ✅ 缓存配置合理');
  }
  
  console.log('\n🎉 数据库优化验证完成！');
  
} catch (error) {
  console.error('❌ 验证过程中发生错误:', error);
} finally {
  db.close();
}

console.log('\n📚 更多信息:');
console.log('- 查看优化报告: docs/数据库查询优化报告.md');
console.log('- 查看维护指南: docs/数据库维护指南.md');
console.log('- 查看交付清单: docs/项目交付清单.md');
