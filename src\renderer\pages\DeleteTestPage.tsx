import React, { useState, useEffect } from 'react'
import { Button } from '../components/ui/button'
import { deleteDebugger } from '../utils/deleteDebugger'
import { useTasks } from '../hooks/useTasks'
import { Bug, Play, Trash2, RotateCcw, RefreshCw } from 'lucide-react'

export function DeleteTestPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [logs, setLogs] = useState<any[]>([])
  const [selectedTasks, setSelectedTasks] = useState<string[]>([])
  const { data: tasks = [], refetch } = useTasks()

  useEffect(() => {
    // 定期更新日志
    const interval = setInterval(() => {
      setLogs(deleteDebugger.getLogs())
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  const runDiagnosis = async () => {
    setIsRunning(true)
    try {
      await deleteDebugger.diagnoseDeleteIssues()
    } catch (error) {
      console.error('Diagnosis failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const testSingleDelete = async (taskId: string) => {
    try {
      await deleteDebugger.testSoftDelete(taskId)
      await refetch()
    } catch (error) {
      console.error('Single delete test failed:', error)
    }
  }

  const testBatchDelete = async () => {
    if (selectedTasks.length === 0) {
      alert('请先选择要删除的任务')
      return
    }

    try {
      await deleteDebugger.testBatchSoftDelete(selectedTasks)
      await refetch()
      setSelectedTasks([])
    } catch (error) {
      console.error('Batch delete test failed:', error)
    }
  }

  const clearLogs = () => {
    deleteDebugger.clearLogs()
    setLogs([])
  }

  const exportLogs = () => {
    const dataStr = deleteDebugger.exportLogs()
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `delete-debug-logs-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const toggleTaskSelection = (taskId: string) => {
    setSelectedTasks(prev => 
      prev.includes(taskId) 
        ? prev.filter(id => id !== taskId)
        : [...prev, taskId]
    )
  }

  const availableTasks = tasks.filter(task => !task.deletedAt)
  const deletedTasks = tasks.filter(task => task.deletedAt)

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 标题 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-4">
            <Bug className="h-6 w-6 text-red-600" />
            <h1 className="text-2xl font-bold text-gray-900">删除功能测试与调试</h1>
          </div>
          <p className="text-gray-600">
            此页面用于测试和调试任务删除功能，包括单个删除、批量删除和恢复操作。
          </p>
        </div>

        {/* 控制面板 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 诊断工具 */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">诊断工具</h3>
            <div className="space-y-3">
              <Button
                onClick={runDiagnosis}
                disabled={isRunning}
                className="w-full"
              >
                <Play className="h-4 w-4 mr-2" />
                {isRunning ? '运行中...' : '运行完整诊断'}
              </Button>
              
              <Button
                variant="outline"
                onClick={clearLogs}
                className="w-full"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                清除日志
              </Button>
              
              <Button
                variant="outline"
                onClick={exportLogs}
                disabled={logs.length === 0}
                className="w-full"
              >
                导出日志
              </Button>
            </div>
          </div>

          {/* 批量测试 */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">批量操作测试</h3>
            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                已选择 {selectedTasks.length} 个任务
              </div>
              
              <Button
                onClick={testBatchDelete}
                disabled={selectedTasks.length === 0}
                variant="destructive"
                className="w-full"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                测试批量删除
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setSelectedTasks([])}
                disabled={selectedTasks.length === 0}
                className="w-full"
              >
                清除选择
              </Button>
            </div>
          </div>
        </div>

        {/* 任务列表 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 可用任务 */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              可用任务 ({availableTasks.length})
            </h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {availableTasks.map(task => (
                <div
                  key={task.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedTasks.includes(task.id)
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleTaskSelection(task.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {task.content}
                      </p>
                      <p className="text-xs text-gray-500">ID: {task.id}</p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation()
                        testSingleDelete(task.id)
                      }}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
              {availableTasks.length === 0 && (
                <p className="text-gray-500 text-center py-4">没有可用任务</p>
              )}
            </div>
          </div>

          {/* 已删除任务 */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              已删除任务 ({deletedTasks.length})
            </h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {deletedTasks.map(task => (
                <div
                  key={task.id}
                  className="p-3 border border-red-200 bg-red-50 rounded-lg"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {task.content}
                      </p>
                      <p className="text-xs text-gray-500">
                        删除时间: {task.deletedAt ? new Date(task.deletedAt).toLocaleString() : 'N/A'}
                      </p>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={async () => {
                        try {
                          await window.electronAPI.task.restore(task.id)
                          await refetch()
                        } catch (error) {
                          console.error('Restore failed:', error)
                        }
                      }}
                    >
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
              {deletedTasks.length === 0 && (
                <p className="text-gray-500 text-center py-4">没有已删除任务</p>
              )}
            </div>
          </div>
        </div>

        {/* 调试日志 */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            调试日志 ({logs.length})
          </h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {logs.map((log, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${
                  log.success 
                    ? 'border-green-200 bg-green-50' 
                    : 'border-red-200 bg-red-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">
                    {log.success ? '✅' : '❌'} {log.operation}
                  </span>
                  <span className="text-xs text-gray-500">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <div className="text-xs text-gray-600">
                  <p>任务: {log.taskIds.join(', ')}</p>
                  <p>耗时: {log.duration}ms</p>
                  {log.error && <p className="text-red-600">错误: {log.error}</p>}
                </div>
              </div>
            ))}
            {logs.length === 0 && (
              <p className="text-gray-500 text-center py-4">暂无日志</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
