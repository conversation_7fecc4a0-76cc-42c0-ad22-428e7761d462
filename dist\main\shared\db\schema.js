"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.taskTimeLogs = exports.recurringRules = exports.taskDependencies = exports.taskTemplates = exports.taskTagRelations = exports.taskTags = exports.customThemes = exports.settings = exports.tasks = exports.SOFT_DELETE_CONFIG = void 0;
const sqlite_core_1 = require("drizzle-orm/sqlite-core");
const drizzle_orm_1 = require("drizzle-orm");
// 软删除和清理配置
exports.SOFT_DELETE_CONFIG = {
    // 已删除任务的保留期限（毫秒）- 默认30天
    RETENTION_PERIOD: 30 * 24 * 60 * 60 * 1000,
    // 自动清理检查间隔（毫秒）- 默认每天检查一次
    CLEANUP_INTERVAL: 24 * 60 * 60 * 1000,
    // 批量删除的批次大小
    BATCH_SIZE: 100,
    // 撤销操作的时间窗口（毫秒）- 默认30秒
    UNDO_WINDOW: 30 * 1000,
};
// 任务表
exports.tasks = (0, sqlite_core_1.sqliteTable)('tasks', {
    id: (0, sqlite_core_1.text)('id').primaryKey(),
    content: (0, sqlite_core_1.text)('content').notNull(),
    isCompleted: (0, sqlite_core_1.integer)('is_completed', { mode: 'boolean' }).notNull().default(false),
    priority: (0, sqlite_core_1.integer)('priority').notNull().default(2), // 1=高, 2=中, 3=低
    dueDate: (0, sqlite_core_1.integer)('due_date'), // Unix timestamp in milliseconds, nullable
    orderIndex: (0, sqlite_core_1.integer)('order_index').notNull(),
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
    deletedAt: (0, sqlite_core_1.integer)('deleted_at'), // 软删除时间戳，null表示未删除
    // 高级功能字段
    parentTaskId: (0, sqlite_core_1.text)('parent_task_id'), // 父任务ID，用于子任务
    taskType: (0, sqlite_core_1.text)('task_type').notNull().default('task'), // 'task', 'subtask', 'template'
    description: (0, sqlite_core_1.text)('description'), // 详细描述
    estimatedDuration: (0, sqlite_core_1.integer)('estimated_duration'), // 预估时长(分钟)
    actualDuration: (0, sqlite_core_1.integer)('actual_duration'), // 实际时长(分钟)
    progress: (0, sqlite_core_1.integer)('progress').notNull().default(0), // 进度百分比 0-100
}, (table) => ({
    // 单列索引 - 优化常用查询字段
    orderIndexIdx: (0, sqlite_core_1.index)('tasks_order_index_idx').on(table.orderIndex),
    isCompletedIdx: (0, sqlite_core_1.index)('tasks_is_completed_idx').on(table.isCompleted),
    dueDateIdx: (0, sqlite_core_1.index)('tasks_due_date_idx').on(table.dueDate),
    parentTaskIdIdx: (0, sqlite_core_1.index)('tasks_parent_task_id_idx').on(table.parentTaskId),
    createdAtIdx: (0, sqlite_core_1.index)('tasks_created_at_idx').on(table.createdAt),
    deletedAtIdx: (0, sqlite_core_1.index)('tasks_deleted_at_idx').on(table.deletedAt),
    deletedAtIdx: (0, sqlite_core_1.index)('tasks_deleted_at_idx').on(table.deletedAt),
    priorityIdx: (0, sqlite_core_1.index)('tasks_priority_idx').on(table.priority),
    taskTypeIdx: (0, sqlite_core_1.index)('tasks_task_type_idx').on(table.taskType),
    // 复合索引 - 优化常用查询组合
    statusOrderIdx: (0, sqlite_core_1.index)('tasks_status_order_idx').on(table.isCompleted, table.orderIndex),
    deletedStatusIdx: (0, sqlite_core_1.index)('tasks_deleted_status_idx').on(table.deletedAt, table.isCompleted),
    parentStatusIdx: (0, sqlite_core_1.index)('tasks_parent_status_idx').on(table.parentTaskId, table.isCompleted),
    dueDateStatusIdx: (0, sqlite_core_1.index)('tasks_due_date_status_idx').on(table.dueDate, table.isCompleted),
    priorityStatusIdx: (0, sqlite_core_1.index)('tasks_priority_status_idx').on(table.priority, table.isCompleted),
    typeStatusIdx: (0, sqlite_core_1.index)('tasks_type_status_idx').on(table.taskType, table.isCompleted),
    // 时间范围查询优化
    createdDateIdx: (0, sqlite_core_1.index)('tasks_created_date_idx').on(table.createdAt, table.deletedAt),
    dueDateRangeIdx: (0, sqlite_core_1.index)('tasks_due_date_range_idx').on(table.dueDate, table.deletedAt, table.isCompleted),
    // 子任务系统专用索引
    parentTypeIdx: (0, sqlite_core_1.index)('tasks_parent_type_idx').on(table.parentTaskId, table.taskType),
    parentOrderIdx: (0, sqlite_core_1.index)('tasks_parent_order_idx').on(table.parentTaskId, table.orderIndex),
    parentProgressIdx: (0, sqlite_core_1.index)('tasks_parent_progress_idx').on(table.parentTaskId, table.progress),
    hierarchyIdx: (0, sqlite_core_1.index)('tasks_hierarchy_idx').on(table.parentTaskId, table.deletedAt, table.isCompleted, table.orderIndex),
    // 批量操作专用索引
    batchDeleteIdx: (0, sqlite_core_1.index)('tasks_batch_delete_idx').on(table.deletedAt, table.createdAt),
    batchRestoreIdx: (0, sqlite_core_1.index)('tasks_batch_restore_idx').on(table.deletedAt, table.parentTaskId),
    cleanupIdx: (0, sqlite_core_1.index)('tasks_cleanup_idx').on(table.deletedAt, table.taskType),
}));
// 设置表（用于存储应用配置）
exports.settings = (0, sqlite_core_1.sqliteTable)('settings', {
    key: (0, sqlite_core_1.text)('key').primaryKey(),
    value: (0, sqlite_core_1.text)('value').notNull(),
    updatedAt: (0, sqlite_core_1.integer)('updated_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
});
// 自定义主题表
exports.customThemes = (0, sqlite_core_1.sqliteTable)('custom_themes', {
    id: (0, sqlite_core_1.text)('id').primaryKey(),
    name: (0, sqlite_core_1.text)('name').notNull(),
    mode: (0, sqlite_core_1.text)('mode').notNull(), // 'light' | 'dark'
    colors: (0, sqlite_core_1.text)('colors').notNull(), // JSON string of ThemeColors
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
    updatedAt: (0, sqlite_core_1.integer)('updated_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
});
// 任务标签表
exports.taskTags = (0, sqlite_core_1.sqliteTable)('task_tags', {
    id: (0, sqlite_core_1.text)('id').primaryKey(),
    name: (0, sqlite_core_1.text)('name').notNull().unique(),
    color: (0, sqlite_core_1.text)('color').notNull().default('#3b82f6'),
    description: (0, sqlite_core_1.text)('description'),
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
    updatedAt: (0, sqlite_core_1.integer)('updated_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
});
// 任务标签关联表
exports.taskTagRelations = (0, sqlite_core_1.sqliteTable)('task_tag_relations', {
    taskId: (0, sqlite_core_1.text)('task_id').notNull(),
    tagId: (0, sqlite_core_1.text)('tag_id').notNull(),
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
}, (table) => ({
    pk: { columns: [table.taskId, table.tagId] },
}));
// 任务模板表
exports.taskTemplates = (0, sqlite_core_1.sqliteTable)('task_templates', {
    id: (0, sqlite_core_1.text)('id').primaryKey(),
    name: (0, sqlite_core_1.text)('name').notNull(),
    content: (0, sqlite_core_1.text)('content').notNull(),
    description: (0, sqlite_core_1.text)('description'),
    priority: (0, sqlite_core_1.integer)('priority').notNull().default(2),
    estimatedDuration: (0, sqlite_core_1.integer)('estimated_duration'),
    tags: (0, sqlite_core_1.text)('tags'), // JSON array of tag IDs
    isPublic: (0, sqlite_core_1.integer)('is_public', { mode: 'boolean' }).notNull().default(false),
    createdBy: (0, sqlite_core_1.text)('created_by'), // 创建者ID（未来用户系统）
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
    updatedAt: (0, sqlite_core_1.integer)('updated_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
}, (table) => ({
    // 模板查询优化索引
    isPublicIdx: (0, sqlite_core_1.index)('task_templates_is_public_idx').on(table.isPublic),
    createdByIdx: (0, sqlite_core_1.index)('task_templates_created_by_idx').on(table.createdBy),
    priorityIdx: (0, sqlite_core_1.index)('task_templates_priority_idx').on(table.priority),
    nameIdx: (0, sqlite_core_1.index)('task_templates_name_idx').on(table.name),
    // 复合索引
    publicCreatedIdx: (0, sqlite_core_1.index)('task_templates_public_created_idx').on(table.isPublic, table.createdAt),
    createdByPriorityIdx: (0, sqlite_core_1.index)('task_templates_created_by_priority_idx').on(table.createdBy, table.priority),
}));
// 任务依赖关系表
exports.taskDependencies = (0, sqlite_core_1.sqliteTable)('task_dependencies', {
    id: (0, sqlite_core_1.text)('id').primaryKey(),
    dependentTaskId: (0, sqlite_core_1.text)('dependent_task_id').notNull(),
    prerequisiteTaskId: (0, sqlite_core_1.text)('prerequisite_task_id').notNull(),
    dependencyType: (0, sqlite_core_1.text)('dependency_type').notNull().default('finish_to_start'),
    lagTime: (0, sqlite_core_1.integer)('lag_time').notNull().default(0), // 延迟时间(分钟)
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
}, (table) => ({
    unique: { columns: [table.dependentTaskId, table.prerequisiteTaskId] },
    // 依赖关系查询优化索引
    dependentTaskIdx: (0, sqlite_core_1.index)('task_dependencies_dependent_task_idx').on(table.dependentTaskId),
    prerequisiteTaskIdx: (0, sqlite_core_1.index)('task_dependencies_prerequisite_task_idx').on(table.prerequisiteTaskId),
    dependencyTypeIdx: (0, sqlite_core_1.index)('task_dependencies_dependency_type_idx').on(table.dependencyType),
}));
// 重复任务规则表
exports.recurringRules = (0, sqlite_core_1.sqliteTable)('recurring_rules', {
    id: (0, sqlite_core_1.text)('id').primaryKey(),
    name: (0, sqlite_core_1.text)('name').notNull(),
    patternType: (0, sqlite_core_1.text)('pattern_type').notNull(), // 'daily', 'weekly', 'monthly', 'yearly', 'custom'
    patternConfig: (0, sqlite_core_1.text)('pattern_config').notNull(), // JSON配置
    startDate: (0, sqlite_core_1.integer)('start_date').notNull(),
    endDate: (0, sqlite_core_1.integer)('end_date'), // 可选结束日期
    maxOccurrences: (0, sqlite_core_1.integer)('max_occurrences'), // 最大重复次数
    isActive: (0, sqlite_core_1.integer)('is_active', { mode: 'boolean' }).notNull().default(true),
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
    updatedAt: (0, sqlite_core_1.integer)('updated_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
});
// 任务时间记录表
exports.taskTimeLogs = (0, sqlite_core_1.sqliteTable)('task_time_logs', {
    id: (0, sqlite_core_1.text)('id').primaryKey(),
    taskId: (0, sqlite_core_1.text)('task_id').notNull(),
    startTime: (0, sqlite_core_1.integer)('start_time').notNull(),
    endTime: (0, sqlite_core_1.integer)('end_time'),
    duration: (0, sqlite_core_1.integer)('duration'), // 持续时间(分钟)
    description: (0, sqlite_core_1.text)('description'),
    createdAt: (0, sqlite_core_1.integer)('created_at').notNull().default((0, drizzle_orm_1.sql) `(unixepoch() * 1000)`),
});
