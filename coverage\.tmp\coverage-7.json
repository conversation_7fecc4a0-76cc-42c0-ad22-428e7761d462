{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 19}], "isBlockCoverage": true}, {"functionName": "getPropertyValue", "ranges": [{"startOffset": 2430, "endOffset": 2608, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 34}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5133, "endOffset": 5235, "count": 150}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5283, "endOffset": 5385, "count": 50}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5438, "endOffset": 5540, "count": 50}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 25}], "isBlockCoverage": true}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 0}], "isBlockCoverage": false}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 7}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/__tests__/TaskItem.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 40381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 40381, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1277, "endOffset": 19599, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1320, "endOffset": 1374, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1423, "endOffset": 5097, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1490, "endOffset": 1998, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2074, "endOffset": 2559, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2642, "endOffset": 3215, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3296, "endOffset": 3875, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3946, "endOffset": 4539, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4611, "endOffset": 5091, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5149, "endOffset": 9955, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5243, "endOffset": 5940, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6019, "endOffset": 6712, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6795, "endOffset": 7502, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7580, "endOffset": 8309, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8378, "endOffset": 9132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9200, "endOffset": 9949, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10011, "endOffset": 12215, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10090, "endOffset": 10744, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10820, "endOffset": 11483, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11556, "endOffset": 12209, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12268, "endOffset": 14668, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12356, "endOffset": 13106, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13182, "endOffset": 13890, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13962, "endOffset": 14662, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14721, "endOffset": 16855, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14792, "endOffset": 15568, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15633, "endOffset": 16200, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16271, "endOffset": 16849, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16909, "endOffset": 18149, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16989, "endOffset": 17517, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 17593, "endOffset": 18143, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18201, "endOffset": 19595, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18286, "endOffset": 18810, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 18879, "endOffset": 19589, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1566", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/TaskItem.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41675, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41675, "count": 1}], "isBlockCoverage": true}, {"functionName": "TaskItem", "ranges": [{"startOffset": 2535, "endOffset": 20291, "count": 25}, {"startOffset": 2875, "endOffset": 2927, "count": 3}, {"startOffset": 2928, "endOffset": 2932, "count": 22}, {"startOffset": 5838, "endOffset": 5874, "count": 0}, {"startOffset": 5944, "endOffset": 5969, "count": 0}, {"startOffset": 5996, "endOffset": 6011, "count": 1}, {"startOffset": 6031, "endOffset": 6051, "count": 1}, {"startOffset": 6052, "endOffset": 6095, "count": 1}, {"startOffset": 6116, "endOffset": 6188, "count": 0}, {"startOffset": 6209, "endOffset": 6264, "count": 0}, {"startOffset": 7302, "endOffset": 7314, "count": 0}, {"startOffset": 7790, "endOffset": 12149, "count": 0}, {"startOffset": 12384, "endOffset": 12415, "count": 1}, {"startOffset": 13305, "endOffset": 14469, "count": 3}, {"startOffset": 13473, "endOffset": 13489, "count": 1}, {"startOffset": 13490, "endOffset": 13507, "count": 2}, {"startOffset": 13559, "endOffset": 13874, "count": 1}, {"startOffset": 13875, "endOffset": 14187, "count": 2}, {"startOffset": 15977, "endOffset": 17337, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleToggleComplete", "ranges": [{"startOffset": 3962, "endOffset": 4162, "count": 1}, {"startOffset": 3995, "endOffset": 4038, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleStartEdit", "ranges": [{"startOffset": 4190, "endOffset": 4401, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleSaveEdit", "ranges": [{"startOffset": 4428, "endOffset": 4740, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCancelEdit", "ranges": [{"startOffset": 4769, "endOffset": 4977, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleDelete", "ranges": [{"startOffset": 5002, "endOffset": 5044, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleConfirmDelete", "ranges": [{"startOffset": 5076, "endOffset": 5407, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleKeyDown", "ranges": [{"startOffset": 5433, "endOffset": 5567, "count": 0}], "isBlockCoverage": false}, {"functionName": "onChange", "ranges": [{"startOffset": 8051, "endOffset": 8088, "count": 0}], "isBlockCoverage": false}, {"functionName": "onChange", "ranges": [{"startOffset": 11381, "endOffset": 11418, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 20388, "endOffset": 20412, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "1586", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/checkbox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 826, "endOffset": 2217, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2380, "endOffset": 2404, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "1605", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 550, "count": 203}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 641, "endOffset": 659, "count": 203}], "isBlockCoverage": true}, {"functionName": "formatDate", "ranges": [{"startOffset": 663, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1280, "endOffset": 1306, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDueDate", "ranges": [{"startOffset": 1310, "endOffset": 1882, "count": 3}, {"startOffset": 1557, "endOffset": 1605, "count": 1}, {"startOffset": 1605, "endOffset": 1880, "count": 2}, {"startOffset": 1633, "endOffset": 1657, "count": 0}, {"startOffset": 1709, "endOffset": 1880, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1984, "endOffset": 2013, "count": 3}], "isBlockCoverage": true}, {"functionName": "isTaskOverdue", "ranges": [{"startOffset": 2017, "endOffset": 2113, "count": 25}, {"startOffset": 2067, "endOffset": 2080, "count": 22}, {"startOffset": 2080, "endOffset": 2112, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2244, "count": 25}], "isBlockCoverage": true}, {"functionName": "debounce", "ranges": [{"startOffset": 2248, "endOffset": 2454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2551, "endOffset": 2575, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 2579, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2908, "endOffset": 2932, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateOrderIndex", "ranges": [{"startOffset": 2936, "endOffset": 3207, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3314, "endOffset": 3348, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1608", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/button.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1951, "endOffset": 2495, "count": 50}, {"startOffset": 2044, "endOffset": 2072, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2625, "endOffset": 2647, "count": 100}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2753, "endOffset": 2783, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1610", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/input.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 569, "endOffset": 1384, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1511, "endOffset": 1532, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1611", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/select.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 996, "endOffset": 2293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2429, "endOffset": 3149, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3301, "endOffset": 4025, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4172, "endOffset": 6521, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6648, "endOffset": 7047, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7169, "endOffset": 8924, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9049, "endOffset": 9438, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9611, "endOffset": 9633, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9736, "endOffset": 9763, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9866, "endOffset": 9893, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9998, "endOffset": 10027, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10132, "endOffset": 10161, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10264, "endOffset": 10291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10393, "endOffset": 10419, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10526, "endOffset": 10557, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10669, "endOffset": 10705, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10819, "endOffset": 10857, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1669", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/delete-confirmation-dialog.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21254, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21254, "count": 1}], "isBlockCoverage": true}, {"functionName": "DeleteConfirmationDialog", "ranges": [{"startOffset": 818, "endOffset": 5658, "count": 25}, {"startOffset": 1194, "endOffset": 1217, "count": 0}, {"startOffset": 4829, "endOffset": 4839, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleConfirm", "ranges": [{"startOffset": 1007, "endOffset": 1060, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCancel", "ranges": [{"startOffset": 1085, "endOffset": 1121, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5771, "endOffset": 5811, "count": 25}], "isBlockCoverage": true}, {"functionName": "BatchDeleteConfirmationDialog", "ranges": [{"startOffset": 5815, "endOffset": 11144, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11262, "endOffset": 11307, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1670", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/dialog.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1021, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1680, "endOffset": 4260, "count": 25}], "isBlockCoverage": true}, {"functionName": "DialogHeader", "ranges": [{"startOffset": 4355, "endOffset": 4751, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>er", "ranges": [{"startOffset": 4817, "endOffset": 5224, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5322, "endOffset": 5749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5878, "endOffset": 6274, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6451, "endOffset": 6473, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6577, "endOffset": 6605, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6710, "endOffset": 6739, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6842, "endOffset": 6869, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6974, "endOffset": 7003, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7108, "endOffset": 7137, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7241, "endOffset": 7269, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7373, "endOffset": 7401, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7504, "endOffset": 7531, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 7640, "endOffset": 7673, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "1672", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/toast.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToastProvider", "ranges": [{"startOffset": 857, "endOffset": 1996, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1015, "endOffset": 1317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1380, "endOffset": 1451, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2098, "endOffset": 2127, "count": 25}], "isBlockCoverage": true}, {"functionName": "useToast", "ranges": [{"startOffset": 2131, "endOffset": 2329, "count": 50}, {"startOffset": 2235, "endOffset": 2309, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2426, "endOffset": 2450, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastContainer", "ranges": [{"startOffset": 2454, "endOffset": 3062, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2670, "endOffset": 2902, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastItem", "ranges": [{"startOffset": 3063, "endOffset": 7280, "count": 0}], "isBlockCoverage": false}, {"functionName": "useToastActions", "ranges": [{"startOffset": 7281, "endOffset": 8394, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7401, "endOffset": 7501, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7570, "endOffset": 7652, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7723, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7875, "endOffset": 7956, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8033, "endOffset": 8277, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8498, "endOffset": 8529, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "1673", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/hooks/useTasks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "useTasks", "ranges": [{"startOffset": 612, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 884, "endOffset": 908, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskStats", "ranges": [{"startOffset": 912, "endOffset": 1097, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1198, "endOffset": 1226, "count": 0}], "isBlockCoverage": false}, {"functionName": "useCreateTask", "ranges": [{"startOffset": 1230, "endOffset": 1870, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1972, "endOffset": 2001, "count": 0}], "isBlockCoverage": false}, {"functionName": "useUpdateTask", "ranges": [{"startOffset": 2005, "endOffset": 2734, "count": 25}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 2155, "endOffset": 2221, "count": 1}], "isBlockCoverage": true}, {"functionName": "onSuccess", "ranges": [{"startOffset": 2238, "endOffset": 2607, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2328, "endOffset": 2504, "count": 1}, {"startOffset": 2388, "endOffset": 2503, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2428, "endOffset": 2485, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 2622, "endOffset": 2726, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2836, "endOffset": 2865, "count": 25}], "isBlockCoverage": true}, {"functionName": "useDeleteTask", "ranges": [{"startOffset": 2869, "endOffset": 3527, "count": 25}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 3019, "endOffset": 3067, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSuccess", "ranges": [{"startOffset": 3084, "endOffset": 3400, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 3415, "endOffset": 3519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3629, "endOffset": 3658, "count": 25}], "isBlockCoverage": true}, {"functionName": "useReorderTasks", "ranges": [{"startOffset": 3662, "endOffset": 4873, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4977, "endOffset": 5008, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1718", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 309, "endOffset": 328, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAll", "ranges": [{"startOffset": 360, "endOffset": 383, "count": 0}], "isBlockCoverage": false}, {"functionName": "create", "ranges": [{"startOffset": 395, "endOffset": 428, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 440, "endOffset": 481, "count": 1}], "isBlockCoverage": true}, {"functionName": "delete", "ranges": [{"startOffset": 493, "endOffset": 520, "count": 0}], "isBlockCoverage": false}, {"functionName": "reorder", "ranges": [{"startOffset": 533, "endOffset": 565, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStats", "ranges": [{"startOffset": 579, "endOffset": 604, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 703, "endOffset": 726, "count": 1}], "isBlockCoverage": true}, {"functionName": "getVersion", "ranges": [{"startOffset": 761, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "quit", "ranges": [{"startOffset": 797, "endOffset": 817, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 915, "endOffset": 937, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 970, "endOffset": 1000, "count": 25}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 1009, "endOffset": 1053, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1156, "endOffset": 1183, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "1719", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/queryClient.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "retry<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 597, "endOffset": 653, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 964, "endOffset": 991, "count": 0}], "isBlockCoverage": false}, {"functionName": "settings", "ranges": [{"startOffset": 1081, "endOffset": 1128, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1264, "endOffset": 1290, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "1720", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/stores/uiStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10606, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10606, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.create.__vite_ssr_import_1__.devtools.name", "ranges": [{"startOffset": 535, "endOffset": 1411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setT<PERSON><PERSON><PERSON>er", "ranges": [{"startOffset": 613, "endOffset": 652, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSidebarOpen", "ranges": [{"startOffset": 716, "endOffset": 752, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleSidebar", "ranges": [{"startOffset": 775, "endOffset": 834, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTheme", "ranges": [{"startOffset": 887, "endOffset": 912, "count": 0}], "isBlockCoverage": false}, {"functionName": "setIsAddingTask", "ranges": [{"startOffset": 981, "endOffset": 1022, "count": 0}], "isBlockCoverage": false}, {"functionName": "setEditingTaskId", "ranges": [{"startOffset": 1094, "endOffset": 1128, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSearch<PERSON>uery", "ranges": [{"startOffset": 1190, "endOffset": 1228, "count": 0}], "isBlockCoverage": false}, {"functionName": "setSortBy", "ranges": [{"startOffset": 1285, "endOffset": 1312, "count": 0}], "isBlockCoverage": false}, {"functionName": "setViewMode", "ranges": [{"startOffset": 1371, "endOffset": 1404, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1553, "endOffset": 1579, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskFilter", "ranges": [{"startOffset": 1605, "endOffset": 1650, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1753, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSetTaskFilter", "ranges": [{"startOffset": 1811, "endOffset": 1859, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1965, "endOffset": 1997, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSidebar", "ranges": [{"startOffset": 2020, "endOffset": 2146, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2246, "endOffset": 2272, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTheme", "ranges": [{"startOffset": 2293, "endOffset": 2376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2474, "endOffset": 2498, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskInput", "ranges": [{"startOffset": 2523, "endOffset": 2626, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2728, "endOffset": 2756, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskEditing", "ranges": [{"startOffset": 2783, "endOffset": 2890, "count": 25}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2800, "endOffset": 2889, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2994, "endOffset": 3024, "count": 25}], "isBlockCoverage": true}, {"functionName": "useSearch", "ranges": [{"startOffset": 3046, "endOffset": 3141, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3240, "endOffset": 3265, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSort", "ranges": [{"startOffset": 3285, "endOffset": 3372, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3469, "endOffset": 3492, "count": 0}], "isBlockCoverage": false}, {"functionName": "useViewMode", "ranges": [{"startOffset": 3516, "endOffset": 3603, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3704, "endOffset": 3731, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1730", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/stores/selectionStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 367, "endOffset": 1402, "count": 1}], "isBlockCoverage": true}, {"functionName": "toggleTaskSelection", "ranges": [{"startOffset": 479, "endOffset": 759, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectAllTasks", "ranges": [{"startOffset": 779, "endOffset": 845, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearSelection", "ranges": [{"startOffset": 865, "endOffset": 933, "count": 0}], "isBlockCoverage": false}, {"functionName": "enterSelectionMode", "ranges": [{"startOffset": 957, "endOffset": 1004, "count": 0}], "isBlockCoverage": false}, {"functionName": "exitSelectionMode", "ranges": [{"startOffset": 1027, "endOffset": 1135, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleSelectionMode", "ranges": [{"startOffset": 1160, "endOffset": 1399, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1510, "endOffset": 1543, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSelection", "ranges": [{"startOffset": 1547, "endOffset": 1854, "count": 25}], "isBlockCoverage": true}, {"functionName": "isTaskSelected", "ranges": [{"startOffset": 1788, "endOffset": 1833, "count": 25}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1955, "endOffset": 1983, "count": 25}], "isBlockCoverage": true}]}, {"scriptId": "1731", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/test-utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTestQueryClient", "ranges": [{"startOffset": 1029, "endOffset": 1433, "count": 50}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1543, "endOffset": 1580, "count": 0}], "isBlockCoverage": false}, {"functionName": "TestWrapper", "ranges": [{"startOffset": 1584, "endOffset": 2468, "count": 26}, {"startOffset": 1663, "endOffset": 1689, "count": 25}], "isBlockCoverage": true}, {"functionName": "renderWithProviders", "ranges": [{"startOffset": 2469, "endOffset": 3001, "count": 25}], "isBlockCoverage": true}, {"functionName": "Wrapper", "ranges": [{"startOffset": 2589, "endOffset": 2845, "count": 40}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3109, "endOffset": 3144, "count": 25}], "isBlockCoverage": true}, {"functionName": "waitF<PERSON><PERSON><PERSON><PERSON><PERSON>oSettle", "ranges": [{"startOffset": 3148, "endOffset": 3624, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3733, "endOffset": 3769, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 3802, "endOffset": 3971, "count": 0}], "isBlockCoverage": false}, {"functionName": "type", "ranges": [{"startOffset": 3981, "endOffset": 4186, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 4197, "endOffset": 4394, "count": 0}], "isBlockCoverage": false}, {"functionName": "keyDown", "ranges": [{"startOffset": 4407, "endOffset": 4592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4693, "endOffset": 4718, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeInDocument", "ranges": [{"startOffset": 4761, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveTextContent", "ranges": [{"startOffset": 4843, "endOffset": 4912, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveValue", "ranges": [{"startOffset": 4929, "endOffset": 4994, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeDisabled", "ranges": [{"startOffset": 5012, "endOffset": 5066, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeEnabled", "ranges": [{"startOffset": 5083, "endOffset": 5136, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5238, "endOffset": 5264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5639, "endOffset": 5679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5778, "endOffset": 5813, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1732", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/contexts/ThemeContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "useSystemTheme", "ranges": [{"startOffset": 885, "endOffset": 1405, "count": 35}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1008, "endOffset": 1375, "count": 25}, {"startOffset": 1221, "endOffset": 1229, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleChange", "ranges": [{"startOffset": 1115, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1310, "endOffset": 1370, "count": 25}], "isBlockCoverage": true}, {"functionName": "applyThemeToDOM", "ranges": [{"startOffset": 1431, "endOffset": 1994, "count": 10}, {"startOffset": 1812, "endOffset": 1849, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1634, "endOffset": 1780, "count": 190}], "isBlockCoverage": true}, {"functionName": "ThemeProvider", "ranges": [{"startOffset": 2018, "endOffset": 7343, "count": 36}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2483, "endOffset": 2652, "count": 35}, {"startOffset": 2595, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2741, "endOffset": 3140, "count": 35}, {"startOffset": 2997, "endOffset": 3082, "count": 0}, {"startOffset": 3083, "endOffset": 3099, "count": 0}, {"startOffset": 3106, "endOffset": 3139, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3030, "endOffset": 3061, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3287, "endOffset": 3358, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3445, "endOffset": 3710, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3769, "endOffset": 3866, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3944, "endOffset": 4214, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4313, "endOffset": 4419, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4499, "endOffset": 4708, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4797, "endOffset": 4877, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4946, "endOffset": 5022, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5063, "endOffset": 5597, "count": 25}], "isBlockCoverage": true}, {"functionName": "loadConfig", "ranges": [{"startOffset": 5094, "endOffset": 5574, "count": 25}, {"startOffset": 5262, "endOffset": 5364, "count": 0}, {"startOffset": 5373, "endOffset": 5521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5638, "endOffset": 5709, "count": 35}, {"startOffset": 5666, "endOffset": 5705, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5785, "endOffset": 6731, "count": 35}, {"startOffset": 5835, "endOffset": 6730, "count": 0}], "isBlockCoverage": true}, {"functionName": "checkAutoSwitch", "ranges": [{"startOffset": 5864, "endOffset": 6605, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6697, "endOffset": 6726, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7446, "endOffset": 7475, "count": 25}], "isBlockCoverage": true}, {"functionName": "useTheme", "ranges": [{"startOffset": 7496, "endOffset": 7690, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7788, "endOffset": 7812, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7917, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1733", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1399, "endOffset": 1432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1854, "endOffset": 1881, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2496, "endOffset": 2529, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7696, "endOffset": 7727, "count": 35}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8083, "endOffset": 8119, "count": 35}], "isBlockCoverage": true}, {"functionName": "getThemeById", "ranges": [{"startOffset": 8144, "endOffset": 8214, "count": 35}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8184, "endOffset": 8210, "count": 35}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8316, "endOffset": 8344, "count": 35}], "isBlockCoverage": true}, {"functionName": "getThemesByMode", "ranges": [{"startOffset": 8372, "endOffset": 8450, "count": 35}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8416, "endOffset": 8446, "count": 210}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8555, "endOffset": 8586, "count": 35}], "isBlockCoverage": true}, {"functionName": "isValidThemeId", "ranges": [{"startOffset": 8613, "endOffset": 8683, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8787, "endOffset": 8817, "count": 0}], "isBlockCoverage": false}]}]}