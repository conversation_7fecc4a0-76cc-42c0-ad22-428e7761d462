#!/usr/bin/env node

/**
 * LinganApp 功能正确性测试脚本
 * 验证数据库优化后功能的正确性
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');
const { ulid } = require('ulid');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const testDbPath = path.join(dataDir, 'test_functionality.db');

console.log('🧪 开始功能正确性测试...');

// 测试结果
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// 测试断言函数
function assert(condition, message) {
  if (condition) {
    console.log(`✅ ${message}`);
    testResults.passed++;
    testResults.tests.push({ name: message, status: 'PASSED' });
  } else {
    console.log(`❌ ${message}`);
    testResults.failed++;
    testResults.tests.push({ name: message, status: 'FAILED' });
  }
}

// 创建测试数据库
function createTestDatabase() {
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  const db = new Database(testDbPath);
  
  // 应用优化设置
  db.pragma('journal_mode = WAL');
  db.pragma('synchronous = NORMAL');
  db.pragma('cache_size = 10000');
  db.pragma('foreign_keys = ON');
  db.pragma('temp_store = MEMORY');
  db.pragma('mmap_size = 268435456');
  db.pragma('page_size = 4096');
  db.pragma('auto_vacuum = INCREMENTAL');

  // 创建表结构
  db.exec(`
    CREATE TABLE tasks (
      id TEXT PRIMARY KEY,
      content TEXT NOT NULL,
      is_completed INTEGER DEFAULT 0 NOT NULL,
      priority INTEGER DEFAULT 2 NOT NULL,
      due_date INTEGER,
      order_index INTEGER NOT NULL,
      created_at INTEGER DEFAULT (unixepoch() * 1000) NOT NULL,
      deleted_at INTEGER,
      parent_task_id TEXT,
      task_type TEXT DEFAULT 'task' NOT NULL,
      description TEXT,
      estimated_duration INTEGER,
      actual_duration INTEGER,
      progress INTEGER DEFAULT 0 NOT NULL
    );
  `);

  // 应用索引
  const indexes = [
    'CREATE INDEX tasks_order_index_idx ON tasks(order_index)',
    'CREATE INDEX tasks_is_completed_idx ON tasks(is_completed)',
    'CREATE INDEX tasks_due_date_idx ON tasks(due_date)',
    'CREATE INDEX tasks_parent_task_id_idx ON tasks(parent_task_id)',
    'CREATE INDEX tasks_created_at_idx ON tasks(created_at)',
    'CREATE INDEX tasks_deleted_at_idx ON tasks(deleted_at)',
    'CREATE INDEX tasks_priority_idx ON tasks(priority)',
    'CREATE INDEX tasks_task_type_idx ON tasks(task_type)',
    'CREATE INDEX tasks_status_order_idx ON tasks(is_completed, order_index)',
    'CREATE INDEX tasks_deleted_status_idx ON tasks(deleted_at, is_completed)',
    'CREATE INDEX tasks_parent_status_idx ON tasks(parent_task_id, is_completed)',
    'CREATE INDEX tasks_due_date_status_idx ON tasks(due_date, is_completed)',
    'CREATE INDEX tasks_priority_status_idx ON tasks(priority, is_completed)',
    'CREATE INDEX tasks_created_date_idx ON tasks(created_at, deleted_at)',
  ];

  indexes.forEach(indexSQL => {
    db.exec(indexSQL);
  });

  db.exec('ANALYZE tasks');

  return db;
}

// 测试基础CRUD操作
function testBasicCRUD(db) {
  console.log('\n📝 测试基础CRUD操作...');

  const taskId = ulid();
  const now = Date.now();

  // 测试创建任务
  const insertStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at)
    VALUES (?, ?, ?, ?, ?, ?)
  `);
  
  const insertResult = insertStmt.run(taskId, '测试任务', 0, 2, 1000, now);
  assert(insertResult.changes === 1, '创建任务成功');

  // 测试读取任务
  const selectStmt = db.prepare('SELECT * FROM tasks WHERE id = ?');
  const task = selectStmt.get(taskId);
  assert(task && task.content === '测试任务', '读取任务成功');
  assert(task.is_completed === 0, '任务状态正确');
  assert(task.priority === 2, '任务优先级正确');

  // 测试更新任务
  const updateStmt = db.prepare('UPDATE tasks SET is_completed = ?, content = ? WHERE id = ?');
  const updateResult = updateStmt.run(1, '更新后的任务', taskId);
  assert(updateResult.changes === 1, '更新任务成功');

  // 验证更新结果
  const updatedTask = selectStmt.get(taskId);
  assert(updatedTask.is_completed === 1, '任务状态更新正确');
  assert(updatedTask.content === '更新后的任务', '任务内容更新正确');

  // 测试软删除
  const softDeleteStmt = db.prepare('UPDATE tasks SET deleted_at = ? WHERE id = ?');
  const deleteResult = softDeleteStmt.run(Date.now(), taskId);
  assert(deleteResult.changes === 1, '软删除任务成功');

  // 验证软删除结果
  const deletedTask = selectStmt.get(taskId);
  assert(deletedTask.deleted_at !== null, '软删除标记正确');

  // 测试硬删除
  const hardDeleteStmt = db.prepare('DELETE FROM tasks WHERE id = ?');
  const hardDeleteResult = hardDeleteStmt.run(taskId);
  assert(hardDeleteResult.changes === 1, '硬删除任务成功');

  // 验证硬删除结果
  const finalTask = selectStmt.get(taskId);
  assert(finalTask === undefined, '硬删除完成');
}

// 测试索引效果
function testIndexEffectiveness(db) {
  console.log('\n📊 测试索引效果...');

  // 插入测试数据
  const insertStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, due_date, parent_task_id)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const now = Date.now();
  const testData = [];
  
  for (let i = 0; i < 1000; i++) {
    const taskId = ulid();
    testData.push({
      id: taskId,
      content: `测试任务 ${i}`,
      isCompleted: i % 3 === 0 ? 1 : 0,
      priority: (i % 3) + 1,
      orderIndex: i * 1000,
      createdAt: now - i * 1000,
      dueDate: i % 2 === 0 ? now + i * 1000 : null,
      parentTaskId: i > 0 && i % 10 === 0 ? testData[i - 1].id : null
    });
  }

  // 批量插入
  const transaction = db.transaction((tasks) => {
    for (const task of tasks) {
      insertStmt.run(
        task.id,
        task.content,
        task.isCompleted,
        task.priority,
        task.orderIndex,
        task.createdAt,
        task.dueDate,
        task.parentTaskId
      );
    }
  });

  transaction(testData);
  assert(true, '批量插入1000条测试数据成功');

  // 测试各种查询
  const queries = [
    {
      name: '按order_index排序查询',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL ORDER BY order_index LIMIT 10'
    },
    {
      name: '按完成状态过滤',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL AND is_completed = 1 LIMIT 10'
    },
    {
      name: '按优先级过滤',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL AND priority = 1 LIMIT 10'
    },
    {
      name: '复合条件查询',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL AND is_completed = 0 AND priority = 1 LIMIT 10'
    },
    {
      name: '截止日期范围查询',
      sql: `SELECT * FROM tasks WHERE deleted_at IS NULL AND due_date BETWEEN ${now} AND ${now + 86400000} LIMIT 10`
    },
    {
      name: '父任务查询',
      sql: 'SELECT * FROM tasks WHERE deleted_at IS NULL AND parent_task_id IS NOT NULL LIMIT 10'
    }
  ];

  queries.forEach(({ name, sql }) => {
    const startTime = Date.now();
    const result = db.prepare(sql).all();
    const duration = Date.now() - startTime;
    
    assert(result.length > 0, `${name} - 返回结果`);
    assert(duration < 50, `${name} - 查询性能良好 (${duration}ms < 50ms)`);
  });
}

// 测试聚合查询
function testAggregateQueries(db) {
  console.log('\n📈 测试聚合查询...');

  // 统计查询
  const statsQuery = `
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed,
      SUM(CASE WHEN is_completed = 0 THEN 1 ELSE 0 END) as pending,
      SUM(CASE WHEN is_completed = 0 AND due_date IS NOT NULL AND due_date < unixepoch() * 1000 THEN 1 ELSE 0 END) as overdue
    FROM tasks WHERE deleted_at IS NULL
  `;

  const stats = db.prepare(statsQuery).get();
  assert(stats.total > 0, '统计查询返回总数');
  assert(stats.completed >= 0, '统计查询返回完成数');
  assert(stats.pending >= 0, '统计查询返回待完成数');
  assert(stats.overdue >= 0, '统计查询返回逾期数');
  assert(stats.total === stats.completed + stats.pending, '统计数据一致性检查');

  // 按优先级分组统计
  const priorityStats = db.prepare(`
    SELECT priority, COUNT(*) as count 
    FROM tasks 
    WHERE deleted_at IS NULL 
    GROUP BY priority 
    ORDER BY priority
  `).all();

  assert(priorityStats.length > 0, '按优先级分组统计成功');
  assert(priorityStats.every(stat => stat.count > 0), '每个优先级都有任务');

  // 按完成状态分组统计
  const statusStats = db.prepare(`
    SELECT is_completed, COUNT(*) as count 
    FROM tasks 
    WHERE deleted_at IS NULL 
    GROUP BY is_completed
  `).all();

  assert(statusStats.length > 0, '按完成状态分组统计成功');
}

// 测试分页查询
function testPaginationQueries(db) {
  console.log('\n📄 测试分页查询...');

  const pageSize = 50;
  const totalCount = db.prepare('SELECT COUNT(*) as count FROM tasks WHERE deleted_at IS NULL').get().count;
  const totalPages = Math.ceil(totalCount / pageSize);

  assert(totalCount > 0, '有数据可供分页测试');

  // 测试第一页
  const firstPage = db.prepare(`
    SELECT * FROM tasks 
    WHERE deleted_at IS NULL 
    ORDER BY order_index 
    LIMIT ? OFFSET ?
  `).all(pageSize, 0);

  assert(firstPage.length > 0, '第一页返回数据');
  assert(firstPage.length <= pageSize, '第一页数据量不超过页面大小');

  // 测试中间页（如果有的话）
  if (totalPages > 2) {
    const middlePage = db.prepare(`
      SELECT * FROM tasks 
      WHERE deleted_at IS NULL 
      ORDER BY order_index 
      LIMIT ? OFFSET ?
    `).all(pageSize, pageSize);

    assert(middlePage.length > 0, '中间页返回数据');
    assert(middlePage.length <= pageSize, '中间页数据量不超过页面大小');
    
    // 验证分页数据不重复
    const firstPageIds = new Set(firstPage.map(task => task.id));
    const middlePageIds = new Set(middlePage.map(task => task.id));
    const intersection = [...firstPageIds].filter(id => middlePageIds.has(id));
    assert(intersection.length === 0, '分页数据无重复');
  }

  // 测试最后一页
  const lastPageOffset = (totalPages - 1) * pageSize;
  const lastPage = db.prepare(`
    SELECT * FROM tasks 
    WHERE deleted_at IS NULL 
    ORDER BY order_index 
    LIMIT ? OFFSET ?
  `).all(pageSize, lastPageOffset);

  assert(lastPage.length > 0, '最后一页返回数据');
  assert(lastPage.length <= pageSize, '最后一页数据量不超过页面大小');
}

// 测试事务操作
function testTransactions(db) {
  console.log('\n🔄 测试事务操作...');

  const taskIds = [ulid(), ulid(), ulid()];
  const now = Date.now();

  // 测试成功事务
  const successTransaction = db.transaction(() => {
    const insertStmt = db.prepare(`
      INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    taskIds.forEach((id, index) => {
      insertStmt.run(id, `事务任务 ${index + 1}`, 0, 2, (index + 1) * 1000, now);
    });
  });

  try {
    successTransaction();
    
    // 验证事务成功
    const insertedTasks = db.prepare(`
      SELECT COUNT(*) as count FROM tasks WHERE id IN (${taskIds.map(() => '?').join(',')})
    `).get(...taskIds);
    
    assert(insertedTasks.count === 3, '成功事务 - 所有任务都已插入');
  } catch (error) {
    assert(false, `成功事务执行失败: ${error.message}`);
  }

  // 测试失败事务回滚
  const failTransaction = db.transaction(() => {
    const insertStmt = db.prepare(`
      INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    // 插入一个正常任务
    insertStmt.run(ulid(), '正常任务', 0, 2, 10000, now);
    
    // 尝试插入一个会失败的任务（重复ID）
    insertStmt.run(taskIds[0], '重复ID任务', 0, 2, 11000, now);
  });

  try {
    failTransaction();
    assert(false, '失败事务应该抛出异常');
  } catch (error) {
    // 验证事务回滚
    const taskCount = db.prepare('SELECT COUNT(*) as count FROM tasks WHERE content = ?').get('正常任务');
    assert(taskCount.count === 0, '失败事务 - 事务已回滚');
  }
}

// 主测试函数
function runFunctionalityTests() {
  try {
    const db = createTestDatabase();
    
    console.log('🧪 开始功能正确性测试...\n');
    
    // 运行各项测试
    testBasicCRUD(db);
    testIndexEffectiveness(db);
    testAggregateQueries(db);
    testPaginationQueries(db);
    testTransactions(db);
    
    // 关闭数据库
    db.close();
    
    // 生成测试报告
    generateTestReport();
    
  } catch (error) {
    console.error('❌ 功能测试失败:', error);
    process.exit(1);
  }
}

// 生成测试报告
function generateTestReport() {
  console.log('\n📊 === 功能测试报告 ===');
  console.log(`✅ 通过测试: ${testResults.passed}`);
  console.log(`❌ 失败测试: ${testResults.failed}`);
  console.log(`📊 总测试数: ${testResults.passed + testResults.failed}`);
  console.log(`🎯 成功率: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.tests
      .filter(test => test.status === 'FAILED')
      .forEach(test => console.log(`   - ${test.name}`));
  }
  
  // 保存测试报告
  const reportPath = path.join(process.cwd(), 'functionality-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  
  // 清理测试数据库
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
    console.log('\n🧹 测试数据库已清理');
  }
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有功能测试通过！数据库优化未影响功能正确性。');
  } else {
    console.log('\n⚠️  部分功能测试失败，请检查优化实现。');
  }
}

// 运行测试
runFunctionalityTests();
