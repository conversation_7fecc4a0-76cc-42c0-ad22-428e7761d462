/**
 * 错误测试组件
 * 用于测试 ErrorBoundary 功能的组件
 * 仅在开发环境中使用
 */

import React, { useState } from 'react'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Alert<PERSON>riangle, Bug } from 'lucide-react'

interface ErrorTestComponentProps {
  onClose?: () => void
}

export function ErrorTestComponent({ onClose }: ErrorTestComponentProps) {
  const [shouldThrow, setShouldThrow] = useState(false)

  // 这个组件会在 shouldThrow 为 true 时抛出错误
  if (shouldThrow) {
    throw new Error('这是一个测试错误，用于验证 ErrorBoundary 功能')
  }

  const triggerJavaScriptError = () => {
    // 触发 JavaScript 错误
    setTimeout(() => {
      throw new Error('这是一个异步 JavaScript 错误')
    }, 100)
  }

  const triggerPromiseRejection = () => {
    // 触发未处理的 Promise 拒绝
    Promise.reject(new Error('这是一个未处理的 Promise 拒绝'))
  }

  const triggerNetworkError = () => {
    // 触发网络错误
    fetch('/non-existent-endpoint')
      .catch(error => {
        console.error('Network error:', error)
      })
  }

  const triggerReactError = () => {
    // 触发 React 错误（会被 ErrorBoundary 捕获）
    setShouldThrow(true)
  }

  const triggerTypeError = () => {
    // 触发类型错误
    const obj: any = null
    console.log(obj.nonExistentProperty.anotherProperty)
  }

  const triggerReferenceError = () => {
    // 触发引用错误
    // @ts-ignore
    console.log(nonExistentVariable)
  }

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <Card className="mb-4 border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800 dark:text-orange-200">
          <Bug className="h-5 w-5" />
          错误测试工具
        </CardTitle>
        <CardDescription className="text-orange-700 dark:text-orange-300">
          开发环境专用：测试各种错误类型和 ErrorBoundary 功能
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <div className="flex items-center gap-2 text-sm text-orange-700 dark:text-orange-300">
          <AlertTriangle className="h-4 w-4" />
          <span>点击按钮测试不同类型的错误处理</span>
        </div>
        
        <div className="grid grid-cols-2 gap-2">
          <Button
            onClick={triggerReactError}
            variant="destructive"
            size="sm"
            className="text-xs"
          >
            React 错误
          </Button>
          
          <Button
            onClick={triggerJavaScriptError}
            variant="destructive"
            size="sm"
            className="text-xs"
          >
            JS 错误
          </Button>
          
          <Button
            onClick={triggerPromiseRejection}
            variant="destructive"
            size="sm"
            className="text-xs"
          >
            Promise 拒绝
          </Button>
          
          <Button
            onClick={triggerNetworkError}
            variant="destructive"
            size="sm"
            className="text-xs"
          >
            网络错误
          </Button>
          
          <Button
            onClick={triggerTypeError}
            variant="destructive"
            size="sm"
            className="text-xs"
          >
            类型错误
          </Button>
          
          <Button
            onClick={triggerReferenceError}
            variant="destructive"
            size="sm"
            className="text-xs"
          >
            引用错误
          </Button>
        </div>
        
        {onClose && (
          <Button
            onClick={onClose}
            variant="outline"
            size="sm"
            className="w-full mt-3"
          >
            关闭测试工具
          </Button>
        )}
        
        <div className="text-xs text-orange-600 dark:text-orange-400 mt-2">
          <p>• React 错误会被 ErrorBoundary 捕获并显示错误页面</p>
          <p>• 其他错误会被全局错误处理器捕获并记录</p>
          <p>• 检查浏览器控制台查看错误日志</p>
        </div>
      </CardContent>
    </Card>
  )
}
