#!/usr/bin/env node

/**
 * 测试任务管理功能
 * 验证基本任务操作、层级任务、IPC通信等
 */

const { app } = require('electron')
const path = require('path')

// 模拟 Electron 环境
process.env.NODE_ENV = 'development'

async function testTaskFunctionality() {
  console.log('🧪 开始测试任务管理功能...\n')
  
  try {
    // 动态导入模块
    const { TaskService } = require('../dist/main/main/services/taskService.js')
    const { getDatabase } = require('../dist/main/shared/db/index.js')
    
    const taskService = new TaskService()
    
    console.log('1️⃣ 测试数据库连接...')
    const db = await getDatabase()
    console.log('✅ 数据库连接成功')

    console.log('\n2️⃣ 测试基本任务查询...')
    const allTasks = await taskService.getAllTasks()
    console.log(`✅ 获取到 ${allTasks.length} 个任务`)

    console.log('\n3️⃣ 测试任务统计...')
    const stats = await taskService.getTaskStats()
    console.log('✅ 任务统计:', stats)

    console.log('\n4️⃣ 测试层级任务查询...')
    const hierarchies = await taskService.getHierarchicalTasks()
    console.log(`✅ 获取到 ${hierarchies.length} 个根任务层级`)
    
    if (hierarchies.length > 0) {
      const firstHierarchy = hierarchies[0]
      console.log(`   - 根任务: ${firstHierarchy.task.content}`)
      console.log(`   - 子任务数: ${firstHierarchy.children.length}`)
      console.log(`   - 深度: ${firstHierarchy.depth}`)
      
      // 递归显示层级结构
      function displayHierarchy(hierarchy, indent = '') {
        console.log(`${indent}📋 ${hierarchy.task.content} (深度: ${hierarchy.depth})`)
        hierarchy.children.forEach(child => {
          displayHierarchy(child, indent + '  ')
        })
      }
      
      console.log('\n   层级结构预览:')
      hierarchies.slice(0, 3).forEach(hierarchy => {
        displayHierarchy(hierarchy)
      })
    }

    console.log('\n5️⃣ 测试任务创建...')
    const testTask = await taskService.createTask({
      content: '测试任务 - ' + new Date().toLocaleTimeString(),
      priority: 2,
      dueDate: null
    })
    console.log(`✅ 创建测试任务: ${testTask.id}`)

    console.log('\n6️⃣ 测试子任务创建...')
    const subTask = await taskService.createTask({
      content: '测试子任务 - ' + new Date().toLocaleTimeString(),
      priority: 2,
      dueDate: null,
      parentTaskId: testTask.id
    })
    console.log(`✅ 创建测试子任务: ${subTask.id}`)

    console.log('\n7️⃣ 测试更新后的层级结构...')
    const updatedHierarchies = await taskService.getHierarchicalTasks()
    const testTaskHierarchy = updatedHierarchies.find(h => h.task.id === testTask.id)
    if (testTaskHierarchy) {
      console.log(`✅ 找到测试任务层级，子任务数: ${testTaskHierarchy.children.length}`)
    }

    console.log('\n8️⃣ 清理测试数据...')
    await taskService.deleteTask(subTask.id)
    await taskService.deleteTask(testTask.id)
    console.log('✅ 测试数据清理完成')

    console.log('\n9️⃣ 性能测试...')
    const startTime = Date.now()
    await Promise.all([
      taskService.getAllTasks(),
      taskService.getHierarchicalTasks(),
      taskService.getTaskStats()
    ])
    const endTime = Date.now()
    console.log(`✅ 并发查询性能: ${endTime - startTime}ms`)

    console.log('\n🎉 所有测试通过！任务管理功能正常工作。')
    
    // 功能验证总结
    console.log('\n📊 功能验证总结:')
    console.log('✅ 数据库连接正常')
    console.log('✅ 基本任务查询正常')
    console.log('✅ 层级任务查询正常')
    console.log('✅ 任务统计正常')
    console.log('✅ 任务创建正常')
    console.log('✅ 子任务创建正常')
    console.log('✅ 任务删除正常')
    console.log('✅ 性能表现良好')
    
    return true
    
  } catch (error) {
    console.error('\n❌ 测试失败:')
    console.error('错误类型:', error.constructor.name)
    console.error('错误信息:', error.message)
    console.error('错误堆栈:', error.stack)
    
    console.log('\n💡 可能的解决方案:')
    if (error.message.includes('database')) {
      console.log('- 检查数据库文件权限')
      console.log('- 重新初始化数据库')
    }
    if (error.message.includes('TaskHierarchy')) {
      console.log('- 检查类型定义')
      console.log('- 重新编译 TypeScript')
    }
    if (error.message.includes('IPC')) {
      console.log('- 检查 IPC 处理器注册')
      console.log('- 重启应用')
    }
    
    return false
  }
}

// 运行测试
if (require.main === module) {
  testTaskFunctionality()
    .then(success => {
      if (success) {
        console.log('\n✅ 测试完成，功能正常')
        process.exit(0)
      } else {
        console.log('\n❌ 测试失败')
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('\n💥 测试过程中发生未捕获的错误:', error)
      process.exit(1)
    })
}

module.exports = { testTaskFunctionality }
