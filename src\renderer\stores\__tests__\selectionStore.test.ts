import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { useSelectionStore, type DeletedTaskInfo } from '../selectionStore'

// Mock setTimeout and clearTimeout
vi.useFakeTimers()

describe('selectionStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useSelectionStore.setState({
      selectedTaskIds: new Set(),
      isSelectionMode: false,
      recentlyDeletedTasks: new Map(),
      undoTimeouts: new Map(),
    })
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('task selection', () => {
    it('should toggle task selection', () => {
      const store = useSelectionStore.getState()
      
      // Select a task
      store.toggleTaskSelection('task1')
      expect(useSelectionStore.getState().selectedTaskIds.has('task1')).toBe(true)
      
      // Deselect the task
      store.toggleTaskSelection('task1')
      expect(useSelectionStore.getState().selectedTaskIds.has('task1')).toBe(false)
    })

    it('should select all tasks', () => {
      const store = useSelectionStore.getState()
      const taskIds = ['task1', 'task2', 'task3']
      
      store.selectAllTasks(taskIds)
      
      const state = useSelectionStore.getState()
      taskIds.forEach(id => {
        expect(state.selectedTaskIds.has(id)).toBe(true)
      })
    })

    it('should clear selection', () => {
      const store = useSelectionStore.getState()
      
      // First select some tasks
      store.selectAllTasks(['task1', 'task2'])
      expect(useSelectionStore.getState().selectedTaskIds.size).toBe(2)
      
      // Then clear selection
      store.clearSelection()
      expect(useSelectionStore.getState().selectedTaskIds.size).toBe(0)
    })
  })

  describe('selection mode', () => {
    it('should enter selection mode', () => {
      const store = useSelectionStore.getState()
      
      store.enterSelectionMode()
      expect(useSelectionStore.getState().isSelectionMode).toBe(true)
    })

    it('should exit selection mode and clear selection', () => {
      const store = useSelectionStore.getState()
      
      // First enter selection mode and select tasks
      store.enterSelectionMode()
      store.selectAllTasks(['task1', 'task2'])
      
      // Then exit selection mode
      store.exitSelectionMode()
      
      const state = useSelectionStore.getState()
      expect(state.isSelectionMode).toBe(false)
      expect(state.selectedTaskIds.size).toBe(0)
    })

    it('should toggle selection mode', () => {
      const store = useSelectionStore.getState()
      
      // Toggle on
      store.toggleSelectionMode()
      expect(useSelectionStore.getState().isSelectionMode).toBe(true)
      
      // Toggle off
      store.toggleSelectionMode()
      expect(useSelectionStore.getState().isSelectionMode).toBe(false)
    })
  })

  describe('deleted tasks management', () => {
    const mockTask: DeletedTaskInfo = {
      id: 'task1',
      content: 'Test task',
      deletedAt: Date.now()
    }

    it('should add deleted task', () => {
      const store = useSelectionStore.getState()
      
      store.addDeletedTask(mockTask)
      
      const state = useSelectionStore.getState()
      expect(state.recentlyDeletedTasks.has(mockTask.id)).toBe(true)
      expect(state.recentlyDeletedTasks.get(mockTask.id)).toEqual(mockTask)
      expect(state.undoTimeouts.has(mockTask.id)).toBe(true)
    })

    it('should remove deleted task', () => {
      const store = useSelectionStore.getState()
      
      // First add a task
      store.addDeletedTask(mockTask)
      expect(useSelectionStore.getState().recentlyDeletedTasks.has(mockTask.id)).toBe(true)
      
      // Then remove it
      store.removeDeletedTask(mockTask.id)
      
      const state = useSelectionStore.getState()
      expect(state.recentlyDeletedTasks.has(mockTask.id)).toBe(false)
      expect(state.undoTimeouts.has(mockTask.id)).toBe(false)
    })

    it('should clear all deleted tasks', () => {
      const store = useSelectionStore.getState()
      
      // Add multiple tasks
      store.addDeletedTask(mockTask)
      store.addDeletedTask({ ...mockTask, id: 'task2' })
      
      expect(useSelectionStore.getState().recentlyDeletedTasks.size).toBe(2)
      
      // Clear all
      store.clearDeletedTasks()
      
      const state = useSelectionStore.getState()
      expect(state.recentlyDeletedTasks.size).toBe(0)
      expect(state.undoTimeouts.size).toBe(0)
    })

    it('should auto-remove deleted task after timeout', () => {
      const store = useSelectionStore.getState()
      
      store.addDeletedTask(mockTask)
      expect(useSelectionStore.getState().recentlyDeletedTasks.has(mockTask.id)).toBe(true)
      
      // Fast-forward time by 30 seconds
      vi.advanceTimersByTime(30000)
      
      const state = useSelectionStore.getState()
      expect(state.recentlyDeletedTasks.has(mockTask.id)).toBe(false)
      expect(state.undoTimeouts.has(mockTask.id)).toBe(false)
    })

    it('should get undoable task', () => {
      const store = useSelectionStore.getState()
      
      store.addDeletedTask(mockTask)
      
      const retrievedTask = store.getUndoableTask(mockTask.id)
      expect(retrievedTask).toEqual(mockTask)
      
      const nonExistentTask = store.getUndoableTask('nonexistent')
      expect(nonExistentTask).toBeUndefined()
    })
  })

  describe('store state validation', () => {
    it('should correctly convert Map to Array for recentlyDeletedTasks', () => {
      const store = useSelectionStore.getState()

      // Add some selections and deleted tasks
      store.selectAllTasks(['task1', 'task2'])
      store.addDeletedTask({
        id: 'deleted1',
        content: 'Deleted task',
        deletedAt: Date.now()
      })

      const state = useSelectionStore.getState()

      // Verify that recentlyDeletedTasks is a Map in the store
      expect(state.recentlyDeletedTasks).toBeInstanceOf(Map)
      expect(state.recentlyDeletedTasks.size).toBe(1)

      // Verify that we can convert it to an array
      const tasksArray = Array.from(state.recentlyDeletedTasks.values())
      expect(Array.isArray(tasksArray)).toBe(true)
      expect(tasksArray).toHaveLength(1)
      expect(tasksArray[0].id).toBe('deleted1')
      expect(tasksArray[0].content).toBe('Deleted task')

      // Verify that the array has map method
      expect(typeof tasksArray.map).toBe('function')
      const ids = tasksArray.map(task => task.id)
      expect(ids).toEqual(['deleted1'])
    })
  })
})
