import React, { useState, useEffect } from 'react'
import {
  X,
  Calendar,
  Star,
  Plus,
  Edit3,
  Clock,
  FileText,
  Flag,
  CheckCircle2,
  Circle,
  ChevronRight,
  Trash2
} from 'lucide-react'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { cn, formatDueDate, isTaskOverdue } from '../../lib/utils'
import { useUpdateTask, useCreateTask, useTasks, useSoftDeleteTask, useRestoreTask } from '../../hooks/useTasks'
import { useToastActions } from '../ui/toast'
import { useSelection } from '../../stores/selectionStore'
import { DeleteConfirmationDialog } from '../ui/delete-confirmation-dialog'
import { SubtaskList } from './SubtaskList'
import type { Task } from '../../../shared/types/task'
import { TaskPriority, PRIORITY_COLORS, PRIORITY_LABELS } from '../../../shared/types/task'

interface TaskDetailPanelProps {
  task: Task | null
  isOpen: boolean
  onClose: () => void
}

export function TaskDetailPanel({ task, isOpen, onClose }: TaskDetailPanelProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedTask, setEditedTask] = useState<Partial<Task>>({})
  const [showSubtasks, setShowSubtasks] = useState(true)
  const [newSubtaskContent, setNewSubtaskContent] = useState('')
  const [isAddingSubtask, setIsAddingSubtask] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const { data: allTasks = [] } = useTasks()
  const updateTask = useUpdateTask()
  const createTask = useCreateTask()
  const softDeleteTask = useSoftDeleteTask()
  const restoreTask = useRestoreTask()
  const { showSuccess, showError, showDeleteSuccess } = useToastActions()
  const { addDeletedTask, removeDeletedTask } = useSelection()

  // 获取子任务
  const subtasks = allTasks.filter(t => t.parentTaskId === task?.id)
  const completedSubtasks = subtasks.filter(t => t.isCompleted).length

  useEffect(() => {
    if (task) {
      setEditedTask({
        content: task.content,
        description: task.description || '',
        priority: task.priority,
        dueDate: task.dueDate,
        isImportant: task.isImportant || false
      })
      setIsEditing(false)
    }
  }, [task])

  if (!isOpen || !task) {
    return null
  }

  const isOverdue = isTaskOverdue(task.dueDate)

  const handleSave = async () => {
    try {
      await updateTask.mutateAsync({
        id: task.id,
        input: editedTask
      })
      setIsEditing(false)
      showSuccess('任务已更新')
    } catch (error) {
      showError('更新任务失败')
    }
  }

  const handleCancel = () => {
    setEditedTask({
      content: task.content,
      description: task.description || '',
      priority: task.priority,
      dueDate: task.dueDate,
      isImportant: task.isImportant || false
    })
    setIsEditing(false)
  }

  const handleToggleComplete = async () => {
    try {
      await updateTask.mutateAsync({
        id: task.id,
        input: { isCompleted: !task.isCompleted }
      })
    } catch (error) {
      showError('更新任务状态失败')
    }
  }

  const handleToggleImportant = async () => {
    try {
      await updateTask.mutateAsync({
        id: task.id,
        input: { isImportant: !task.isImportant }
      })
    } catch (error) {
      showError('更新任务重要性失败')
    }
  }

  const handleAddSubtask = async () => {
    if (!newSubtaskContent.trim()) return

    try {
      await createTask.mutateAsync({
        content: newSubtaskContent.trim(),
        priority: 2,
        parentTaskId: task.id,
        taskType: 'subtask'
      })
      setNewSubtaskContent('')
      setIsAddingSubtask(false)
      showSuccess('子任务已添加')
    } catch (error) {
      showError('添加子任务失败')
    }
  }

  const handleSubtaskToggle = async (subtaskId: string, isCompleted: boolean) => {
    try {
      await updateTask.mutateAsync({
        id: subtaskId,
        input: { isCompleted: !isCompleted }
      })
    } catch (error) {
      showError('更新子任务状态失败')
    }
  }

  const handleDelete = () => {
    setShowDeleteDialog(true)
  }

  const handleConfirmDelete = async () => {
    setIsDeleting(true)

    try {
      // 执行软删除
      await softDeleteTask.mutateAsync(task.id)

      // 添加到撤销列表
      addDeletedTask({
        id: task.id,
        content: task.content,
        deletedAt: Date.now()
      })

      // 显示成功提示和撤销选项
      showDeleteSuccess(task.content, async () => {
        try {
          // 先从撤销列表中移除，防止重复操作
          removeDeletedTask(task.id)

          await restoreTask.mutateAsync(task.id)
          showDeleteSuccess('撤销成功', () => {})
        } catch (error) {
          console.error('Failed to restore task:', error)

          let errorMessage = '无法恢复已删除的任务'
          if (error instanceof Error) {
            if (error.message.includes('任务不存在或未被删除')) {
              errorMessage = '该任务可能已经被恢复'
            } else {
              errorMessage = error.message
            }
          }

          showError('撤销失败', errorMessage)
        }
      })

      // 关闭详情面板
      onClose()

    } catch (error) {
      console.error('Failed to delete task:', error)
      showError('删除失败', error instanceof Error ? error.message : '删除任务时发生错误')
    } finally {
      setIsDeleting(false)
      setShowDeleteDialog(false)
    }
  }

  const getPriorityColor = (priority: number) => {
    return PRIORITY_COLORS[priority] || 'bg-gray-100'
  }

  const formatDate = (timestamp: number | null) => {
    if (!timestamp) return ''
    return new Date(timestamp).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <>
      {/* 遮罩层 - 移动端 */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden animate-in fade-in duration-300"
          onClick={onClose}
        />
      )}

      {/* 详情面板 */}
      <div className={cn(
        "fixed inset-y-0 right-0 bg-background border-l border-border shadow-xl transform transition-all duration-300 ease-out z-50",
        "w-full md:w-96", // 移动端全屏，桌面端固定宽度
        isOpen ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
      )}>
        <div className="flex flex-col h-full">
          {/* 头部 */}
          <div className="flex items-center justify-between p-4 border-b border-border">
            <h2 className="text-lg font-semibold text-foreground">任务详情</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-1 h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* 内容区域 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-6">
            {/* 任务标题和完成状态 */}
            <div className="flex items-start gap-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleComplete}
                className="p-0 h-6 w-6 rounded-full hover:bg-transparent mt-1"
              >
                {task.isCompleted ? (
                  <CheckCircle2 className="h-6 w-6 text-green-600 fill-green-100" />
                ) : (
                  <Circle className="h-6 w-6 text-muted-foreground hover:text-primary transition-colors" />
                )}
              </Button>

              <div className="flex-1 min-w-0">
                {isEditing ? (
                  <Input
                    value={editedTask.content || ''}
                    onChange={(e) => setEditedTask({ ...editedTask, content: e.target.value })}
                    className="text-lg font-medium"
                    placeholder="任务标题"
                  />
                ) : (
                  <h3 className={cn(
                    "text-lg font-medium text-foreground",
                    task.isCompleted && "line-through text-muted-foreground"
                  )}>
                    {task.content}
                  </h3>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleImportant}
                className="p-1 h-8 w-8"
              >
                <Star className={cn(
                  "h-4 w-4",
                  task.isImportant ? "text-yellow-500 fill-current" : "text-muted-foreground"
                )} />
              </Button>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-2">
              {isEditing ? (
                <>
                  <Button onClick={handleSave} size="sm">
                    保存
                  </Button>
                  <Button onClick={handleCancel} variant="outline" size="sm">
                    取消
                  </Button>
                </>
              ) : (
                <>
                  <Button onClick={() => setIsEditing(true)} variant="outline" size="sm" className="gap-2">
                    <Edit3 className="h-4 w-4" />
                    编辑
                  </Button>
                  <Button
                    onClick={handleDelete}
                    variant="outline"
                    size="sm"
                    className="gap-2 text-red-600 hover:text-red-700 hover:border-red-300 hover:bg-red-50 dark:hover:bg-red-950/20"
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4" />
                    删除
                  </Button>
                </>
              )}
            </div>

            {/* 任务属性 */}
            <div className="space-y-4">
              {/* 优先级 */}
              <div className="flex items-center gap-3">
                <Flag className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground w-16">优先级</span>
                {isEditing ? (
                  <Select
                    value={editedTask.priority?.toString()}
                    onValueChange={(value) => setEditedTask({ ...editedTask, priority: parseInt(value) })}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">低</SelectItem>
                      <SelectItem value="2">中</SelectItem>
                      <SelectItem value="3">高</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex items-center gap-2">
                    <div className={cn("w-2 h-2 rounded-full", getPriorityColor(task.priority))} />
                    <span className="text-sm">{PRIORITY_LABELS[task.priority] || '中'}</span>
                  </div>
                )}
              </div>

              {/* 截止日期 */}
              <div className="flex items-center gap-3">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground w-16">截止日期</span>
                {isEditing ? (
                  <Input
                    type="date"
                    value={editedTask.dueDate ? new Date(editedTask.dueDate).toISOString().split('T')[0] : ''}
                    onChange={(e) => setEditedTask({
                      ...editedTask,
                      dueDate: e.target.value ? new Date(e.target.value).getTime() : null
                    })}
                    className="w-40"
                  />
                ) : (
                  <span className={cn(
                    "text-sm",
                    isOverdue && !task.isCompleted ? "text-red-600" : "text-foreground"
                  )}>
                    {task.dueDate ? formatDate(task.dueDate) : '未设置'}
                  </span>
                )}
              </div>

              {/* 创建时间 */}
              <div className="flex items-center gap-3">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground w-16">创建时间</span>
                <span className="text-sm text-foreground">
                  {formatDate(task.createdAt)}
                </span>
              </div>
            </div>

            {/* 描述 */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium text-foreground">描述</span>
              </div>
              {isEditing ? (
                <Textarea
                  value={editedTask.description || ''}
                  onChange={(e) => setEditedTask({ ...editedTask, description: e.target.value })}
                  placeholder="添加描述..."
                  className="min-h-[80px]"
                />
              ) : (
                <div className="text-sm text-muted-foreground bg-muted/30 rounded-md p-3 min-h-[80px]">
                  {task.description || '暂无描述'}
                </div>
              )}
            </div>

            {/* 子任务列表 */}
            <div className="border-t border-border pt-4">
              <SubtaskList
                parentTask={task}
                subtasks={subtasks}
                className="ml-0"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleConfirmDelete}
        itemName={task.content}
        isLoading={isDeleting}
        description={subtasks.length > 0 ?
          `确定要删除"${task.content}"吗？这将同时删除 ${subtasks.length} 个子任务。此操作可在30秒内撤销。` :
          `确定要删除"${task.content}"吗？此操作可在30秒内撤销。`
        }
      />
    </>
  )
}