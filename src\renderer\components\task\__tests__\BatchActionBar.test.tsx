import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BatchActionBar } from '../BatchActionBar'
import { useSelection } from '../../../stores/selectionStore'
import { useTasks } from '../../../hooks/useTasks'

// Mock dependencies
vi.mock('../../../stores/selectionStore')
vi.mock('../../../hooks/useTasks')
vi.mock('../../../components/ui/toast')

// Mock window.electronAPI
const mockElectronAPI = {
  task: {
    batchSoftDelete: vi.fn(),
    batchRestore: vi.fn(),
  }
}

Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
})

const mockUseSelection = vi.mocked(useSelection)
const mockUseTasks = vi.mocked(useTasks)

describe('BatchActionBar', () => {
  let queryClient: QueryClient

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    // Reset mocks
    vi.clearAllMocks()

    // Default mock implementations
    mockUseSelection.mockReturnValue({
      selectedCount: 0,
      isSelectionMode: false,
      exitSelectionMode: vi.fn(),
      clearSelection: vi.fn(),
      selectedTaskIds: [],
      selectAllTasks: vi.fn(),
      addDeletedTask: vi.fn(),
      toggleTaskSelection: vi.fn(),
      enterSelectionMode: vi.fn(),
      toggleSelectionMode: vi.fn(),
      removeDeletedTask: vi.fn(),
      clearDeletedTasks: vi.fn(),
      getUndoableTask: vi.fn(),
      recentlyDeletedTasks: [],
      hasUndoableTasks: false,
      isTaskSelected: vi.fn(),
    })

    mockUseTasks.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    } as any)
  })

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    )
  }

  it('should not render when not in selection mode', () => {
    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: false,
      selectedCount: 0,
    })

    renderWithProviders(<BatchActionBar />)
    
    expect(screen.queryByText(/已选择/)).not.toBeInTheDocument()
  })

  it('should not render when no tasks are selected', () => {
    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 0,
    })

    renderWithProviders(<BatchActionBar />)
    
    expect(screen.queryByText(/已选择/)).not.toBeInTheDocument()
  })

  it('should render when in selection mode with selected tasks', () => {
    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 2,
      selectedTaskIds: ['task1', 'task2'],
    })

    renderWithProviders(<BatchActionBar />)
    
    expect(screen.getByText('已选择 2 个任务')).toBeInTheDocument()
    expect(screen.getByText('全选')).toBeInTheDocument()
    expect(screen.getByText('取消选择')).toBeInTheDocument()
    expect(screen.getByText('删除选中')).toBeInTheDocument()
  })

  it('should show "取消全选" when all tasks are selected', () => {
    const mockTasks = [
      { id: 'task1', content: 'Task 1', deletedAt: null },
      { id: 'task2', content: 'Task 2', deletedAt: null },
    ]

    mockUseTasks.mockReturnValue({
      data: mockTasks,
      isLoading: false,
      error: null,
    } as any)

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 2,
      selectedTaskIds: ['task1', 'task2'],
    })

    renderWithProviders(<BatchActionBar />)
    
    expect(screen.getByText('取消全选')).toBeInTheDocument()
  })

  it('should handle select all action', () => {
    const mockSelectAllTasks = vi.fn()
    const mockTasks = [
      { id: 'task1', content: 'Task 1', deletedAt: null },
      { id: 'task2', content: 'Task 2', deletedAt: null },
    ]

    mockUseTasks.mockReturnValue({
      data: mockTasks,
      isLoading: false,
      error: null,
    } as any)

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 1,
      selectedTaskIds: ['task1'],
      selectAllTasks: mockSelectAllTasks,
    })

    renderWithProviders(<BatchActionBar />)
    
    fireEvent.click(screen.getByText('全选'))
    
    expect(mockSelectAllTasks).toHaveBeenCalledWith(['task1', 'task2'])
  })

  it('should handle clear selection action', () => {
    const mockClearSelection = vi.fn()

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 2,
      clearSelection: mockClearSelection,
    })

    renderWithProviders(<BatchActionBar />)
    
    fireEvent.click(screen.getByText('取消选择'))
    
    expect(mockClearSelection).toHaveBeenCalled()
  })

  it('should handle exit selection mode', () => {
    const mockExitSelectionMode = vi.fn()

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 2,
      exitSelectionMode: mockExitSelectionMode,
    })

    renderWithProviders(<BatchActionBar />)
    
    // Click the X button
    const exitButton = screen.getByRole('button', { name: '' }) // X button has no text
    fireEvent.click(exitButton)
    
    expect(mockExitSelectionMode).toHaveBeenCalled()
  })

  it('should open delete confirmation dialog', () => {
    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 2,
    })

    renderWithProviders(<BatchActionBar />)
    
    fireEvent.click(screen.getByText('删除选中'))
    
    // The dialog should be rendered (though we're not testing the dialog component itself)
    expect(screen.getByText('删除选中')).toBeInTheDocument()
  })

  it('should handle batch soft delete', async () => {
    const mockAddDeletedTask = vi.fn()
    const mockExitSelectionMode = vi.fn()
    const mockTasks = [
      { id: 'task1', content: 'Task 1', deletedAt: null },
      { id: 'task2', content: 'Task 2', deletedAt: null },
    ]

    mockElectronAPI.task.batchSoftDelete.mockResolvedValue({
      deletedCount: 2,
      deletedTaskIds: ['task1', 'task2'],
    })

    mockUseTasks.mockReturnValue({
      data: mockTasks,
      isLoading: false,
      error: null,
    } as any)

    mockUseSelection.mockReturnValue({
      ...mockUseSelection(),
      isSelectionMode: true,
      selectedCount: 2,
      selectedTaskIds: ['task1', 'task2'],
      addDeletedTask: mockAddDeletedTask,
      exitSelectionMode: mockExitSelectionMode,
    })

    renderWithProviders(<BatchActionBar />)
    
    // This would require more complex testing to actually trigger the batch delete
    // In a real test, you'd need to mock the dialog component and its onConfirm callback
  })
})
