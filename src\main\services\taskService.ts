import { eq, ne, desc, asc, and, or, isNull, isNotNull, gte, lte, count, sql } from 'drizzle-orm'
import { getDatabase, schema } from '../../shared/db'
import { SOFT_DELETE_CONFIG } from '../../shared/db/schema'
import { ulid } from 'ulid'
import type {
  CreateTaskInput,
  UpdateTaskInput,
  ReorderTaskInput,
  Task,
  TaskStats,
  TaskHierarchy,
  SubTaskStats,
  CreateSubTaskInput,
  SubTaskOperationResult
} from '../../shared/types/task'
import { HIERARCHY_CONFIG } from '../../shared/types/task'
import { log } from '../utils'

// 查询过滤器接口
export interface TaskFilter {
  isCompleted?: boolean
  parentTaskId?: string | null
  priority?: number
  taskType?: string
  dueDateFrom?: number
  dueDateTo?: number
  createdFrom?: number
  createdTo?: number
  searchText?: string
  limit?: number
  offset?: number
}

// 性能监控接口
interface QueryPerformance {
  query: string
  duration: number
  timestamp: number
  recordCount: number
}

const { tasks } = schema

export class TaskService {
  private db: any = null
  private performanceLog: QueryPerformance[] = []
  private readonly MAX_PERFORMANCE_LOG_SIZE = 1000

  private async getDb() {
    if (!this.db) {
      this.db = await getDatabase()
    }
    return this.db
  }

  // 性能监控方法
  private logQueryPerformance(query: string, duration: number, recordCount: number) {
    const performance: QueryPerformance = {
      query,
      duration,
      timestamp: Date.now(),
      recordCount
    }

    this.performanceLog.push(performance)

    // 保持日志大小在限制内
    if (this.performanceLog.length > this.MAX_PERFORMANCE_LOG_SIZE) {
      this.performanceLog = this.performanceLog.slice(-this.MAX_PERFORMANCE_LOG_SIZE)
    }

    // 记录慢查询（超过100ms）
    if (duration > 100) {
      log('warn', `Slow query detected: ${query.substring(0, 100)}... (${duration}ms, ${recordCount} records)`)
    }
  }

  // 获取性能统计
  getPerformanceStats() {
    if (this.performanceLog.length === 0) {
      return { averageDuration: 0, slowQueries: 0, totalQueries: 0 }
    }

    const totalDuration = this.performanceLog.reduce((sum, log) => sum + log.duration, 0)
    const averageDuration = totalDuration / this.performanceLog.length
    const slowQueries = this.performanceLog.filter(log => log.duration > 100).length

    return {
      averageDuration: Math.round(averageDuration * 100) / 100,
      slowQueries,
      totalQueries: this.performanceLog.length,
      recentQueries: this.performanceLog.slice(-10)
    }
  }

  // 获取所有任务，按 order_index 排序（优化版本）
  async getAllTasks(): Promise<Task[]> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const result = await db
        .select()
        .from(tasks)
        .where(isNull(tasks.deletedAt)) // 只获取未删除的任务
        .orderBy(asc(tasks.orderIndex))

      const duration = Date.now() - startTime
      this.logQueryPerformance('getAllTasks', duration, result.length)

      return result.map(this.mapDbTaskToTask)
    } catch (error) {
      log('error', 'Failed to get all tasks:', error)
      throw new Error('获取任务列表失败')
    }
  }

  // 获取层级结构化的任务列表（用于UI显示）
  async getHierarchicalTasks(): Promise<TaskHierarchy[]> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 获取所有未删除的任务
      const allTasks = await db
        .select()
        .from(tasks)
        .where(isNull(tasks.deletedAt))
        .orderBy(asc(tasks.orderIndex))

      const taskMap = new Map<string, Task>()
      const rootTasks: Task[] = []

      // 将数据库任务转换为应用任务对象并建立映射
      const convertedTasks = allTasks.map(this.mapDbTaskToTask)
      convertedTasks.forEach(task => {
        taskMap.set(task.id, task)
        if (!task.parentTaskId) {
          rootTasks.push(task)
        }
      })

      // 构建层级结构
      const hierarchies: TaskHierarchy[] = []
      for (const rootTask of rootTasks) {
        const hierarchy = this.buildTaskHierarchyFromMap(rootTask, taskMap, 0, [rootTask.id])
        hierarchies.push(hierarchy)
      }

      const duration = Date.now() - startTime
      this.logQueryPerformance('getHierarchicalTasks', duration, convertedTasks.length)

      return hierarchies
    } catch (error) {
      log('error', 'Failed to get hierarchical tasks:', error)
      throw new Error('获取层级任务列表失败')
    }
  }

  // 从任务映射构建层级结构（优化版本）
  private buildTaskHierarchyFromMap(
    task: Task,
    taskMap: Map<string, Task>,
    depth: number,
    path: string[]
  ): TaskHierarchy {
    // 获取子任务
    const children: TaskHierarchy[] = []

    for (const [id, childTask] of taskMap) {
      if (childTask.parentTaskId === task.id) {
        const childPath = [...path, id]
        const childHierarchy = this.buildTaskHierarchyFromMap(childTask, taskMap, depth + 1, childPath)
        children.push(childHierarchy)
      }
    }

    // 按 orderIndex 排序子任务
    children.sort((a, b) => a.task.orderIndex - b.task.orderIndex)

    return {
      task,
      children,
      depth,
      path
    }
  }

  // 高效的条件查询方法
  async getTasksWithFilters(filter: TaskFilter): Promise<Task[]> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 构建查询条件
      const conditions = [isNull(tasks.deletedAt)] // 基础条件：未删除

      if (filter.isCompleted !== undefined) {
        conditions.push(eq(tasks.isCompleted, filter.isCompleted))
      }

      if (filter.parentTaskId !== undefined) {
        if (filter.parentTaskId === null) {
          conditions.push(isNull(tasks.parentTaskId))
        } else {
          conditions.push(eq(tasks.parentTaskId, filter.parentTaskId))
        }
      }

      if (filter.priority !== undefined) {
        conditions.push(eq(tasks.priority, filter.priority))
      }

      if (filter.taskType !== undefined) {
        conditions.push(eq(tasks.taskType, filter.taskType))
      }

      if (filter.dueDateFrom !== undefined) {
        conditions.push(gte(tasks.dueDate, filter.dueDateFrom))
      }

      if (filter.dueDateTo !== undefined) {
        conditions.push(lte(tasks.dueDate, filter.dueDateTo))
      }

      if (filter.createdFrom !== undefined) {
        conditions.push(gte(tasks.createdAt, filter.createdFrom))
      }

      if (filter.createdTo !== undefined) {
        conditions.push(lte(tasks.createdAt, filter.createdTo))
      }

      // 文本搜索（在内容和描述中搜索）
      if (filter.searchText) {
        const searchPattern = `%${filter.searchText}%`
        conditions.push(
          or(
            sql`${tasks.content} LIKE ${searchPattern}`,
            sql`${tasks.description} LIKE ${searchPattern}`
          )
        )
      }

      // 构建查询
      let query = db
        .select()
        .from(tasks)
        .where(and(...conditions))
        .orderBy(asc(tasks.orderIndex))

      // 添加分页
      if (filter.limit !== undefined) {
        query = query.limit(filter.limit)
      }

      if (filter.offset !== undefined) {
        query = query.offset(filter.offset)
      }

      const result = await query

      const duration = Date.now() - startTime
      this.logQueryPerformance('getTasksWithFilters', duration, result.length)

      return result.map(this.mapDbTaskToTask)
    } catch (error) {
      log('error', 'Failed to get tasks with filters:', error)
      throw new Error('条件查询失败')
    }
  }

  // 获取任务总数（用于分页）
  async getTaskCount(filter?: Omit<TaskFilter, 'limit' | 'offset'>): Promise<number> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      const conditions = [isNull(tasks.deletedAt)]

      if (filter) {
        if (filter.isCompleted !== undefined) {
          conditions.push(eq(tasks.isCompleted, filter.isCompleted))
        }

        if (filter.parentTaskId !== undefined) {
          if (filter.parentTaskId === null) {
            conditions.push(isNull(tasks.parentTaskId))
          } else {
            conditions.push(eq(tasks.parentTaskId, filter.parentTaskId))
          }
        }

        if (filter.priority !== undefined) {
          conditions.push(eq(tasks.priority, filter.priority))
        }

        if (filter.taskType !== undefined) {
          conditions.push(eq(tasks.taskType, filter.taskType))
        }

        if (filter.searchText) {
          const searchPattern = `%${filter.searchText}%`
          conditions.push(
            or(
              sql`${tasks.content} LIKE ${searchPattern}`,
              sql`${tasks.description} LIKE ${searchPattern}`
            )
          )
        }
      }

      const result = await db
        .select({ count: count() })
        .from(tasks)
        .where(and(...conditions))

      const duration = Date.now() - startTime
      this.logQueryPerformance('getTaskCount', duration, 1)

      return result[0].count
    } catch (error) {
      log('error', 'Failed to get task count:', error)
      throw new Error('获取任务数量失败')
    }
  }

  // 创建新任务
  async createTask(input: CreateTaskInput): Promise<Task> {
    try {
      const db = await this.getDb()
      const id = ulid()
      const now = Date.now()

      // 获取当前最大的 order_index
      const maxOrderResult = await db
        .select({ maxOrder: tasks.orderIndex })
        .from(tasks)
        .orderBy(desc(tasks.orderIndex))
        .limit(1)

      const maxOrder = maxOrderResult[0]?.maxOrder || 0
      const newOrderIndex = maxOrder + 1000 // 使用稀疏索引

      const newTask = {
        id,
        content: input.content,
        isCompleted: false,
        priority: input.priority || 2,
        dueDate: input.dueDate || null,
        orderIndex: newOrderIndex,
        createdAt: now,
        deletedAt: null,
      }

      await db.insert(tasks).values(newTask)

      log('info', `Task created: ${id}`)
      return this.mapDbTaskToTask(newTask)
    } catch (error) {
      log('error', 'Failed to create task:', error)
      throw new Error('创建任务失败')
    }
  }

  // 更新任务（支持子任务逻辑）
  async updateTask(id: string, input: UpdateTaskInput): Promise<Task> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 获取当前任务信息
      const currentTask = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, id), isNull(tasks.deletedAt)))
        .limit(1)

      if (currentTask.length === 0) {
        throw new Error('任务不存在')
      }

      const task = currentTask[0]
      const updateData: Partial<typeof tasks.$inferInsert> = {}

      if (input.content !== undefined) updateData.content = input.content
      if (input.isCompleted !== undefined) updateData.isCompleted = input.isCompleted
      if (input.priority !== undefined) updateData.priority = input.priority
      if (input.dueDate !== undefined) updateData.dueDate = input.dueDate
      if (input.progress !== undefined) updateData.progress = input.progress
      if (input.description !== undefined) updateData.description = input.description
      if (input.estimatedDuration !== undefined) updateData.estimatedDuration = input.estimatedDuration
      if (input.actualDuration !== undefined) updateData.actualDuration = input.actualDuration

      await db
        .update(tasks)
        .set(updateData)
        .where(eq(tasks.id, id))

      // 如果是子任务且状态发生变化，更新父任务
      if (task.parentTaskId && input.isCompleted !== undefined && HIERARCHY_CONFIG.AUTO_COMPLETE_PARENT) {
        await this.updateParentTaskStatus(task.parentTaskId)
      }

      // 如果是父任务且状态发生变化，需要重新计算进度
      if (!task.parentTaskId && input.isCompleted !== undefined) {
        await this.recalculateTaskProgress(id)
      }

      const updatedTask = await db
        .select()
        .from(tasks)
        .where(eq(tasks.id, id))
        .limit(1)

      const duration = Date.now() - startTime
      this.logQueryPerformance('updateTask', duration, 1)

      log('info', `Task updated: ${id}`)
      return this.mapDbTaskToTask(updatedTask[0])
    } catch (error) {
      log('error', 'Failed to update task:', error)
      throw new Error('更新任务失败')
    }
  }

  // 软删除单个任务（支持撤销）
  async softDeleteTask(id: string): Promise<boolean> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 首先检查任务是否存在（包括已删除的）
      const existingTask = await db
        .select()
        .from(tasks)
        .where(eq(tasks.id, id))
        .limit(1)

      if (existingTask.length === 0) {
        log('error', `Task not found in database: ${id}`)
        throw new Error(`任务不存在 (ID: ${id})`)
      }

      // 检查任务是否已经被删除
      if (existingTask[0].deletedAt !== null) {
        log('warn', `Task already deleted: ${id}, deletedAt: ${existingTask[0].deletedAt}`)
        throw new Error(`任务已经被删除 (ID: ${id})`)
      }

      const parentId = existingTask[0].parentTaskId
      const now = Date.now()

      // 递归软删除任务及其所有子任务
      const deletedTaskIds = await this.recursiveDeleteTask(id, now)

      // 如果有父任务，更新父任务状态
      if (parentId && HIERARCHY_CONFIG.AUTO_COMPLETE_PARENT) {
        await this.updateParentTaskStatus(parentId)
      }

      const duration = Date.now() - startTime
      this.logQueryPerformance('softDeleteTask', duration, deletedTaskIds.length)

      log('info', `Task soft deleted: ${id}, cascade deleted: ${deletedTaskIds.length} tasks`)
      return true
    } catch (error) {
      log('error', 'Failed to soft delete task:', error)
      // 保持原始错误信息，不要覆盖
      if (error instanceof Error) {
        throw error
      }
      throw new Error('软删除任务失败')
    }
  }

  // 恢复单个任务
  async restoreTask(id: string): Promise<boolean> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 获取已删除的任务信息
      const task = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, id), isNotNull(tasks.deletedAt)))
        .limit(1)

      if (task.length === 0) {
        throw new Error('任务不存在或未被删除')
      }

      // 恢复任务（清除deletedAt字段）
      await db
        .update(tasks)
        .set({ deletedAt: null })
        .where(eq(tasks.id, id))

      const duration = Date.now() - startTime
      this.logQueryPerformance('restoreTask', duration, 1)

      log('info', `Task restored: ${id}`)
      return true
    } catch (error) {
      log('error', 'Failed to restore task:', error)
      throw new Error('恢复任务失败')
    }
  }

  // 删除任务（支持级联删除子任务）
  async deleteTask(id: string): Promise<boolean> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 获取任务信息
      const task = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, id), isNull(tasks.deletedAt)))
        .limit(1)

      if (task.length === 0) {
        throw new Error('任务不存在')
      }

      const parentId = task[0].parentTaskId
      const now = Date.now()

      // 递归删除任务及其所有子任务（软删除）
      const deletedTaskIds = await this.recursiveDeleteTask(id, now)

      // 如果有父任务，更新父任务状态
      if (parentId && HIERARCHY_CONFIG.AUTO_COMPLETE_PARENT) {
        await this.updateParentTaskStatus(parentId)
      }

      const duration = Date.now() - startTime
      this.logQueryPerformance('deleteTask', duration, deletedTaskIds.length)

      log('info', `Task deleted: ${id}, cascade deleted: ${deletedTaskIds.length} tasks`)
      return true
    } catch (error) {
      log('error', 'Failed to delete task:', error)
      throw new Error('删除任务失败')
    }
  }

  // 根据ID获取单个任务
  async getTaskById(id: string): Promise<Task | null> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      const result = await db
        .select()
        .from(tasks)
        .where(eq(tasks.id, id))
        .limit(1)

      const duration = Date.now() - startTime
      this.logQueryPerformance('getTaskById', duration, 1)

      if (result.length === 0) {
        return null
      }

      return this.mapDbTaskToTask(result[0])
    } catch (error) {
      log('error', 'Failed to get task by id:', error)
      throw new Error('获取任务失败')
    }
  }

  // 优化的重新排序任务方法（使用事务）
  async reorderTasks(reorderData: ReorderTaskInput[]): Promise<boolean> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 使用事务确保数据一致性
      await db.transaction(async (tx) => {
        // 批量更新，减少数据库往返次数
        const updatePromises = reorderData.map(item =>
          tx
            .update(tasks)
            .set({ orderIndex: item.orderIndex })
            .where(eq(tasks.id, item.id))
        )

        await Promise.all(updatePromises)
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('reorderTasks', duration, reorderData.length)

      log('info', `Tasks reordered: ${reorderData.length} items in ${duration}ms`)
      return true
    } catch (error) {
      log('error', 'Failed to reorder tasks:', error)
      throw new Error('重新排序失败')
    }
  }

  // 批量更新任务方法
  async batchUpdateTasks(updates: Array<{ id: string; data: Partial<UpdateTaskInput> }>): Promise<boolean> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      await db.transaction(async (tx) => {
        const updatePromises = updates.map(({ id, data }) => {
          const updateData: Partial<typeof tasks.$inferInsert> = {}

          if (data.content !== undefined) updateData.content = data.content
          if (data.isCompleted !== undefined) updateData.isCompleted = data.isCompleted
          if (data.priority !== undefined) updateData.priority = data.priority
          if (data.dueDate !== undefined) updateData.dueDate = data.dueDate

          return tx
            .update(tasks)
            .set(updateData)
            .where(eq(tasks.id, id))
        })

        await Promise.all(updatePromises)
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('batchUpdateTasks', duration, updates.length)

      log('info', `Batch updated ${updates.length} tasks in ${duration}ms`)
      return true
    } catch (error) {
      log('error', 'Failed to batch update tasks:', error)
      throw new Error('批量更新失败')
    }
  }

  // 优化的获取任务统计方法（使用聚合查询）
  async getTaskStats(): Promise<TaskStats> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const now = Date.now()

      // 使用聚合查询而不是获取所有数据后过滤
      // 重要：只统计主任务，排除子任务（taskType !== 'subtask'）
      const [totalResult, completedResult, pendingResult, overdueResult] = await Promise.all([
        // 总任务数（只统计主任务）
        db
          .select({ count: count() })
          .from(tasks)
          .where(and(
            isNull(tasks.deletedAt),
            or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
          )),

        // 已完成任务数（只统计主任务）
        db
          .select({ count: count() })
          .from(tasks)
          .where(and(
            isNull(tasks.deletedAt),
            eq(tasks.isCompleted, true),
            or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
          )),

        // 待完成任务数（只统计主任务）
        db
          .select({ count: count() })
          .from(tasks)
          .where(and(
            isNull(tasks.deletedAt),
            eq(tasks.isCompleted, false),
            or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
          )),

        // 逾期任务数（只统计主任务）
        db
          .select({ count: count() })
          .from(tasks)
          .where(and(
            isNull(tasks.deletedAt),
            eq(tasks.isCompleted, false),
            isNotNull(tasks.dueDate),
            sql`${tasks.dueDate} < ${now}`,
            or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
          ))
      ])

      const stats: TaskStats = {
        total: totalResult[0].count,
        completed: completedResult[0].count,
        pending: pendingResult[0].count,
        overdue: overdueResult[0].count,
        withSubtasks: 0, // 暂时设为0，后续可以实现
        templates: 0, // 暂时设为0，后续可以实现
      }

      const duration = Date.now() - startTime
      this.logQueryPerformance('getTaskStats', duration, 4)

      return stats
    } catch (error) {
      log('error', 'Failed to get task stats:', error)
      throw new Error('获取任务统计失败')
    }
  }

  // 批量创建任务
  async batchCreateTasks(inputs: CreateTaskInput[]): Promise<Task[]> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const now = Date.now()

      // 获取当前最大的 order_index
      const maxOrderResult = await db
        .select({ maxOrder: tasks.orderIndex })
        .from(tasks)
        .orderBy(desc(tasks.orderIndex))
        .limit(1)

      let currentMaxOrder = maxOrderResult[0]?.maxOrder || 0

      // 准备批量插入数据
      const newTasks = inputs.map((input, index) => {
        const id = ulid()
        const orderIndex = currentMaxOrder + (index + 1) * 1000 // 使用稀疏索引

        return {
          id,
          content: input.content,
          isCompleted: false,
          priority: input.priority || 2,
          dueDate: input.dueDate || null,
          orderIndex,
          createdAt: now,
          deletedAt: null,
          parentTaskId: null,
          taskType: 'task',
          description: null,
          estimatedDuration: null,
          actualDuration: null,
          progress: 0,
        }
      })

      // 使用事务批量插入
      await db.transaction(async (tx) => {
        // 分批插入以避免SQL语句过长
        const batchSize = 100
        for (let i = 0; i < newTasks.length; i += batchSize) {
          const batch = newTasks.slice(i, i + batchSize)
          await tx.insert(tasks).values(batch)
        }
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('batchCreateTasks', duration, newTasks.length)

      log('info', `Batch created ${newTasks.length} tasks in ${duration}ms`)
      return newTasks.map(this.mapDbTaskToTask)
    } catch (error) {
      log('error', 'Failed to batch create tasks:', error)
      throw new Error('批量创建任务失败')
    }
  }

  // 批量删除任务（软删除）
  async batchDeleteTasks(taskIds: string[]): Promise<boolean> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const now = Date.now()

      await db.transaction(async (tx) => {
        // 分批删除以避免SQL语句过长
        const batchSize = 100
        for (let i = 0; i < taskIds.length; i += batchSize) {
          const batch = taskIds.slice(i, i + batchSize)
          await tx
            .update(tasks)
            .set({ deletedAt: now })
            .where(sql`${tasks.id} IN (${batch.map(() => '?').join(',')})`)
        }
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('batchDeleteTasks', duration, taskIds.length)

      log('info', `Batch deleted ${taskIds.length} tasks in ${duration}ms`)
      return true
    } catch (error) {
      log('error', 'Failed to batch delete tasks:', error)
      throw new Error('批量删除任务失败')
    }
  }

  // 批量硬删除任务（永久删除）
  async batchHardDeleteTasks(taskIds: string[]): Promise<boolean> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      await db.transaction(async (tx) => {
        // 分批删除以避免SQL语句过长
        const batchSize = 100
        for (let i = 0; i < taskIds.length; i += batchSize) {
          const batch = taskIds.slice(i, i + batchSize)
          await tx
            .delete(tasks)
            .where(sql`${tasks.id} IN (${batch.map(() => '?').join(',')})`)
        }
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('batchHardDeleteTasks', duration, taskIds.length)

      log('info', `Batch hard deleted ${taskIds.length} tasks in ${duration}ms`)
      return true
    } catch (error) {
      log('error', 'Failed to batch hard delete tasks:', error)
      throw new Error('批量永久删除任务失败')
    }
  }

  // 批量软删除任务（支持撤销）
  async batchSoftDeleteTasks(taskIds: string[]): Promise<{ deletedCount: number; deletedTaskIds: string[] }> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const now = Date.now()
      const deletedTaskIds: string[] = []

      await db.transaction(async (tx) => {
        // 分批处理以避免SQL语句过长
        const batchSize = 100
        for (let i = 0; i < taskIds.length; i += batchSize) {
          const batch = taskIds.slice(i, i + batchSize)

          // 首先检查任务是否存在且未被删除
          const existingTasks = await tx
            .select({ id: tasks.id })
            .from(tasks)
            .where(and(
              sql`${tasks.id} IN (${batch.map(() => '?').join(',')})`,
              isNull(tasks.deletedAt)
            ))

          const validTaskIds = existingTasks.map(t => t.id)

          if (validTaskIds.length > 0) {
            // 执行软删除
            await tx
              .update(tasks)
              .set({ deletedAt: now })
              .where(sql`${tasks.id} IN (${validTaskIds.map(() => '?').join(',')})`)

            deletedTaskIds.push(...validTaskIds)
          }
        }
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('batchSoftDeleteTasks', duration, deletedTaskIds.length)

      log('info', `Batch soft deleted ${deletedTaskIds.length} tasks in ${duration}ms`)
      return { deletedCount: deletedTaskIds.length, deletedTaskIds }
    } catch (error) {
      log('error', 'Failed to batch soft delete tasks:', error)
      throw new Error('批量软删除任务失败')
    }
  }

  // 批量恢复已删除的任务
  async batchRestoreTasks(taskIds: string[]): Promise<{ restoredCount: number; restoredTaskIds: string[] }> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const restoredTaskIds: string[] = []

      await db.transaction(async (tx) => {
        // 分批处理以避免SQL语句过长
        const batchSize = 100
        for (let i = 0; i < taskIds.length; i += batchSize) {
          const batch = taskIds.slice(i, i + batchSize)

          // 首先检查任务是否存在且已被删除
          const deletedTasks = await tx
            .select({ id: tasks.id })
            .from(tasks)
            .where(and(
              sql`${tasks.id} IN (${batch.map(() => '?').join(',')})`,
              isNotNull(tasks.deletedAt)
            ))

          const validTaskIds = deletedTasks.map(t => t.id)

          if (validTaskIds.length > 0) {
            // 执行恢复（清除deletedAt字段）
            await tx
              .update(tasks)
              .set({ deletedAt: null })
              .where(sql`${tasks.id} IN (${validTaskIds.map(() => '?').join(',')})`)

            restoredTaskIds.push(...validTaskIds)
          }
        }
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('batchRestoreTasks', duration, restoredTaskIds.length)

      log('info', `Batch restored ${restoredTaskIds.length} tasks in ${duration}ms`)
      return { restoredCount: restoredTaskIds.length, restoredTaskIds }
    } catch (error) {
      log('error', 'Failed to batch restore tasks:', error)
      throw new Error('批量恢复任务失败')
    }
  }

  // ==================== 子任务系统方法 ====================

  // 获取指定任务的所有子任务
  async getSubTasks(parentId: string): Promise<Task[]> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      const result = await db
        .select()
        .from(tasks)
        .where(and(
          eq(tasks.parentTaskId, parentId),
          isNull(tasks.deletedAt)
        ))
        .orderBy(asc(tasks.orderIndex))

      const duration = Date.now() - startTime
      this.logQueryPerformance('getSubTasks', duration, result.length)

      return result.map(this.mapDbTaskToTask)
    } catch (error) {
      log('error', 'Failed to get subtasks:', error)
      throw new Error('获取子任务失败')
    }
  }

  // 创建子任务
  async createSubTask(parentId: string, input: CreateTaskInput): Promise<SubTaskOperationResult> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 验证父任务存在且未删除
      const parentTask = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, parentId), isNull(tasks.deletedAt)))
        .limit(1)

      if (parentTask.length === 0) {
        throw new Error('父任务不存在或已删除')
      }

      // 检查层级深度
      const depth = await this.getTaskDepth(parentId)
      if (depth >= HIERARCHY_CONFIG.MAX_DEPTH) {
        throw new Error(`超过最大层级深度限制 (${HIERARCHY_CONFIG.MAX_DEPTH})`)
      }

      // 检查子任务数量限制
      const existingSubTasks = await this.getSubTaskCount(parentId)
      if (existingSubTasks >= HIERARCHY_CONFIG.MAX_CHILDREN_PER_PARENT) {
        throw new Error(`超过每个父任务最大子任务数限制 (${HIERARCHY_CONFIG.MAX_CHILDREN_PER_PARENT})`)
      }

      // 获取当前最大的 order_index（在同一父任务下）
      const maxOrderResult = await db
        .select({ maxOrder: tasks.orderIndex })
        .from(tasks)
        .where(and(
          eq(tasks.parentTaskId, parentId),
          isNull(tasks.deletedAt)
        ))
        .orderBy(desc(tasks.orderIndex))
        .limit(1)

      const maxOrder = maxOrderResult[0]?.maxOrder || 0
      const newOrderIndex = maxOrder + 1000

      const id = ulid()
      const now = Date.now()

      const newSubTask = {
        id,
        content: input.content,
        isCompleted: false,
        priority: input.priority || 2,
        dueDate: input.dueDate || null,
        orderIndex: newOrderIndex,
        createdAt: now,
        deletedAt: null,
        parentTaskId: parentId,
        taskType: 'subtask',
        description: input.description || null,
        estimatedDuration: input.estimatedDuration || null,
        actualDuration: null,
        progress: input.progress || 0,
      }

      await db.insert(tasks).values(newSubTask)

      const duration = Date.now() - startTime
      this.logQueryPerformance('createSubTask', duration, 1)

      log('info', `SubTask created: ${id} under parent: ${parentId}`)

      return {
        success: true,
        affectedTasks: [id, parentId],
        parentUpdated: false,
        message: '子任务创建成功'
      }
    } catch (error) {
      log('error', 'Failed to create subtask:', error)
      throw new Error(`创建子任务失败: ${error.message}`)
    }
  }

  // 获取完整的任务层级结构
  async getTaskHierarchy(taskId: string): Promise<TaskHierarchy> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 获取根任务
      const rootTask = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, taskId), isNull(tasks.deletedAt)))
        .limit(1)

      if (rootTask.length === 0) {
        throw new Error('任务不存在')
      }

      const hierarchy = await this.buildTaskHierarchy(rootTask[0], 0, [taskId])

      const duration = Date.now() - startTime
      this.logQueryPerformance('getTaskHierarchy', duration, 1)

      return hierarchy
    } catch (error) {
      log('error', 'Failed to get task hierarchy:', error)
      throw new Error('获取任务层级结构失败')
    }
  }

  // 递归构建任务层级结构
  private async buildTaskHierarchy(
    dbTask: typeof tasks.$inferSelect,
    depth: number,
    path: string[]
  ): Promise<TaskHierarchy> {
    const db = await this.getDb()

    // 获取子任务
    const children = await db
      .select()
      .from(tasks)
      .where(and(
        eq(tasks.parentTaskId, dbTask.id),
        isNull(tasks.deletedAt)
      ))
      .orderBy(asc(tasks.orderIndex))

    // 递归构建子任务层级
    const childHierarchies: TaskHierarchy[] = []
    for (const child of children) {
      const childPath = [...path, child.id]
      const childHierarchy = await this.buildTaskHierarchy(child, depth + 1, childPath)
      childHierarchies.push(childHierarchy)
    }

    return {
      task: this.mapDbTaskToTask(dbTask),
      children: childHierarchies,
      depth,
      path
    }
  }

  // 获取子任务统计信息
  async getSubTaskStats(parentId: string): Promise<SubTaskStats> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      const [totalResult, completedResult] = await Promise.all([
        // 总子任务数
        db
          .select({ count: count() })
          .from(tasks)
          .where(and(
            eq(tasks.parentTaskId, parentId),
            isNull(tasks.deletedAt)
          )),

        // 已完成子任务数
        db
          .select({ count: count() })
          .from(tasks)
          .where(and(
            eq(tasks.parentTaskId, parentId),
            isNull(tasks.deletedAt),
            eq(tasks.isCompleted, true)
          ))
      ])

      const total = totalResult[0].count
      const completed = completedResult[0].count
      const pending = total - completed
      const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

      const duration = Date.now() - startTime
      this.logQueryPerformance('getSubTaskStats', duration, 2)

      return {
        total,
        completed,
        pending,
        completionRate
      }
    } catch (error) {
      log('error', 'Failed to get subtask stats:', error)
      throw new Error('获取子任务统计失败')
    }
  }

  // 更新子任务并处理父任务状态
  async updateSubTask(id: string, input: UpdateTaskInput): Promise<SubTaskOperationResult> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 获取当前子任务信息
      const currentTask = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, id), isNull(tasks.deletedAt)))
        .limit(1)

      if (currentTask.length === 0) {
        throw new Error('子任务不存在')
      }

      const task = currentTask[0]
      const parentId = task.parentTaskId

      // 更新子任务
      const updateData: Partial<typeof tasks.$inferInsert> = {}
      if (input.content !== undefined) updateData.content = input.content
      if (input.isCompleted !== undefined) updateData.isCompleted = input.isCompleted
      if (input.priority !== undefined) updateData.priority = input.priority
      if (input.dueDate !== undefined) updateData.dueDate = input.dueDate
      if (input.progress !== undefined) updateData.progress = input.progress

      await db
        .update(tasks)
        .set(updateData)
        .where(eq(tasks.id, id))

      let parentUpdated = false
      const affectedTasks = [id]

      // 如果有父任务，处理父任务状态更新
      if (parentId && HIERARCHY_CONFIG.AUTO_COMPLETE_PARENT) {
        const parentUpdateResult = await this.updateParentTaskStatus(parentId)
        if (parentUpdateResult) {
          parentUpdated = true
          affectedTasks.push(parentId)
        }
      }

      const duration = Date.now() - startTime
      this.logQueryPerformance('updateSubTask', duration, 1)

      log('info', `SubTask updated: ${id}, parent updated: ${parentUpdated}`)

      return {
        success: true,
        affectedTasks,
        parentUpdated,
        message: '子任务更新成功'
      }
    } catch (error) {
      log('error', 'Failed to update subtask:', error)
      throw new Error(`更新子任务失败: ${error.message}`)
    }
  }

  // 删除子任务（软删除）
  async deleteSubTask(id: string): Promise<SubTaskOperationResult> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 获取子任务信息
      const subTask = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, id), isNull(tasks.deletedAt)))
        .limit(1)

      if (subTask.length === 0) {
        throw new Error('子任务不存在')
      }

      const parentId = subTask[0].parentTaskId
      const now = Date.now()

      // 递归删除所有子任务
      const deletedTaskIds = await this.recursiveDeleteTask(id, now)

      let parentUpdated = false
      const affectedTasks = [...deletedTaskIds]

      // 如果有父任务，更新父任务状态
      if (parentId && HIERARCHY_CONFIG.AUTO_COMPLETE_PARENT) {
        const parentUpdateResult = await this.updateParentTaskStatus(parentId)
        if (parentUpdateResult) {
          parentUpdated = true
          affectedTasks.push(parentId)
        }
      }

      const duration = Date.now() - startTime
      this.logQueryPerformance('deleteSubTask', duration, deletedTaskIds.length)

      log('info', `SubTask deleted: ${id}, cascade deleted: ${deletedTaskIds.length} tasks`)

      return {
        success: true,
        affectedTasks,
        parentUpdated,
        message: `成功删除 ${deletedTaskIds.length} 个任务`
      }
    } catch (error) {
      log('error', 'Failed to delete subtask:', error)
      throw new Error(`删除子任务失败: ${error.message}`)
    }
  }

  // 批量创建子任务
  async batchCreateSubTasks(parentId: string, inputs: CreateTaskInput[]): Promise<SubTaskOperationResult> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      // 验证父任务存在
      const parentTask = await db
        .select()
        .from(tasks)
        .where(and(eq(tasks.id, parentId), isNull(tasks.deletedAt)))
        .limit(1)

      if (parentTask.length === 0) {
        throw new Error('父任务不存在或已删除')
      }

      // 检查层级深度
      const depth = await this.getTaskDepth(parentId)
      if (depth >= HIERARCHY_CONFIG.MAX_DEPTH) {
        throw new Error(`超过最大层级深度限制 (${HIERARCHY_CONFIG.MAX_DEPTH})`)
      }

      // 检查子任务数量限制
      const existingSubTasks = await this.getSubTaskCount(parentId)
      if (existingSubTasks + inputs.length > HIERARCHY_CONFIG.MAX_CHILDREN_PER_PARENT) {
        throw new Error(`超过每个父任务最大子任务数限制 (${HIERARCHY_CONFIG.MAX_CHILDREN_PER_PARENT})`)
      }

      // 获取当前最大的 order_index
      const maxOrderResult = await db
        .select({ maxOrder: tasks.orderIndex })
        .from(tasks)
        .where(and(
          eq(tasks.parentTaskId, parentId),
          isNull(tasks.deletedAt)
        ))
        .orderBy(desc(tasks.orderIndex))
        .limit(1)

      let currentMaxOrder = maxOrderResult[0]?.maxOrder || 0
      const now = Date.now()

      // 准备批量插入数据
      const newSubTasks = inputs.map((input, index) => {
        const id = ulid()
        const orderIndex = currentMaxOrder + (index + 1) * 1000

        return {
          id,
          content: input.content,
          isCompleted: false,
          priority: input.priority || 2,
          dueDate: input.dueDate || null,
          orderIndex,
          createdAt: now,
          deletedAt: null,
          parentTaskId: parentId,
          taskType: 'subtask',
          description: input.description || null,
          estimatedDuration: input.estimatedDuration || null,
          actualDuration: null,
          progress: input.progress || 0,
        }
      })

      // 使用事务批量插入
      await db.transaction(async (tx) => {
        const batchSize = 50
        for (let i = 0; i < newSubTasks.length; i += batchSize) {
          const batch = newSubTasks.slice(i, i + batchSize)
          await tx.insert(tasks).values(batch)
        }
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('batchCreateSubTasks', duration, newSubTasks.length)

      const taskIds = newSubTasks.map(task => task.id)
      log('info', `Batch created ${newSubTasks.length} subtasks under parent: ${parentId}`)

      return {
        success: true,
        affectedTasks: [...taskIds, parentId],
        parentUpdated: false,
        message: `成功创建 ${newSubTasks.length} 个子任务`
      }
    } catch (error) {
      log('error', 'Failed to batch create subtasks:', error)
      throw new Error(`批量创建子任务失败: ${error.message}`)
    }
  }

  // ==================== 辅助方法 ====================

  // 获取任务层级深度
  private async getTaskDepth(taskId: string): Promise<number> {
    const db = await this.getDb()
    let depth = 0
    let currentTaskId = taskId

    while (currentTaskId) {
      const task = await db
        .select({ parentTaskId: tasks.parentTaskId })
        .from(tasks)
        .where(and(eq(tasks.id, currentTaskId), isNull(tasks.deletedAt)))
        .limit(1)

      if (task.length === 0 || !task[0].parentTaskId) {
        break
      }

      depth++
      currentTaskId = task[0].parentTaskId

      // 防止无限循环
      if (depth > HIERARCHY_CONFIG.MAX_DEPTH) {
        break
      }
    }

    return depth
  }

  // 获取子任务数量
  private async getSubTaskCount(parentId: string): Promise<number> {
    const db = await this.getDb()

    const result = await db
      .select({ count: count() })
      .from(tasks)
      .where(and(
        eq(tasks.parentTaskId, parentId),
        isNull(tasks.deletedAt)
      ))

    return result[0].count
  }

  // 更新父任务状态（基于子任务完成情况）
  private async updateParentTaskStatus(parentId: string): Promise<boolean> {
    const db = await this.getDb()

    // 获取子任务统计
    const stats = await this.getSubTaskStats(parentId)

    if (stats.total === 0) {
      return false // 没有子任务，不需要更新
    }

    let needUpdate = false
    const updateData: Partial<typeof tasks.$inferInsert> = {}

    // 自动计算进度
    if (HIERARCHY_CONFIG.AUTO_CALCULATE_PROGRESS) {
      updateData.progress = stats.completionRate
      needUpdate = true
    }

    // 自动完成父任务
    if (HIERARCHY_CONFIG.AUTO_COMPLETE_PARENT && stats.completionRate === 100) {
      updateData.isCompleted = true
      needUpdate = true
    }

    if (needUpdate) {
      await db
        .update(tasks)
        .set(updateData)
        .where(eq(tasks.id, parentId))

      log('info', `Parent task ${parentId} updated: progress=${stats.completionRate}%, completed=${updateData.isCompleted || false}`)
    }

    return needUpdate
  }

  // 重新计算任务进度（当父任务状态手动变更时）
  private async recalculateTaskProgress(taskId: string): Promise<void> {
    const db = await this.getDb()

    // 获取当前任务
    const currentTask = await db
      .select()
      .from(tasks)
      .where(and(eq(tasks.id, taskId), isNull(tasks.deletedAt)))
      .limit(1)

    if (currentTask.length === 0) {
      return
    }

    const task = currentTask[0]

    // 获取子任务统计
    const stats = await this.getSubTaskStats(taskId)

    if (stats.total === 0) {
      // 没有子任务，如果任务被标记为未完成，进度应该重置为0
      if (!task.isCompleted && task.progress > 0) {
        await db
          .update(tasks)
          .set({ progress: 0 })
          .where(eq(tasks.id, taskId))

        log('info', `Task ${taskId} progress reset to 0 (no subtasks, marked incomplete)`)
      }
      return
    }

    // 有子任务的情况下，根据子任务完成情况重新计算进度
    let newProgress = stats.completionRate

    // 如果父任务被手动标记为完成，但子任务没有全部完成，保持当前进度
    if (task.isCompleted && stats.completionRate < 100) {
      newProgress = task.progress || stats.completionRate
    }
    // 如果父任务被手动标记为未完成，进度应该反映实际的子任务完成情况
    else if (!task.isCompleted) {
      newProgress = stats.completionRate
    }

    // 更新进度
    if (Math.abs(newProgress - (task.progress || 0)) > 0.1) { // 避免微小差异的频繁更新
      await db
        .update(tasks)
        .set({ progress: newProgress })
        .where(eq(tasks.id, taskId))

      log('info', `Task ${taskId} progress recalculated: ${newProgress}% (was ${task.progress || 0}%)`)
    }
  }

  // 递归删除任务及其所有子任务
  private async recursiveDeleteTask(taskId: string, deletedAt: number): Promise<string[]> {
    const db = await this.getDb()
    const deletedIds: string[] = []

    // 获取所有子任务
    const children = await db
      .select({ id: tasks.id })
      .from(tasks)
      .where(and(
        eq(tasks.parentTaskId, taskId),
        isNull(tasks.deletedAt)
      ))

    // 递归删除子任务
    for (const child of children) {
      const childDeletedIds = await this.recursiveDeleteTask(child.id, deletedAt)
      deletedIds.push(...childDeletedIds)
    }

    // 删除当前任务
    await db
      .update(tasks)
      .set({ deletedAt })
      .where(eq(tasks.id, taskId))

    deletedIds.push(taskId)
    return deletedIds
  }

  // 验证任务层级结构的完整性
  async validateTaskHierarchy(taskId: string): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = []

    try {
      const db = await this.getDb()

      // 检查循环引用
      const visited = new Set<string>()
      const path = new Set<string>()

      const hasCycle = await this.detectCycle(taskId, visited, path)
      if (hasCycle) {
        errors.push('检测到循环引用')
      }

      // 检查层级深度
      const depth = await this.getTaskDepth(taskId)
      if (depth > HIERARCHY_CONFIG.MAX_DEPTH) {
        errors.push(`层级深度超过限制 (${depth} > ${HIERARCHY_CONFIG.MAX_DEPTH})`)
      }

      // 检查子任务数量
      const subTaskCount = await this.getSubTaskCount(taskId)
      if (subTaskCount > HIERARCHY_CONFIG.MAX_CHILDREN_PER_PARENT) {
        errors.push(`子任务数量超过限制 (${subTaskCount} > ${HIERARCHY_CONFIG.MAX_CHILDREN_PER_PARENT})`)
      }

    } catch (error) {
      errors.push(`验证过程中发生错误: ${error.message}`)
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 检测循环引用
  private async detectCycle(taskId: string, visited: Set<string>, path: Set<string>): Promise<boolean> {
    if (path.has(taskId)) {
      return true // 发现循环
    }

    if (visited.has(taskId)) {
      return false // 已经检查过，没有循环
    }

    visited.add(taskId)
    path.add(taskId)

    const db = await this.getDb()

    // 获取父任务
    const task = await db
      .select({ parentTaskId: tasks.parentTaskId })
      .from(tasks)
      .where(and(eq(tasks.id, taskId), isNull(tasks.deletedAt)))
      .limit(1)

    if (task.length > 0 && task[0].parentTaskId) {
      const hasCycle = await this.detectCycle(task[0].parentTaskId, visited, path)
      if (hasCycle) {
        return true
      }
    }

    path.delete(taskId)
    return false
  }

  // 数据库任务对象转换为应用任务对象
  private mapDbTaskToTask(dbTask: typeof tasks.$inferSelect): Task {
    return {
      id: dbTask.id,
      content: dbTask.content,
      isCompleted: dbTask.isCompleted,
      priority: dbTask.priority,
      dueDate: dbTask.dueDate,
      orderIndex: dbTask.orderIndex,
      createdAt: dbTask.createdAt,
      parentTaskId: dbTask.parentTaskId,
      taskType: dbTask.taskType as 'task' | 'subtask' | 'template',
      description: dbTask.description,
      estimatedDuration: dbTask.estimatedDuration,
      actualDuration: dbTask.actualDuration,
      progress: dbTask.progress,
    }
  }

  // ==================== 自动清理方法 ====================

  // 清理超过保留期限的已删除任务
  async cleanupDeletedTasks(retentionPeriod?: number): Promise<{ cleanedCount: number; cleanedTaskIds: string[] }> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const cutoffTime = Date.now() - (retentionPeriod || SOFT_DELETE_CONFIG.RETENTION_PERIOD)
      const cleanedTaskIds: string[] = []

      await db.transaction(async (tx) => {
        // 首先获取需要清理的任务ID
        const tasksToClean = await tx
          .select({ id: tasks.id })
          .from(tasks)
          .where(and(
            isNotNull(tasks.deletedAt),
            lte(tasks.deletedAt, cutoffTime)
          ))

        if (tasksToClean.length > 0) {
          const taskIds = tasksToClean.map(t => t.id)
          cleanedTaskIds.push(...taskIds)

          // 分批删除以避免SQL语句过长
          const batchSize = SOFT_DELETE_CONFIG.BATCH_SIZE
          for (let i = 0; i < taskIds.length; i += batchSize) {
            const batch = taskIds.slice(i, i + batchSize)
            await tx
              .delete(tasks)
              .where(sql`${tasks.id} IN (${batch.map(() => '?').join(',')})`)
          }
        }
      })

      const duration = Date.now() - startTime
      this.logQueryPerformance('cleanupDeletedTasks', duration, cleanedTaskIds.length)

      log('info', `Cleaned up ${cleanedTaskIds.length} deleted tasks in ${duration}ms`)
      return { cleanedCount: cleanedTaskIds.length, cleanedTaskIds }
    } catch (error) {
      log('error', 'Failed to cleanup deleted tasks:', error)
      throw new Error('清理已删除任务失败')
    }
  }

  // 获取已删除任务的统计信息
  async getDeletedTasksStats(): Promise<{ count: number; oldestDeletedAt: number | null; totalSize: number }> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()

      const stats = await db
        .select({
          count: count(),
          oldestDeletedAt: sql<number>`MIN(${tasks.deletedAt})`,
          totalSize: count(tasks.id)
        })
        .from(tasks)
        .where(isNotNull(tasks.deletedAt))

      const duration = Date.now() - startTime
      this.logQueryPerformance('getDeletedTasksStats', duration)

      const result = stats[0] || { count: 0, oldestDeletedAt: null, totalSize: 0 }
      return result
    } catch (error) {
      log('error', 'Failed to get deleted tasks stats:', error)
      throw new Error('获取已删除任务统计失败')
    }
  }

  // 获取可以撤销的已删除任务（在撤销时间窗口内）
  async getUndoableTasks(): Promise<Task[]> {
    const startTime = Date.now()
    try {
      const db = await this.getDb()
      const cutoffTime = Date.now() - SOFT_DELETE_CONFIG.UNDO_WINDOW

      const result = await db
        .select()
        .from(tasks)
        .where(and(
          isNotNull(tasks.deletedAt),
          gte(tasks.deletedAt, cutoffTime)
        ))
        .orderBy(desc(tasks.deletedAt))

      const duration = Date.now() - startTime
      this.logQueryPerformance('getUndoableTasks', duration, result.length)

      return result.map(this.mapDbTaskToTask)
    } catch (error) {
      log('error', 'Failed to get undoable tasks:', error)
      throw new Error('获取可撤销任务失败')
    }
  }
}
