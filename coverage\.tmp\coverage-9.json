{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 104}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 89}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 22}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 10}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 1006}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3603, "endOffset": 3761, "count": 1006}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5133, "endOffset": 5235, "count": 102}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5283, "endOffset": 5385, "count": 34}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5438, "endOffset": 5540, "count": 34}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 17}], "isBlockCoverage": true}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 16}], "isBlockCoverage": true}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 1}], "isBlockCoverage": true}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 1}], "isBlockCoverage": true}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 20}], "isBlockCoverage": true}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 91}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/__tests__/TaskList.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 34572, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 34572, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 341, "endOffset": 506, "count": 1}], "isBlockCoverage": true}, {"functionName": "useTaskFilter", "ranges": [{"startOffset": 367, "endOffset": 395, "count": 46}], "isBlockCoverage": true}, {"functionName": "useSearch", "ranges": [{"startOffset": 410, "endOffset": 452, "count": 46}], "isBlockCoverage": true}, {"functionName": "useSort", "ranges": [{"startOffset": 465, "endOffset": 503, "count": 46}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1130, "endOffset": 14582, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1151, "endOffset": 1304, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1331, "endOffset": 3851, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1387, "endOffset": 1850, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1904, "endOffset": 2657, "count": 1}, {"startOffset": 2438, "endOffset": 2656, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2336, "endOffset": 2436, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2710, "endOffset": 3220, "count": 1}, {"startOffset": 3214, "endOffset": 3219, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3119, "endOffset": 3212, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3276, "endOffset": 3845, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3742, "endOffset": 3837, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3883, "endOffset": 6246, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3944, "endOffset": 4654, "count": 1}, {"startOffset": 4648, "endOffset": 4653, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4414, "endOffset": 4646, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4727, "endOffset": 5450, "count": 1}, {"startOffset": 5444, "endOffset": 5449, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5204, "endOffset": 5442, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5519, "endOffset": 6240, "count": 1}, {"startOffset": 6234, "endOffset": 6239, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5994, "endOffset": 6232, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6275, "endOffset": 7780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6329, "endOffset": 7131, "count": 1}, {"startOffset": 7125, "endOffset": 7130, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6946, "endOffset": 7123, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7194, "endOffset": 7774, "count": 1}, {"startOffset": 7768, "endOffset": 7773, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7669, "endOffset": 7766, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7810, "endOffset": 9648, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7869, "endOffset": 8738, "count": 1}, {"startOffset": 8732, "endOffset": 8737, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8517, "endOffset": 8730, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8775, "endOffset": 9642, "count": 1}, {"startOffset": 9636, "endOffset": 9641, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9419, "endOffset": 9634, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9679, "endOffset": 10441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9726, "endOffset": 10435, "count": 1}, {"startOffset": 10326, "endOffset": 10434, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10224, "endOffset": 10324, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10476, "endOffset": 12486, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10524, "endOffset": 11552, "count": 1}, {"startOffset": 11168, "endOffset": 11551, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11066, "endOffset": 11166, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11365, "endOffset": 11544, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11584, "endOffset": 12480, "count": 1}, {"startOffset": 12182, "endOffset": 12479, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12080, "endOffset": 12180, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12346, "endOffset": 12472, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12515, "endOffset": 13240, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12569, "endOffset": 13234, "count": 1}, {"startOffset": 13152, "endOffset": 13233, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13050, "endOffset": 13150, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13271, "endOffset": 14578, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13320, "endOffset": 13908, "count": 1}, {"startOffset": 13902, "endOffset": 13907, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13753, "endOffset": 13900, "count": 22}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13950, "endOffset": 14572, "count": 1}, {"startOffset": 14566, "endOffset": 14571, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14383, "endOffset": 14564, "count": 22}], "isBlockCoverage": true}]}, {"scriptId": "1566", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/TaskList.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21480, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21480, "count": 1}], "isBlockCoverage": true}, {"functionName": "TaskList", "ranges": [{"startOffset": 1722, "endOffset": 9152, "count": 47}, {"startOffset": 4583, "endOffset": 5238, "count": 17}, {"startOffset": 5238, "endOffset": 5252, "count": 29}, {"startOffset": 5252, "endOffset": 6121, "count": 1}, {"startOffset": 6121, "endOffset": 6165, "count": 28}, {"startOffset": 6165, "endOffset": 6950, "count": 2}, {"startOffset": 6211, "endOffset": 6224, "count": 1}, {"startOffset": 6225, "endOffset": 6394, "count": 1}, {"startOffset": 6285, "endOffset": 6297, "count": 0}, {"startOffset": 6356, "endOffset": 6368, "count": 0}, {"startOffset": 6950, "endOffset": 9151, "count": 26}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2491, "endOffset": 3649, "count": 46}, {"startOffset": 2594, "endOffset": 2721, "count": 3}, {"startOffset": 2728, "endOffset": 2854, "count": 3}, {"startOffset": 2861, "endOffset": 2884, "count": 40}, {"startOffset": 2919, "endOffset": 3077, "count": 5}, {"startOffset": 3106, "endOffset": 3193, "count": 0}, {"startOffset": 3200, "endOffset": 3286, "count": 3}, {"startOffset": 3293, "endOffset": 3529, "count": 0}, {"startOffset": 3536, "endOffset": 3550, "count": 43}, {"startOffset": 3557, "endOffset": 3639, "count": 43}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2531, "endOffset": 2559, "count": 2107}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2678, "endOffset": 2704, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2810, "endOffset": 2837, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3010, "endOffset": 3062, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3156, "endOffset": 3191, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3251, "endOffset": 3284, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3343, "endOffset": 3527, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3600, "endOffset": 3637, "count": 2046}], "isBlockCoverage": true}, {"functionName": "handleDragEnd", "ranges": [{"startOffset": 3718, "endOffset": 4564, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4714, "endOffset": 5062, "count": 51}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7482, "endOffset": 7499, "count": 2074}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7750, "endOffset": 8055, "count": 2074}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9249, "endOffset": 9273, "count": 17}], "isBlockCoverage": true}]}, {"scriptId": "1588", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/AdvancedTaskItem.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7484, "count": 1}], "isBlockCoverage": true}, {"functionName": "AdvancedTaskItem", "ranges": [{"startOffset": 882, "endOffset": 3519, "count": 2075}, {"startOffset": 1273, "endOffset": 1580, "count": 0}, {"startOffset": 1580, "endOffset": 2086, "count": 2074}, {"startOffset": 2086, "endOffset": 2778, "count": 16}, {"startOffset": 2984, "endOffset": 3344, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1035, "endOffset": 1109, "count": 2074}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1070, "endOffset": 1103, "count": 2000340}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1177, "endOffset": 1197, "count": 16}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3625, "endOffset": 3657, "count": 2074}], "isBlockCoverage": true}]}, {"scriptId": "1589", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/TaskItem.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 41675, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 41675, "count": 1}], "isBlockCoverage": true}, {"functionName": "TaskItem", "ranges": [{"startOffset": 2535, "endOffset": 20291, "count": 4181}, {"startOffset": 2875, "endOffset": 2927, "count": 64}, {"startOffset": 2928, "endOffset": 2932, "count": 4116}, {"startOffset": 5838, "endOffset": 5874, "count": 0}, {"startOffset": 5875, "endOffset": 5923, "count": 0}, {"startOffset": 5944, "endOffset": 5969, "count": 0}, {"startOffset": 5996, "endOffset": 6011, "count": 0}, {"startOffset": 6031, "endOffset": 6051, "count": 0}, {"startOffset": 6052, "endOffset": 6095, "count": 0}, {"startOffset": 6116, "endOffset": 6188, "count": 0}, {"startOffset": 6209, "endOffset": 6264, "count": 0}, {"startOffset": 7302, "endOffset": 7314, "count": 0}, {"startOffset": 7315, "endOffset": 7333, "count": 0}, {"startOffset": 7790, "endOffset": 12149, "count": 0}, {"startOffset": 12150, "endOffset": 15616, "count": 0}, {"startOffset": 15977, "endOffset": 17337, "count": 0}, {"startOffset": 17338, "endOffset": 19317, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleToggleComplete", "ranges": [{"startOffset": 3962, "endOffset": 4162, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleStartEdit", "ranges": [{"startOffset": 4190, "endOffset": 4401, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleSaveEdit", "ranges": [{"startOffset": 4428, "endOffset": 4740, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleCancelEdit", "ranges": [{"startOffset": 4769, "endOffset": 4977, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleDelete", "ranges": [{"startOffset": 5002, "endOffset": 5044, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleConfirmDelete", "ranges": [{"startOffset": 5076, "endOffset": 5407, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleKeyDown", "ranges": [{"startOffset": 5433, "endOffset": 5567, "count": 0}], "isBlockCoverage": false}, {"functionName": "onChange", "ranges": [{"startOffset": 8051, "endOffset": 8088, "count": 0}], "isBlockCoverage": false}, {"functionName": "onChange", "ranges": [{"startOffset": 11381, "endOffset": 11418, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 20388, "endOffset": 20412, "count": 2090}], "isBlockCoverage": true}]}, {"scriptId": "1593", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/checkbox.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4827, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 826, "endOffset": 2217, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2380, "endOffset": 2404, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1612", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 550, "count": 32}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 641, "endOffset": 659, "count": 32}], "isBlockCoverage": true}, {"functionName": "formatDate", "ranges": [{"startOffset": 663, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1280, "endOffset": 1306, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDueDate", "ranges": [{"startOffset": 1310, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1984, "endOffset": 2013, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTaskOverdue", "ranges": [{"startOffset": 2017, "endOffset": 2113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2244, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 2248, "endOffset": 2454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2551, "endOffset": 2575, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 2579, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2908, "endOffset": 2932, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateOrderIndex", "ranges": [{"startOffset": 2936, "endOffset": 3207, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3314, "endOffset": 3348, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1615", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/button.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6598, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1951, "endOffset": 2495, "count": 32}, {"startOffset": 2044, "endOffset": 2072, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2625, "endOffset": 2647, "count": 32}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2753, "endOffset": 2783, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1617", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/input.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3383, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 569, "endOffset": 1384, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1511, "endOffset": 1532, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1618", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/select.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22604, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 996, "endOffset": 2293, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2429, "endOffset": 3149, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3301, "endOffset": 4025, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4172, "endOffset": 6521, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6648, "endOffset": 7047, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7169, "endOffset": 8924, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9049, "endOffset": 9438, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9611, "endOffset": 9633, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9736, "endOffset": 9763, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9866, "endOffset": 9893, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 9998, "endOffset": 10027, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10132, "endOffset": 10161, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10264, "endOffset": 10291, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10393, "endOffset": 10419, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10526, "endOffset": 10557, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10669, "endOffset": 10705, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 10819, "endOffset": 10857, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1676", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/delete-confirmation-dialog.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 21254, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 21254, "count": 1}], "isBlockCoverage": true}, {"functionName": "DeleteConfirmationDialog", "ranges": [{"startOffset": 818, "endOffset": 5658, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5771, "endOffset": 5811, "count": 0}], "isBlockCoverage": false}, {"functionName": "BatchDeleteConfirmationDialog", "ranges": [{"startOffset": 5815, "endOffset": 11144, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11262, "endOffset": 11307, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1677", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/dialog.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15804, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1021, "endOffset": 1551, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1680, "endOffset": 4260, "count": 0}], "isBlockCoverage": false}, {"functionName": "DialogHeader", "ranges": [{"startOffset": 4355, "endOffset": 4751, "count": 0}], "isBlockCoverage": false}, {"functionName": "<PERSON><PERSON><PERSON><PERSON>er", "ranges": [{"startOffset": 4817, "endOffset": 5224, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5322, "endOffset": 5749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5878, "endOffset": 6274, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6451, "endOffset": 6473, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6577, "endOffset": 6605, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6710, "endOffset": 6739, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6842, "endOffset": 6869, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6974, "endOffset": 7003, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7108, "endOffset": 7137, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7241, "endOffset": 7269, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7373, "endOffset": 7401, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7504, "endOffset": 7531, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7640, "endOffset": 7673, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1679", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/toast.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToastProvider", "ranges": [{"startOffset": 857, "endOffset": 1996, "count": 18}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1015, "endOffset": 1317, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1380, "endOffset": 1451, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2098, "endOffset": 2127, "count": 17}], "isBlockCoverage": true}, {"functionName": "useToast", "ranges": [{"startOffset": 2131, "endOffset": 2329, "count": 43}, {"startOffset": 2235, "endOffset": 2309, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2426, "endOffset": 2450, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastContainer", "ranges": [{"startOffset": 2454, "endOffset": 3062, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2670, "endOffset": 2902, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastItem", "ranges": [{"startOffset": 3063, "endOffset": 7280, "count": 0}], "isBlockCoverage": false}, {"functionName": "useToastActions", "ranges": [{"startOffset": 7281, "endOffset": 8394, "count": 26}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7401, "endOffset": 7501, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7570, "endOffset": 7652, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7723, "endOffset": 7807, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7875, "endOffset": 7956, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8033, "endOffset": 8277, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8498, "endOffset": 8529, "count": 26}], "isBlockCoverage": true}]}, {"scriptId": "1680", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/hooks/useTasks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "useTasks", "ranges": [{"startOffset": 612, "endOffset": 787, "count": 2121}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 884, "endOffset": 908, "count": 2121}], "isBlockCoverage": true}, {"functionName": "useTaskStats", "ranges": [{"startOffset": 912, "endOffset": 1097, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1198, "endOffset": 1226, "count": 0}], "isBlockCoverage": false}, {"functionName": "useCreateTask", "ranges": [{"startOffset": 1230, "endOffset": 1870, "count": 16}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 1380, "endOffset": 1434, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSuccess", "ranges": [{"startOffset": 1451, "endOffset": 1743, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 1758, "endOffset": 1862, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1972, "endOffset": 2001, "count": 16}], "isBlockCoverage": true}, {"functionName": "useUpdateTask", "ranges": [{"startOffset": 2005, "endOffset": 2734, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2836, "endOffset": 2865, "count": 0}], "isBlockCoverage": false}, {"functionName": "useDeleteTask", "ranges": [{"startOffset": 2869, "endOffset": 3527, "count": 26}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 3019, "endOffset": 3067, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSuccess", "ranges": [{"startOffset": 3084, "endOffset": 3400, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 3415, "endOffset": 3519, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3629, "endOffset": 3658, "count": 26}], "isBlockCoverage": true}, {"functionName": "useReorderTasks", "ranges": [{"startOffset": 3662, "endOffset": 4873, "count": 46}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 3814, "endOffset": 3881, "count": 0}], "isBlockCoverage": false}, {"functionName": "onMutate", "ranges": [{"startOffset": 3897, "endOffset": 4561, "count": 0}], "isBlockCoverage": false}, {"functionName": "onError", "ranges": [{"startOffset": 4576, "endOffset": 4744, "count": 0}], "isBlockCoverage": false}, {"functionName": "onSettled", "ranges": [{"startOffset": 4761, "endOffset": 4865, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4977, "endOffset": 5008, "count": 46}], "isBlockCoverage": true}]}, {"scriptId": "1725", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 309, "endOffset": 328, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAll", "ranges": [{"startOffset": 360, "endOffset": 383, "count": 17}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 395, "endOffset": 428, "count": 0}], "isBlockCoverage": false}, {"functionName": "update", "ranges": [{"startOffset": 440, "endOffset": 481, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 493, "endOffset": 520, "count": 0}], "isBlockCoverage": false}, {"functionName": "reorder", "ranges": [{"startOffset": 533, "endOffset": 565, "count": 0}], "isBlockCoverage": false}, {"functionName": "getStats", "ranges": [{"startOffset": 579, "endOffset": 604, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 703, "endOffset": 726, "count": 2121}], "isBlockCoverage": true}, {"functionName": "getVersion", "ranges": [{"startOffset": 761, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "quit", "ranges": [{"startOffset": 797, "endOffset": 817, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 915, "endOffset": 937, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 970, "endOffset": 1000, "count": 17}], "isBlockCoverage": true}, {"functionName": "set", "ranges": [{"startOffset": 1009, "endOffset": 1053, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1156, "endOffset": 1183, "count": 17}], "isBlockCoverage": true}]}, {"scriptId": "1726", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/queryClient.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "retry<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 597, "endOffset": 653, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 964, "endOffset": 991, "count": 0}], "isBlockCoverage": false}, {"functionName": "settings", "ranges": [{"startOffset": 1081, "endOffset": 1128, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1264, "endOffset": 1290, "count": 2121}], "isBlockCoverage": true}]}, {"scriptId": "1727", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/stores/selectionStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6230, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 367, "endOffset": 1402, "count": 1}], "isBlockCoverage": true}, {"functionName": "toggleTaskSelection", "ranges": [{"startOffset": 479, "endOffset": 759, "count": 0}], "isBlockCoverage": false}, {"functionName": "selectAllTasks", "ranges": [{"startOffset": 779, "endOffset": 845, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearSelection", "ranges": [{"startOffset": 865, "endOffset": 933, "count": 0}], "isBlockCoverage": false}, {"functionName": "enterSelectionMode", "ranges": [{"startOffset": 957, "endOffset": 1004, "count": 0}], "isBlockCoverage": false}, {"functionName": "exitSelectionMode", "ranges": [{"startOffset": 1027, "endOffset": 1135, "count": 0}], "isBlockCoverage": false}, {"functionName": "toggleSelectionMode", "ranges": [{"startOffset": 1160, "endOffset": 1399, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1510, "endOffset": 1543, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSelection", "ranges": [{"startOffset": 1547, "endOffset": 1854, "count": 26}], "isBlockCoverage": true}, {"functionName": "isTaskSelected", "ranges": [{"startOffset": 1788, "endOffset": 1833, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1955, "endOffset": 1983, "count": 26}], "isBlockCoverage": true}]}, {"scriptId": "1736", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/SubtaskList.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 27237, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 27237, "count": 1}], "isBlockCoverage": true}, {"functionName": "SubtaskList", "ranges": [{"startOffset": 1274, "endOffset": 11424, "count": 17}, {"startOffset": 2474, "endOffset": 2493, "count": 0}, {"startOffset": 2495, "endOffset": 3602, "count": 0}, {"startOffset": 3602, "endOffset": 6411, "count": 16}, {"startOffset": 4459, "endOffset": 4761, "count": 0}, {"startOffset": 6428, "endOffset": 8030, "count": 16}, {"startOffset": 8052, "endOffset": 11254, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleAddSubtask", "ranges": [{"startOffset": 1666, "endOffset": 2123, "count": 0}], "isBlockCoverage": false}, {"functionName": "handleKeyPress", "ranges": [{"startOffset": 2150, "endOffset": 2325, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2371, "endOffset": 2397, "count": 16}], "isBlockCoverage": true}, {"functionName": "onClick", "ranges": [{"startOffset": 2757, "endOffset": 2787, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 4013, "endOffset": 4045, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 5566, "endOffset": 5596, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6532, "endOffset": 7856, "count": 16}], "isBlockCoverage": true}, {"functionName": "onChange", "ranges": [{"startOffset": 8873, "endOffset": 8916, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 10500, "endOffset": 10602, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 11525, "endOffset": 11552, "count": 16}], "isBlockCoverage": true}, {"functionName": "SubtaskProgress", "ranges": [{"startOffset": 11580, "endOffset": 13118, "count": 16}, {"startOffset": 11649, "endOffset": 11661, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 13223, "endOffset": 13254, "count": 16}], "isBlockCoverage": true}]}, {"scriptId": "1737", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/task/BatchActionBar.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14175, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14175, "count": 1}], "isBlockCoverage": true}, {"functionName": "BatchActionBar", "ranges": [{"startOffset": 1439, "endOffset": 7235, "count": 26}, {"startOffset": 1880, "endOffset": 1902, "count": 0}, {"startOffset": 1926, "endOffset": 7234, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleBatchDelete", "ranges": [{"startOffset": 1955, "endOffset": 2374, "count": 0}], "isBlockCoverage": false}, {"functionName": "onClick", "ranges": [{"startOffset": 4663, "endOffset": 4694, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7338, "endOffset": 7368, "count": 26}], "isBlockCoverage": true}]}, {"scriptId": "1738", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/test-utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTestQueryClient", "ranges": [{"startOffset": 1029, "endOffset": 1433, "count": 34}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1543, "endOffset": 1580, "count": 0}], "isBlockCoverage": false}, {"functionName": "TestWrapper", "ranges": [{"startOffset": 1584, "endOffset": 2468, "count": 18}, {"startOffset": 1663, "endOffset": 1689, "count": 17}], "isBlockCoverage": true}, {"functionName": "renderWithProviders", "ranges": [{"startOffset": 2469, "endOffset": 3001, "count": 17}], "isBlockCoverage": true}, {"functionName": "Wrapper", "ranges": [{"startOffset": 2589, "endOffset": 2845, "count": 30}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3109, "endOffset": 3144, "count": 17}], "isBlockCoverage": true}, {"functionName": "waitF<PERSON><PERSON><PERSON><PERSON><PERSON>oSettle", "ranges": [{"startOffset": 3148, "endOffset": 3624, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3733, "endOffset": 3769, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 3802, "endOffset": 3971, "count": 0}], "isBlockCoverage": false}, {"functionName": "type", "ranges": [{"startOffset": 3981, "endOffset": 4186, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 4197, "endOffset": 4394, "count": 0}], "isBlockCoverage": false}, {"functionName": "keyDown", "ranges": [{"startOffset": 4407, "endOffset": 4592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4693, "endOffset": 4718, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeInDocument", "ranges": [{"startOffset": 4761, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveTextContent", "ranges": [{"startOffset": 4843, "endOffset": 4912, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveValue", "ranges": [{"startOffset": 4929, "endOffset": 4994, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeDisabled", "ranges": [{"startOffset": 5012, "endOffset": 5066, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeEnabled", "ranges": [{"startOffset": 5083, "endOffset": 5136, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5238, "endOffset": 5264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5639, "endOffset": 5679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5778, "endOffset": 5813, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1739", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/contexts/ThemeContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "useSystemTheme", "ranges": [{"startOffset": 885, "endOffset": 1405, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1008, "endOffset": 1375, "count": 17}, {"startOffset": 1221, "endOffset": 1229, "count": 0}], "isBlockCoverage": true}, {"functionName": "handleChange", "ranges": [{"startOffset": 1115, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1310, "endOffset": 1370, "count": 17}], "isBlockCoverage": true}, {"functionName": "applyThemeToDOM", "ranges": [{"startOffset": 1431, "endOffset": 1994, "count": 16}, {"startOffset": 1812, "endOffset": 1849, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1634, "endOffset": 1780, "count": 304}], "isBlockCoverage": true}, {"functionName": "ThemeProvider", "ranges": [{"startOffset": 2018, "endOffset": 7343, "count": 34}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2483, "endOffset": 2652, "count": 33}, {"startOffset": 2595, "endOffset": 2651, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2741, "endOffset": 3140, "count": 33}, {"startOffset": 2997, "endOffset": 3082, "count": 0}, {"startOffset": 3083, "endOffset": 3099, "count": 0}, {"startOffset": 3106, "endOffset": 3139, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3030, "endOffset": 3061, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3287, "endOffset": 3358, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3445, "endOffset": 3710, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3769, "endOffset": 3866, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3944, "endOffset": 4214, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4313, "endOffset": 4419, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4499, "endOffset": 4708, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4797, "endOffset": 4877, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4946, "endOffset": 5022, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5063, "endOffset": 5597, "count": 17}], "isBlockCoverage": true}, {"functionName": "loadConfig", "ranges": [{"startOffset": 5094, "endOffset": 5574, "count": 17}, {"startOffset": 5262, "endOffset": 5364, "count": 0}, {"startOffset": 5373, "endOffset": 5521, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5638, "endOffset": 5709, "count": 33}, {"startOffset": 5666, "endOffset": 5705, "count": 16}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5785, "endOffset": 6731, "count": 33}, {"startOffset": 5835, "endOffset": 6730, "count": 0}], "isBlockCoverage": true}, {"functionName": "checkAutoSwitch", "ranges": [{"startOffset": 5864, "endOffset": 6605, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6697, "endOffset": 6726, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7446, "endOffset": 7475, "count": 17}], "isBlockCoverage": true}, {"functionName": "useTheme", "ranges": [{"startOffset": 7496, "endOffset": 7690, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7788, "endOffset": 7812, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7917, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1740", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1399, "endOffset": 1432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1854, "endOffset": 1881, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2496, "endOffset": 2529, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7696, "endOffset": 7727, "count": 33}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8083, "endOffset": 8119, "count": 33}], "isBlockCoverage": true}, {"functionName": "getThemeById", "ranges": [{"startOffset": 8144, "endOffset": 8214, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8184, "endOffset": 8210, "count": 33}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8316, "endOffset": 8344, "count": 33}], "isBlockCoverage": true}, {"functionName": "getThemesByMode", "ranges": [{"startOffset": 8372, "endOffset": 8450, "count": 33}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8416, "endOffset": 8446, "count": 198}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8555, "endOffset": 8586, "count": 33}], "isBlockCoverage": true}, {"functionName": "isValidThemeId", "ranges": [{"startOffset": 8613, "endOffset": 8683, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8787, "endOffset": 8817, "count": 0}], "isBlockCoverage": false}]}]}