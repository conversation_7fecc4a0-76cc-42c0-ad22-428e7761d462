import React, { useState } from 'react'
import { ChevronRight, ChevronDown, Plus } from 'lucide-react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { cn } from '../../lib/utils'
import { TaskItem } from './TaskItem'
import { Button } from '../ui/button'
import type { TaskHierarchy } from '../../../shared/types/task'

interface HierarchicalTaskItemProps {
  hierarchy: TaskHierarchy
  isDragDisabled?: boolean
  onAddSubtask?: (parentId: string) => void
}

export function HierarchicalTaskItem({ 
  hierarchy, 
  isDragDisabled = false,
  onAddSubtask 
}: HierarchicalTaskItemProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const { task, children, depth } = hierarchy
  const hasChildren = children.length > 0

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id,
    disabled: isDragDisabled || depth > 0, // 只允许拖拽根任务
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  // 计算缩进
  const indentLevel = depth * 20 // 每层缩进20px

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "w-full",
        isDragging && "opacity-50 shadow-lg"
      )}
    >
      {/* 任务项容器 */}
      <div 
        className={cn(
          "flex items-center gap-2 group relative",
          depth > 0 && "ml-4 border-l-2 border-gray-200 pl-4"
        )}
        style={{ 
          marginLeft: depth > 0 ? `${indentLevel}px` : '0px'
        }}
      >
        {/* 层级连接线 */}
        {depth > 0 && (
          <div className="absolute left-0 top-0 w-4 h-6 border-l-2 border-b-2 border-gray-200 rounded-bl-md" />
        )}

        {/* 展开/收起按钮 */}
        <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
          {hasChildren ? (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="w-full h-full flex items-center justify-center hover:bg-gray-100 rounded transition-colors"
              title={isExpanded ? "收起子任务" : "展开子任务"}
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-500" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-500" />
              )}
            </button>
          ) : (
            <div className="w-4 h-4" />
          )}
        </div>

        {/* 任务内容 */}
        <div className="flex-1 min-w-0">
          <TaskItem
            task={task}
            isSubtask={depth > 0}
            className={cn(
              depth > 0 && "bg-gray-50/50 border-gray-200"
            )}
            {...(depth === 0 ? { ...attributes, ...listeners } : {})}
          />
        </div>

        {/* 子任务统计和操作 */}
        {hasChildren && (
          <div className="flex-shrink-0 flex items-center gap-2 text-xs text-gray-500">
            <span>
              {children.filter(c => c.task.isCompleted).length}/{children.length}
            </span>
          </div>
        )}

        {/* 添加子任务按钮 */}
        {onAddSubtask && depth < 4 && ( // 限制最大层级为5层
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAddSubtask(task.id)}
            className="flex-shrink-0 w-8 h-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
            title="添加子任务"
          >
            <Plus className="w-4 h-4" />
          </Button>
        )}
      </div>

      {/* 子任务列表 */}
      {hasChildren && isExpanded && (
        <div className="mt-2 space-y-2">
          {children.map((childHierarchy) => (
            <HierarchicalTaskItem
              key={childHierarchy.task.id}
              hierarchy={childHierarchy}
              isDragDisabled={isDragDisabled}
              onAddSubtask={onAddSubtask}
            />
          ))}
        </div>
      )}

      {/* 子任务进度条 */}
      {hasChildren && (
        <div className="mt-2 ml-8">
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <div className="flex-1 bg-gray-200 rounded-full h-1.5">
              <div
                className="bg-green-500 h-1.5 rounded-full transition-all duration-300"
                style={{
                  width: `${(() => {
                    // 计算实际的子任务完成进度
                    const completedCount = children.filter(c => c.task.isCompleted).length
                    const totalCount = children.length
                    const calculatedProgress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0

                    // 如果父任务有明确的进度值且不是完成状态，使用进度值
                    // 如果父任务是完成状态但子任务没有全部完成，显示实际子任务进度
                    if (task.progress !== undefined && task.progress !== null) {
                      if (task.isCompleted && calculatedProgress < 100) {
                        return calculatedProgress
                      }
                      return task.progress
                    }

                    return calculatedProgress
                  })()}%`
                }}
              />
            </div>
            <span className="whitespace-nowrap">
              {(() => {
                const completedCount = children.filter(c => c.task.isCompleted).length
                const totalCount = children.length
                const calculatedProgress = totalCount > 0 ? (completedCount / totalCount) * 100 : 0

                if (task.progress !== undefined && task.progress !== null) {
                  if (task.isCompleted && calculatedProgress < 100) {
                    return Math.round(calculatedProgress)
                  }
                  return Math.round(task.progress)
                }

                return Math.round(calculatedProgress)
              })()}%
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

export default HierarchicalTaskItem
