{"version": 3, "sources": ["../../src/supabase/rls.ts"], "sourcesContent": ["import { bigserial, pgSchema, text, timestamp, uuid, varchar } from '~/pg-core/index.ts';\nimport { pgRole } from '~/pg-core/roles.ts';\nimport { sql } from '~/sql/sql.ts';\n\nexport const anonRole = pgRole('anon').existing();\nexport const authenticatedRole = pgRole('authenticated').existing();\nexport const serviceRole = pgRole('service_role').existing();\nexport const postgresRole = pgRole('postgres_role').existing();\nexport const supabaseAuthAdminRole = pgRole('supabase_auth_admin').existing();\n\n/* ------------------------------ auth schema; ------------------------------ */\nconst auth = pgSchema('auth');\n\nexport const authUsers = auth.table('users', {\n\tid: uuid().primaryKey().notNull(),\n\temail: varchar({ length: 255 }),\n\tphone: text().unique(),\n\temailConfirmedAt: timestamp('email_confirmed_at', { withTimezone: true }),\n\tphoneConfirmedAt: timestamp('phone_confirmed_at', { withTimezone: true }),\n\tlastSignInAt: timestamp('last_sign_in_at', { withTimezone: true }),\n\tcreatedAt: timestamp('created_at', { withTimezone: true }),\n\tupdatedAt: timestamp('updated_at', { withTimezone: true }),\n});\n\n/* ------------------------------ realtime schema; ------------------------------- */\nconst realtime = pgSchema('realtime');\n\nexport const realtimeMessages = realtime.table(\n\t'messages',\n\t{\n\t\tid: bigserial({ mode: 'bigint' }).primaryKey(),\n\t\ttopic: text().notNull(),\n\t\textension: text({\n\t\t\tenum: ['presence', 'broadcast', 'postgres_changes'],\n\t\t}).notNull(),\n\t},\n);\n\nexport const authUid = sql`(select auth.uid())`;\nexport const realtimeTopic = sql`realtime.topic()`;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAAoE;AACpE,mBAAuB;AACvB,iBAAoB;AAEb,MAAM,eAAW,qBAAO,MAAM,EAAE,SAAS;AACzC,MAAM,wBAAoB,qBAAO,eAAe,EAAE,SAAS;AAC3D,MAAM,kBAAc,qBAAO,cAAc,EAAE,SAAS;AACpD,MAAM,mBAAe,qBAAO,eAAe,EAAE,SAAS;AACtD,MAAM,4BAAwB,qBAAO,qBAAqB,EAAE,SAAS;AAG5E,MAAM,WAAO,yBAAS,MAAM;AAErB,MAAM,YAAY,KAAK,MAAM,SAAS;AAAA,EAC5C,QAAI,qBAAK,EAAE,WAAW,EAAE,QAAQ;AAAA,EAChC,WAAO,wBAAQ,EAAE,QAAQ,IAAI,CAAC;AAAA,EAC9B,WAAO,qBAAK,EAAE,OAAO;AAAA,EACrB,sBAAkB,0BAAU,sBAAsB,EAAE,cAAc,KAAK,CAAC;AAAA,EACxE,sBAAkB,0BAAU,sBAAsB,EAAE,cAAc,KAAK,CAAC;AAAA,EACxE,kBAAc,0BAAU,mBAAmB,EAAE,cAAc,KAAK,CAAC;AAAA,EACjE,eAAW,0BAAU,cAAc,EAAE,cAAc,KAAK,CAAC;AAAA,EACzD,eAAW,0BAAU,cAAc,EAAE,cAAc,KAAK,CAAC;AAC1D,CAAC;AAGD,MAAM,eAAW,yBAAS,UAAU;AAE7B,MAAM,mBAAmB,SAAS;AAAA,EACxC;AAAA,EACA;AAAA,IACC,QAAI,0BAAU,EAAE,MAAM,SAAS,CAAC,EAAE,WAAW;AAAA,IAC7C,WAAO,qBAAK,EAAE,QAAQ;AAAA,IACtB,eAAW,qBAAK;AAAA,MACf,MAAM,CAAC,YAAY,aAAa,kBAAkB;AAAA,IACnD,CAAC,EAAE,QAAQ;AAAA,EACZ;AACD;AAEO,MAAM,UAAU;AAChB,MAAM,gBAAgB;", "names": []}