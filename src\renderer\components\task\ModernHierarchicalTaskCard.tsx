import React, { useState, useRef } from 'react'
import {
  Calendar,
  Star,
  MoreHorizontal,
  Plus,
  ChevronRight,
  ChevronDown,
  Circle,
  CheckCircle2,
  Edit3,
  Trash2,
  <PERSON><PERSON>,
  Archive
} from 'lucide-react'
import { But<PERSON> } from '../ui/button'
import { cn, formatDueDate, isTaskOverdue } from '../../lib/utils'
import { useUpdateTask, useDeleteTask, useSoftDeleteTask, useRestoreTask } from '../../hooks/useTasks'
import { useSelection } from '../../stores/selectionStore'
import { useLongPress } from '../../hooks/useLongPress'
import { createSelectionAnimation, InteractionFeedback } from '../../utils/animations'
import { useToastActions } from '../ui/toast'
import { Checkbox } from '../ui/checkbox'
import { DeleteConfirmationDialog } from '../ui/delete-confirmation-dialog'
import type { Task, TaskHierarchy } from '../../../shared/types/task'
import { TaskPriority, PRIORITY_COLORS } from '../../../shared/types/task'

interface ModernHierarchicalTaskCardProps {
  hierarchy: TaskHierarchy
  onToggleComplete?: (taskId: string) => void
  onAddSubtask?: (parentTaskId: string) => void
  onEdit?: (task: Task) => void
  onTaskClick?: (task: Task) => void
  className?: string
}

export function ModernHierarchicalTaskCard({
  hierarchy,
  onToggleComplete,
  onAddSubtask,
  onEdit,
  onTaskClick,
  className
}: ModernHierarchicalTaskCardProps) {
  const { task, children, depth } = hierarchy
  const [isExpanded, setIsExpanded] = useState(true)
  const [isHovered, setIsHovered] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  const updateTask = useUpdateTask()
  const deleteTask = useDeleteTask()
  const softDeleteTask = useSoftDeleteTask()
  const restoreTask = useRestoreTask()

  const {
    isSelectionMode,
    isTaskSelected,
    toggleTaskSelection,
    enterSelectionMode,
    addDeletedTask,
    removeDeletedTask
  } = useSelection()

  const { showDeleteSuccess, showError } = useToastActions()

  const isOverdue = isTaskOverdue(task.dueDate)
  const hasChildren = children.length > 0
  const completedChildren = children.filter(c => c.task.isCompleted).length
  const progress = hasChildren ? (completedChildren / children.length) * 100 : 0
  const isSelected = isTaskSelected(task.id)

  // 计算缩进 - 响应式设计
  const getIndentClass = (depth: number) => {
    const indentClasses = [
      '', // depth 0
      'ml-4 md:ml-6', // depth 1: 16px mobile, 24px desktop
      'ml-8 md:ml-12', // depth 2: 32px mobile, 48px desktop
      'ml-12 md:ml-18', // depth 3: 48px mobile, 72px desktop
      'ml-16 md:ml-24', // depth 4: 64px mobile, 96px desktop
    ]
    return indentClasses[depth] || indentClasses[4]
  }

  const longPressProps = useLongPress({
    onLongPress: handleLongPress,
    onClick: handleCardClick,
    threshold: 500
  })

  function handleToggleComplete() {
    if (isSelectionMode) {
      toggleTaskSelection(task.id)
    } else {
      onToggleComplete?.(task.id)
    }
  }

  function handleCardClick() {
    if (isSelectionMode) {
      toggleTaskSelection(task.id)
      // 添加选择动画反馈
      if (cardRef.current) {
        createSelectionAnimation(cardRef.current, !isSelected)
      }
    } else {
      // 点击任务卡片打开详情面板
      onTaskClick?.(task)
    }
  }

  function handleLongPress() {
    if (!isSelectionMode) {
      enterSelectionMode()
      toggleTaskSelection(task.id)
    }
  }

  function handleAddSubtask() {
    onAddSubtask?.(task.id)
  }

  function handleDelete() {
    setShowDeleteDialog(true)
  }

  async function handleConfirmDelete() {
    setIsDeleting(true)

    try {
      // 添加删除动画
      if (cardRef.current) {
        InteractionFeedback.buttonClick(cardRef.current)
      }

      // 执行软删除
      await softDeleteTask.mutateAsync(task.id)

      // 添加到撤销列表
      addDeletedTask({
        id: task.id,
        content: task.content,
        deletedAt: Date.now()
      })

      // 显示成功提示和撤销选项
      showDeleteSuccess(task.content, async () => {
        try {
          // 先从撤销列表中移除，防止重复操作
          removeDeletedTask(task.id)

          await restoreTask.mutateAsync(task.id)
          showDeleteSuccess('撤销成功', () => {})
        } catch (error) {
          console.error('Failed to restore task:', error)

          let errorMessage = '无法恢复已删除的任务'
          if (error instanceof Error) {
            if (error.message.includes('任务不存在或未被删除')) {
              errorMessage = '该任务可能已经被恢复'
            } else {
              errorMessage = error.message
            }
          }

          showError('撤销失败', errorMessage)
        }
      })

    } catch (error) {
      console.error('Failed to delete task:', error)
      showError('删除失败', error instanceof Error ? error.message : '删除任务时发生错误')
    } finally {
      setIsDeleting(false)
      setShowDeleteDialog(false)
    }
  }

  function getPriorityColor(priority: number) {
    return PRIORITY_COLORS[priority] || 'bg-gray-100'
  }

  return (
    <div
      className={cn(
        "w-full",
        getIndentClass(depth),
        className
      )}
    >
      {/* 主任务卡片 */}
      <div
        ref={cardRef}
        {...longPressProps}
        className={cn(
          "group bg-card rounded-xl border border-border/50 transition-all duration-200 hover:border-border hover:shadow-md hover:shadow-primary/5 hover:-translate-y-0.5",
          task.isCompleted && "opacity-75",
          isOverdue && !task.isCompleted && "border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20",
          isSelectionMode && "cursor-pointer",
          isSelected && "border-primary bg-primary/5 shadow-md transform scale-[0.98]",
          isDeleting && "opacity-50 pointer-events-none",
          depth > 0 && "bg-muted/30 border-muted hover:bg-muted/40"
        )}
        onMouseEnter={() => {
          setIsHovered(true)
          if (cardRef.current && !isSelectionMode) {
            InteractionFeedback.hover(cardRef.current, true)
          }
        }}
        onMouseLeave={() => {
          setIsHovered(false)
          if (cardRef.current && !isSelectionMode) {
            InteractionFeedback.hover(cardRef.current, false)
          }
        }}
      >
        <div className="flex items-start gap-3 md:gap-4 p-3 md:p-4">
          {/* 展开/收起按钮 */}
          <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
            {hasChildren ? (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="w-full h-full flex items-center justify-center hover:bg-gray-100 rounded transition-all duration-200"
                title={isExpanded ? "收起子任务" : "展开子任务"}
              >
                <ChevronRight className={cn(
                  "w-4 h-4 text-gray-500 transition-transform duration-200",
                  isExpanded && "rotate-90"
                )} />
              </button>
            ) : (
              <div className="w-4 h-4" />
            )}
          </div>

          {/* 完成状态按钮或选择复选框 */}
          {isSelectionMode ? (
            <Checkbox
              checked={isSelected}
              onCheckedChange={() => toggleTaskSelection(task.id)}
              className="mt-1"
            />
          ) : (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleToggleComplete}
              className="p-0 h-6 w-6 rounded-full hover:bg-transparent"
            >
              {task.isCompleted ? (
                <CheckCircle2 className="h-6 w-6 text-green-600 fill-green-100" />
              ) : (
                <Circle className="h-6 w-6 text-muted-foreground hover:text-primary transition-colors" />
              )}
            </Button>
          )}

          {/* 任务内容 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <div className="flex-1 min-w-0">
                <h3
                  className={cn(
                    "text-sm md:text-base font-medium text-foreground cursor-pointer hover:text-primary transition-colors",
                    task.isCompleted && "line-through text-muted-foreground",
                    depth > 0 && "text-xs md:text-sm"
                  )}
                  onClick={handleCardClick}
                >
                  {task.content}
                </h3>
                
                {/* 任务元信息 */}
                <div className="flex items-center gap-3 mt-2">
                  {/* 优先级指示器 */}
                  {task.priority !== TaskPriority.MEDIUM && (
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      getPriorityColor(task.priority)
                    )} />
                  )}
                  
                  {/* 截止日期 */}
                  {task.dueDate && (
                    <div className={cn(
                      "flex items-center gap-1 text-xs px-2 py-1 rounded-md",
                      isOverdue && !task.isCompleted 
                        ? "text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-950/30" 
                        : "text-muted-foreground bg-muted/50"
                    )}>
                      <Calendar className="h-3 w-3" />
                      <span>{formatDueDate(task.dueDate)}</span>
                    </div>
                  )}

                  {/* 子任务进度 */}
                  {hasChildren && (
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <div className="w-12 h-1.5 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-primary transition-all duration-300"
                            style={{ width: `${progress}%` }}
                          />
                        </div>
                        <span>{completedChildren}/{children.length}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                {/* 添加子任务按钮 */}
                {onAddSubtask && depth < 4 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleAddSubtask}
                    className="p-1 h-6 w-6"
                    title="添加子任务"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                )}

                {/* 编辑按钮 */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1 h-6 w-6"
                  onClick={() => onEdit?.(task)}
                  title="编辑任务"
                >
                  <Edit3 className="h-3 w-3" />
                </Button>

                {/* 删除按钮 */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="p-1 h-6 w-6 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                  onClick={handleDelete}
                  title="删除任务"
                  disabled={isDeleting}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 子任务列表 - 带动画 */}
      {hasChildren && (
        <div className={cn(
          "overflow-hidden transition-all duration-300 ease-in-out",
          isExpanded ? "max-h-[2000px] opacity-100 mt-3" : "max-h-0 opacity-0 mt-0"
        )}>
          <div className="space-y-3">
            {children.map((childHierarchy) => (
              <ModernHierarchicalTaskCard
                key={childHierarchy.task.id}
                hierarchy={childHierarchy}
                onToggleComplete={onToggleComplete}
                onAddSubtask={onAddSubtask}
                onEdit={onEdit}
                onTaskClick={onTaskClick}
              />
            ))}
          </div>
        </div>
      )}

      {/* 删除确认对话框 */}
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleConfirmDelete}
        itemName={task.content}
        isLoading={isDeleting}
        description={hasChildren ?
          `确定要删除"${task.content}"吗？这将同时删除 ${children.length} 个子任务。此操作可在30秒内撤销。` :
          `确定要删除"${task.content}"吗？此操作可在30秒内撤销。`
        }
      />
    </div>
  )
}
