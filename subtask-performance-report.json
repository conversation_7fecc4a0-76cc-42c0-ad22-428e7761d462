{"dataGeneration": {"medium": {"totalTasks": 550, "duration": 48, "parentIds": []}, "large": {"totalTasks": 2100, "duration": 154, "parentIds": []}, "deep": {"duration": 10}}, "queries": {"medium": {"getSubTasks": {"avgTime": 0.2, "minTime": 0, "maxTime": 1, "iterations": 5}, "getSubTaskStats": {"avgTime": 0.2, "minTime": 0, "maxTime": 1, "iterations": 5}, "getCompletedSubTasks": {"avgTime": 0.2, "minTime": 0, "maxTime": 1, "iterations": 5}, "getSubTasksByPriority": {"avgTime": 0, "minTime": 0, "maxTime": 0, "iterations": 5}, "getHierarchyLevel": {"avgTime": 0.2, "minTime": 0, "maxTime": 1, "iterations": 5}, "batchSubTaskStats": {"avgTime": 0.2, "minTime": 0, "maxTime": 1, "iterations": 5}}, "large": {"getSubTasks": {"avgTime": 0, "minTime": 0, "maxTime": 0, "iterations": 5}, "getSubTaskStats": {"avgTime": 0, "minTime": 0, "maxTime": 0, "iterations": 5}, "getCompletedSubTasks": {"avgTime": 0, "minTime": 0, "maxTime": 0, "iterations": 5}, "getSubTasksByPriority": {"avgTime": 0.2, "minTime": 0, "maxTime": 1, "iterations": 5}, "getHierarchyLevel": {"avgTime": 0, "minTime": 0, "maxTime": 0, "iterations": 5}, "batchSubTaskStats": {"avgTime": 0.2, "minTime": 0, "maxTime": 1, "iterations": 5}}}, "operations": {"batchCreate": 4, "batchUpdate": 1, "cascadeDelete": 6}}