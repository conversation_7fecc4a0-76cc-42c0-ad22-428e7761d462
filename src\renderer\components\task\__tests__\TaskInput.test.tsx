/**
 * TaskInput 组件集成测试
 * 测试任务输入组件的表单功能和验证
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { TaskInput } from '../TaskInput'
import { 
  mockCreateTaskInput,
  mockElectronAPI,
  resetAllMocks,
  createMockTask 
} from '../../../__tests__/mocks'
import { renderWithProviders } from '../../../__tests__/test-utils'
import { TaskPriority } from '../../../../shared/types/task'

describe('TaskInput', () => {
  beforeEach(() => {
    resetAllMocks()
  })

  describe('Rendering', () => {
    it('should render input form elements', () => {
      renderWithProviders(<TaskInput />)
      
      expect(screen.getByPlaceholderText('输入任务内容...')).toBeInTheDocument()
      expect(screen.getByLabelText('优先级')).toBeInTheDocument()
      expect(screen.getByLabelText('截止日期')).toBeInTheDocument()
      expect(screen.getByText('添加任务')).toBeInTheDocument()
    })

    it('should render with default values', () => {
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const prioritySelect = screen.getByLabelText('优先级')
      const dueDateInput = screen.getByLabelText('截止日期')
      
      expect(contentInput).toHaveValue('')
      expect(prioritySelect).toHaveValue('2') // 中等优先级
      expect(dueDateInput).toHaveValue('')
    })

    it('should show cancel button when in editing mode', () => {
      renderWithProviders(<TaskInput isEditing={true} />)
      
      expect(screen.getByText('取消')).toBeInTheDocument()
    })

    it('should not show cancel button when not editing', () => {
      renderWithProviders(<TaskInput />)
      
      expect(screen.queryByText('取消')).not.toBeInTheDocument()
    })
  })

  describe('Form Interactions', () => {
    it('should update content input value', async () => {
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      
      fireEvent.change(contentInput, { target: { value: '新任务内容' } })
      
      expect(contentInput).toHaveValue('新任务内容')
    })

    it('should update priority selection', async () => {
      renderWithProviders(<TaskInput />)
      
      const prioritySelect = screen.getByLabelText('优先级')
      
      fireEvent.change(prioritySelect, { target: { value: '1' } })
      
      expect(prioritySelect).toHaveValue('1')
    })

    it('should update due date input', async () => {
      renderWithProviders(<TaskInput />)
      
      const dueDateInput = screen.getByLabelText('截止日期')
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      const dateString = tomorrow.toISOString().split('T')[0]
      
      fireEvent.change(dueDateInput, { target: { value: dateString } })
      
      expect(dueDateInput).toHaveValue(dateString)
    })
  })

  describe('Form Submission', () => {
    it('should create task on form submission', async () => {
      const newTask = createMockTask({ content: '新任务' })
      mockElectronAPI.task.create.mockResolvedValue(newTask)
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '新任务' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(mockElectronAPI.task.create).toHaveBeenCalledWith({
          content: '新任务',
          priority: TaskPriority.MEDIUM,
          dueDate: undefined,
          parentTaskId: null,
          taskType: 'task',
          description: null,
          estimatedDuration: null,
          progress: 0,
        })
      })
    })

    it('should create task with all fields filled', async () => {
      const newTask = createMockTask({ 
        content: '完整任务',
        priority: TaskPriority.HIGH 
      })
      mockElectronAPI.task.create.mockResolvedValue(newTask)
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const prioritySelect = screen.getByLabelText('优先级')
      const dueDateInput = screen.getByLabelText('截止日期')
      const submitButton = screen.getByText('添加任务')
      
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      const dateString = tomorrow.toISOString().split('T')[0]
      
      fireEvent.change(contentInput, { target: { value: '完整任务' } })
      fireEvent.change(prioritySelect, { target: { value: '1' } })
      fireEvent.change(dueDateInput, { target: { value: dateString } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(mockElectronAPI.task.create).toHaveBeenCalledWith({
          content: '完整任务',
          priority: TaskPriority.HIGH,
          dueDate: new Date(dateString).getTime(),
          parentTaskId: null,
          taskType: 'task',
          description: null,
          estimatedDuration: null,
          progress: 0,
        })
      })
    })

    it('should clear form after successful submission', async () => {
      const newTask = createMockTask({ content: '新任务' })
      mockElectronAPI.task.create.mockResolvedValue(newTask)
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const prioritySelect = screen.getByLabelText('优先级')
      const dueDateInput = screen.getByLabelText('截止日期')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '新任务' } })
      fireEvent.change(prioritySelect, { target: { value: '1' } })
      fireEvent.change(dueDateInput, { target: { value: '2024-12-31' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(contentInput).toHaveValue('')
        expect(prioritySelect).toHaveValue('2')
        expect(dueDateInput).toHaveValue('')
      })
    })

    it('should handle form submission with Enter key', async () => {
      const newTask = createMockTask({ content: '新任务' })
      mockElectronAPI.task.create.mockResolvedValue(newTask)
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      
      fireEvent.change(contentInput, { target: { value: '新任务' } })
      fireEvent.keyDown(contentInput, { key: 'Enter' })
      
      await waitFor(() => {
        expect(mockElectronAPI.task.create).toHaveBeenCalled()
      })
    })
  })

  describe('Form Validation', () => {
    it('should not submit with empty content', async () => {
      renderWithProviders(<TaskInput />)
      
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.click(submitButton)
      
      // 不应该调用 API
      expect(mockElectronAPI.task.create).not.toHaveBeenCalled()
    })

    it('should not submit with only whitespace content', async () => {
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '   ' } })
      fireEvent.click(submitButton)
      
      expect(mockElectronAPI.task.create).not.toHaveBeenCalled()
    })

    it('should trim whitespace from content', async () => {
      const newTask = createMockTask({ content: '任务内容' })
      mockElectronAPI.task.create.mockResolvedValue(newTask)
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '  任务内容  ' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(mockElectronAPI.task.create).toHaveBeenCalledWith(
          expect.objectContaining({
            content: '任务内容'
          })
        )
      })
    })

    it('should show validation error for empty content', async () => {
      renderWithProviders(<TaskInput />)
      
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.click(submitButton)
      
      expect(screen.getByText('任务内容不能为空')).toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    it('should show loading state during submission', async () => {
      // 模拟延迟响应
      mockElectronAPI.task.create.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(createMockTask()), 100))
      )
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '新任务' } })
      fireEvent.click(submitButton)
      
      // 检查加载状态
      expect(screen.getByText('创建中...')).toBeInTheDocument()
      expect(submitButton).toBeDisabled()
    })

    it('should disable form during submission', async () => {
      mockElectronAPI.task.create.mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(createMockTask()), 100))
      )
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const prioritySelect = screen.getByLabelText('优先级')
      const dueDateInput = screen.getByLabelText('截止日期')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '新任务' } })
      fireEvent.click(submitButton)
      
      expect(contentInput).toBeDisabled()
      expect(prioritySelect).toBeDisabled()
      expect(dueDateInput).toBeDisabled()
      expect(submitButton).toBeDisabled()
    })
  })

  describe('Error Handling', () => {
    it('should show error message on submission failure', async () => {
      const error = new Error('创建任务失败')
      mockElectronAPI.task.create.mockRejectedValue(error)
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '新任务' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText('创建任务失败，请重试')).toBeInTheDocument()
      })
    })

    it('should not clear form on submission failure', async () => {
      const error = new Error('创建任务失败')
      mockElectronAPI.task.create.mockRejectedValue(error)
      
      renderWithProviders(<TaskInput />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const submitButton = screen.getByText('添加任务')
      
      fireEvent.change(contentInput, { target: { value: '新任务' } })
      fireEvent.click(submitButton)
      
      await waitFor(() => {
        expect(screen.getByText('创建任务失败，请重试')).toBeInTheDocument()
      })
      
      // 表单内容应该保持
      expect(contentInput).toHaveValue('新任务')
    })
  })

  describe('Cancel Functionality', () => {
    it('should call onCancel when cancel button clicked', () => {
      const onCancel = vi.fn()
      renderWithProviders(<TaskInput isEditing={true} onCancel={onCancel} />)
      
      const cancelButton = screen.getByText('取消')
      fireEvent.click(cancelButton)
      
      expect(onCancel).toHaveBeenCalled()
    })

    it('should clear form when cancelled', () => {
      const onCancel = vi.fn()
      renderWithProviders(<TaskInput isEditing={true} onCancel={onCancel} />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      const cancelButton = screen.getByText('取消')
      
      fireEvent.change(contentInput, { target: { value: '测试内容' } })
      fireEvent.click(cancelButton)
      
      expect(contentInput).toHaveValue('')
    })

    it('should handle Escape key to cancel', () => {
      const onCancel = vi.fn()
      renderWithProviders(<TaskInput isEditing={true} onCancel={onCancel} />)
      
      const contentInput = screen.getByPlaceholderText('输入任务内容...')
      
      fireEvent.keyDown(contentInput, { key: 'Escape' })
      
      expect(onCancel).toHaveBeenCalled()
    })
  })
})
