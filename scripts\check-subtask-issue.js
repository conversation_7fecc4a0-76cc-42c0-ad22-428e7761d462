const path = require('path')
const fs = require('fs')
const { execSync } = require('child_process')

console.log('🔍 检查子任务显示问题')
console.log('='.repeat(50))

async function checkSubtaskIssue() {
  try {
    const dbPath = path.join(__dirname, '..', 'data', 'app.db')
    console.log(`📁 数据库路径: ${dbPath}`)
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ 数据库文件不存在')
      return
    }
    
    console.log('✅ 开始检查')
    
    // 1. 检查已完成任务的类型分布
    console.log('\n📊 1. 已完成任务类型分析')
    console.log('-'.repeat(30))
    
    const queries = [
      { 
        name: '已完成主任务', 
        query: "SELECT COUNT(*) FROM tasks WHERE isCompleted = 1 AND deletedAt IS NULL AND parentTaskId IS NULL;" 
      },
      { 
        name: '已完成子任务', 
        query: "SELECT COUNT(*) FROM tasks WHERE isCompleted = 1 AND deletedAt IS NULL AND parentTaskId IS NOT NULL;" 
      },
      { 
        name: '总已完成任务', 
        query: "SELECT COUNT(*) FROM tasks WHERE isCompleted = 1 AND deletedAt IS NULL;" 
      }
    ]
    
    const stats = {}
    queries.forEach(({ name, query }) => {
      const result = execSync(`sqlite3 "${dbPath}" "${query}"`, { encoding: 'utf8' }).trim()
      stats[name] = parseInt(result)
      console.log(`${name}: ${stats[name]}`)
    })
    
    // 2. 详细分析
    console.log('\n🔍 2. 问题分析')
    console.log('-'.repeat(30))
    
    if (stats['总已完成任务'] === 0) {
      console.log('ℹ️ 没有已完成的任务')
      return
    }
    
    if (stats['已完成主任务'] === 0 && stats['已完成子任务'] > 0) {
      console.log('🎯 问题确认: 所有已完成任务都是子任务！')
      console.log('📝 ModernTaskList 组件只显示主任务，所以已完成列表为空')
      
      // 显示已完成子任务的详情
      console.log('\n📋 已完成子任务详情:')
      const subtaskQuery = `
        SELECT t.id, t.content, t.parentTaskId, p.content as parentContent
        FROM tasks t
        LEFT JOIN tasks p ON t.parentTaskId = p.id
        WHERE t.isCompleted = 1 AND t.deletedAt IS NULL AND t.parentTaskId IS NOT NULL
        ORDER BY t.createdAt DESC;
      `
      
      const subtaskResult = execSync(`sqlite3 "${dbPath}" "${subtaskQuery}"`, { encoding: 'utf8' })
      const subtaskLines = subtaskResult.trim().split('\n').filter(line => line.trim())
      
      subtaskLines.forEach((line, index) => {
        const parts = line.split('|')
        if (parts.length >= 4) {
          const [id, content, parentTaskId, parentContent] = parts
          console.log(`${index + 1}. [${id.slice(-8)}] ${content}`)
          console.log(`   父任务: [${parentTaskId.slice(-8)}] ${parentContent || '(已删除)'}`)
        }
      })
      
    } else if (stats['已完成主任务'] > 0) {
      console.log('✅ 有已完成的主任务，应该能正常显示')
      
      // 显示已完成主任务的详情
      console.log('\n📋 已完成主任务详情:')
      const mainTaskQuery = `
        SELECT id, content, createdAt
        FROM tasks 
        WHERE isCompleted = 1 AND deletedAt IS NULL AND parentTaskId IS NULL
        ORDER BY createdAt DESC;
      `
      
      const mainTaskResult = execSync(`sqlite3 "${dbPath}" "${mainTaskQuery}"`, { encoding: 'utf8' })
      const mainTaskLines = mainTaskResult.trim().split('\n').filter(line => line.trim())
      
      mainTaskLines.forEach((line, index) => {
        const parts = line.split('|')
        if (parts.length >= 3) {
          const [id, content, createdAt] = parts
          console.log(`${index + 1}. [${id.slice(-8)}] ${content}`)
          console.log(`   创建时间: ${new Date(parseInt(createdAt)).toLocaleString()}`)
        }
      })
    }
    
    // 3. 解决方案建议
    console.log('\n💡 3. 解决方案')
    console.log('-'.repeat(30))
    
    if (stats['已完成主任务'] === 0 && stats['已完成子任务'] > 0) {
      console.log('🔧 方案1: 修改前端逻辑，在已完成视图中也显示子任务')
      console.log('   - 修改 ModernTaskList.tsx 中的过滤逻辑')
      console.log('   - 在 completed 视图中不过滤子任务')
      console.log('')
      console.log('🔧 方案2: 将已完成的子任务提升为主任务')
      console.log('   - 清除已完成子任务的 parentTaskId')
      console.log('   - 使其在主任务列表中显示')
      console.log('')
      console.log('🔧 方案3: 使用层级视图显示所有任务')
      console.log('   - 切换到层级视图模式')
      console.log('   - 显示完整的任务层级结构')
    }
    
    // 4. 提供修复代码
    if (stats['已完成主任务'] === 0 && stats['已完成子任务'] > 0) {
      console.log('\n📝 4. 推荐修复代码')
      console.log('-'.repeat(30))
      console.log('修改 src/renderer/components/task/ModernTaskList.tsx:')
      console.log('')
      console.log('// 在 case "completed" 分支中，不过滤子任务')
      console.log('case "completed":')
      console.log('  // 对于已完成视图，显示所有已完成任务（包括子任务）')
      console.log('  filtered = tasks.filter(task => task.isCompleted)')
      console.log('  break')
      console.log('')
      console.log('// 或者在初始过滤中添加条件')
      console.log('let filtered = tasks.filter(task => {')
      console.log('  // 已完成视图显示所有任务，其他视图只显示主任务')
      console.log('  return activeView === "completed" || !task.parentTaskId')
      console.log('})')
    }
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error.message)
  }
}

// 运行检查
checkSubtaskIssue().then(() => {
  console.log('\n🎉 检查完成')
}).catch(error => {
  console.error('❌ 检查失败:', error)
  process.exit(1)
})
