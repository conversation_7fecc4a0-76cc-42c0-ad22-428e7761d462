{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 7}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 6}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 6}], "isBlockCoverage": true}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 4}], "isBlockCoverage": true}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 11}], "isBlockCoverage": true}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5133, "endOffset": 5235, "count": 102}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5283, "endOffset": 5385, "count": 34}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5438, "endOffset": 5540, "count": 34}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 17}], "isBlockCoverage": true}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 3}], "isBlockCoverage": true}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 1}], "isBlockCoverage": true}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 2}], "isBlockCoverage": true}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 2}], "isBlockCoverage": true}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 2}], "isBlockCoverage": true}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 2}], "isBlockCoverage": true}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 1}], "isBlockCoverage": true}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 14}], "isBlockCoverage": true}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/hooks/__tests__/useTasks.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 43685, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 43685, "count": 1}], "isBlockCoverage": true}, {"functionName": "createWrapper", "ranges": [{"startOffset": 1391, "endOffset": 1802, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1799, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1846, "endOffset": 16811, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1908, "endOffset": 2027, "count": 17}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2080, "endOffset": 4287, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2152, "endOffset": 3039, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2315, "endOffset": 2353, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2588, "endOffset": 2678, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3106, "endOffset": 3778, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3297, "endOffset": 3335, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3424, "endOffset": 3512, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3853, "endOffset": 4281, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3987, "endOffset": 4025, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4114, "endOffset": 4204, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4344, "endOffset": 5618, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4421, "endOffset": 5033, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4588, "endOffset": 4630, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4719, "endOffset": 4809, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5100, "endOffset": 5612, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5312, "endOffset": 5354, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5443, "endOffset": 5531, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5676, "endOffset": 8468, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5748, "endOffset": 6648, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6121, "endOffset": 6164, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6325, "endOffset": 6415, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6715, "endOffset": 7284, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6911, "endOffset": 6954, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7115, "endOffset": 7203, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7369, "endOffset": 8462, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_2__.renderHook.wrapper.children.children", "ranges": [{"startOffset": 7713, "endOffset": 7756, "count": 2}], "isBlockCoverage": true}, {"functionName": "wrapper", "ranges": [{"startOffset": 7777, "endOffset": 8106, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8231, "endOffset": 8321, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8526, "endOffset": 11479, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8598, "endOffset": 9484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8896, "endOffset": 8939, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9147, "endOffset": 9237, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9551, "endOffset": 10181, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9761, "endOffset": 9804, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10012, "endOffset": 10100, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10258, "endOffset": 11473, "count": 1}, {"startOffset": 11371, "endOffset": 11377, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_2__.renderHook.wrapper.children.children", "ranges": [{"startOffset": 10632, "endOffset": 10675, "count": 2}], "isBlockCoverage": true}, {"functionName": "wrapper", "ranges": [{"startOffset": 10696, "endOffset": 11025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11197, "endOffset": 11287, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11378, "endOffset": 11402, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 11537, "endOffset": 13880, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11609, "endOffset": 12205, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11750, "endOffset": 11793, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11921, "endOffset": 12011, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12272, "endOffset": 12822, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12482, "endOffset": 12525, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12653, "endOffset": 12741, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12901, "endOffset": 13874, "count": 1}, {"startOffset": 13777, "endOffset": 13783, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_2__.renderHook.wrapper.children.children", "ranges": [{"startOffset": 13118, "endOffset": 13161, "count": 2}], "isBlockCoverage": true}, {"functionName": "wrapper", "ranges": [{"startOffset": 13182, "endOffset": 13511, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13603, "endOffset": 13693, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13784, "endOffset": 13808, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 13940, "endOffset": 16807, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14014, "endOffset": 14743, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14279, "endOffset": 14324, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14455, "endOffset": 14545, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 14812, "endOffset": 15492, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15025, "endOffset": 15070, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15323, "endOffset": 15411, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 15567, "endOffset": 16801, "count": 1}, {"startOffset": 16573, "endOffset": 16579, "count": 0}, {"startOffset": 16632, "endOffset": 16638, "count": 0}, {"startOffset": 16706, "endOffset": 16718, "count": 0}, {"startOffset": 16771, "endOffset": 16783, "count": 0}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_2__.renderHook.wrapper.children.children", "ranges": [{"startOffset": 15908, "endOffset": 15953, "count": 2}], "isBlockCoverage": true}, {"functionName": "wrapper", "ranges": [{"startOffset": 15974, "endOffset": 16303, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16398, "endOffset": 16488, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 16580, "endOffset": 16604, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 16639, "endOffset": 16663, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1616", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/hooks/useTasks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13931, "count": 1}], "isBlockCoverage": true}, {"functionName": "useTasks", "ranges": [{"startOffset": 612, "endOffset": 787, "count": 6}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 884, "endOffset": 908, "count": 6}], "isBlockCoverage": true}, {"functionName": "useTaskStats", "ranges": [{"startOffset": 912, "endOffset": 1097, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1198, "endOffset": 1226, "count": 4}], "isBlockCoverage": true}, {"functionName": "useCreateTask", "ranges": [{"startOffset": 1230, "endOffset": 1870, "count": 6}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 1380, "endOffset": 1434, "count": 3}], "isBlockCoverage": true}, {"functionName": "onSuccess", "ranges": [{"startOffset": 1451, "endOffset": 1743, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1537, "endOffset": 1640, "count": 2}, {"startOffset": 1576, "endOffset": 1639, "count": 1}], "isBlockCoverage": true}, {"functionName": "onError", "ranges": [{"startOffset": 1758, "endOffset": 1862, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1972, "endOffset": 2001, "count": 6}], "isBlockCoverage": true}, {"functionName": "useUpdateTask", "ranges": [{"startOffset": 2005, "endOffset": 2734, "count": 6}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 2155, "endOffset": 2221, "count": 3}], "isBlockCoverage": true}, {"functionName": "onSuccess", "ranges": [{"startOffset": 2238, "endOffset": 2607, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2328, "endOffset": 2504, "count": 2}, {"startOffset": 2367, "endOffset": 2503, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2428, "endOffset": 2485, "count": 5}, {"startOffset": 2465, "endOffset": 2478, "count": 1}, {"startOffset": 2479, "endOffset": 2485, "count": 4}], "isBlockCoverage": true}, {"functionName": "onError", "ranges": [{"startOffset": 2622, "endOffset": 2726, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2836, "endOffset": 2865, "count": 6}], "isBlockCoverage": true}, {"functionName": "useDeleteTask", "ranges": [{"startOffset": 2869, "endOffset": 3527, "count": 6}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 3019, "endOffset": 3067, "count": 3}], "isBlockCoverage": true}, {"functionName": "onSuccess", "ranges": [{"startOffset": 3084, "endOffset": 3400, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3175, "endOffset": 3297, "count": 2}, {"startOffset": 3214, "endOffset": 3296, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3256, "endOffset": 3287, "count": 5}], "isBlockCoverage": true}, {"functionName": "onError", "ranges": [{"startOffset": 3415, "endOffset": 3519, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3629, "endOffset": 3658, "count": 6}], "isBlockCoverage": true}, {"functionName": "useReorderTasks", "ranges": [{"startOffset": 3662, "endOffset": 4873, "count": 6}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 3814, "endOffset": 3881, "count": 3}], "isBlockCoverage": true}, {"functionName": "onMutate", "ranges": [{"startOffset": 3897, "endOffset": 4561, "count": 3}, {"startOffset": 4134, "endOffset": 4523, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4187, "endOffset": 4223, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4274, "endOffset": 4381, "count": 5}, {"startOffset": 4352, "endOffset": 4370, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4388, "endOffset": 4425, "count": 8}], "isBlockCoverage": true}, {"functionName": "onError", "ranges": [{"startOffset": 4576, "endOffset": 4744, "count": 1}, {"startOffset": 4632, "endOffset": 4738, "count": 0}], "isBlockCoverage": true}, {"functionName": "onSettled", "ranges": [{"startOffset": 4761, "endOffset": 4865, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 4977, "endOffset": 5008, "count": 6}], "isBlockCoverage": true}]}, {"scriptId": "1617", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3422, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 309, "endOffset": 328, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAll", "ranges": [{"startOffset": 360, "endOffset": 383, "count": 3}], "isBlockCoverage": true}, {"functionName": "create", "ranges": [{"startOffset": 395, "endOffset": 428, "count": 3}], "isBlockCoverage": true}, {"functionName": "update", "ranges": [{"startOffset": 440, "endOffset": 481, "count": 3}], "isBlockCoverage": true}, {"functionName": "delete", "ranges": [{"startOffset": 493, "endOffset": 520, "count": 3}], "isBlockCoverage": true}, {"functionName": "reorder", "ranges": [{"startOffset": 533, "endOffset": 565, "count": 3}], "isBlockCoverage": true}, {"functionName": "getStats", "ranges": [{"startOffset": 579, "endOffset": 604, "count": 2}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 703, "endOffset": 726, "count": 22}], "isBlockCoverage": true}, {"functionName": "getVersion", "ranges": [{"startOffset": 761, "endOffset": 787, "count": 0}], "isBlockCoverage": false}, {"functionName": "quit", "ranges": [{"startOffset": 797, "endOffset": 817, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 915, "endOffset": 937, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 970, "endOffset": 1000, "count": 0}], "isBlockCoverage": false}, {"functionName": "set", "ranges": [{"startOffset": 1009, "endOffset": 1053, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1156, "endOffset": 1183, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1618", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/queryClient.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 3657, "count": 1}], "isBlockCoverage": true}, {"functionName": "retry<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 597, "endOffset": 653, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 964, "endOffset": 991, "count": 0}], "isBlockCoverage": false}, {"functionName": "settings", "ranges": [{"startOffset": 1081, "endOffset": 1128, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1264, "endOffset": 1290, "count": 35}], "isBlockCoverage": true}]}, {"scriptId": "1619", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/test-utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14108, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTestQueryClient", "ranges": [{"startOffset": 1029, "endOffset": 1433, "count": 30}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1543, "endOffset": 1580, "count": 30}], "isBlockCoverage": true}, {"functionName": "TestWrapper", "ranges": [{"startOffset": 1584, "endOffset": 2468, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithProviders", "ranges": [{"startOffset": 2469, "endOffset": 3001, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3109, "endOffset": 3144, "count": 0}], "isBlockCoverage": false}, {"functionName": "waitF<PERSON><PERSON><PERSON><PERSON><PERSON>oSettle", "ranges": [{"startOffset": 3148, "endOffset": 3624, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3733, "endOffset": 3769, "count": 0}], "isBlockCoverage": false}, {"functionName": "click", "ranges": [{"startOffset": 3802, "endOffset": 3971, "count": 0}], "isBlockCoverage": false}, {"functionName": "type", "ranges": [{"startOffset": 3981, "endOffset": 4186, "count": 0}], "isBlockCoverage": false}, {"functionName": "clear", "ranges": [{"startOffset": 4197, "endOffset": 4394, "count": 0}], "isBlockCoverage": false}, {"functionName": "keyDown", "ranges": [{"startOffset": 4407, "endOffset": 4592, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4693, "endOffset": 4718, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeInDocument", "ranges": [{"startOffset": 4761, "endOffset": 4820, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveTextContent", "ranges": [{"startOffset": 4843, "endOffset": 4912, "count": 0}], "isBlockCoverage": false}, {"functionName": "toHaveValue", "ranges": [{"startOffset": 4929, "endOffset": 4994, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeDisabled", "ranges": [{"startOffset": 5012, "endOffset": 5066, "count": 0}], "isBlockCoverage": false}, {"functionName": "toBeEnabled", "ranges": [{"startOffset": 5083, "endOffset": 5136, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5238, "endOffset": 5264, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5639, "endOffset": 5679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5778, "endOffset": 5813, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1620", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/contexts/ThemeContext.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26580, "count": 1}], "isBlockCoverage": true}, {"functionName": "useSystemTheme", "ranges": [{"startOffset": 885, "endOffset": 1405, "count": 0}], "isBlockCoverage": false}, {"functionName": "applyThemeToDOM", "ranges": [{"startOffset": 1431, "endOffset": 1994, "count": 0}], "isBlockCoverage": false}, {"functionName": "ThemeProvider", "ranges": [{"startOffset": 2018, "endOffset": 7343, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7446, "endOffset": 7475, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTheme", "ranges": [{"startOffset": 7496, "endOffset": 7690, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7788, "endOffset": 7812, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7917, "endOffset": 7945, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1621", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1399, "endOffset": 1432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1854, "endOffset": 1881, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2496, "endOffset": 2529, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7696, "endOffset": 7727, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8083, "endOffset": 8119, "count": 0}], "isBlockCoverage": false}, {"functionName": "getThemeById", "ranges": [{"startOffset": 8144, "endOffset": 8214, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8316, "endOffset": 8344, "count": 0}], "isBlockCoverage": false}, {"functionName": "getThemesByMode", "ranges": [{"startOffset": 8372, "endOffset": 8450, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8555, "endOffset": 8586, "count": 0}], "isBlockCoverage": false}, {"functionName": "isValidThemeId", "ranges": [{"startOffset": 8613, "endOffset": 8683, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8787, "endOffset": 8817, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1622", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/components/ui/toast.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19596, "count": 1}], "isBlockCoverage": true}, {"functionName": "ToastProvider", "ranges": [{"startOffset": 857, "endOffset": 1996, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2098, "endOffset": 2127, "count": 0}], "isBlockCoverage": false}, {"functionName": "useToast", "ranges": [{"startOffset": 2131, "endOffset": 2329, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2426, "endOffset": 2450, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastContainer", "ranges": [{"startOffset": 2454, "endOffset": 3062, "count": 0}], "isBlockCoverage": false}, {"functionName": "ToastItem", "ranges": [{"startOffset": 3063, "endOffset": 7280, "count": 0}], "isBlockCoverage": false}, {"functionName": "useToastActions", "ranges": [{"startOffset": 7281, "endOffset": 8394, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8498, "endOffset": 8529, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1625", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9407, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 550, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 641, "endOffset": 659, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDate", "ranges": [{"startOffset": 663, "endOffset": 1181, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1280, "endOffset": 1306, "count": 0}], "isBlockCoverage": false}, {"functionName": "formatDueDate", "ranges": [{"startOffset": 1310, "endOffset": 1882, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1984, "endOffset": 2013, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTaskOverdue", "ranges": [{"startOffset": 2017, "endOffset": 2113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2215, "endOffset": 2244, "count": 0}], "isBlockCoverage": false}, {"functionName": "debounce", "ranges": [{"startOffset": 2248, "endOffset": 2454, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2551, "endOffset": 2575, "count": 0}], "isBlockCoverage": false}, {"functionName": "throttle", "ranges": [{"startOffset": 2579, "endOffset": 2811, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2908, "endOffset": 2932, "count": 0}], "isBlockCoverage": false}, {"functionName": "generateOrderIndex", "ranges": [{"startOffset": 2936, "endOffset": 3207, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3314, "endOffset": 3348, "count": 0}], "isBlockCoverage": false}]}]}