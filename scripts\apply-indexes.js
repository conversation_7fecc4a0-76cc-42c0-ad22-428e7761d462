#!/usr/bin/env node

/**
 * 手动应用数据库索引优化脚本
 * 用于添加性能优化索引到现有数据库
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const dbPath = path.join(dataDir, 'app.db');

console.log('🚀 开始应用数据库索引优化...');

// 检查数据库是否存在
if (!fs.existsSync(dbPath)) {
  console.error('❌ 数据库文件不存在:', dbPath);
  process.exit(1);
}

// 连接数据库
const db = new Database(dbPath);

// 启用 WAL 模式
db.pragma('journal_mode = WAL');
db.pragma('foreign_keys = ON');

try {
  // 读取迁移文件
  const migrationPath = path.join(process.cwd(), 'src', 'shared', 'db', 'migrations', '0004_add_performance_indexes.sql');
  
  if (!fs.existsSync(migrationPath)) {
    console.error('❌ 迁移文件不存在:', migrationPath);
    process.exit(1);
  }

  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  
  console.log('📝 读取迁移文件:', migrationPath);
  
  // 开始事务
  const transaction = db.transaction(() => {
    // 分割 SQL 语句并执行
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📊 准备执行 ${statements.length} 个 SQL 语句...`);
    
    let successCount = 0;
    let skipCount = 0;
    
    for (const statement of statements) {
      try {
        if (statement.toLowerCase().includes('create index')) {
          // 检查索引是否已存在
          const indexName = statement.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
          if (indexName) {
            const existingIndex = db.prepare(
              "SELECT name FROM sqlite_master WHERE type='index' AND name=?"
            ).get(indexName);
            
            if (existingIndex) {
              console.log(`⏭️  索引已存在，跳过: ${indexName}`);
              skipCount++;
              continue;
            }
          }
        }
        
        db.exec(statement);
        
        if (statement.toLowerCase().includes('create index')) {
          const indexName = statement.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
          console.log(`✅ 创建索引: ${indexName}`);
        } else if (statement.toLowerCase().includes('analyze')) {
          const tableName = statement.match(/ANALYZE (\w+)/i)?.[1];
          console.log(`📈 分析表: ${tableName}`);
        }
        
        successCount++;
      } catch (error) {
        console.warn(`⚠️  执行语句失败 (可能已存在): ${statement.substring(0, 50)}...`);
        console.warn(`   错误: ${error.message}`);
        skipCount++;
      }
    }
    
    console.log(`\n📊 执行结果:`);
    console.log(`   ✅ 成功: ${successCount} 个语句`);
    console.log(`   ⏭️  跳过: ${skipCount} 个语句`);
  });
  
  // 执行事务
  transaction();
  
  // 获取索引信息
  console.log('\n📋 当前数据库索引列表:');
  const indexes = db.prepare(
    "SELECT name, tbl_name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%' ORDER BY tbl_name, name"
  ).all();
  
  const indexesByTable = {};
  indexes.forEach(index => {
    if (!indexesByTable[index.tbl_name]) {
      indexesByTable[index.tbl_name] = [];
    }
    indexesByTable[index.tbl_name].push(index.name);
  });
  
  Object.entries(indexesByTable).forEach(([tableName, tableIndexes]) => {
    console.log(`   📊 ${tableName}: ${tableIndexes.length} 个索引`);
    tableIndexes.forEach(indexName => {
      console.log(`      - ${indexName}`);
    });
  });
  
  // 获取数据库统计信息
  console.log('\n📈 数据库统计信息:');
  try {
    const taskCount = db.prepare("SELECT COUNT(*) as count FROM tasks").get();
    console.log(`   📝 任务数量: ${taskCount.count}`);
    
    const dbSize = fs.statSync(dbPath).size;
    console.log(`   💾 数据库大小: ${(dbSize / 1024 / 1024).toFixed(2)} MB`);
  } catch (error) {
    console.log(`   ⚠️  无法获取统计信息: ${error.message}`);
  }
  
  console.log('\n🎉 数据库索引优化完成！');
  
} catch (error) {
  console.error('❌ 应用索引时发生错误:', error);
  process.exit(1);
} finally {
  db.close();
}
