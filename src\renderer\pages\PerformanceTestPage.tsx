import React, { useState } from 'react'
import { Button } from '../components/ui/button'
import { performanceTestRunner, type PerformanceTestResult } from '../utils/performanceTest'
import { Play, BarChart3, Trash2, Download } from 'lucide-react'

export function PerformanceTestPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<PerformanceTestResult[]>([])
  const [selectedTest, setSelectedTest] = useState<string>('full')

  const runTest = async (testType: string) => {
    setIsRunning(true)
    try {
      let testResults: PerformanceTestResult[] = []

      switch (testType) {
        case 'full':
          testResults = await performanceTestRunner.runFullTestSuite()
          break
        case 'selection':
          const taskIds = Array.from({ length: 1000 }, (_, i) => `task-${i}`)
          const selectionResult = await performanceTestRunner.testBatchSelection(taskIds)
          testResults = [selectionResult]
          break
        case 'delete':
          const deleteIds = Array.from({ length: 500 }, (_, i) => `task-${i}`)
          const deleteResult = await performanceTestRunner.testBatchDelete(deleteIds)
          testResults = [deleteResult]
          break
        case 'restore':
          const restoreIds = Array.from({ length: 500 }, (_, i) => `task-${i}`)
          const restoreResult = await performanceTestRunner.testBatchRestore(restoreIds)
          testResults = [restoreResult]
          break
        case 'rendering':
          const renderResult = await performanceTestRunner.testTaskListRendering(1000)
          testResults = [renderResult]
          break
      }

      setResults(testResults)
    } catch (error) {
      console.error('Performance test failed:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const clearResults = () => {
    performanceTestRunner.clearResults()
    setResults([])
  }

  const exportResults = () => {
    const dataStr = JSON.stringify(results, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `performance-test-results-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const getOperationDisplayName = (operation: string) => {
    const names: Record<string, string> = {
      batch_selection: '批量选择',
      batch_delete: '批量删除',
      batch_restore: '批量恢复',
      task_list_rendering: '任务列表渲染'
    }
    return names[operation] || operation
  }

  const getPerformanceRating = (averageTime: number, operation: string) => {
    let threshold = 1 // 默认阈值 1ms
    
    if (operation === 'task_list_rendering') {
      threshold = 2
    } else if (operation === 'batch_selection') {
      threshold = 0.5
    }

    if (averageTime < threshold * 0.5) return { rating: '优秀', color: 'text-green-600' }
    if (averageTime < threshold) return { rating: '良好', color: 'text-blue-600' }
    if (averageTime < threshold * 2) return { rating: '一般', color: 'text-yellow-600' }
    return { rating: '需优化', color: 'text-red-600' }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-3 mb-6">
            <BarChart3 className="h-6 w-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">性能测试控制台</h1>
          </div>

          {/* 测试控制面板 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">测试类型</h3>
              <div className="space-y-2">
                {[
                  { value: 'full', label: '完整测试套件', desc: '测试所有操作的性能' },
                  { value: 'selection', label: '批量选择测试', desc: '测试1000个任务的选择性能' },
                  { value: 'delete', label: '批量删除测试', desc: '测试500个任务的删除性能' },
                  { value: 'restore', label: '批量恢复测试', desc: '测试500个任务的恢复性能' },
                  { value: 'rendering', label: '渲染性能测试', desc: '测试1000个任务的渲染性能' },
                ].map(test => (
                  <label key={test.value} className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="radio"
                      name="testType"
                      value={test.value}
                      checked={selectedTest === test.value}
                      onChange={(e) => setSelectedTest(e.target.value)}
                      className="mt-1"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{test.label}</div>
                      <div className="text-sm text-gray-500">{test.desc}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">操作</h3>
              <div className="space-y-3">
                <Button
                  onClick={() => runTest(selectedTest)}
                  disabled={isRunning}
                  className="w-full"
                >
                  <Play className="h-4 w-4 mr-2" />
                  {isRunning ? '测试运行中...' : '开始测试'}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={clearResults}
                  disabled={isRunning || results.length === 0}
                  className="w-full"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  清除结果
                </Button>
                
                <Button
                  variant="outline"
                  onClick={exportResults}
                  disabled={results.length === 0}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  导出结果
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-semibold text-gray-900">测试说明</h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>• 完整测试套件会测试10、50、100、500、1000个任务的性能</p>
                <p>• 单项测试只测试指定数量的任务</p>
                <p>• 结果包含执行时间和内存使用情况</p>
                <p>• 建议在生产环境配置下进行测试</p>
              </div>
            </div>
          </div>

          {/* 测试结果 */}
          {results.length > 0 && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">测试结果</h3>
              
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">操作类型</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">任务数量</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">总耗时 (ms)</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">平均耗时 (ms/项)</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">内存变化 (MB)</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">性能评级</th>
                    </tr>
                  </thead>
                  <tbody>
                    {results.map((result, index) => {
                      const rating = getPerformanceRating(result.averageTimePerItem, result.operation)
                      return (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="border border-gray-300 px-4 py-2">
                            {getOperationDisplayName(result.operation)}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {result.itemCount.toLocaleString()}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {result.duration.toFixed(2)}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {result.averageTimePerItem.toFixed(3)}
                          </td>
                          <td className="border border-gray-300 px-4 py-2">
                            {result.memoryUsage ? 
                              (result.memoryUsage.delta > 0 ? '+' : '') + result.memoryUsage.delta.toFixed(2) 
                              : 'N/A'
                            }
                          </td>
                          <td className={`border border-gray-300 px-4 py-2 font-medium ${rating.color}`}>
                            {rating.rating}
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>
              </div>

              {/* 性能图表占位符 */}
              <div className="bg-gray-100 rounded-lg p-8 text-center text-gray-500">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>性能图表功能开发中...</p>
                <p className="text-sm">将显示性能趋势和对比图表</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
