#!/usr/bin/env node

/**
 * 子任务系统功能正确性测试脚本
 * 验证子任务系统的各项功能是否正常工作
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');
const { ulid } = require('ulid');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const testDbPath = path.join(dataDir, 'test_subtask_functionality.db');

console.log('🧪 开始子任务系统功能测试...');

// 测试结果
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// 测试断言函数
function assert(condition, message) {
  if (condition) {
    console.log(`✅ ${message}`);
    testResults.passed++;
    testResults.tests.push({ name: message, status: 'PASSED' });
  } else {
    console.log(`❌ ${message}`);
    testResults.failed++;
    testResults.tests.push({ name: message, status: 'FAILED' });
  }
}

// 创建测试数据库
function createTestDatabase() {
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
  }

  const db = new Database(testDbPath);
  
  // 应用优化设置
  db.pragma('journal_mode = WAL');
  db.pragma('synchronous = NORMAL');
  db.pragma('cache_size = 10000');
  db.pragma('foreign_keys = ON');

  // 创建表结构
  db.exec(`
    CREATE TABLE tasks (
      id TEXT PRIMARY KEY,
      content TEXT NOT NULL,
      is_completed INTEGER DEFAULT 0 NOT NULL,
      priority INTEGER DEFAULT 2 NOT NULL,
      due_date INTEGER,
      order_index INTEGER NOT NULL,
      created_at INTEGER DEFAULT (unixepoch() * 1000) NOT NULL,
      deleted_at INTEGER,
      parent_task_id TEXT,
      task_type TEXT DEFAULT 'task' NOT NULL,
      description TEXT,
      estimated_duration INTEGER,
      actual_duration INTEGER,
      progress INTEGER DEFAULT 0 NOT NULL
    );
  `);

  // 应用子任务系统索引
  const indexes = [
    'CREATE INDEX tasks_order_index_idx ON tasks(order_index)',
    'CREATE INDEX tasks_is_completed_idx ON tasks(is_completed)',
    'CREATE INDEX tasks_parent_task_id_idx ON tasks(parent_task_id)',
    'CREATE INDEX tasks_deleted_at_idx ON tasks(deleted_at)',
    'CREATE INDEX tasks_parent_status_idx ON tasks(parent_task_id, is_completed)',
    'CREATE INDEX tasks_parent_type_idx ON tasks(parent_task_id, task_type)',
    'CREATE INDEX tasks_parent_order_idx ON tasks(parent_task_id, order_index)',
    'CREATE INDEX tasks_hierarchy_idx ON tasks(parent_task_id, deleted_at, is_completed, order_index)',
    'CREATE INDEX tasks_parent_stats_idx ON tasks(parent_task_id, is_completed, deleted_at)',
  ];

  indexes.forEach(indexSQL => {
    db.exec(indexSQL);
  });

  db.exec('ANALYZE tasks');

  return db;
}

// 测试基础子任务CRUD操作
function testSubTaskCRUD(db) {
  console.log('\n📝 测试子任务基础CRUD操作...');

  const parentTaskId = ulid();
  const subTaskId = ulid();
  const now = Date.now();

  // 创建父任务
  const insertParentStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  
  const parentResult = insertParentStmt.run(parentTaskId, '父任务', 0, 2, 1000, now, 'task');
  assert(parentResult.changes === 1, '创建父任务成功');

  // 创建子任务
  const insertSubStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, parent_task_id, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  const subResult = insertSubStmt.run(subTaskId, '子任务', 0, 2, 2000, now, parentTaskId, 'subtask');
  assert(subResult.changes === 1, '创建子任务成功');

  // 验证父子关系
  const selectSubStmt = db.prepare('SELECT * FROM tasks WHERE id = ?');
  const subTask = selectSubStmt.get(subTaskId);
  assert(subTask && subTask.parent_task_id === parentTaskId, '子任务父子关系正确');
  assert(subTask.task_type === 'subtask', '子任务类型正确');

  // 获取子任务列表
  const getSubTasksStmt = db.prepare(`
    SELECT * FROM tasks 
    WHERE parent_task_id = ? AND deleted_at IS NULL 
    ORDER BY order_index
  `);
  const subTasks = getSubTasksStmt.all(parentTaskId);
  assert(subTasks.length === 1, '获取子任务列表成功');
  assert(subTasks[0].id === subTaskId, '子任务ID正确');

  // 更新子任务
  const updateSubStmt = db.prepare('UPDATE tasks SET is_completed = ?, content = ? WHERE id = ?');
  const updateResult = updateSubStmt.run(1, '更新后的子任务', subTaskId);
  assert(updateResult.changes === 1, '更新子任务成功');

  // 验证更新结果
  const updatedSubTask = selectSubStmt.get(subTaskId);
  assert(updatedSubTask.is_completed === 1, '子任务状态更新正确');
  assert(updatedSubTask.content === '更新后的子任务', '子任务内容更新正确');

  // 软删除子任务
  const softDeleteStmt = db.prepare('UPDATE tasks SET deleted_at = ? WHERE id = ?');
  const deleteResult = softDeleteStmt.run(Date.now(), subTaskId);
  assert(deleteResult.changes === 1, '软删除子任务成功');

  // 验证软删除后子任务列表
  const subTasksAfterDelete = getSubTasksStmt.all(parentTaskId);
  assert(subTasksAfterDelete.length === 0, '软删除后子任务列表为空');
}

// 测试层级结构
function testTaskHierarchy(db) {
  console.log('\n🌳 测试任务层级结构...');

  const now = Date.now();
  const rootTaskId = ulid();
  const level1TaskId = ulid();
  const level2TaskId = ulid();
  const level3TaskId = ulid();

  // 创建多层级任务结构
  const insertStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, parent_task_id, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  // 根任务
  insertStmt.run(rootTaskId, '根任务', 0, 2, 1000, now, null, 'task');
  
  // 第一层子任务
  insertStmt.run(level1TaskId, '第一层子任务', 0, 2, 2000, now, rootTaskId, 'subtask');
  
  // 第二层子任务
  insertStmt.run(level2TaskId, '第二层子任务', 0, 2, 3000, now, level1TaskId, 'subtask');
  
  // 第三层子任务
  insertStmt.run(level3TaskId, '第三层子任务', 0, 2, 4000, now, level2TaskId, 'subtask');

  // 验证层级关系
  const getChildrenStmt = db.prepare(`
    SELECT id, content, parent_task_id 
    FROM tasks 
    WHERE parent_task_id = ? AND deleted_at IS NULL
  `);

  const rootChildren = getChildrenStmt.all(rootTaskId);
  assert(rootChildren.length === 1 && rootChildren[0].id === level1TaskId, '根任务的子任务正确');

  const level1Children = getChildrenStmt.all(level1TaskId);
  assert(level1Children.length === 1 && level1Children[0].id === level2TaskId, '第一层子任务的子任务正确');

  const level2Children = getChildrenStmt.all(level2TaskId);
  assert(level2Children.length === 1 && level2Children[0].id === level3TaskId, '第二层子任务的子任务正确');

  const level3Children = getChildrenStmt.all(level3TaskId);
  assert(level3Children.length === 0, '第三层子任务没有子任务');

  // 测试层级深度计算
  function getTaskDepth(taskId) {
    let depth = 0;
    let currentTaskId = taskId;
    const getParentStmt = db.prepare('SELECT parent_task_id FROM tasks WHERE id = ? AND deleted_at IS NULL');

    while (currentTaskId) {
      const task = getParentStmt.get(currentTaskId);
      if (!task || !task.parent_task_id) {
        break;
      }
      depth++;
      currentTaskId = task.parent_task_id;
      if (depth > 10) break; // 防止无限循环
    }
    return depth;
  }

  assert(getTaskDepth(rootTaskId) === 0, '根任务深度为0');
  assert(getTaskDepth(level1TaskId) === 1, '第一层任务深度为1');
  assert(getTaskDepth(level2TaskId) === 2, '第二层任务深度为2');
  assert(getTaskDepth(level3TaskId) === 3, '第三层任务深度为3');
}

// 测试子任务统计
function testSubTaskStats(db) {
  console.log('\n📊 测试子任务统计功能...');

  const parentTaskId = ulid();
  const now = Date.now();

  // 创建父任务
  const insertParentStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  insertParentStmt.run(parentTaskId, '统计测试父任务', 0, 2, 1000, now, 'task');

  // 创建多个子任务
  const insertSubStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, parent_task_id, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const subTaskIds = [];
  for (let i = 0; i < 5; i++) {
    const subTaskId = ulid();
    const isCompleted = i < 2 ? 1 : 0; // 前两个完成，后三个未完成
    insertSubStmt.run(subTaskId, `子任务${i + 1}`, isCompleted, 2, (i + 1) * 1000, now, parentTaskId, 'subtask');
    subTaskIds.push(subTaskId);
  }

  // 测试子任务统计查询
  const statsQuery = `
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed,
      SUM(CASE WHEN is_completed = 0 THEN 1 ELSE 0 END) as pending
    FROM tasks 
    WHERE parent_task_id = ? AND deleted_at IS NULL
  `;

  const stats = db.prepare(statsQuery).get(parentTaskId);
  assert(stats.total === 5, '子任务总数统计正确');
  assert(stats.completed === 2, '已完成子任务数统计正确');
  assert(stats.pending === 3, '待完成子任务数统计正确');

  // 计算完成率
  const completionRate = Math.round((stats.completed / stats.total) * 100);
  assert(completionRate === 40, '子任务完成率计算正确');

  // 测试按优先级分组统计
  const priorityStatsQuery = `
    SELECT priority, COUNT(*) as count 
    FROM tasks 
    WHERE parent_task_id = ? AND deleted_at IS NULL 
    GROUP BY priority
  `;

  const priorityStats = db.prepare(priorityStatsQuery).all(parentTaskId);
  assert(priorityStats.length === 1 && priorityStats[0].count === 5, '按优先级分组统计正确');
}

// 测试级联删除
function testCascadeDelete(db) {
  console.log('\n🗑️ 测试级联删除功能...');

  const now = Date.now();
  const parentTaskId = ulid();
  const subTask1Id = ulid();
  const subTask2Id = ulid();
  const grandChildId = ulid();

  // 创建层级结构
  const insertStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, parent_task_id, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  insertStmt.run(parentTaskId, '级联删除测试父任务', 0, 2, 1000, now, null, 'task');
  insertStmt.run(subTask1Id, '子任务1', 0, 2, 2000, now, parentTaskId, 'subtask');
  insertStmt.run(subTask2Id, '子任务2', 0, 2, 3000, now, parentTaskId, 'subtask');
  insertStmt.run(grandChildId, '孙任务', 0, 2, 4000, now, subTask1Id, 'subtask');

  // 验证创建成功
  const countStmt = db.prepare('SELECT COUNT(*) as count FROM tasks WHERE deleted_at IS NULL');
  const initialCount = countStmt.get().count;
  assert(initialCount >= 4, '层级结构创建成功');

  // 模拟级联删除（删除父任务）
  function recursiveDelete(taskId, deletedAt) {
    const getChildrenStmt = db.prepare('SELECT id FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL');
    const children = getChildrenStmt.all(taskId);
    
    // 递归删除子任务
    children.forEach(child => {
      recursiveDelete(child.id, deletedAt);
    });
    
    // 删除当前任务
    const deleteStmt = db.prepare('UPDATE tasks SET deleted_at = ? WHERE id = ?');
    deleteStmt.run(deletedAt, taskId);
  }

  // 执行级联删除
  const deleteTime = Date.now();
  recursiveDelete(parentTaskId, deleteTime);

  // 验证级联删除结果
  const remainingCount = countStmt.get().count;
  assert(remainingCount === initialCount - 4, '级联删除成功');

  // 验证所有相关任务都被标记为删除
  const deletedStmt = db.prepare('SELECT COUNT(*) as count FROM tasks WHERE deleted_at = ?');
  const deletedCount = deletedStmt.get(deleteTime).count;
  assert(deletedCount === 4, '所有相关任务都被正确删除');

  // 验证子任务查询不返回已删除的任务
  const getSubTasksStmt = db.prepare(`
    SELECT * FROM tasks 
    WHERE parent_task_id = ? AND deleted_at IS NULL
  `);
  const remainingSubTasks = getSubTasksStmt.all(parentTaskId);
  assert(remainingSubTasks.length === 0, '已删除任务的子任务查询为空');
}

// 测试索引性能
function testIndexPerformance(db) {
  console.log('\n⚡ 测试子任务索引性能...');

  const now = Date.now();
  const parentTaskId = ulid();

  // 创建父任务
  const insertParentStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `);
  insertParentStmt.run(parentTaskId, '性能测试父任务', 0, 2, 1000, now, 'task');

  // 批量创建子任务
  const insertSubStmt = db.prepare(`
    INSERT INTO tasks (id, content, is_completed, priority, order_index, created_at, parent_task_id, task_type)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `);

  const transaction = db.transaction(() => {
    for (let i = 0; i < 100; i++) {
      const subTaskId = ulid();
      const isCompleted = Math.random() < 0.3 ? 1 : 0;
      insertSubStmt.run(subTaskId, `性能测试子任务${i + 1}`, isCompleted, 2, (i + 1) * 1000, now, parentTaskId, 'subtask');
    }
  });

  transaction();
  assert(true, '批量创建100个子任务成功');

  // 测试子任务查询性能
  const queries = [
    {
      name: '获取所有子任务',
      sql: 'SELECT * FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL ORDER BY order_index'
    },
    {
      name: '获取已完成子任务',
      sql: 'SELECT * FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL AND is_completed = 1'
    },
    {
      name: '子任务统计查询',
      sql: 'SELECT COUNT(*) as total, SUM(is_completed) as completed FROM tasks WHERE parent_task_id = ? AND deleted_at IS NULL'
    },
    {
      name: '层级查询',
      sql: 'SELECT t1.id, t1.content, t2.id as child_id FROM tasks t1 LEFT JOIN tasks t2 ON t1.id = t2.parent_task_id WHERE t1.parent_task_id = ? AND t1.deleted_at IS NULL'
    }
  ];

  queries.forEach(({ name, sql }) => {
    const start = Date.now();
    const result = db.prepare(sql).all(parentTaskId);
    const duration = Date.now() - start;
    
    console.log(`   ${name}: ${duration}ms (${result.length} 条记录)`);
    assert(duration < 50, `${name} - 查询性能良好 (${duration}ms < 50ms)`);
  });

  // 测试查询计划
  const explainResult = db.prepare(`
    EXPLAIN QUERY PLAN 
    SELECT * FROM tasks 
    WHERE parent_task_id = ? AND deleted_at IS NULL 
    ORDER BY order_index
  `).all(parentTaskId);

  const usesIndex = explainResult.some(row => 
    row.detail && row.detail.toLowerCase().includes('index')
  );
  assert(usesIndex, '子任务查询使用了索引优化');
}

// 主测试函数
function runSubTaskTests() {
  try {
    const db = createTestDatabase();
    
    console.log('🧪 开始子任务系统功能测试...\n');
    
    // 运行各项测试
    testSubTaskCRUD(db);
    testTaskHierarchy(db);
    testSubTaskStats(db);
    testCascadeDelete(db);
    testIndexPerformance(db);
    
    // 关闭数据库
    db.close();
    
    // 生成测试报告
    generateTestReport();
    
  } catch (error) {
    console.error('❌ 子任务功能测试失败:', error);
    process.exit(1);
  }
}

// 生成测试报告
function generateTestReport() {
  console.log('\n📊 === 子任务系统功能测试报告 ===');
  console.log(`✅ 通过测试: ${testResults.passed}`);
  console.log(`❌ 失败测试: ${testResults.failed}`);
  console.log(`📊 总测试数: ${testResults.passed + testResults.failed}`);
  console.log(`🎯 成功率: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.tests
      .filter(test => test.status === 'FAILED')
      .forEach(test => console.log(`   - ${test.name}`));
  }
  
  // 保存测试报告
  const reportPath = path.join(process.cwd(), 'subtask-functionality-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(testResults, null, 2));
  console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  
  // 清理测试数据库
  if (fs.existsSync(testDbPath)) {
    fs.unlinkSync(testDbPath);
    console.log('\n🧹 测试数据库已清理');
  }
  
  if (testResults.failed === 0) {
    console.log('\n🎉 所有子任务功能测试通过！子任务系统实现正确。');
  } else {
    console.log('\n⚠️  部分子任务功能测试失败，请检查实现。');
  }
}

// 运行测试
runSubTaskTests();
