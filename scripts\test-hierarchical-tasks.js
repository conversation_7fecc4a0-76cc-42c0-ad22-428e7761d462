#!/usr/bin/env node

/**
 * 测试层级任务功能
 * 验证层级任务的创建、查询和显示功能
 */

const { TaskService } = require('../dist/main/services/taskService.js')
const { TaskPriority } = require('../dist/shared/types/task.js')

async function testHierarchicalTasks() {
  console.log('🧪 开始测试层级任务功能...\n')
  
  const taskService = new TaskService()
  
  try {
    // 1. 创建主任务
    console.log('1️⃣ 创建主任务...')
    const mainTask = await taskService.createTask({
      content: '完成项目开发',
      priority: TaskPriority.HIGH,
      taskType: 'task',
      description: '这是一个主要的项目任务'
    })
    console.log(`✅ 主任务创建成功: ${mainTask.content} (ID: ${mainTask.id})`)

    // 2. 创建子任务
    console.log('\n2️⃣ 创建子任务...')
    const subtask1 = await taskService.createSubTask(mainTask.id, {
      content: '设计数据库结构',
      priority: TaskPriority.MEDIUM,
      description: '设计完整的数据库Schema'
    })
    console.log(`✅ 子任务1创建成功: ${subtask1.content}`)

    const subtask2 = await taskService.createSubTask(mainTask.id, {
      content: '实现API接口',
      priority: TaskPriority.HIGH,
      description: '实现所有必要的API端点'
    })
    console.log(`✅ 子任务2创建成功: ${subtask2.content}`)

    // 3. 创建子子任务
    console.log('\n3️⃣ 创建子子任务...')
    const subSubtask1 = await taskService.createSubTask(subtask1.id, {
      content: '创建用户表',
      priority: TaskPriority.MEDIUM,
      description: '设计用户相关的数据表'
    })
    console.log(`✅ 子子任务创建成功: ${subSubtask1.content}`)

    // 4. 测试层级查询
    console.log('\n4️⃣ 测试层级查询...')
    const hierarchies = await taskService.getHierarchicalTasks()
    console.log(`✅ 获取到 ${hierarchies.length} 个根任务层级`)

    // 5. 显示层级结构
    console.log('\n5️⃣ 显示层级结构:')
    function printHierarchy(hierarchy, indent = '') {
      const { task, children, depth } = hierarchy
      const status = task.isCompleted ? '✅' : '⏳'
      const priority = ['', '🔴', '🟡', '🟢'][task.priority] || ''
      
      console.log(`${indent}${status} ${priority} ${task.content} (深度: ${depth})`)
      
      if (task.description) {
        console.log(`${indent}   📝 ${task.description}`)
      }
      
      children.forEach(child => {
        printHierarchy(child, indent + '  ')
      })
    }

    hierarchies.forEach(hierarchy => {
      printHierarchy(hierarchy)
      console.log('')
    })

    // 6. 测试子任务统计
    console.log('6️⃣ 测试子任务统计...')
    const stats = await taskService.getSubTaskStats(mainTask.id)
    console.log(`✅ 主任务统计:`)
    console.log(`   - 总计: ${stats.total}`)
    console.log(`   - 已完成: ${stats.completed}`)
    console.log(`   - 待完成: ${stats.pending}`)
    console.log(`   - 完成率: ${stats.completionRate}%`)

    // 7. 测试完成子任务
    console.log('\n7️⃣ 测试完成子任务...')
    await taskService.updateSubTask(subSubtask1.id, { isCompleted: true })
    console.log(`✅ 标记子子任务为已完成: ${subSubtask1.content}`)

    // 8. 重新查看统计
    console.log('\n8️⃣ 重新查看统计...')
    const updatedStats = await taskService.getSubTaskStats(mainTask.id)
    console.log(`✅ 更新后的统计:`)
    console.log(`   - 总计: ${updatedStats.total}`)
    console.log(`   - 已完成: ${updatedStats.completed}`)
    console.log(`   - 待完成: ${updatedStats.pending}`)
    console.log(`   - 完成率: ${updatedStats.completionRate}%`)

    // 9. 测试层级验证
    console.log('\n9️⃣ 测试层级验证...')
    const validation = await taskService.validateTaskHierarchy(mainTask.id)
    console.log(`✅ 层级验证结果:`)
    console.log(`   - 有效: ${validation.isValid}`)
    console.log(`   - 最大深度: ${validation.maxDepth}`)
    console.log(`   - 总任务数: ${validation.totalTasks}`)

    // 10. 性能测试
    console.log('\n🔟 性能测试...')
    const startTime = Date.now()
    for (let i = 0; i < 10; i++) {
      await taskService.getHierarchicalTasks()
    }
    const endTime = Date.now()
    const avgTime = (endTime - startTime) / 10
    console.log(`✅ 平均查询时间: ${avgTime.toFixed(2)}ms`)

    console.log('\n🎉 所有测试通过！层级任务功能正常工作。')
    
  } catch (error) {
    console.error('❌ 测试失败:', error)
    process.exit(1)
  }
}

// 运行测试
if (require.main === module) {
  testHierarchicalTasks()
    .then(() => {
      console.log('\n✅ 层级任务测试完成')
      process.exit(0)
    })
    .catch(error => {
      console.error('\n❌ 层级任务测试失败:', error)
      process.exit(1)
    })
}

module.exports = { testHierarchicalTasks }
