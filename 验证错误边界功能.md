# 🛡️ ErrorBoundary 功能验证指南

## 验证步骤

### 1. 启动应用
```bash
npm run dev
```

### 2. 显示错误测试工具
- 在应用右上角点击"显示错误测试"按钮（仅在开发环境显示）
- 错误测试工具面板将出现在页面顶部

### 3. 测试不同类型的错误

#### 3.1 React 错误测试
- 点击"React 错误"按钮
- **预期结果**: 应用显示友好的错误页面，包含：
  - 错误标题："应用出现错误"
  - 错误描述
  - 错误ID
  - 重试按钮
  - 返回主页按钮
  - 重启应用按钮
  - 开发者信息（开发环境）

#### 3.2 JavaScript 错误测试
- 点击"JS 错误"按钮
- **预期结果**: 控制台显示错误日志，应用继续正常运行

#### 3.3 Promise 拒绝测试
- 点击"Promise 拒绝"按钮
- **预期结果**: 控制台显示未处理的 Promise 拒绝错误

#### 3.4 网络错误测试
- 点击"网络错误"按钮
- **预期结果**: 控制台显示网络请求失败错误

#### 3.5 类型错误测试
- 点击"类型错误"按钮
- **预期结果**: 控制台显示类型错误

#### 3.6 引用错误测试
- 点击"引用错误"按钮
- **预期结果**: 控制台显示引用错误

### 4. 验证错误恢复功能

#### 4.1 重试功能
- 触发 React 错误后，点击"重试"按钮
- **预期结果**: 错误页面消失，应用恢复正常

#### 4.2 返回主页功能
- 触发 React 错误后，点击"返回主页"按钮
- **预期结果**: 错误状态重置，应用恢复正常

#### 4.3 重启应用功能
- 触发 React 错误后，点击"重启应用"按钮
- **预期结果**: Electron 应用退出（或在浏览器中重新加载页面）

### 5. 验证开发者功能（开发环境）

#### 5.1 错误详情显示
- 触发 React 错误后，点击"开发者信息"展开详情
- **预期结果**: 显示错误堆栈和组件堆栈信息

#### 5.2 复制错误详情
- 在开发者信息中，点击"复制错误详情"按钮
- **预期结果**: 错误详情被复制到剪贴板

### 6. 验证错误报告功能

#### 6.1 控制台日志
- 触发任何错误后，检查浏览器控制台
- **预期结果**: 看到详细的错误报告日志

#### 6.2 主进程日志
- 触发错误后，检查 Electron 主进程控制台
- **预期结果**: 看到"Error reported from renderer"日志

## 验证清单

- [ ] React 错误被正确捕获并显示友好页面
- [ ] JavaScript 错误被全局错误处理器捕获
- [ ] Promise 拒绝被正确处理
- [ ] 网络错误被记录
- [ ] 重试功能正常工作
- [ ] 返回主页功能正常工作
- [ ] 重启应用功能正常工作
- [ ] 开发者信息正确显示（开发环境）
- [ ] 错误详情可以复制到剪贴板
- [ ] 错误报告被发送到主进程
- [ ] 控制台显示详细的错误日志

## 注意事项

1. **开发环境特性**: 错误测试工具和开发者信息仅在开发环境中显示
2. **生产环境**: 在生产环境中，错误页面会更简洁，不显示技术细节
3. **错误恢复**: 重试功能有次数限制（最多3次）
4. **错误报告**: 所有错误都会被记录到本地存储和主进程日志中

## 故障排除

### 如果错误边界不工作
1. 检查 ErrorBoundary 是否正确包装了应用组件
2. 确认 React 版本支持错误边界功能
3. 检查控制台是否有其他错误阻止了错误边界的工作

### 如果错误报告不工作
1. 检查 IPC 通道是否正确设置
2. 确认主进程的错误处理器已注册
3. 检查网络连接（如果启用了远程报告）

### 如果测试工具不显示
1. 确认当前环境是开发环境 (`NODE_ENV=development`)
2. 检查 ErrorTestComponent 是否正确导入
3. 确认没有其他错误阻止了组件渲染
