/**
 * 删除功能调试工具
 * 用于诊断和修复删除操作的问题
 */

export interface DeleteDebugInfo {
  timestamp: number
  operation: 'softDelete' | 'batchSoftDelete' | 'restore' | 'batchRestore'
  taskIds: string[]
  success: boolean
  error?: string
  duration: number
  details?: any
}

class DeleteDebugger {
  private logs: DeleteDebugInfo[] = []
  private isEnabled = process.env.NODE_ENV === 'development'

  /**
   * 记录删除操作
   */
  log(info: Omit<DeleteDebugInfo, 'timestamp'>): void {
    if (!this.isEnabled) return

    const logEntry: DeleteDebugInfo = {
      ...info,
      timestamp: Date.now()
    }

    this.logs.push(logEntry)
    
    // 保持最近100条记录
    if (this.logs.length > 100) {
      this.logs = this.logs.slice(-100)
    }

    // 输出到控制台
    const status = info.success ? '✅' : '❌'
    console.group(`${status} Delete Operation: ${info.operation}`)
    console.log('Task IDs:', info.taskIds)
    console.log('Duration:', `${info.duration}ms`)
    if (info.error) {
      console.error('Error:', info.error)
    }
    if (info.details) {
      console.log('Details:', info.details)
    }
    console.groupEnd()
  }

  /**
   * 测试单个任务删除
   */
  async testSoftDelete(taskId: string): Promise<boolean> {
    const startTime = Date.now()
    
    try {
      console.log(`🧪 Testing soft delete for task: ${taskId}`)
      
      // 检查任务是否存在
      const task = await window.electronAPI.task.getById(taskId)
      if (!task) {
        throw new Error('Task not found')
      }
      
      console.log('Task found:', task)
      
      // 执行软删除
      const result = await window.electronAPI.task.softDelete(taskId)
      
      const duration = Date.now() - startTime
      
      this.log({
        operation: 'softDelete',
        taskIds: [taskId],
        success: result,
        duration,
        details: { originalTask: task, result }
      })
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      this.log({
        operation: 'softDelete',
        taskIds: [taskId],
        success: false,
        error: errorMessage,
        duration
      })
      
      throw error
    }
  }

  /**
   * 测试批量删除
   */
  async testBatchSoftDelete(taskIds: string[]): Promise<any> {
    const startTime = Date.now()
    
    try {
      console.log(`🧪 Testing batch soft delete for tasks:`, taskIds)
      
      // 检查任务是否存在
      const tasks = await Promise.all(
        taskIds.map(id => window.electronAPI.task.getById(id))
      )
      
      const existingTasks = tasks.filter(Boolean)
      console.log(`Found ${existingTasks.length} out of ${taskIds.length} tasks`)
      
      // 执行批量软删除
      const result = await window.electronAPI.task.batchSoftDelete(taskIds)
      
      const duration = Date.now() - startTime
      
      this.log({
        operation: 'batchSoftDelete',
        taskIds,
        success: result && result.deletedCount > 0,
        duration,
        details: { existingTasks, result }
      })
      
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      this.log({
        operation: 'batchSoftDelete',
        taskIds,
        success: false,
        error: errorMessage,
        duration
      })
      
      throw error
    }
  }

  /**
   * 测试API连接
   */
  async testApiConnection(): Promise<boolean> {
    try {
      console.log('🔌 Testing API connection...')
      
      // 测试基本API调用
      const stats = await window.electronAPI.task.getStats()
      console.log('✅ API connection successful, stats:', stats)
      
      // 测试任务列表获取
      const tasks = await window.electronAPI.task.getAll()
      console.log(`✅ Retrieved ${tasks.length} tasks`)
      
      return true
    } catch (error) {
      console.error('❌ API connection failed:', error)
      return false
    }
  }

  /**
   * 诊断删除问题
   */
  async diagnoseDeleteIssues(): Promise<void> {
    console.group('🔍 Diagnosing Delete Issues')
    
    try {
      // 1. 测试API连接
      const apiConnected = await this.testApiConnection()
      if (!apiConnected) {
        console.error('❌ API connection failed - this is likely the root cause')
        return
      }
      
      // 2. 检查可用的API方法
      console.log('📋 Available API methods:')
      console.log('- softDelete:', typeof window.electronAPI.task.softDelete)
      console.log('- batchSoftDelete:', typeof window.electronAPI.task.batchSoftDelete)
      console.log('- restore:', typeof window.electronAPI.task.restore)
      console.log('- batchRestore:', typeof window.electronAPI.task.batchRestore)
      console.log('- getById:', typeof window.electronAPI.task.getById)
      
      // 3. 获取一些测试任务
      const tasks = await window.electronAPI.task.getAll()
      const testTasks = tasks.filter(t => !t.deletedAt).slice(0, 2)
      
      if (testTasks.length === 0) {
        console.warn('⚠️ No tasks available for testing')
        return
      }
      
      console.log(`🎯 Using test tasks:`, testTasks.map(t => ({ id: t.id, content: t.content })))
      
      // 4. 测试单个删除
      if (testTasks.length > 0) {
        try {
          await this.testSoftDelete(testTasks[0].id)
          console.log('✅ Single task delete test passed')
        } catch (error) {
          console.error('❌ Single task delete test failed:', error)
        }
      }
      
      // 5. 测试批量删除
      if (testTasks.length > 1) {
        try {
          await this.testBatchSoftDelete(testTasks.slice(0, 2).map(t => t.id))
          console.log('✅ Batch delete test passed')
        } catch (error) {
          console.error('❌ Batch delete test failed:', error)
        }
      }
      
    } catch (error) {
      console.error('❌ Diagnosis failed:', error)
    } finally {
      console.groupEnd()
    }
  }

  /**
   * 获取调试日志
   */
  getLogs(): DeleteDebugInfo[] {
    return [...this.logs]
  }

  /**
   * 清除调试日志
   */
  clearLogs(): void {
    this.logs = []
  }

  /**
   * 导出调试日志
   */
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }

  /**
   * 启用/禁用调试
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }
}

// 导出单例实例
export const deleteDebugger = new DeleteDebugger()

// 在开发环境下暴露到全局对象
if (process.env.NODE_ENV === 'development') {
  (window as any).deleteDebugger = deleteDebugger
  
  // 添加快捷调试命令
  (window as any).debugDelete = {
    test: () => deleteDebugger.diagnoseDeleteIssues(),
    logs: () => deleteDebugger.getLogs(),
    clear: () => deleteDebugger.clearLogs(),
    export: () => deleteDebugger.exportLogs()
  }
  
  console.log('🛠️ Delete debugger available at window.deleteDebugger')
  console.log('🛠️ Quick commands: window.debugDelete.test(), .logs(), .clear(), .export()')
}
