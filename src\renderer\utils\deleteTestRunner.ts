/**
 * 删除功能测试运行器
 * 用于自动化测试删除功能的各个方面
 */

export interface TestResult {
  testName: string
  success: boolean
  error?: string
  duration: number
  details?: any
}

export class DeleteTestRunner {
  private results: TestResult[] = []

  /**
   * 运行所有删除功能测试
   */
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 开始运行删除功能测试...')
    
    this.results = []
    
    // 测试API连接
    await this.testApiConnection()
    
    // 测试单个任务软删除
    await this.testSingleSoftDelete()
    
    // 测试单个任务恢复
    await this.testSingleRestore()
    
    // 测试批量软删除
    await this.testBatchSoftDelete()
    
    // 测试批量恢复
    await this.testBatchRestore()
    
    // 测试错误处理
    await this.testErrorHandling()
    
    this.printResults()
    return this.results
  }

  /**
   * 测试API连接
   */
  private async testApiConnection(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // 测试基本API调用
      const stats = await window.electronAPI.task.getStats()
      const tasks = await window.electronAPI.task.getAll()
      
      this.addResult({
        testName: 'API连接测试',
        success: true,
        duration: Date.now() - startTime,
        details: { stats, taskCount: tasks.length }
      })
    } catch (error) {
      this.addResult({
        testName: 'API连接测试',
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
    }
  }

  /**
   * 测试单个任务软删除
   */
  private async testSingleSoftDelete(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // 创建测试任务
      const testTask = await window.electronAPI.task.create({
        content: '测试删除任务 - ' + Date.now(),
        priority: 1,
        taskType: 'task'
      })
      
      // 执行软删除
      const deleteResult = await window.electronAPI.task.softDelete(testTask.id)
      
      // 验证任务是否被软删除
      const deletedTask = await window.electronAPI.task.getById(testTask.id)
      
      this.addResult({
        testName: '单个任务软删除',
        success: deleteResult && deletedTask?.deletedAt !== null,
        duration: Date.now() - startTime,
        details: { taskId: testTask.id, deleteResult, deletedTask }
      })
    } catch (error) {
      this.addResult({
        testName: '单个任务软删除',
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
    }
  }

  /**
   * 测试单个任务恢复
   */
  private async testSingleRestore(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // 创建并删除测试任务
      const testTask = await window.electronAPI.task.create({
        content: '测试恢复任务 - ' + Date.now(),
        priority: 1,
        taskType: 'task'
      })
      
      await window.electronAPI.task.softDelete(testTask.id)
      
      // 执行恢复
      const restoreResult = await window.electronAPI.task.restore(testTask.id)
      
      // 验证任务是否被恢复
      const restoredTask = await window.electronAPI.task.getById(testTask.id)
      
      this.addResult({
        testName: '单个任务恢复',
        success: restoreResult && restoredTask?.deletedAt === null,
        duration: Date.now() - startTime,
        details: { taskId: testTask.id, restoreResult, restoredTask }
      })
    } catch (error) {
      this.addResult({
        testName: '单个任务恢复',
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
    }
  }

  /**
   * 测试批量软删除
   */
  private async testBatchSoftDelete(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // 创建多个测试任务
      const testTasks = await Promise.all([
        window.electronAPI.task.create({
          content: '批量删除测试任务1 - ' + Date.now(),
          priority: 1,
          taskType: 'task'
        }),
        window.electronAPI.task.create({
          content: '批量删除测试任务2 - ' + Date.now(),
          priority: 1,
          taskType: 'task'
        }),
        window.electronAPI.task.create({
          content: '批量删除测试任务3 - ' + Date.now(),
          priority: 1,
          taskType: 'task'
        })
      ])
      
      const taskIds = testTasks.map(task => task.id)
      
      // 执行批量软删除
      const deleteResult = await window.electronAPI.task.batchSoftDelete(taskIds)
      
      // 验证任务是否被软删除
      const deletedTasks = await Promise.all(
        taskIds.map(id => window.electronAPI.task.getById(id))
      )
      
      const allDeleted = deletedTasks.every(task => task?.deletedAt !== null)
      
      this.addResult({
        testName: '批量任务软删除',
        success: deleteResult.deletedCount === taskIds.length && allDeleted,
        duration: Date.now() - startTime,
        details: { taskIds, deleteResult, deletedTasks }
      })
    } catch (error) {
      this.addResult({
        testName: '批量任务软删除',
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
    }
  }

  /**
   * 测试批量恢复
   */
  private async testBatchRestore(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // 创建并删除多个测试任务
      const testTasks = await Promise.all([
        window.electronAPI.task.create({
          content: '批量恢复测试任务1 - ' + Date.now(),
          priority: 1,
          taskType: 'task'
        }),
        window.electronAPI.task.create({
          content: '批量恢复测试任务2 - ' + Date.now(),
          priority: 1,
          taskType: 'task'
        })
      ])
      
      const taskIds = testTasks.map(task => task.id)
      
      // 先删除任务
      await window.electronAPI.task.batchSoftDelete(taskIds)
      
      // 执行批量恢复
      const restoreResult = await window.electronAPI.task.batchRestore(taskIds)
      
      // 验证任务是否被恢复
      const restoredTasks = await Promise.all(
        taskIds.map(id => window.electronAPI.task.getById(id))
      )
      
      const allRestored = restoredTasks.every(task => task?.deletedAt === null)
      
      this.addResult({
        testName: '批量任务恢复',
        success: restoreResult.restoredCount === taskIds.length && allRestored,
        duration: Date.now() - startTime,
        details: { taskIds, restoreResult, restoredTasks }
      })
    } catch (error) {
      this.addResult({
        testName: '批量任务恢复',
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
    }
  }

  /**
   * 测试错误处理
   */
  private async testErrorHandling(): Promise<void> {
    const startTime = Date.now()
    
    try {
      // 测试删除不存在的任务
      try {
        await window.electronAPI.task.softDelete('non-existent-task-id')
        // 如果没有抛出错误，说明错误处理有问题
        this.addResult({
          testName: '错误处理测试',
          success: false,
          error: '删除不存在的任务应该抛出错误',
          duration: Date.now() - startTime
        })
      } catch (error) {
        // 应该抛出错误，这是正确的行为
        this.addResult({
          testName: '错误处理测试',
          success: true,
          duration: Date.now() - startTime,
          details: { expectedError: error instanceof Error ? error.message : String(error) }
        })
      }
    } catch (error) {
      this.addResult({
        testName: '错误处理测试',
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration: Date.now() - startTime
      })
    }
  }

  /**
   * 添加测试结果
   */
  private addResult(result: Omit<TestResult, 'testName'> & { testName: string }): void {
    this.results.push(result)
    
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${result.testName} (${result.duration}ms)`)
    
    if (result.error) {
      console.error(`   错误: ${result.error}`)
    }
  }

  /**
   * 打印测试结果
   */
  private printResults(): void {
    console.log('\n📊 删除功能测试结果:')
    console.log('=' .repeat(50))
    
    const passed = this.results.filter(r => r.success).length
    const total = this.results.length
    
    console.log(`总测试数: ${total}`)
    console.log(`通过: ${passed}`)
    console.log(`失败: ${total - passed}`)
    console.log(`成功率: ${((passed / total) * 100).toFixed(1)}%`)
    
    if (total - passed > 0) {
      console.log('\n❌ 失败的测试:')
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`- ${r.testName}: ${r.error}`)
        })
    }
  }

  /**
   * 获取测试结果
   */
  getResults(): TestResult[] {
    return [...this.results]
  }
}

// 导出单例实例
export const deleteTestRunner = new DeleteTestRunner()

// 在开发环境下暴露到全局对象
if (process.env.NODE_ENV === 'development') {
  (window as any).deleteTestRunner = deleteTestRunner
  (window as any).testDelete = () => deleteTestRunner.runAllTests()
  
  console.log('🧪 Delete test runner available at window.deleteTestRunner')
  console.log('🧪 Quick test: window.testDelete()')
}
