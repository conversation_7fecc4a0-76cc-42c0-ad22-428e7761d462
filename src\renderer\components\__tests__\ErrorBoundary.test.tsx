/**
 * ErrorBoundary 组件测试
 * 测试错误边界的错误捕获和显示功能
 */

import React from 'react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ErrorBoundary } from '../ErrorBoundary'

// 模拟有问题的组件
function ThrowError({ shouldThrow = false }: { shouldThrow?: boolean }) {
  if (shouldThrow) {
    throw new Error('Test error')
  }
  return <div>No error</div>
}

// 模拟 Electron API
const mockElectronAPI = {
  app: {
    quit: vi.fn(),
    getVersion: vi.fn().mockResolvedValue('1.0.0'),
  },
  invoke: vi.fn(),
}

// 设置全局 mock
Object.defineProperty(window, 'electronAPI', {
  value: mockElectronAPI,
  writable: true,
})

// 模拟 navigator.clipboard
Object.defineProperty(navigator, 'clipboard', {
  value: {
    writeText: vi.fn().mockResolvedValue(undefined),
  },
  writable: true,
})

describe('ErrorBoundary', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // 抑制控制台错误输出，因为我们故意触发错误
    vi.spyOn(console, 'error').mockImplementation(() => {})
  })

  describe('Normal Operation', () => {
    it('should render children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      )

      expect(screen.getByText('No error')).toBeInTheDocument()
    })

    it('should not show error UI when everything is working', () => {
      render(
        <ErrorBoundary>
          <div>Working component</div>
        </ErrorBoundary>
      )

      expect(screen.getByText('Working component')).toBeInTheDocument()
      expect(screen.queryByText('应用出现错误')).not.toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    it('should catch and display error when child component throws', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('应用出现错误')).toBeInTheDocument()
      expect(screen.getByText('很抱歉，应用遇到了意外错误。您可以尝试以下操作来恢复。')).toBeInTheDocument()
    })

    it('should display error message', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('Test error')).toBeInTheDocument()
    })

    it('should show error ID', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText(/错误ID:/)).toBeInTheDocument()
    })
  })

  describe('Error Recovery', () => {
    it('should show retry button', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText(/重试/)).toBeInTheDocument()
    })

    it('should show return home button', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('返回主页')).toBeInTheDocument()
    })

    it('should show restart app button', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('重启应用')).toBeInTheDocument()
    })

    it('should call quit when restart button is clicked', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      const restartButton = screen.getByText('重启应用')
      fireEvent.click(restartButton)

      expect(mockElectronAPI.app.quit).toHaveBeenCalled()
    })
  })

  describe('Development Mode Features', () => {
    const originalEnv = process.env.NODE_ENV

    beforeEach(() => {
      process.env.NODE_ENV = 'development'
    })

    afterEach(() => {
      process.env.NODE_ENV = originalEnv
    })

    it('should show developer information in development mode', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('开发者信息')).toBeInTheDocument()
    })

    it('should show error stack in development mode', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // 点击展开开发者信息
      const devInfo = screen.getByText('开发者信息')
      fireEvent.click(devInfo)

      expect(screen.getByText('错误堆栈:')).toBeInTheDocument()
    })

    it('should show copy error details button in development mode', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // 点击展开开发者信息
      const devInfo = screen.getByText('开发者信息')
      fireEvent.click(devInfo)

      expect(screen.getByText('复制错误详情')).toBeInTheDocument()
    })

    it('should copy error details to clipboard when button is clicked', async () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // 点击展开开发者信息
      const devInfo = screen.getByText('开发者信息')
      fireEvent.click(devInfo)

      // 点击复制按钮
      const copyButton = screen.getByText('复制错误详情')
      fireEvent.click(copyButton)

      expect(navigator.clipboard.writeText).toHaveBeenCalled()
    })
  })

  describe('Custom Error Handler', () => {
    it('should call onError callback when provided', () => {
      const onError = vi.fn()

      render(
        <ErrorBoundary onError={onError}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(onError).toHaveBeenCalled()
      expect(onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String),
        })
      )
    })
  })

  describe('Custom Fallback', () => {
    it('should render custom fallback when provided', () => {
      const customFallback = <div>Custom error message</div>

      render(
        <ErrorBoundary fallback={customFallback}>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      expect(screen.getByText('Custom error message')).toBeInTheDocument()
      expect(screen.queryByText('应用出现错误')).not.toBeInTheDocument()
    })
  })

  describe('Retry Functionality', () => {
    it('should reset error state when retry is clicked', () => {
      const TestComponent = () => {
        const [shouldThrow, setShouldThrow] = React.useState(true)

        React.useEffect(() => {
          // 在第一次重试后不再抛出错误
          const timer = setTimeout(() => {
            setShouldThrow(false)
          }, 100)
          return () => clearTimeout(timer)
        }, [])

        return <ThrowError shouldThrow={shouldThrow} />
      }

      render(
        <ErrorBoundary>
          <TestComponent />
        </ErrorBoundary>
      )

      // 应该显示错误页面
      expect(screen.getByText('应用出现错误')).toBeInTheDocument()

      // 点击重试按钮
      const retryButton = screen.getByText(/重试/)
      fireEvent.click(retryButton)

      // 错误状态应该被重置
      // 注意：由于组件的实现方式，这个测试可能需要调整
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(
        <ErrorBoundary>
          <ThrowError shouldThrow={true} />
        </ErrorBoundary>
      )

      // 检查是否有适当的语义结构
      expect(screen.getByRole('button', { name: /重试/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /返回主页/ })).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /重启应用/ })).toBeInTheDocument()
    })
  })
})
