-- 子任务系统索引优化
-- 创建时间: 2025-06-28
-- 目的: 为子任务系统添加专用索引，优化层级查询性能

-- 为子任务系统添加专用索引
CREATE INDEX IF NOT EXISTS tasks_parent_type_idx ON tasks(parent_task_id, task_type);
CREATE INDEX IF NOT EXISTS tasks_parent_order_idx ON tasks(parent_task_id, order_index);
CREATE INDEX IF NOT EXISTS tasks_parent_progress_idx ON tasks(parent_task_id, progress);

-- 层级查询优化的复合索引
CREATE INDEX IF NOT EXISTS tasks_hierarchy_idx ON tasks(parent_task_id, deleted_at, is_completed, order_index);

-- 子任务统计查询优化
CREATE INDEX IF NOT EXISTS tasks_parent_stats_idx ON tasks(parent_task_id, is_completed, deleted_at);

-- 层级深度查询优化（用于防止过深嵌套）
CREATE INDEX IF NOT EXISTS tasks_type_parent_idx ON tasks(task_type, parent_task_id, deleted_at);

-- 更新统计信息
ANALYZE tasks;
