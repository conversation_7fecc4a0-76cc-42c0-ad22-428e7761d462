/**
 * API验证工具
 * 用于验证删除相关API是否正常工作
 */

export interface ApiVerificationResult {
  apiName: string
  exists: boolean
  callable: boolean
  error?: string
}

export class ApiVerifier {
  /**
   * 验证所有删除相关API
   */
  async verifyDeleteApis(): Promise<ApiVerificationResult[]> {
    console.log('🔍 开始验证删除相关API...')
    
    const results: ApiVerificationResult[] = []
    
    // 检查API对象是否存在
    if (!window.electronAPI) {
      console.error('❌ window.electronAPI 不存在')
      return [{
        apiName: 'window.electronAPI',
        exists: false,
        callable: false,
        error: 'electronAPI对象不存在'
      }]
    }
    
    if (!window.electronAPI.task) {
      console.error('❌ window.electronAPI.task 不存在')
      return [{
        apiName: 'window.electronAPI.task',
        exists: false,
        callable: false,
        error: 'task API对象不存在'
      }]
    }
    
    // 要验证的API列表
    const apisToVerify = [
      'softDelete',
      'restore', 
      'getById',
      'batchSoftDelete',
      'batchRestore',
      'getAll',
      'create',
      'update',
      'delete',
      'getStats'
    ]
    
    for (const apiName of apisToVerify) {
      const result = await this.verifyApi(apiName)
      results.push(result)
    }
    
    this.printVerificationResults(results)
    return results
  }
  
  /**
   * 验证单个API
   */
  private async verifyApi(apiName: string): Promise<ApiVerificationResult> {
    const api = (window.electronAPI.task as any)[apiName]
    
    // 检查API是否存在
    if (api === undefined) {
      return {
        apiName,
        exists: false,
        callable: false,
        error: 'API不存在'
      }
    }
    
    // 检查是否为函数
    if (typeof api !== 'function') {
      return {
        apiName,
        exists: true,
        callable: false,
        error: `API存在但不是函数，类型为: ${typeof api}`
      }
    }
    
    // 对于某些API，尝试调用以验证是否可用
    try {
      if (apiName === 'getStats') {
        await api()
        return {
          apiName,
          exists: true,
          callable: true
        }
      } else if (apiName === 'getAll') {
        await api()
        return {
          apiName,
          exists: true,
          callable: true
        }
      } else {
        // 对于其他API，只检查存在性和类型
        return {
          apiName,
          exists: true,
          callable: true
        }
      }
    } catch (error) {
      return {
        apiName,
        exists: true,
        callable: false,
        error: `调用失败: ${error instanceof Error ? error.message : String(error)}`
      }
    }
  }
  
  /**
   * 打印验证结果
   */
  private printVerificationResults(results: ApiVerificationResult[]): void {
    console.log('\n📋 API验证结果:')
    console.log('=' .repeat(60))
    
    results.forEach(result => {
      const status = result.exists && result.callable ? '✅' : '❌'
      console.log(`${status} ${result.apiName}`)
      
      if (!result.exists) {
        console.log(`   ❌ 不存在`)
      } else if (!result.callable) {
        console.log(`   ⚠️  存在但不可调用: ${result.error}`)
      } else {
        console.log(`   ✅ 可用`)
      }
    })
    
    const available = results.filter(r => r.exists && r.callable).length
    const total = results.length
    
    console.log('\n📊 总结:')
    console.log(`可用API: ${available}/${total}`)
    console.log(`可用率: ${((available / total) * 100).toFixed(1)}%`)
    
    if (available < total) {
      console.log('\n❌ 不可用的API:')
      results
        .filter(r => !r.exists || !r.callable)
        .forEach(r => {
          console.log(`- ${r.apiName}: ${r.error}`)
        })
    }
  }
  
  /**
   * 快速测试删除功能
   */
  async quickDeleteTest(): Promise<boolean> {
    console.log('🧪 开始快速删除功能测试...')
    
    try {
      // 1. 检查API是否存在
      if (typeof window.electronAPI.task.softDelete !== 'function') {
        console.error('❌ softDelete API不存在或不是函数')
        return false
      }
      
      // 2. 创建测试任务
      console.log('📝 创建测试任务...')
      const testTask = await window.electronAPI.task.create({
        content: '删除功能测试任务 - ' + Date.now(),
        priority: 1,
        taskType: 'task'
      })
      console.log('✅ 测试任务创建成功:', testTask.id)
      
      // 3. 测试软删除
      console.log('🗑️ 测试软删除...')
      const deleteResult = await window.electronAPI.task.softDelete(testTask.id)
      console.log('✅ 软删除结果:', deleteResult)
      
      // 4. 验证任务是否被软删除
      console.log('🔍 验证任务状态...')
      const deletedTask = await window.electronAPI.task.getById(testTask.id)
      console.log('📋 删除后任务状态:', deletedTask)
      
      if (!deletedTask || deletedTask.deletedAt === null) {
        console.error('❌ 任务未被正确软删除')
        return false
      }
      
      // 5. 测试恢复
      console.log('🔄 测试恢复...')
      const restoreResult = await window.electronAPI.task.restore(testTask.id)
      console.log('✅ 恢复结果:', restoreResult)
      
      // 6. 验证任务是否被恢复
      console.log('🔍 验证恢复状态...')
      const restoredTask = await window.electronAPI.task.getById(testTask.id)
      console.log('📋 恢复后任务状态:', restoredTask)
      
      if (!restoredTask || restoredTask.deletedAt !== null) {
        console.error('❌ 任务未被正确恢复')
        return false
      }
      
      // 7. 清理测试任务
      console.log('🧹 清理测试任务...')
      await window.electronAPI.task.delete(testTask.id)
      
      console.log('✅ 删除功能测试完成，所有功能正常！')
      return true
      
    } catch (error) {
      console.error('❌ 删除功能测试失败:', error)
      return false
    }
  }
}

// 导出单例实例
export const apiVerifier = new ApiVerifier()

// 在开发环境下暴露到全局对象
if (process.env.NODE_ENV === 'development') {
  (window as any).apiVerifier = apiVerifier
  (window as any).verifyApis = () => apiVerifier.verifyDeleteApis()
  (window as any).quickDeleteTest = () => apiVerifier.quickDeleteTest()
  
  console.log('🔍 API验证工具已加载')
  console.log('🔍 运行 window.verifyApis() 验证所有API')
  console.log('🧪 运行 window.quickDeleteTest() 快速测试删除功能')
}
