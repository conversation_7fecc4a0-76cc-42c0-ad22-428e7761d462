{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 0}], "isBlockCoverage": false}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/stores/__tests__/uiStore.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31122, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31122, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 664, "endOffset": 11676, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 707, "endOffset": 947, "count": 23}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 998, "endOffset": 1995, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1079, "endOffset": 1268, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1145, "endOffset": 1185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1329, "endOffset": 1625, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1395, "endOffset": 1435, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1470, "endOffset": 1536, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1697, "endOffset": 1989, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1763, "endOffset": 1803, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1838, "endOffset": 1902, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2048, "endOffset": 3258, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2129, "endOffset": 2318, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2195, "endOffset": 2235, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2383, "endOffset": 2669, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2449, "endOffset": 2489, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2524, "endOffset": 2585, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2732, "endOffset": 3252, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2798, "endOffset": 2838, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2948, "endOffset": 3003, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3114, "endOffset": 3169, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3309, "endOffset": 4253, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3387, "endOffset": 3574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3453, "endOffset": 3493, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3629, "endOffset": 3905, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3695, "endOffset": 3735, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3770, "endOffset": 3826, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3969, "endOffset": 4247, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4035, "endOffset": 4075, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4110, "endOffset": 4167, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 5116, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4391, "endOffset": 4582, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4457, "endOffset": 4497, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4650, "endOffset": 5110, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4716, "endOffset": 4756, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4791, "endOffset": 4852, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4963, "endOffset": 5025, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5174, "endOffset": 5986, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5256, "endOffset": 5447, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5322, "endOffset": 5362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5510, "endOffset": 5980, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5576, "endOffset": 5616, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5651, "endOffset": 5717, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5833, "endOffset": 5895, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6044, "endOffset": 7214, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6132, "endOffset": 6319, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6198, "endOffset": 6238, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6381, "endOffset": 6681, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6447, "endOffset": 6487, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6522, "endOffset": 6590, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6742, "endOffset": 7208, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6808, "endOffset": 6848, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6883, "endOffset": 6951, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7069, "endOffset": 7127, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7267, "endOffset": 8368, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7346, "endOffset": 7534, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7412, "endOffset": 7452, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7590, "endOffset": 7876, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7656, "endOffset": 7696, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7731, "endOffset": 7792, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7952, "endOffset": 8362, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8018, "endOffset": 8058, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8161, "endOffset": 8354, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8209, "endOffset": 8270, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8430, "endOffset": 10144, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8517, "endOffset": 9547, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8583, "endOffset": 8623, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8658, "endOffset": 9001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9617, "endOffset": 10138, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9683, "endOffset": 9723, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9758, "endOffset": 9824, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10201, "endOffset": 11672, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10294, "endOffset": 10793, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10369, "endOffset": 10409, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10479, "endOffset": 10519, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10554, "endOffset": 10621, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10880, "endOffset": 11666, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10955, "endOffset": 10995, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11065, "endOffset": 11105, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11140, "endOffset": 11209, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 11412, "endOffset": 11484, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1562", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/stores/uiStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10606, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10606, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.create.__vite_ssr_import_1__.devtools.name", "ranges": [{"startOffset": 535, "endOffset": 1411, "count": 1}], "isBlockCoverage": true}, {"functionName": "setT<PERSON><PERSON><PERSON>er", "ranges": [{"startOffset": 613, "endOffset": 652, "count": 5}], "isBlockCoverage": true}, {"functionName": "setSidebarOpen", "ranges": [{"startOffset": 716, "endOffset": 752, "count": 2}], "isBlockCoverage": true}, {"functionName": "toggleSidebar", "ranges": [{"startOffset": 775, "endOffset": 834, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 785, "endOffset": 833, "count": 2}], "isBlockCoverage": true}, {"functionName": "setTheme", "ranges": [{"startOffset": 887, "endOffset": 912, "count": 3}], "isBlockCoverage": true}, {"functionName": "setIsAddingTask", "ranges": [{"startOffset": 981, "endOffset": 1022, "count": 3}], "isBlockCoverage": true}, {"functionName": "setEditingTaskId", "ranges": [{"startOffset": 1094, "endOffset": 1128, "count": 3}], "isBlockCoverage": true}, {"functionName": "setSearch<PERSON>uery", "ranges": [{"startOffset": 1190, "endOffset": 1228, "count": 6}], "isBlockCoverage": true}, {"functionName": "setSortBy", "ranges": [{"startOffset": 1285, "endOffset": 1312, "count": 6}], "isBlockCoverage": true}, {"functionName": "setViewMode", "ranges": [{"startOffset": 1371, "endOffset": 1404, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1553, "endOffset": 1579, "count": 75}], "isBlockCoverage": true}, {"functionName": "useTaskFilter", "ranges": [{"startOffset": 1605, "endOffset": 1650, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1753, "endOffset": 1782, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSetTaskFilter", "ranges": [{"startOffset": 1811, "endOffset": 1859, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1965, "endOffset": 1997, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSidebar", "ranges": [{"startOffset": 2020, "endOffset": 2146, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2246, "endOffset": 2272, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTheme", "ranges": [{"startOffset": 2293, "endOffset": 2376, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2474, "endOffset": 2498, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskInput", "ranges": [{"startOffset": 2523, "endOffset": 2626, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2728, "endOffset": 2756, "count": 0}], "isBlockCoverage": false}, {"functionName": "useTaskEditing", "ranges": [{"startOffset": 2783, "endOffset": 2890, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2994, "endOffset": 3024, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSearch", "ranges": [{"startOffset": 3046, "endOffset": 3141, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3240, "endOffset": 3265, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSort", "ranges": [{"startOffset": 3285, "endOffset": 3372, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3469, "endOffset": 3492, "count": 0}], "isBlockCoverage": false}, {"functionName": "useViewMode", "ranges": [{"startOffset": 3516, "endOffset": 3603, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3704, "endOffset": 3731, "count": 0}], "isBlockCoverage": false}]}]}