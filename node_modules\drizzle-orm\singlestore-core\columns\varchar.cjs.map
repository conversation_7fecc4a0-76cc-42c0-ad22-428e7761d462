{"version": 3, "sources": ["../../../src/singlestore-core/columns/varchar.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreVarCharBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = SingleStoreVarCharBuilder<\n\t{\n\t\tname: TName;\n\t\tdataType: 'string';\n\t\tcolumnType: 'SingleStoreVarChar';\n\t\tdata: TEnum[number];\n\t\tdriverParam: number | string;\n\t\tenumValues: TEnum;\n\t\tgenerated: undefined;\n\t\tlength: TLength;\n\t}\n>;\n\nexport class SingleStoreVarCharBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'SingleStoreVarChar'> & { length?: number | undefined },\n> extends SingleStoreColumnBuilder<T, SingleStoreVarCharConfig<T['enumValues'], T['length']>, { length: T['length'] }> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreVarCharBuilder';\n\n\t/** @internal */\n\tconstructor(name: T['name'], config: SingleStoreVarCharConfig<T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'SingleStoreVarChar');\n\t\tthis.config.length = config.length;\n\t\tthis.config.enum = config.enum;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreVarChar<MakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }> {\n\t\treturn new SingleStoreVarChar<\n\t\t\tMakeColumnConfig<T, TTableName> & { length: T['length']; enumValues: T['enumValues'] }\n\t\t>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreVarChar<\n\tT extends ColumnBaseConfig<'string', 'SingleStoreVarChar'> & { length?: number | undefined },\n> extends SingleStoreColumn<T, SingleStoreVarCharConfig<T['enumValues'], T['length']>, { length: T['length'] }> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreVarChar';\n\n\treadonly length: T['length'] = this.config.length;\n\toverride readonly enumValues = this.config.enum;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varchar` : `varchar(${this.length})`;\n\t}\n}\n\nexport interface SingleStoreVarCharConfig<\n\tTEnum extends string[] | readonly string[] | undefined = string[] | readonly string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> {\n\tenum?: TEnum;\n\tlength: TLength;\n}\n\nexport function varchar<U extends string, T extends Readonly<[U, ...U[]]>, L extends number | undefined>(\n\tconfig: SingleStoreVarCharConfig<T | Writable<T>, L>,\n): SingleStoreVarCharBuilderInitial<'', Writable<T>, L>;\nexport function varchar<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n>(\n\tname: TName,\n\tconfig: SingleStoreVarCharConfig<T | Writable<T>, L>,\n): SingleStoreVarCharBuilderInitial<TName, Writable<T>, L>;\nexport function varchar(a?: string | SingleStoreVarCharConfig, b?: SingleStoreVarCharConfig): any {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreVarCharConfig>(a, b);\n\treturn new SingleStoreVarCharBuilder(name, config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAsD;AACtD,oBAA4D;AAmBrD,MAAM,kCAEH,uCAA6G;AAAA,EACtH,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGhD,YAAY,MAAiB,QAAgE;AAC5F,UAAM,MAAM,UAAU,oBAAoB;AAC1C,SAAK,OAAO,SAAS,OAAO;AAC5B,SAAK,OAAO,OAAO,OAAO;AAAA,EAC3B;AAAA;AAAA,EAGS,MACR,OAC6G;AAC7G,WAAO,IAAI;AAAA,MAGV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,2BAEH,gCAAsG;AAAA,EAC/G,QAA0B,wBAAU,IAAY;AAAA,EAEvC,SAAsB,KAAK,OAAO;AAAA,EACzB,aAAa,KAAK,OAAO;AAAA,EAE3C,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,YAAY,WAAW,KAAK,MAAM;AAAA,EACtE;AACD;AAsBO,SAAS,QAAQ,GAAuC,GAAmC;AACjG,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAiD,GAAG,CAAC;AAC9E,SAAO,IAAI,0BAA0B,MAAM,MAAa;AACzD;", "names": []}