{"version": 3, "sources": ["../../../src/singlestore-core/columns/bigint.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumnBuilderWithAutoIncrement, SingleStoreColumnWithAutoIncrement } from './common.ts';\n\nexport type SingleStoreBigInt53BuilderInitial<TName extends string> = SingleStoreBigInt53Builder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SingleStoreBigInt53';\n\tdata: number;\n\tdriverParam: number | string;\n\tenumValues: undefined;\n}>;\n\nexport class SingleStoreBigInt53Builder<T extends ColumnBuilderBaseConfig<'number', 'SingleStoreBigInt53'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreBigInt53Builder';\n\n\tconstructor(name: T['name'], unsigned: boolean = false) {\n\t\tsuper(name, 'number', 'SingleStoreBigInt53');\n\t\tthis.config.unsigned = unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreBigInt53<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreBigInt53<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreBigInt53<T extends ColumnBaseConfig<'number', 'SingleStoreBigInt53'>>\n\textends SingleStoreColumnWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreBigInt53';\n\n\tgetSQLType(): string {\n\t\treturn `bigint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\toverride mapFromDriverValue(value: number | string): number {\n\t\tif (typeof value === 'number') {\n\t\t\treturn value;\n\t\t}\n\t\treturn Number(value);\n\t}\n}\n\nexport type SingleStoreBigInt64BuilderInitial<TName extends string> = SingleStoreBigInt64Builder<{\n\tname: TName;\n\tdataType: 'bigint';\n\tcolumnType: 'SingleStoreBigInt64';\n\tdata: bigint;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreBigInt64Builder<T extends ColumnBuilderBaseConfig<'bigint', 'SingleStoreBigInt64'>>\n\textends SingleStoreColumnBuilderWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreBigInt64Builder';\n\n\tconstructor(name: T['name'], unsigned: boolean = false) {\n\t\tsuper(name, 'bigint', 'SingleStoreBigInt64');\n\t\tthis.config.unsigned = unsigned;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreBigInt64<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreBigInt64<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreBigInt64<T extends ColumnBaseConfig<'bigint', 'SingleStoreBigInt64'>>\n\textends SingleStoreColumnWithAutoIncrement<T, { unsigned: boolean }>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreBigInt64';\n\n\tgetSQLType(): string {\n\t\treturn `bigint${this.config.unsigned ? ' unsigned' : ''}`;\n\t}\n\n\t// eslint-disable-next-line unicorn/prefer-native-coercion-functions\n\toverride mapFromDriverValue(value: string): bigint {\n\t\treturn BigInt(value);\n\t}\n}\n\nexport interface SingleStoreBigIntConfig<T extends 'number' | 'bigint' = 'number' | 'bigint'> {\n\tmode: T;\n\tunsigned?: boolean;\n}\n\nexport function bigint<TMode extends SingleStoreBigIntConfig['mode']>(\n\tconfig: SingleStoreBigIntConfig<TMode>,\n): TMode extends 'number' ? SingleStoreBigInt53BuilderInitial<''> : SingleStoreBigInt64BuilderInitial<''>;\nexport function bigint<TName extends string, TMode extends SingleStoreBigIntConfig['mode']>(\n\tname: TName,\n\tconfig: SingleStoreBigIntConfig<TMode>,\n): TMode extends 'number' ? SingleStoreBigInt53BuilderInitial<TName> : SingleStoreBigInt64BuilderInitial<TName>;\nexport function bigint(a?: string | SingleStoreBigIntConfig, b?: SingleStoreBigIntConfig) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreBigIntConfig>(a, b);\n\tif (config.mode === 'number') {\n\t\treturn new SingleStoreBigInt53Builder(name, config.unsigned);\n\t}\n\treturn new SingleStoreBigInt64Builder(name, config.unsigned);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA8F;AAWvF,MAAM,mCACJ,wDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,WAAoB,OAAO;AACvD,UAAM,MAAM,UAAU,qBAAqB;AAC3C,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA;AAAA,EAGS,MACR,OACuD;AACvD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,4BACJ,iDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,SAAS,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACxD;AAAA,EAES,mBAAmB,OAAgC;AAC3D,QAAI,OAAO,UAAU,UAAU;AAC9B,aAAO;AAAA,IACR;AACA,WAAO,OAAO,KAAK;AAAA,EACpB;AACD;AAYO,MAAM,mCACJ,wDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,WAAoB,OAAO;AACvD,UAAM,MAAM,UAAU,qBAAqB;AAC3C,SAAK,OAAO,WAAW;AAAA,EACxB;AAAA;AAAA,EAGS,MACR,OACuD;AACvD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,4BACJ,iDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO,SAAS,KAAK,OAAO,WAAW,cAAc,EAAE;AAAA,EACxD;AAAA;AAAA,EAGS,mBAAmB,OAAuB;AAClD,WAAO,OAAO,KAAK;AAAA,EACpB;AACD;AAcO,SAAS,OAAO,GAAsC,GAA6B;AACzF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAgD,GAAG,CAAC;AAC7E,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO,IAAI,2BAA2B,MAAM,OAAO,QAAQ;AAAA,EAC5D;AACA,SAAO,IAAI,2BAA2B,MAAM,OAAO,QAAQ;AAC5D;", "names": []}