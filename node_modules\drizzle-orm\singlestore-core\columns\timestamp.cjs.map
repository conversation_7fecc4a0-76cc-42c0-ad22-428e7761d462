{"version": 3, "sources": ["../../../src/singlestore-core/columns/timestamp.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { sql } from '~/sql/sql.ts';\nimport { type Equal, getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreDateBaseColumn, SingleStoreDateColumnBaseBuilder } from './date.common.ts';\n\nexport type SingleStoreTimestampBuilderInitial<TName extends string> = SingleStoreTimestampBuilder<{\n\tname: TName;\n\tdataType: 'date';\n\tcolumnType: 'SingleStoreTimestamp';\n\tdata: Date;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreTimestampBuilder<T extends ColumnBuilderBaseConfig<'date', 'SingleStoreTimestamp'>>\n\textends SingleStoreDateColumnBaseBuilder<T, SingleStoreTimestampConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreTimestampBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'date', 'SingleStoreTimestamp');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreTimestamp<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreTimestamp<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n\n\toverride defaultNow() {\n\t\treturn this.default(sql`CURRENT_TIMESTAMP`);\n\t}\n}\n\nexport class SingleStoreTimestamp<T extends ColumnBaseConfig<'date', 'SingleStoreTimestamp'>>\n\textends SingleStoreDateBaseColumn<T, SingleStoreTimestampConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreTimestamp';\n\n\tgetSQLType(): string {\n\t\treturn `timestamp`;\n\t}\n\n\toverride mapFromDriverValue(value: string): Date {\n\t\treturn new Date(value + '+0000');\n\t}\n\n\toverride mapToDriverValue(value: Date): string {\n\t\treturn value.toISOString().slice(0, -1).replace('T', ' ');\n\t}\n}\n\nexport type SingleStoreTimestampStringBuilderInitial<TName extends string> = SingleStoreTimestampStringBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreTimestampString';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreTimestampStringBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'SingleStoreTimestampString'>,\n> extends SingleStoreDateColumnBaseBuilder<T, SingleStoreTimestampConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreTimestampStringBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'string', 'SingleStoreTimestampString');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreTimestampString<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreTimestampString<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n\n\toverride defaultNow() {\n\t\treturn this.default(sql`CURRENT_TIMESTAMP`);\n\t}\n}\n\nexport class SingleStoreTimestampString<T extends ColumnBaseConfig<'string', 'SingleStoreTimestampString'>>\n\textends SingleStoreDateBaseColumn<T, SingleStoreTimestampConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreTimestampString';\n\n\tgetSQLType(): string {\n\t\treturn `timestamp`;\n\t}\n}\n\nexport interface SingleStoreTimestampConfig<TMode extends 'string' | 'date' = 'string' | 'date'> {\n\tmode?: TMode;\n}\n\nexport function timestamp(): SingleStoreTimestampBuilderInitial<''>;\nexport function timestamp<TMode extends SingleStoreTimestampConfig['mode'] & {}>(\n\tconfig?: SingleStoreTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? SingleStoreTimestampStringBuilderInitial<''>\n\t: SingleStoreTimestampBuilderInitial<''>;\nexport function timestamp<TName extends string, TMode extends SingleStoreTimestampConfig['mode'] & {}>(\n\tname: TName,\n\tconfig?: SingleStoreTimestampConfig<TMode>,\n): Equal<TMode, 'string'> extends true ? SingleStoreTimestampStringBuilderInitial<TName>\n\t: SingleStoreTimestampBuilderInitial<TName>;\nexport function timestamp(a?: string | SingleStoreTimestampConfig, b: SingleStoreTimestampConfig = {}) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreTimestampConfig | undefined>(a, b);\n\tif (config?.mode === 'string') {\n\t\treturn new SingleStoreTimestampStringBuilder(name);\n\t}\n\treturn new SingleStoreTimestampBuilder(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,iBAAoB;AACpB,mBAAmD;AACnD,yBAA4E;AAYrE,MAAM,oCACJ,oDACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,sBAAsB;AAAA,EAC3C;AAAA;AAAA,EAGS,MACR,OACwD;AACxD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA,EAES,aAAa;AACrB,WAAO,KAAK,QAAQ,iCAAsB;AAAA,EAC3C;AACD;AAEO,MAAM,6BACJ,6CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAAqB;AAChD,WAAO,oBAAI,KAAK,QAAQ,OAAO;AAAA,EAChC;AAAA,EAES,iBAAiB,OAAqB;AAC9C,WAAO,MAAM,YAAY,EAAE,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,GAAG;AAAA,EACzD;AACD;AAYO,MAAM,0CAEH,oDAAgE;AAAA,EACzE,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,4BAA4B;AAAA,EACnD;AAAA;AAAA,EAGS,MACR,OAC8D;AAC9D,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AAAA,EAES,aAAa;AACrB,WAAO,KAAK,QAAQ,iCAAsB;AAAA,EAC3C;AACD;AAEO,MAAM,mCACJ,6CACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAgBO,SAAS,UAAU,GAAyC,IAAgC,CAAC,GAAG;AACtG,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA+D,GAAG,CAAC;AAC5F,MAAI,QAAQ,SAAS,UAAU;AAC9B,WAAO,IAAI,kCAAkC,IAAI;AAAA,EAClD;AACA,SAAO,IAAI,4BAA4B,IAAI;AAC5C;", "names": []}