-- 添加数据库性能优化索引
-- 创建时间: 2025-06-28
-- 目的: 优化 LinganApp 数据库查询性能

-- 为 tasks 表添加单列索引
CREATE INDEX IF NOT EXISTS tasks_order_index_idx ON tasks(order_index);
CREATE INDEX IF NOT EXISTS tasks_is_completed_idx ON tasks(is_completed);
CREATE INDEX IF NOT EXISTS tasks_due_date_idx ON tasks(due_date);
CREATE INDEX IF NOT EXISTS tasks_parent_task_id_idx ON tasks(parent_task_id);
CREATE INDEX IF NOT EXISTS tasks_created_at_idx ON tasks(created_at);
CREATE INDEX IF NOT EXISTS tasks_deleted_at_idx ON tasks(deleted_at);
CREATE INDEX IF NOT EXISTS tasks_priority_idx ON tasks(priority);
CREATE INDEX IF NOT EXISTS tasks_task_type_idx ON tasks(task_type);

-- 为 tasks 表添加复合索引 - 优化常用查询组合
CREATE INDEX IF NOT EXISTS tasks_status_order_idx ON tasks(is_completed, order_index);
CREATE INDEX IF NOT EXISTS tasks_deleted_status_idx ON tasks(deleted_at, is_completed);
CREATE INDEX IF NOT EXISTS tasks_parent_status_idx ON tasks(parent_task_id, is_completed);
CREATE INDEX IF NOT EXISTS tasks_due_date_status_idx ON tasks(due_date, is_completed);
CREATE INDEX IF NOT EXISTS tasks_priority_status_idx ON tasks(priority, is_completed);
CREATE INDEX IF NOT EXISTS tasks_type_status_idx ON tasks(task_type, is_completed);

-- 时间范围查询优化
CREATE INDEX IF NOT EXISTS tasks_created_date_idx ON tasks(created_at, deleted_at);
CREATE INDEX IF NOT EXISTS tasks_due_date_range_idx ON tasks(due_date, deleted_at, is_completed);

-- 为 task_templates 表添加索引
CREATE INDEX IF NOT EXISTS task_templates_is_public_idx ON task_templates(is_public);
CREATE INDEX IF NOT EXISTS task_templates_created_by_idx ON task_templates(created_by);
CREATE INDEX IF NOT EXISTS task_templates_priority_idx ON task_templates(priority);
CREATE INDEX IF NOT EXISTS task_templates_name_idx ON task_templates(name);

-- 为 task_templates 表添加复合索引
CREATE INDEX IF NOT EXISTS task_templates_public_created_idx ON task_templates(is_public, created_at);
CREATE INDEX IF NOT EXISTS task_templates_created_by_priority_idx ON task_templates(created_by, priority);

-- 为 task_dependencies 表添加索引
CREATE INDEX IF NOT EXISTS task_dependencies_dependent_task_idx ON task_dependencies(dependent_task_id);
CREATE INDEX IF NOT EXISTS task_dependencies_prerequisite_task_idx ON task_dependencies(prerequisite_task_id);
CREATE INDEX IF NOT EXISTS task_dependencies_dependency_type_idx ON task_dependencies(dependency_type);

-- 为 settings 表添加索引（如果需要频繁查询）
CREATE INDEX IF NOT EXISTS settings_updated_at_idx ON settings(updated_at);

-- 为 tags 表添加索引（如果存在）
CREATE INDEX IF NOT EXISTS tags_name_idx ON tags(name) WHERE name IS NOT NULL;
CREATE INDEX IF NOT EXISTS tags_color_idx ON tags(color) WHERE color IS NOT NULL;

-- 为 task_tags 表添加索引
CREATE INDEX IF NOT EXISTS task_tags_task_id_idx ON task_tags(task_id);
CREATE INDEX IF NOT EXISTS task_tags_tag_id_idx ON task_tags(tag_id);
CREATE INDEX IF NOT EXISTS task_tags_created_at_idx ON task_tags(created_at);

-- 为 recurring_rules 表添加索引
CREATE INDEX IF NOT EXISTS recurring_rules_pattern_type_idx ON recurring_rules(pattern_type);
CREATE INDEX IF NOT EXISTS recurring_rules_is_active_idx ON recurring_rules(is_active);
CREATE INDEX IF NOT EXISTS recurring_rules_start_date_idx ON recurring_rules(start_date);
CREATE INDEX IF NOT EXISTS recurring_rules_end_date_idx ON recurring_rules(end_date);

-- 复合索引用于重复规则查询
CREATE INDEX IF NOT EXISTS recurring_rules_active_pattern_idx ON recurring_rules(is_active, pattern_type);
CREATE INDEX IF NOT EXISTS recurring_rules_date_range_idx ON recurring_rules(start_date, end_date, is_active);

-- 分析表统计信息以优化查询计划
ANALYZE tasks;
ANALYZE task_templates;
ANALYZE task_dependencies;
ANALYZE settings;
ANALYZE tags;
ANALYZE task_tags;
ANALYZE recurring_rules;
