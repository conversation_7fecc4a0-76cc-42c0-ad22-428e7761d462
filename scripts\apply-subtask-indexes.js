#!/usr/bin/env node

/**
 * 子任务系统索引应用脚本
 * 为子任务系统添加专用索引优化
 */

const Database = require('better-sqlite3');
const fs = require('fs');
const path = require('path');

// 数据库路径
const dataDir = path.join(process.cwd(), 'data');
const dbPath = path.join(dataDir, 'app.db');

console.log('🚀 开始应用子任务系统索引优化...');

// 检查数据库是否存在
if (!fs.existsSync(dbPath)) {
  console.error('❌ 数据库文件不存在:', dbPath);
  process.exit(1);
}

// 连接数据库
const db = new Database(dbPath);

// 启用优化设置
db.pragma('journal_mode = WAL');
db.pragma('foreign_keys = ON');
db.pragma('synchronous = NORMAL');

try {
  console.log('📝 应用子任务系统索引...');
  
  // 定义子任务系统专用索引
  const subtaskIndexes = [
    // 子任务系统专用索引
    'CREATE INDEX IF NOT EXISTS tasks_parent_type_idx ON tasks(parent_task_id, task_type)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_order_idx ON tasks(parent_task_id, order_index)',
    'CREATE INDEX IF NOT EXISTS tasks_parent_progress_idx ON tasks(parent_task_id, progress)',
    
    // 层级查询优化的复合索引
    'CREATE INDEX IF NOT EXISTS tasks_hierarchy_idx ON tasks(parent_task_id, deleted_at, is_completed, order_index)',
    
    // 子任务统计查询优化
    'CREATE INDEX IF NOT EXISTS tasks_parent_stats_idx ON tasks(parent_task_id, is_completed, deleted_at)',
    
    // 层级深度查询优化（用于防止过深嵌套）
    'CREATE INDEX IF NOT EXISTS tasks_type_parent_idx ON tasks(task_type, parent_task_id, deleted_at)',
  ];
  
  let successCount = 0;
  let skipCount = 0;
  
  // 开始事务
  const transaction = db.transaction(() => {
    for (const indexSQL of subtaskIndexes) {
      try {
        db.exec(indexSQL);
        
        // 提取索引名称
        const indexName = indexSQL.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
        console.log(`✅ 创建子任务索引: ${indexName}`);
        successCount++;
      } catch (error) {
        if (error.message.includes('already exists')) {
          const indexName = indexSQL.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)?.[1];
          console.log(`⏭️  索引已存在，跳过: ${indexName}`);
          skipCount++;
        } else {
          console.warn(`⚠️  创建索引失败: ${error.message}`);
          skipCount++;
        }
      }
    }
  });
  
  // 执行事务
  transaction();
  
  // 更新统计信息
  console.log('📊 更新数据库统计信息...');
  db.exec('ANALYZE tasks');
  
  console.log(`\n📊 子任务索引应用结果:`);
  console.log(`   ✅ 成功创建: ${successCount} 个索引`);
  console.log(`   ⏭️  跳过: ${skipCount} 个索引`);
  
  // 获取子任务相关索引列表
  console.log('\n📋 子任务系统索引列表:');
  const subtaskIndexList = db.prepare(`
    SELECT name, tbl_name 
    FROM sqlite_master 
    WHERE type='index' 
      AND name NOT LIKE 'sqlite_%' 
      AND tbl_name='tasks'
      AND (name LIKE '%parent%' OR name LIKE '%hierarchy%' OR name LIKE '%type%')
    ORDER BY name
  `).all();
  
  subtaskIndexList.forEach(index => {
    console.log(`   📊 ${index.name} (表: ${index.tbl_name})`);
  });
  
  // 测试子任务查询性能
  console.log('\n⚡ 子任务查询性能测试:');
  
  // 检查是否有测试数据
  const taskCount = db.prepare("SELECT COUNT(*) as count FROM tasks WHERE deleted_at IS NULL").get();
  console.log(`   📝 当前任务数量: ${taskCount.count}`);
  
  if (taskCount.count > 0) {
    // 测试子任务相关查询
    const testQueries = [
      {
        name: '获取子任务查询',
        sql: `SELECT * FROM tasks 
              WHERE parent_task_id IS NOT NULL 
                AND deleted_at IS NULL 
              ORDER BY order_index 
              LIMIT 10`
      },
      {
        name: '父任务统计查询',
        sql: `SELECT parent_task_id, COUNT(*) as subtask_count,
                     SUM(CASE WHEN is_completed = 1 THEN 1 ELSE 0 END) as completed_count
              FROM tasks 
              WHERE parent_task_id IS NOT NULL 
                AND deleted_at IS NULL 
              GROUP BY parent_task_id 
              LIMIT 5`
      },
      {
        name: '层级结构查询',
        sql: `SELECT t1.id as parent_id, t1.content as parent_content,
                     t2.id as child_id, t2.content as child_content
              FROM tasks t1 
              LEFT JOIN tasks t2 ON t1.id = t2.parent_task_id 
              WHERE t1.deleted_at IS NULL 
                AND (t2.deleted_at IS NULL OR t2.id IS NULL)
              LIMIT 10`
      }
    ];
    
    testQueries.forEach(({ name, sql }) => {
      const start = Date.now();
      const result = db.prepare(sql).all();
      const duration = Date.now() - start;
      
      console.log(`   ${name}: ${duration}ms (${result.length} 条记录)`);
      
      if (duration > 50) {
        console.log(`     ⚠️  查询较慢，可能需要进一步优化`);
      } else {
        console.log(`     ✅ 查询性能良好`);
      }
    });
  } else {
    console.log('   ℹ️  没有测试数据，跳过性能测试');
  }
  
  // 检查查询计划
  console.log('\n🔍 子任务查询计划分析:');
  const explainResult = db.prepare(`
    EXPLAIN QUERY PLAN 
    SELECT * FROM tasks 
    WHERE parent_task_id = 'test' 
      AND deleted_at IS NULL 
    ORDER BY order_index
  `).all();
  
  const usesIndex = explainResult.some(row => 
    row.detail && row.detail.toLowerCase().includes('index')
  );
  
  if (usesIndex) {
    console.log('   ✅ 子任务查询使用了索引优化');
    explainResult.forEach(row => {
      if (row.detail && row.detail.toLowerCase().includes('index')) {
        console.log(`   📊 ${row.detail}`);
      }
    });
  } else {
    console.log('   ⚠️  子任务查询可能未使用索引');
  }
  
  // 数据库统计信息
  console.log('\n📈 数据库统计信息:');
  try {
    const totalTasks = db.prepare("SELECT COUNT(*) as count FROM tasks WHERE deleted_at IS NULL").get();
    const parentTasks = db.prepare("SELECT COUNT(*) as count FROM tasks WHERE parent_task_id IS NULL AND deleted_at IS NULL").get();
    const subTasks = db.prepare("SELECT COUNT(*) as count FROM tasks WHERE parent_task_id IS NOT NULL AND deleted_at IS NULL").get();
    
    console.log(`   📝 总任务数量: ${totalTasks.count}`);
    console.log(`   📝 父任务数量: ${parentTasks.count}`);
    console.log(`   📝 子任务数量: ${subTasks.count}`);
    
    if (totalTasks.count > 0) {
      const subtaskRatio = Math.round((subTasks.count / totalTasks.count) * 100);
      console.log(`   📊 子任务比例: ${subtaskRatio}%`);
    }
    
    const dbSize = fs.statSync(dbPath).size;
    console.log(`   💾 数据库大小: ${(dbSize / 1024 / 1024).toFixed(2)} MB`);
    
    console.log(`   📊 子任务索引数: ${subtaskIndexList.length}`);
  } catch (error) {
    console.log(`   ⚠️  无法获取统计信息: ${error.message}`);
  }
  
  console.log('\n🎉 子任务系统索引优化应用完成！');
  
} catch (error) {
  console.error('❌ 应用子任务索引时发生错误:', error);
  process.exit(1);
} finally {
  db.close();
}

console.log('\n📋 后续步骤:');
console.log('1. 重启应用以使用优化后的子任务系统');
console.log('2. 运行子任务功能测试验证功能正确性');
console.log('3. 监控子任务查询性能和应用响应时间');
console.log('4. 查看子任务系统文档了解使用方法');
