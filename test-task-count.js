const { getDatabase } = require('./dist/shared/db/index.js')
const { tasks } = require('./dist/shared/db/schema.js')
const { eq, ne, and, or, isNull, count } = require('drizzle-orm')

async function testTaskCount() {
  console.log('开始测试任务计数逻辑...')
  
  try {
    const db = getDatabase()
    
    // 1. 获取所有任务
    console.log('\n=== 所有任务 ===')
    const allTasks = await db.select().from(tasks).where(isNull(tasks.deletedAt))
    console.log('所有任务数量:', allTasks.length)
    allTasks.forEach(task => {
      console.log(`- ${task.content} (类型: ${task.taskType || '主任务'}, 父任务: ${task.parentTaskId || '无'})`)
    })
    
    // 2. 获取主任务（排除子任务）
    console.log('\n=== 主任务（排除子任务）===')
    const mainTasks = await db.select().from(tasks).where(
      and(
        isNull(tasks.deletedAt),
        or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
      )
    )
    console.log('主任务数量:', mainTasks.length)
    mainTasks.forEach(task => {
      console.log(`- ${task.content} (类型: ${task.taskType || '主任务'})`)
    })
    
    // 3. 获取子任务
    console.log('\n=== 子任务 ===')
    const subtasks = await db.select().from(tasks).where(
      and(
        isNull(tasks.deletedAt),
        eq(tasks.taskType, 'subtask')
      )
    )
    console.log('子任务数量:', subtasks.length)
    subtasks.forEach(task => {
      console.log(`- ${task.content} (父任务: ${task.parentTaskId})`)
    })
    
    // 4. 测试统计查询
    console.log('\n=== 统计查询测试 ===')
    const [totalResult, completedResult, pendingResult] = await Promise.all([
      // 总主任务数
      db
        .select({ count: count() })
        .from(tasks)
        .where(and(
          isNull(tasks.deletedAt),
          or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
        )),
      
      // 已完成主任务数
      db
        .select({ count: count() })
        .from(tasks)
        .where(and(
          isNull(tasks.deletedAt),
          eq(tasks.isCompleted, true),
          or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
        )),
      
      // 待完成主任务数
      db
        .select({ count: count() })
        .from(tasks)
        .where(and(
          isNull(tasks.deletedAt),
          eq(tasks.isCompleted, false),
          or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
        ))
    ])
    
    console.log('统计结果:')
    console.log('- 总主任务数:', totalResult[0].count)
    console.log('- 已完成主任务数:', completedResult[0].count)
    console.log('- 待完成主任务数:', pendingResult[0].count)
    
    // 5. 创建测试数据
    console.log('\n=== 创建测试数据 ===')
    
    // 创建一个主任务
    const mainTaskId = 'test-main-' + Date.now()
    await db.insert(tasks).values({
      id: mainTaskId,
      content: '测试主任务',
      isCompleted: false,
      priority: 2,
      orderIndex: 1,
      createdAt: Date.now(),
      deletedAt: null,
      parentTaskId: null,
      taskType: null,
      description: null,
      dueDate: null,
      isImportant: false,
      estimatedDuration: null,
      progress: 0
    })
    console.log('创建主任务:', mainTaskId)
    
    // 创建两个子任务
    const subtask1Id = 'test-sub1-' + Date.now()
    const subtask2Id = 'test-sub2-' + Date.now()
    
    await db.insert(tasks).values([
      {
        id: subtask1Id,
        content: '测试子任务1',
        isCompleted: false,
        priority: 2,
        orderIndex: 1,
        createdAt: Date.now(),
        deletedAt: null,
        parentTaskId: mainTaskId,
        taskType: 'subtask',
        description: null,
        dueDate: null,
        isImportant: false,
        estimatedDuration: null,
        progress: 0
      },
      {
        id: subtask2Id,
        content: '测试子任务2',
        isCompleted: true,
        priority: 2,
        orderIndex: 2,
        createdAt: Date.now(),
        deletedAt: null,
        parentTaskId: mainTaskId,
        taskType: 'subtask',
        description: null,
        dueDate: null,
        isImportant: false,
        estimatedDuration: null,
        progress: 100
      }
    ])
    console.log('创建子任务:', subtask1Id, subtask2Id)
    
    // 6. 重新测试统计
    console.log('\n=== 创建测试数据后的统计 ===')
    const [newTotalResult, newCompletedResult, newPendingResult] = await Promise.all([
      // 总主任务数
      db
        .select({ count: count() })
        .from(tasks)
        .where(and(
          isNull(tasks.deletedAt),
          or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
        )),
      
      // 已完成主任务数
      db
        .select({ count: count() })
        .from(tasks)
        .where(and(
          isNull(tasks.deletedAt),
          eq(tasks.isCompleted, true),
          or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
        )),
      
      // 待完成主任务数
      db
        .select({ count: count() })
        .from(tasks)
        .where(and(
          isNull(tasks.deletedAt),
          eq(tasks.isCompleted, false),
          or(isNull(tasks.taskType), ne(tasks.taskType, 'subtask'))
        ))
    ])
    
    console.log('新的统计结果:')
    console.log('- 总主任务数:', newTotalResult[0].count, '(应该比之前增加1)')
    console.log('- 已完成主任务数:', newCompletedResult[0].count)
    console.log('- 待完成主任务数:', newPendingResult[0].count, '(应该比之前增加1)')
    
    // 验证结果
    const expectedTotal = totalResult[0].count + 1
    const expectedPending = pendingResult[0].count + 1
    
    if (newTotalResult[0].count === expectedTotal && newPendingResult[0].count === expectedPending) {
      console.log('\n✅ 测试通过！任务计数逻辑正确排除了子任务')
    } else {
      console.log('\n❌ 测试失败！任务计数逻辑有问题')
    }
    
  } catch (error) {
    console.error('测试失败:', error)
  }
}

testTaskCount()
