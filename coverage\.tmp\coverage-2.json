{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 1}], "isBlockCoverage": true}, {"functionName": "getPropertyValue", "ranges": [{"startOffset": 2430, "endOffset": 2608, "count": 2}, {"startOffset": 2541, "endOffset": 2546, "count": 0}, {"startOffset": 2555, "endOffset": 2607, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 0}], "isBlockCoverage": false}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1415", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/theme.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 25473, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 25473, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 562, "endOffset": 7189, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 611, "endOffset": 2001, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 661, "endOffset": 765, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 814, "endOffset": 1326, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 874, "endOffset": 1318, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1372, "endOffset": 1574, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1440, "endOffset": 1459, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1621, "endOffset": 1995, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1700, "endOffset": 1733, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1806, "endOffset": 1838, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2047, "endOffset": 3204, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2110, "endOffset": 2384, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2453, "endOffset": 2603, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2666, "endOffset": 3198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2995, "endOffset": 3079, "count": 5}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3107, "endOffset": 3190, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3250, "endOffset": 4394, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3306, "endOffset": 4079, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3839, "endOffset": 4071, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3886, "endOffset": 4061, "count": 114}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4129, "endOffset": 4388, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4189, "endOffset": 4380, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4246, "endOffset": 4370, "count": 114}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4440, "endOffset": 5553, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4491, "endOffset": 5222, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5271, "endOffset": 5547, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5599, "endOffset": 7185, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5652, "endOffset": 5938, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5986, "endOffset": 6275, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6323, "endOffset": 6586, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6635, "endOffset": 7179, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7235, "endOffset": 9027, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7278, "endOffset": 7383, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7429, "endOffset": 8304, "count": 1}, {"startOffset": 7524, "endOffset": 7549, "count": 0}], "isBlockCoverage": true}, {"functionName": "applyTheme", "ranges": [{"startOffset": 7573, "endOffset": 7968, "count": 1}, {"startOffset": 7863, "endOffset": 7908, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7676, "endOffset": 7826, "count": 19}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8351, "endOffset": 8634, "count": 1}, {"startOffset": 8453, "endOffset": 8480, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8681, "endOffset": 9023, "count": 1}, {"startOffset": 8786, "endOffset": 8813, "count": 0}], "isBlockCoverage": true}]}, {"scriptId": "1416", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/theme.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 26540, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1399, "endOffset": 1432, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1854, "endOffset": 1881, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2496, "endOffset": 2529, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7696, "endOffset": 7727, "count": 7}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8083, "endOffset": 8119, "count": 7}], "isBlockCoverage": true}, {"functionName": "getThemeById", "ranges": [{"startOffset": 8144, "endOffset": 8214, "count": 11}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8184, "endOffset": 8210, "count": 32}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8316, "endOffset": 8344, "count": 11}], "isBlockCoverage": true}, {"functionName": "getThemesByMode", "ranges": [{"startOffset": 8372, "endOffset": 8450, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8416, "endOffset": 8446, "count": 12}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 8555, "endOffset": 8586, "count": 2}], "isBlockCoverage": true}, {"functionName": "isValidThemeId", "ranges": [{"startOffset": 8613, "endOffset": 8683, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8787, "endOffset": 8817, "count": 0}], "isBlockCoverage": false}]}]}