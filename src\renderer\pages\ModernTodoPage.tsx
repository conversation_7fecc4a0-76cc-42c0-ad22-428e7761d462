import React, { useRef } from 'react'
import { ModernLayout } from '../components/layout/ModernLayout'
import { ModernTaskInput } from '../components/task/ModernTaskInput'
import { ModernTaskList } from '../components/task/ModernTaskList'
import { UndoActionPanel } from '../components/task/UndoActionPanel'
import { TaskStats } from '../components/task/TaskStats'
import { useViewStore } from '../stores/viewStore'

export function ModernTodoPage() {
  const taskInputRef = useRef<HTMLDivElement>(null)
  const { setTaskInputFocused } = useViewStore()

  const handleAddTaskClick = () => {
    setTaskInputFocused(true)
    // 滚动到任务输入区域
    taskInputRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
  }

  return (
    <ModernLayout onAddTaskClick={handleAddTaskClick}>
      <div className="space-y-6">
        {/* 任务统计 - 现代化样式 */}
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6 border border-primary/20">
          <TaskStats />
        </div>

        {/* 添加任务 */}
        <div ref={taskInputRef}>
          <ModernTaskInput />
        </div>

        {/* 任务列表 */}
        <ModernTaskList />
      </div>

      {/* 撤销操作面板 */}
      <UndoActionPanel />
    </ModernLayout>
  )
}
