const path = require('path')
const fs = require('fs')
const { execSync } = require('child_process')

console.log('🧪 测试任务统计修复')
console.log('='.repeat(50))

async function testTaskStatsFix() {
  try {
    // 检查数据库文件是否存在
    const dbPath = path.join(__dirname, '..', 'data', 'app.db')
    console.log(`📁 数据库路径: ${dbPath}`)
    
    if (!fs.existsSync(dbPath)) {
      console.log('❌ 数据库文件不存在')
      return
    }
    
    console.log('✅ 开始测试')
    
    // 1. 检查当前数据库状态
    console.log('\n📊 1. 当前数据库状态')
    console.log('-'.repeat(30))
    
    const statsQueries = [
      { name: '总任务数', query: "SELECT COUNT(*) FROM tasks;" },
      { name: '活跃任务数', query: "SELECT COUNT(*) FROM tasks WHERE deletedAt IS NULL;" },
      { name: '已删除任务数', query: "SELECT COUNT(*) FROM tasks WHERE deletedAt IS NOT NULL;" },
      { name: '已完成任务(全部)', query: "SELECT COUNT(*) FROM tasks WHERE isCompleted = 1;" },
      { name: '已完成任务(活跃)', query: "SELECT COUNT(*) FROM tasks WHERE isCompleted = 1 AND deletedAt IS NULL;" },
      { name: '已完成任务(已删除)', query: "SELECT COUNT(*) FROM tasks WHERE isCompleted = 1 AND deletedAt IS NOT NULL;" }
    ]
    
    const stats = {}
    statsQueries.forEach(({ name, query }) => {
      const result = execSync(`sqlite3 "${dbPath}" "${query}"`, { encoding: 'utf8' }).trim()
      stats[name] = parseInt(result)
      console.log(`${name}: ${stats[name]}`)
    })
    
    // 2. 分析问题
    console.log('\n🔍 2. 问题分析')
    console.log('-'.repeat(30))
    
    const hasDeletedCompletedTasks = stats['已完成任务(已删除)'] > 0
    const hasActiveCompletedTasks = stats['已完成任务(活跃)'] > 0
    
    if (hasDeletedCompletedTasks) {
      console.log(`⚠️ 发现 ${stats['已完成任务(已删除)']} 个已完成但已删除的任务`)
      console.log('这些任务会被旧的统计逻辑错误计入已完成任务数')
    }
    
    if (hasActiveCompletedTasks) {
      console.log(`✅ 发现 ${stats['已完成任务(活跃)']} 个正常的已完成任务`)
      console.log('这些任务应该在已完成任务列表中显示')
    }
    
    // 3. 模拟修复前后的统计结果
    console.log('\n📈 3. 修复前后对比')
    console.log('-'.repeat(30))
    
    const beforeFix = stats['已完成任务(全部)']  // 旧逻辑：不过滤删除的任务
    const afterFix = stats['已完成任务(活跃)']   // 新逻辑：只计算活跃任务
    
    console.log(`修复前统计 (错误): ${beforeFix} 个已完成任务`)
    console.log(`修复后统计 (正确): ${afterFix} 个已完成任务`)
    
    if (beforeFix > afterFix) {
      console.log(`✅ 修复有效: 减少了 ${beforeFix - afterFix} 个错误计入的已删除任务`)
    } else if (beforeFix === afterFix) {
      console.log('ℹ️ 数据一致: 没有已删除的已完成任务')
    }
    
    // 4. 检查已完成任务的具体内容
    if (hasActiveCompletedTasks) {
      console.log('\n📝 4. 已完成任务详情')
      console.log('-'.repeat(30))
      
      const completedQuery = `
        SELECT id, content, createdAt, parentTaskId
        FROM tasks 
        WHERE isCompleted = 1 AND deletedAt IS NULL
        ORDER BY createdAt DESC;
      `
      
      const completedResult = execSync(`sqlite3 "${dbPath}" "${completedQuery}"`, { encoding: 'utf8' })
      const completedLines = completedResult.trim().split('\n').filter(line => line.trim())
      
      console.log(`找到 ${completedLines.length} 个已完成的活跃任务:`)
      completedLines.forEach((line, index) => {
        const parts = line.split('|')
        if (parts.length >= 4) {
          const [id, content, createdAt, parentTaskId] = parts
          const taskType = parentTaskId && parentTaskId !== '' ? '子任务' : '主任务'
          console.log(`${index + 1}. [${id.slice(-8)}] ${content} (${taskType})`)
        }
      })
    }
    
    // 5. 验证前端过滤逻辑
    console.log('\n🔍 5. 前端过滤验证')
    console.log('-'.repeat(30))
    
    if (hasActiveCompletedTasks) {
      // 检查是否有主任务（非子任务）
      const mainCompletedQuery = `
        SELECT COUNT(*) 
        FROM tasks 
        WHERE isCompleted = 1 AND deletedAt IS NULL AND parentTaskId IS NULL;
      `
      
      const mainCompletedResult = execSync(`sqlite3 "${dbPath}" "${mainCompletedQuery}"`, { encoding: 'utf8' }).trim()
      const mainCompletedCount = parseInt(mainCompletedResult)
      
      console.log(`已完成的主任务数: ${mainCompletedCount}`)
      console.log(`已完成的子任务数: ${afterFix - mainCompletedCount}`)
      
      if (mainCompletedCount === 0) {
        console.log('⚠️ 所有已完成任务都是子任务')
        console.log('前端可能只显示主任务，这解释了为什么列表为空')
      } else {
        console.log('✅ 有已完成的主任务，应该在列表中显示')
      }
    }
    
    // 6. 总结和建议
    console.log('\n🎯 6. 总结和建议')
    console.log('-'.repeat(30))
    
    if (beforeFix > afterFix) {
      console.log('✅ 主要问题: 统计逻辑已修复')
      console.log('📝 已修改 src/main/index.ts 中的 task:getStats 处理器')
      console.log('🔄 需要重启应用以生效')
    }
    
    if (hasActiveCompletedTasks && stats['已完成任务(活跃)'] > 0) {
      const mainCompletedQuery = `
        SELECT COUNT(*) 
        FROM tasks 
        WHERE isCompleted = 1 AND deletedAt IS NULL AND parentTaskId IS NULL;
      `
      const mainCompletedResult = execSync(`sqlite3 "${dbPath}" "${mainCompletedQuery}"`, { encoding: 'utf8' }).trim()
      const mainCompletedCount = parseInt(mainCompletedResult)
      
      if (mainCompletedCount === 0) {
        console.log('⚠️ 次要问题: 前端可能只显示主任务')
        console.log('📝 建议检查前端过滤逻辑，确保显示所有已完成任务')
      }
    }
    
    console.log('\n🚀 下一步操作:')
    console.log('1. 重启 LinganApp 应用')
    console.log('2. 检查统计数字是否正确')
    console.log('3. 验证已完成任务列表是否显示正常')
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message)
  }
}

// 运行测试
testTaskStatsFix().then(() => {
  console.log('\n🎉 测试完成')
}).catch(error => {
  console.error('❌ 测试失败:', error)
  process.exit(1)
})
