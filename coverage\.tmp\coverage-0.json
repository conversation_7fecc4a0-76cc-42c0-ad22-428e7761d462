{"result": [{"scriptId": "1132", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7464, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 620, "endOffset": 761, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 886, "endOffset": 1219, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1347, "endOffset": 1716, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 1936, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2018, "endOffset": 2159, "count": 0}], "isBlockCoverage": false}, {"functionName": "window.getComputedStyle", "ranges": [{"startOffset": 2306, "endOffset": 2635, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1402", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/renderer/__tests__/mocks.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 18636, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 868, "endOffset": 892, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1123, "endOffset": 1156, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1464, "endOffset": 1500, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1743, "endOffset": 1774, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2022, "endOffset": 2049, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2263, "endOffset": 2288, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2510, "endOffset": 2539, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2877, "endOffset": 2912, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3150, "endOffset": 3185, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTask", "ranges": [{"startOffset": 3189, "endOffset": 3368, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3471, "endOffset": 3501, "count": 0}], "isBlockCoverage": false}, {"functionName": "createMockTasks", "ranges": [{"startOffset": 3505, "endOffset": 3768, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3872, "endOffset": 3903, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4083, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4309, "endOffset": 4395, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5025, "endOffset": 5056, "count": 0}], "isBlockCoverage": false}, {"functionName": "resetAllMocks", "ranges": [{"startOffset": 5060, "endOffset": 5544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5646, "endOffset": 5675, "count": 0}], "isBlockCoverage": false}, {"functionName": "setTasksResponse", "ranges": [{"startOffset": 5721, "endOffset": 5795, "count": 0}], "isBlockCoverage": false}, {"functionName": "setStatsResponse", "ranges": [{"startOffset": 5817, "endOffset": 5893, "count": 0}], "isBlockCoverage": false}, {"functionName": "setCreateTaskResponse", "ranges": [{"startOffset": 5920, "endOffset": 5992, "count": 0}], "isBlockCoverage": false}, {"functionName": "setUpdateTaskResponse", "ranges": [{"startOffset": 6019, "endOffset": 6091, "count": 0}], "isBlockCoverage": false}, {"functionName": "setDeleteTaskResponse", "ranges": [{"startOffset": 6118, "endOffset": 6203, "count": 0}], "isBlockCoverage": false}, {"functionName": "setReorderTasksResponse", "ranges": [{"startOffset": 6232, "endOffset": 6318, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeTasksThrow", "ranges": [{"startOffset": 6338, "endOffset": 6412, "count": 0}], "isBlockCoverage": false}, {"functionName": "makeCreateTaskThrow", "ranges": [{"startOffset": 6437, "endOffset": 6511, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6614, "endOffset": 6641, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1403", "url": "file:///D:/data/Study-Jacksu/LinganApp/src/shared/types/task.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 24903, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 466, "endOffset": 494, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 667, "endOffset": 693, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 880, "endOffset": 906, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1089, "endOffset": 1113, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1398, "endOffset": 1428, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1673, "endOffset": 1709, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2818, "endOffset": 2844, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3641, "endOffset": 3673, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4599, "endOffset": 4631, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4896, "endOffset": 4929, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5451, "endOffset": 5480, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5887, "endOffset": 5922, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6775, "endOffset": 6809, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7515, "endOffset": 7555, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 7775, "endOffset": 7806, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 8149, "endOffset": 8180, "count": 0}], "isBlockCoverage": false}]}]}