/**
 * 测试运行脚本
 * 用于绕过终端问题直接运行测试
 */

const { spawn } = require('child_process')
const path = require('path')

console.log('🧪 开始运行 LinganApp 测试套件...\n')

// 运行基础功能测试
function runBasicTests() {
  return new Promise((resolve, reject) => {
    console.log('📋 运行基础功能测试...')
    
    const testProcess = spawn('npx', ['vitest', 'run', 'src/renderer/__tests__/basic-functionality.test.ts'], {
      cwd: process.cwd(),
      stdio: 'inherit',
      shell: true
    })

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 基础功能测试通过\n')
        resolve()
      } else {
        console.log('❌ 基础功能测试失败\n')
        reject(new Error(`Test failed with code ${code}`))
      }
    })

    testProcess.on('error', (error) => {
      console.error('❌ 测试进程错误:', error)
      reject(error)
    })
  })
}

// 运行所有测试
function runAllTests() {
  return new Promise((resolve, reject) => {
    console.log('📋 运行所有测试...')
    
    const testProcess = spawn('npx', ['vitest', 'run'], {
      cwd: process.cwd(),
      stdio: 'inherit',
      shell: true
    })

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 所有测试通过\n')
        resolve()
      } else {
        console.log('⚠️ 部分测试失败或有问题\n')
        resolve() // 不阻止后续步骤
      }
    })

    testProcess.on('error', (error) => {
      console.error('❌ 测试进程错误:', error)
      reject(error)
    })
  })
}

// 生成覆盖率报告
function runCoverageTests() {
  return new Promise((resolve, reject) => {
    console.log('📊 生成测试覆盖率报告...')
    
    const testProcess = spawn('npx', ['vitest', 'run', '--coverage'], {
      cwd: process.cwd(),
      stdio: 'inherit',
      shell: true
    })

    testProcess.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 覆盖率报告生成成功\n')
        resolve()
      } else {
        console.log('⚠️ 覆盖率报告生成有问题\n')
        resolve() // 不阻止后续步骤
      }
    })

    testProcess.on('error', (error) => {
      console.error('❌ 覆盖率进程错误:', error)
      reject(error)
    })
  })
}

// 主执行函数
async function main() {
  try {
    console.log('🔧 检查环境...')
    console.log('Node.js 版本:', process.version)
    console.log('工作目录:', process.cwd())
    console.log('')

    // 步骤1: 运行基础功能测试
    await runBasicTests()

    // 步骤2: 运行所有测试
    await runAllTests()

    // 步骤3: 生成覆盖率报告
    await runCoverageTests()

    console.log('🎉 测试套件运行完成!')
    console.log('')
    console.log('📊 查看覆盖率报告:')
    console.log('   - 打开 coverage/index.html 查看详细报告')
    console.log('   - 或查看控制台输出的覆盖率摘要')
    console.log('')

  } catch (error) {
    console.error('❌ 测试运行失败:', error.message)
    process.exit(1)
  }
}

// 运行主函数
main().catch(error => {
  console.error('❌ 意外错误:', error)
  process.exit(1)
})
