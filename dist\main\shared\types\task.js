"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PRIORITY_COLORS = exports.PRIORITY_LABELS = exports.CreateTaskTemplateSchema = exports.TaskTemplateSchema = exports.CreateTaskTagSchema = exports.TaskTagSchema = exports.HIERARCHY_CONFIG = exports.TaskHierarchySchema = exports.ReorderTaskSchema = exports.UpdateTaskSchema = exports.CreateTaskSchema = exports.TaskSchema = exports.RecurringPatternType = exports.DependencyType = exports.TaskType = exports.TaskFilter = exports.TaskStatus = exports.TaskPriority = void 0;
const zod_1 = require("zod");
// 任务优先级枚举
exports.TaskPriority = {
    HIGH: 1,
    MEDIUM: 2,
    LOW: 3,
};
// 任务状态枚举
exports.TaskStatus = {
    PENDING: 'pending',
    COMPLETED: 'completed',
};
// 任务过滤器类型
exports.TaskFilter = {
    ALL: 'all',
    COMPLETED: 'completed',
    PENDING: 'pending',
};
// 任务类型枚举
exports.TaskType = {
    TASK: 'task',
    SUBTASK: 'subtask',
    TEMPLATE: 'template',
};
// 依赖类型枚举
exports.DependencyType = {
    FINISH_TO_START: 'finish_to_start',
    START_TO_START: 'start_to_start',
    FINISH_TO_FINISH: 'finish_to_finish',
    START_TO_FINISH: 'start_to_finish',
};
// 重复模式类型枚举
exports.RecurringPatternType = {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    MONTHLY: 'monthly',
    YEARLY: 'yearly',
    CUSTOM: 'custom',
};
// Zod 验证 schemas
exports.TaskSchema = zod_1.z.object({
    id: zod_1.z.string(),
    content: zod_1.z.string().min(1, '任务内容不能为空'),
    isCompleted: zod_1.z.boolean(),
    priority: zod_1.z.number().int().min(1).max(3),
    dueDate: zod_1.z.number().int().nullable(),
    orderIndex: zod_1.z.number().int(),
    createdAt: zod_1.z.number().int(),
    deletedAt: zod_1.z.number().int().nullable().optional(),
    // 高级功能字段
    parentTaskId: zod_1.z.string().nullable().optional(),
    taskType: zod_1.z.enum(['task', 'subtask', 'template']).default('task'),
    description: zod_1.z.string().nullable().optional(),
    estimatedDuration: zod_1.z.number().int().nullable().optional(),
    actualDuration: zod_1.z.number().int().nullable().optional(),
    progress: zod_1.z.number().int().min(0).max(100).default(0),
});
exports.CreateTaskSchema = zod_1.z.object({
    content: zod_1.z.string().min(1, '任务内容不能为空'),
    priority: zod_1.z.number().int().min(1).max(3).default(exports.TaskPriority.MEDIUM),
    dueDate: zod_1.z.number().int().nullable().optional(),
    parentTaskId: zod_1.z.string().nullable().optional(),
    taskType: zod_1.z.enum(['task', 'subtask', 'template']).default('task'),
    description: zod_1.z.string().nullable().optional(),
    estimatedDuration: zod_1.z.number().int().nullable().optional(),
    progress: zod_1.z.number().int().min(0).max(100).default(0),
});
exports.UpdateTaskSchema = zod_1.z.object({
    content: zod_1.z.string().min(1, '任务内容不能为空').optional(),
    isCompleted: zod_1.z.boolean().optional(),
    priority: zod_1.z.number().int().min(1).max(3).optional(),
    dueDate: zod_1.z.number().int().nullable().optional(),
    parentTaskId: zod_1.z.string().nullable().optional(),
    taskType: zod_1.z.enum(['task', 'subtask', 'template']).optional(),
    description: zod_1.z.string().nullable().optional(),
    estimatedDuration: zod_1.z.number().int().nullable().optional(),
    actualDuration: zod_1.z.number().int().nullable().optional(),
    progress: zod_1.z.number().int().min(0).max(100).optional(),
});
exports.ReorderTaskSchema = zod_1.z.object({
    id: zod_1.z.string(),
    orderIndex: zod_1.z.number().int(),
});
// 先定义递归Schema，稍后再添加类型注解
const TaskHierarchySchemaBase = zod_1.z.lazy(() => zod_1.z.object({
    task: exports.TaskSchema,
    children: zod_1.z.array(TaskHierarchySchemaBase),
    depth: zod_1.z.number().int(),
    path: zod_1.z.array(zod_1.z.string()),
}));
// 导出带类型注解的Schema
exports.TaskHierarchySchema = TaskHierarchySchemaBase;
// 层级限制配置
exports.HIERARCHY_CONFIG = {
    MAX_DEPTH: 5, // 最大层级深度
    MAX_CHILDREN_PER_PARENT: 50, // 每个父任务最大子任务数
    AUTO_COMPLETE_PARENT: true, // 所有子任务完成时自动完成父任务
    AUTO_CALCULATE_PROGRESS: true, // 自动计算父任务进度
};
// 标签相关Schema
exports.TaskTagSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string().min(1, '标签名称不能为空'),
    color: zod_1.z.string().regex(/^#[0-9A-Fa-f]{6}$/, '颜色格式不正确'),
    description: zod_1.z.string().nullable().optional(),
    createdAt: zod_1.z.number().int(),
    updatedAt: zod_1.z.number().int(),
});
exports.CreateTaskTagSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, '标签名称不能为空'),
    color: zod_1.z.string().regex(/^#[0-9A-Fa-f]{6}$/, '颜色格式不正确').default('#3b82f6'),
    description: zod_1.z.string().nullable().optional(),
});
// 模板相关Schema
exports.TaskTemplateSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string().min(1, '模板名称不能为空'),
    content: zod_1.z.string().min(1, '模板内容不能为空'),
    description: zod_1.z.string().nullable().optional(),
    priority: zod_1.z.number().int().min(1).max(3),
    estimatedDuration: zod_1.z.number().int().nullable().optional(),
    tags: zod_1.z.array(zod_1.z.string()),
    isPublic: zod_1.z.boolean(),
    createdBy: zod_1.z.string().nullable().optional(),
    createdAt: zod_1.z.number().int(),
    updatedAt: zod_1.z.number().int(),
});
exports.CreateTaskTemplateSchema = zod_1.z.object({
    name: zod_1.z.string().min(1, '模板名称不能为空'),
    content: zod_1.z.string().min(1, '模板内容不能为空'),
    description: zod_1.z.string().nullable().optional(),
    priority: zod_1.z.number().int().min(1).max(3).default(exports.TaskPriority.MEDIUM),
    estimatedDuration: zod_1.z.number().int().nullable().optional(),
    tags: zod_1.z.array(zod_1.z.string()).default([]),
    isPublic: zod_1.z.boolean().default(false),
});
// 优先级标签映射
exports.PRIORITY_LABELS = {
    [exports.TaskPriority.HIGH]: '高',
    [exports.TaskPriority.MEDIUM]: '中',
    [exports.TaskPriority.LOW]: '低',
};
// 优先级颜色映射
exports.PRIORITY_COLORS = {
    [exports.TaskPriority.HIGH]: 'text-red-600 bg-red-50 border-red-200',
    [exports.TaskPriority.MEDIUM]: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    [exports.TaskPriority.LOW]: 'text-green-600 bg-green-50 border-green-200',
};
