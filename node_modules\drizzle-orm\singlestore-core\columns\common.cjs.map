{"version": 3, "sources": ["../../../src/singlestore-core/columns/common.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBase,\n\tColumnBuilderBaseConfig,\n\tColumnBuilderExtraConfig,\n\tColumnBuilderRuntimeConfig,\n\tColumnDataType,\n\tHasDefault,\n\tIsAutoincrement,\n\tMakeColumnConfig,\n} from '~/column-builder.ts';\nimport { ColumnBuilder } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable, SingleStoreTable } from '~/singlestore-core/table.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { Update } from '~/utils.ts';\nimport { uniqueKeyName } from '../unique-constraint.ts';\n\nexport interface SingleStoreColumnBuilderBase<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTTypeConfig extends object = object,\n> extends ColumnBuilderBase<T, TTypeConfig & { dialect: 'singlestore' }> {}\n\nexport interface SingleStoreGeneratedColumnConfig {\n\tmode?: 'virtual' | 'stored';\n}\n\nexport abstract class SingleStoreColumnBuilder<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string> & {\n\t\tdata: any;\n\t},\n\tTRuntimeConfig extends object = object,\n\tTTypeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends ColumnBuilder<T, TRuntimeConfig, TTypeConfig & { dialect: 'singlestore' }, TExtraConfig>\n\timplements SingleStoreColumnBuilderBase<T, TTypeConfig>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreColumnBuilder';\n\n\tunique(name?: string): this {\n\t\tthis.config.isUnique = true;\n\t\tthis.config.uniqueName = name;\n\t\treturn this;\n\t}\n\n\t// TODO: Implement generated columns for SingleStore (https://docs.singlestore.com/cloud/create-a-database/using-persistent-computed-columns/)\n\t/** @internal */\n\tgeneratedAlwaysAs(as: SQL | T['data'] | (() => SQL), config?: SingleStoreGeneratedColumnConfig) {\n\t\tthis.config.generated = {\n\t\t\tas,\n\t\t\ttype: 'always',\n\t\t\tmode: config?.mode ?? 'virtual',\n\t\t};\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tabstract build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreColumn<MakeColumnConfig<T, TTableName>>;\n}\n\n// To understand how to use `SingleStoreColumn` and `AnySingleStoreColumn`, see `Column` and `AnyColumn` documentation.\nexport abstract class SingleStoreColumn<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = {},\n\tTTypeConfig extends object = {},\n> extends Column<T, TRuntimeConfig, TTypeConfig & { dialect: 'singlestore' }> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreColumn';\n\n\tconstructor(\n\t\toverride readonly table: SingleStoreTable,\n\t\tconfig: ColumnBuilderRuntimeConfig<T['data'], TRuntimeConfig>,\n\t) {\n\t\tif (!config.uniqueName) {\n\t\t\tconfig.uniqueName = uniqueKeyName(table, [config.name]);\n\t\t}\n\t\tsuper(table, config);\n\t}\n}\n\nexport type AnySingleStoreColumn<TPartial extends Partial<ColumnBaseConfig<ColumnDataType, string>> = {}> =\n\tSingleStoreColumn<\n\t\tRequired<Update<ColumnBaseConfig<ColumnDataType, string>, TPartial>>\n\t>;\n\nexport interface SingleStoreColumnWithAutoIncrementConfig {\n\tautoIncrement: boolean;\n}\n\nexport abstract class SingleStoreColumnBuilderWithAutoIncrement<\n\tT extends ColumnBuilderBaseConfig<ColumnDataType, string> = ColumnBuilderBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n\tTExtraConfig extends ColumnBuilderExtraConfig = ColumnBuilderExtraConfig,\n> extends SingleStoreColumnBuilder<T, TRuntimeConfig & SingleStoreColumnWithAutoIncrementConfig, TExtraConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreColumnBuilderWithAutoIncrement';\n\n\tconstructor(name: NonNullable<T['name']>, dataType: T['dataType'], columnType: T['columnType']) {\n\t\tsuper(name, dataType, columnType);\n\t\tthis.config.autoIncrement = false;\n\t}\n\n\tautoincrement(): IsAutoincrement<HasDefault<this>> {\n\t\tthis.config.autoIncrement = true;\n\t\tthis.config.hasDefault = true;\n\t\treturn this as IsAutoincrement<HasDefault<this>>;\n\t}\n}\n\nexport abstract class SingleStoreColumnWithAutoIncrement<\n\tT extends ColumnBaseConfig<ColumnDataType, string> = ColumnBaseConfig<ColumnDataType, string>,\n\tTRuntimeConfig extends object = object,\n> extends SingleStoreColumn<T, SingleStoreColumnWithAutoIncrementConfig & TRuntimeConfig> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreColumnWithAutoIncrement';\n\n\treadonly autoIncrement: boolean = this.config.autoIncrement;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA,4BAA8B;AAE9B,oBAAuB;AACvB,oBAA2B;AAI3B,+BAA8B;AAWvB,MAAe,iCAOZ,oCAEV;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,OAAO,MAAqB;AAC3B,SAAK,OAAO,WAAW;AACvB,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA,EAIA,kBAAkB,IAAmC,QAA2C;AAC/F,SAAK,OAAO,YAAY;AAAA,MACvB;AAAA,MACA,MAAM;AAAA,MACN,MAAM,QAAQ,QAAQ;AAAA,IACvB;AACA,WAAO;AAAA,EACR;AAMD;AAGO,MAAe,0BAIZ,qBAAoE;AAAA,EAG7E,YACmB,OAClB,QACC;AACD,QAAI,CAAC,OAAO,YAAY;AACvB,aAAO,iBAAa,wCAAc,OAAO,CAAC,OAAO,IAAI,CAAC;AAAA,IACvD;AACA,UAAM,OAAO,MAAM;AAND;AAAA,EAOnB;AAAA,EAVA,QAA0B,wBAAU,IAAY;AAWjD;AAWO,MAAe,kDAIZ,yBAAqG;AAAA,EAC9G,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAA8B,UAAyB,YAA6B;AAC/F,UAAM,MAAM,UAAU,UAAU;AAChC,SAAK,OAAO,gBAAgB;AAAA,EAC7B;AAAA,EAEA,gBAAmD;AAClD,SAAK,OAAO,gBAAgB;AAC5B,SAAK,OAAO,aAAa;AACzB,WAAO;AAAA,EACR;AACD;AAEO,MAAe,2CAGZ,kBAAgF;AAAA,EACzF,QAA0B,wBAAU,IAAY;AAAA,EAEvC,gBAAyB,KAAK,OAAO;AAC/C;", "names": []}