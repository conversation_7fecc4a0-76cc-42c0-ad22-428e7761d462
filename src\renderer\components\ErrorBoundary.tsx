/**
 * 全局错误边界组件
 * 捕获 React 组件树中的 JavaScript 错误，记录错误并显示友好的错误页面
 */

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Button } from './ui/button'
import { Al<PERSON><PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, Bug, Home } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
  errorId: string
}

export class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0
  private maxRetries = 3

  constructor(props: Props) {
    super(props)
    this.state = { 
      hasError: false,
      errorId: this.generateErrorId()
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return { 
      hasError: true, 
      error,
      errorId: ErrorBoundary.prototype.generateErrorId()
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({ errorInfo })
    
    // 调用外部错误处理函数
    this.props.onError?.(error, errorInfo)
    
    // 发送错误报告
    this.reportError(error, errorInfo)
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    const errorReport = {
      id: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      retryCount: this.retryCount,
    }
    
    try {
      // 发送到错误监控服务或本地日志
      console.error('Error Report:', errorReport)
      
      // 如果有 Electron API，可以保存到本地文件
      if (window.electronAPI?.app) {
        // 这里可以调用主进程的错误日志记录功能
        console.log('Error logged to main process')
      }
      
      // 在开发环境下，可以发送到开发服务器
      if (process.env.NODE_ENV === 'development') {
        // 可以集成 Sentry 或其他错误监控服务
        console.warn('Development mode: Error report not sent to external service')
      }
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError)
    }
  }

  private handleReset = () => {
    this.retryCount++
    this.setState({ 
      hasError: false, 
      error: undefined, 
      errorInfo: undefined,
      errorId: this.generateErrorId()
    })
  }

  private handleRestart = () => {
    if (window.electronAPI?.app?.quit) {
      window.electronAPI.app.quit()
    } else {
      // 在浏览器环境中重新加载页面
      window.location.reload()
    }
  }

  private handleGoHome = () => {
    // 重置应用状态并返回主页
    this.handleReset()
    // 这里可以添加路由重置逻辑
  }

  private copyErrorDetails = () => {
    const errorDetails = {
      id: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
    }
    
    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        console.log('Error details copied to clipboard')
      })
      .catch((err) => {
        console.error('Failed to copy error details:', err)
      })
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义 fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback
      }

      const canRetry = this.retryCount < this.maxRetries
      const isRecoverable = this.state.error?.name !== 'ChunkLoadError'

      return (
        <div className="min-h-screen flex items-center justify-center bg-background p-4">
          <Card className="max-w-lg w-full">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <CardTitle className="text-xl font-semibold">
                应用出现错误
              </CardTitle>
              <CardDescription>
                很抱歉，应用遇到了意外错误。您可以尝试以下操作来恢复。
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* 错误信息 */}
              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium text-muted-foreground mb-1">
                  错误信息:
                </p>
                <p className="text-sm text-foreground">
                  {this.state.error?.message || '未知错误'}
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  错误ID: {this.state.errorId}
                </p>
              </div>

              {/* 操作按钮 */}
              <div className="space-y-2">
                {canRetry && isRecoverable && (
                  <Button 
                    onClick={this.handleReset} 
                    className="w-full"
                    variant="default"
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    重试 ({this.maxRetries - this.retryCount} 次机会)
                  </Button>
                )}
                
                <Button 
                  onClick={this.handleGoHome} 
                  variant="outline" 
                  className="w-full"
                >
                  <Home className="mr-2 h-4 w-4" />
                  返回主页
                </Button>
                
                <Button 
                  onClick={this.handleRestart} 
                  variant="outline" 
                  className="w-full"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  重启应用
                </Button>
              </div>

              {/* 开发环境下的详细信息 */}
              {process.env.NODE_ENV === 'development' && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
                    <Bug className="inline mr-1 h-3 w-3" />
                    开发者信息
                  </summary>
                  <div className="mt-2 space-y-2">
                    <div className="bg-muted p-2 rounded text-xs">
                      <p className="font-medium">错误堆栈:</p>
                      <pre className="mt-1 overflow-auto max-h-32 text-xs">
                        {this.state.error?.stack}
                      </pre>
                    </div>
                    
                    {this.state.errorInfo && (
                      <div className="bg-muted p-2 rounded text-xs">
                        <p className="font-medium">组件堆栈:</p>
                        <pre className="mt-1 overflow-auto max-h-32 text-xs">
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </div>
                    )}
                    
                    <Button 
                      onClick={this.copyErrorDetails}
                      variant="outline" 
                      size="sm"
                      className="w-full"
                    >
                      复制错误详情
                    </Button>
                  </div>
                </details>
              )}

              {/* 帮助信息 */}
              <div className="text-center text-xs text-muted-foreground">
                如果问题持续存在，请联系技术支持
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}
