/* Microsoft To Do 风格增强样式 */

/* 全局变量 - Microsoft To Do 配色方案 */
:root {
  /* 主色调 */
  --ms-blue-primary: #0078d4;
  --ms-blue-hover: #106ebe;
  --ms-blue-active: #005a9e;
  --ms-blue-light: #deecf9;
  --ms-blue-lighter: #f3f9fd;
  
  /* 中性色 */
  --ms-gray-50: #faf9f8;
  --ms-gray-100: #f3f2f1;
  --ms-gray-200: #edebe9;
  --ms-gray-300: #e1dfdd;
  --ms-gray-400: #d2d0ce;
  --ms-gray-500: #a19f9d;
  --ms-gray-600: #8a8886;
  --ms-gray-700: #605e5c;
  --ms-gray-800: #323130;
  --ms-gray-900: #201f1e;
  
  /* 语义色彩 */
  --ms-success: #107c10;
  --ms-warning: #ff8c00;
  --ms-error: #d13438;
  --ms-info: #0078d4;
  
  /* 阴影 */
  --ms-shadow-2: 0 1px 2px rgba(0, 0, 0, 0.14), 0 0px 2px rgba(0, 0, 0, 0.12);
  --ms-shadow-4: 0 2px 4px rgba(0, 0, 0, 0.14), 0 0px 4px rgba(0, 0, 0, 0.12);
  --ms-shadow-8: 0 4px 8px rgba(0, 0, 0, 0.14), 0 0px 8px rgba(0, 0, 0, 0.12);
  --ms-shadow-16: 0 8px 16px rgba(0, 0, 0, 0.14), 0 0px 16px rgba(0, 0, 0, 0.12);
  
  /* 圆角 */
  --ms-radius-small: 2px;
  --ms-radius-medium: 4px;
  --ms-radius-large: 8px;
  
  /* 过渡动画 */
  --ms-transition-fast: 0.1s ease-out;
  --ms-transition-normal: 0.2s ease-out;
  --ms-transition-slow: 0.3s ease-out;
}

/* 深色模式变量 */
.dark {
  --ms-gray-50: #1e1e1e;
  --ms-gray-100: #2d2d2d;
  --ms-gray-200: #3a3a3a;
  --ms-gray-300: #484848;
  --ms-gray-400: #5a5a5a;
  --ms-gray-500: #6d6d6d;
  --ms-gray-600: #8a8a8a;
  --ms-gray-700: #a6a6a6;
  --ms-gray-800: #c8c8c8;
  --ms-gray-900: #ffffff;
}

/* 增强的按钮样式 */
.ms-button {
  @apply inline-flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium rounded-md transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  border: 1px solid transparent;
}

.ms-button-primary {
  background-color: var(--ms-blue-primary);
  color: white;
  box-shadow: var(--ms-shadow-2);
}

.ms-button-primary:hover {
  background-color: var(--ms-blue-hover);
  box-shadow: var(--ms-shadow-4);
  transform: translateY(-1px);
}

.ms-button-primary:active {
  background-color: var(--ms-blue-active);
  box-shadow: var(--ms-shadow-2);
  transform: translateY(0);
}

.ms-button-secondary {
  background-color: var(--ms-gray-100);
  color: var(--ms-gray-800);
  border-color: var(--ms-gray-300);
}

.ms-button-secondary:hover {
  background-color: var(--ms-gray-200);
  border-color: var(--ms-gray-400);
  box-shadow: var(--ms-shadow-2);
}

.ms-button-ghost {
  background-color: transparent;
  color: var(--ms-gray-700);
}

.ms-button-ghost:hover {
  background-color: var(--ms-gray-100);
  color: var(--ms-gray-800);
}

/* 增强的输入框样式 */
.ms-input {
  @apply w-full px-3 py-2 text-sm border rounded-md transition-all duration-200;
  background-color: var(--ms-gray-50);
  border-color: var(--ms-gray-300);
  color: var(--ms-gray-800);
}

.ms-input:focus {
  outline: none;
  border-color: var(--ms-blue-primary);
  box-shadow: 0 0 0 1px var(--ms-blue-primary);
  background-color: white;
}

.ms-input::placeholder {
  color: var(--ms-gray-500);
}

/* 增强的卡片样式 */
.ms-card {
  background-color: white;
  border: 1px solid var(--ms-gray-200);
  border-radius: var(--ms-radius-large);
  box-shadow: var(--ms-shadow-2);
  transition: all var(--ms-transition-normal);
}

.ms-card:hover {
  box-shadow: var(--ms-shadow-4);
  border-color: var(--ms-gray-300);
}

.ms-card-elevated {
  box-shadow: var(--ms-shadow-8);
}

/* 任务卡片增强 */
.ms-task-card {
  @apply ms-card p-4 mb-3;
  cursor: pointer;
}

.ms-task-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--ms-shadow-8);
}

.ms-task-card.completed {
  background-color: var(--ms-gray-50);
  opacity: 0.8;
}

/* 导航栏增强 */
.ms-nav-item {
  @apply flex items-center gap-3 px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200;
  color: var(--ms-gray-700);
}

.ms-nav-item:hover {
  background-color: var(--ms-blue-lighter);
  color: var(--ms-blue-primary);
}

.ms-nav-item.active {
  background-color: var(--ms-blue-light);
  color: var(--ms-blue-primary);
  font-weight: 600;
}

/* 统计卡片增强 */
.ms-stats-card {
  @apply ms-card p-4;
  background: linear-gradient(135deg, var(--ms-blue-lighter) 0%, white 100%);
  border-color: var(--ms-blue-light);
}

/* 动画增强 */
.ms-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.ms-slide-up {
  animation: slideUp 0.3s ease-out;
}

.ms-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* 加载状态增强 */
.ms-loading-skeleton {
  background: linear-gradient(90deg, var(--ms-gray-200) 25%, var(--ms-gray-100) 50%, var(--ms-gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* 响应式增强 */
@media (max-width: 768px) {
  .ms-button {
    @apply px-3 py-2 text-xs;
  }
  
  .ms-card {
    @apply mx-2;
  }
  
  .ms-nav-item {
    @apply px-3 py-2 text-xs;
  }
}

/* 可访问性增强 */
.ms-focus-visible:focus-visible {
  outline: 2px solid var(--ms-blue-primary);
  outline-offset: 2px;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .ms-button-primary {
    border: 2px solid var(--ms-blue-primary);
  }
  
  .ms-input {
    border-width: 2px;
  }
  
  .ms-card {
    border-width: 2px;
  }
}
