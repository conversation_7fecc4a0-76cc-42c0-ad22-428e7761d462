{"version": 3, "sources": ["../../../src/singlestore-core/columns/time.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreTimeBuilderInitial<TName extends string> = SingleStoreTimeBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreTime';\n\tdata: string;\n\tdriverParam: string | number;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreTimeBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreTime'>>\n\textends SingleStoreColumnBuilder<\n\t\tT\n\t>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreTimeBuilder';\n\n\tconstructor(\n\t\tname: T['name'],\n\t) {\n\t\tsuper(name, 'string', 'SingleStoreTime');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreTime<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreTime<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreTime<\n\tT extends ColumnBaseConfig<'string', 'SingleStoreTime'>,\n> extends SingleStoreColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreTime';\n\n\tgetSQLType(): string {\n\t\treturn `time`;\n\t}\n}\n\nexport function time(): SingleStoreTimeBuilderInitial<''>;\nexport function time<TName extends string>(name: TName): SingleStoreTimeBuilderInitial<TName>;\nexport function time(name?: string) {\n\treturn new SingleStoreTimeBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,oBAA4D;AAYrD,MAAM,+BACJ,uCAGT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YACC,MACC;AACD,UAAM,MAAM,UAAU,iBAAiB;AAAA,EACxC;AAAA;AAAA,EAGS,MACR,OACmD;AACnD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,wBAEH,gCAAqB;AAAA,EAC9B,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,uBAAuB,QAAQ,EAAE;AAC7C;", "names": []}