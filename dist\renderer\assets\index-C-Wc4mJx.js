var dg=Object.defineProperty;var Gc=e=>{throw TypeError(e)};var fg=(e,t,n)=>t in e?dg(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var vt=(e,t,n)=>fg(e,typeof t!="symbol"?t+"":t,n),Ta=(e,t,n)=>t.has(e)||Gc("Cannot "+n);var k=(e,t,n)=>(Ta(e,t,"read from private field"),n?n.call(e):t.get(e)),z=(e,t,n)=>t.has(e)?Gc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),I=(e,t,n,r)=>(Ta(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),H=(e,t,n)=>(Ta(e,t,"access private method"),n);var Li=(e,t,n,r)=>({set _(s){I(e,t,s,n)},get _(){return k(e,t,r)}});function hg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const s in r)if(s!=="default"&&!(s in e)){const i=Object.getOwnPropertyDescriptor(r,s);i&&Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:()=>r[s]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function Ou(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var dh={exports:{}},ia={},fh={exports:{}},K={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ji=Symbol.for("react.element"),pg=Symbol.for("react.portal"),mg=Symbol.for("react.fragment"),yg=Symbol.for("react.strict_mode"),gg=Symbol.for("react.profiler"),vg=Symbol.for("react.provider"),xg=Symbol.for("react.context"),wg=Symbol.for("react.forward_ref"),kg=Symbol.for("react.suspense"),Sg=Symbol.for("react.memo"),Cg=Symbol.for("react.lazy"),qc=Symbol.iterator;function _g(e){return e===null||typeof e!="object"?null:(e=qc&&e[qc]||e["@@iterator"],typeof e=="function"?e:null)}var hh={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ph=Object.assign,mh={};function _s(e,t,n){this.props=e,this.context=t,this.refs=mh,this.updater=n||hh}_s.prototype.isReactComponent={};_s.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};_s.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function yh(){}yh.prototype=_s.prototype;function Du(e,t,n){this.props=e,this.context=t,this.refs=mh,this.updater=n||hh}var Mu=Du.prototype=new yh;Mu.constructor=Du;ph(Mu,_s.prototype);Mu.isPureReactComponent=!0;var Yc=Array.isArray,gh=Object.prototype.hasOwnProperty,Au={current:null},vh={key:!0,ref:!0,__self:!0,__source:!0};function xh(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)gh.call(t,r)&&!vh.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];s.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:ji,type:e,key:i,ref:o,props:s,_owner:Au.current}}function bg(e,t){return{$$typeof:ji,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Fu(e){return typeof e=="object"&&e!==null&&e.$$typeof===ji}function Eg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Xc=/\/+/g;function ja(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Eg(""+e.key):t.toString(36)}function lo(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ji:case pg:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+ja(o,0):r,Yc(s)?(n="",e!=null&&(n=e.replace(Xc,"$&/")+"/"),lo(s,t,n,"",function(u){return u})):s!=null&&(Fu(s)&&(s=bg(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Xc,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",Yc(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+ja(i,a);o+=lo(i,t,n,l,s)}else if(l=_g(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+ja(i,a++),o+=lo(i,t,n,l,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function zi(e,t,n){if(e==null)return e;var r=[],s=0;return lo(e,r,"","",function(i){return t.call(n,i,s++)}),r}function Ng(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ve={current:null},uo={transition:null},Tg={ReactCurrentDispatcher:Ve,ReactCurrentBatchConfig:uo,ReactCurrentOwner:Au};function wh(){throw Error("act(...) is not supported in production builds of React.")}K.Children={map:zi,forEach:function(e,t,n){zi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return zi(e,function(){t++}),t},toArray:function(e){return zi(e,function(t){return t})||[]},only:function(e){if(!Fu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};K.Component=_s;K.Fragment=mg;K.Profiler=gg;K.PureComponent=Du;K.StrictMode=yg;K.Suspense=kg;K.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Tg;K.act=wh;K.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ph({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Au.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)gh.call(t,l)&&!vh.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ji,type:e.type,key:s,ref:i,props:r,_owner:o}};K.createContext=function(e){return e={$$typeof:xg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:vg,_context:e},e.Consumer=e};K.createElement=xh;K.createFactory=function(e){var t=xh.bind(null,e);return t.type=e,t};K.createRef=function(){return{current:null}};K.forwardRef=function(e){return{$$typeof:wg,render:e}};K.isValidElement=Fu;K.lazy=function(e){return{$$typeof:Cg,_payload:{_status:-1,_result:e},_init:Ng}};K.memo=function(e,t){return{$$typeof:Sg,type:e,compare:t===void 0?null:t}};K.startTransition=function(e){var t=uo.transition;uo.transition={};try{e()}finally{uo.transition=t}};K.unstable_act=wh;K.useCallback=function(e,t){return Ve.current.useCallback(e,t)};K.useContext=function(e){return Ve.current.useContext(e)};K.useDebugValue=function(){};K.useDeferredValue=function(e){return Ve.current.useDeferredValue(e)};K.useEffect=function(e,t){return Ve.current.useEffect(e,t)};K.useId=function(){return Ve.current.useId()};K.useImperativeHandle=function(e,t,n){return Ve.current.useImperativeHandle(e,t,n)};K.useInsertionEffect=function(e,t){return Ve.current.useInsertionEffect(e,t)};K.useLayoutEffect=function(e,t){return Ve.current.useLayoutEffect(e,t)};K.useMemo=function(e,t){return Ve.current.useMemo(e,t)};K.useReducer=function(e,t,n){return Ve.current.useReducer(e,t,n)};K.useRef=function(e){return Ve.current.useRef(e)};K.useState=function(e){return Ve.current.useState(e)};K.useSyncExternalStore=function(e,t,n){return Ve.current.useSyncExternalStore(e,t,n)};K.useTransition=function(){return Ve.current.useTransition()};K.version="18.3.1";fh.exports=K;var v=fh.exports;const Bt=Ou(v),kh=hg({__proto__:null,default:Bt},[v]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jg=v,Rg=Symbol.for("react.element"),Pg=Symbol.for("react.fragment"),Ig=Object.prototype.hasOwnProperty,Og=jg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Dg={key:!0,ref:!0,__self:!0,__source:!0};function Sh(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Ig.call(t,r)&&!Dg.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:Rg,type:e,key:i,ref:o,props:s,_owner:Og.current}}ia.Fragment=Pg;ia.jsx=Sh;ia.jsxs=Sh;dh.exports=ia;var d=dh.exports,pl={},Ch={exports:{}},ot={},_h={exports:{}},bh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,D){var U=P.length;P.push(D);e:for(;0<U;){var G=U-1>>>1,Ce=P[G];if(0<s(Ce,D))P[G]=D,P[U]=Ce,U=G;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var D=P[0],U=P.pop();if(U!==D){P[0]=U;e:for(var G=0,Ce=P.length,Ai=Ce>>>1;G<Ai;){var qn=2*(G+1)-1,Na=P[qn],Yn=qn+1,Fi=P[Yn];if(0>s(Na,U))Yn<Ce&&0>s(Fi,Na)?(P[G]=Fi,P[Yn]=U,G=Yn):(P[G]=Na,P[qn]=U,G=qn);else if(Yn<Ce&&0>s(Fi,U))P[G]=Fi,P[Yn]=U,G=Yn;else break e}}return D}function s(P,D){var U=P.sortIndex-D.sortIndex;return U!==0?U:P.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],f=1,h=null,m=3,w=!1,S=!1,y=!1,C=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(P){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=P)r(u),D.sortIndex=D.expirationTime,t(l,D);else break;D=n(u)}}function x(P){if(y=!1,g(P),!S)if(n(l)!==null)S=!0,Rt(_);else{var D=n(u);D!==null&&an(x,D.startTime-P)}}function _(P,D){S=!1,y&&(y=!1,p(E),E=-1),w=!0;var U=m;try{for(g(D),h=n(l);h!==null&&(!(h.expirationTime>D)||P&&!re());){var G=h.callback;if(typeof G=="function"){h.callback=null,m=h.priorityLevel;var Ce=G(h.expirationTime<=D);D=e.unstable_now(),typeof Ce=="function"?h.callback=Ce:h===n(l)&&r(l),g(D)}else r(l);h=n(l)}if(h!==null)var Ai=!0;else{var qn=n(u);qn!==null&&an(x,qn.startTime-D),Ai=!1}return Ai}finally{h=null,m=U,w=!1}}var b=!1,N=null,E=-1,L=5,R=-1;function re(){return!(e.unstable_now()-R<L)}function Q(){if(N!==null){var P=e.unstable_now();R=P;var D=!0;try{D=N(!0,P)}finally{D?Ee():(b=!1,N=null)}}else b=!1}var Ee;if(typeof c=="function")Ee=function(){c(Q)};else if(typeof MessageChannel<"u"){var gt=new MessageChannel,Gn=gt.port2;gt.port1.onmessage=Q,Ee=function(){Gn.postMessage(null)}}else Ee=function(){C(Q,0)};function Rt(P){N=P,b||(b=!0,Ee())}function an(P,D){E=C(function(){P(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){S||w||(S=!0,Rt(_))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(P){switch(m){case 1:case 2:case 3:var D=3;break;default:D=m}var U=m;m=D;try{return P()}finally{m=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,D){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var U=m;m=P;try{return D()}finally{m=U}},e.unstable_scheduleCallback=function(P,D,U){var G=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?G+U:G):U=G,P){case 1:var Ce=-1;break;case 2:Ce=250;break;case 5:Ce=**********;break;case 4:Ce=1e4;break;default:Ce=5e3}return Ce=U+Ce,P={id:f++,callback:D,priorityLevel:P,startTime:U,expirationTime:Ce,sortIndex:-1},U>G?(P.sortIndex=U,t(u,P),n(l)===null&&P===n(u)&&(y?(p(E),E=-1):y=!0,an(x,U-G))):(P.sortIndex=Ce,t(l,P),S||w||(S=!0,Rt(_))),P},e.unstable_shouldYield=re,e.unstable_wrapCallback=function(P){var D=m;return function(){var U=m;m=D;try{return P.apply(this,arguments)}finally{m=U}}}})(bh);_h.exports=bh;var Mg=_h.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ag=v,rt=Mg;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Eh=new Set,ni={};function br(e,t){cs(e,t),cs(e+"Capture",t)}function cs(e,t){for(ni[e]=t,e=0;e<t.length;e++)Eh.add(t[e])}var Jt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ml=Object.prototype.hasOwnProperty,Fg=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Jc={},ed={};function Lg(e){return ml.call(ed,e)?!0:ml.call(Jc,e)?!1:Fg.test(e)?ed[e]=!0:(Jc[e]=!0,!1)}function zg(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function $g(e,t,n,r){if(t===null||typeof t>"u"||zg(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Qe(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new Qe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new Qe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new Qe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new Qe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new Qe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new Qe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new Qe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new Qe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new Qe(e,5,!1,e.toLowerCase(),null,!1,!1)});var Lu=/[\-:]([a-z])/g;function zu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Lu,zu);Re[t]=new Qe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Lu,zu);Re[t]=new Qe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Lu,zu);Re[t]=new Qe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new Qe(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new Qe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new Qe(e,1,!1,e.toLowerCase(),null,!0,!0)});function $u(e,t,n,r){var s=Re.hasOwnProperty(t)?Re[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&($g(t,n,s,r)&&(n=null),r||s===null?Lg(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var sn=Ag.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,$i=Symbol.for("react.element"),Ir=Symbol.for("react.portal"),Or=Symbol.for("react.fragment"),Uu=Symbol.for("react.strict_mode"),yl=Symbol.for("react.profiler"),Nh=Symbol.for("react.provider"),Th=Symbol.for("react.context"),Bu=Symbol.for("react.forward_ref"),gl=Symbol.for("react.suspense"),vl=Symbol.for("react.suspense_list"),Vu=Symbol.for("react.memo"),hn=Symbol.for("react.lazy"),jh=Symbol.for("react.offscreen"),td=Symbol.iterator;function Ps(e){return e===null||typeof e!="object"?null:(e=td&&e[td]||e["@@iterator"],typeof e=="function"?e:null)}var pe=Object.assign,Ra;function Us(e){if(Ra===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ra=t&&t[1]||""}return`
`+Ra+e}var Pa=!1;function Ia(e,t){if(!e||Pa)return"";Pa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var l=`
`+s[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{Pa=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Us(e):""}function Ug(e){switch(e.tag){case 5:return Us(e.type);case 16:return Us("Lazy");case 13:return Us("Suspense");case 19:return Us("SuspenseList");case 0:case 2:case 15:return e=Ia(e.type,!1),e;case 11:return e=Ia(e.type.render,!1),e;case 1:return e=Ia(e.type,!0),e;default:return""}}function xl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Or:return"Fragment";case Ir:return"Portal";case yl:return"Profiler";case Uu:return"StrictMode";case gl:return"Suspense";case vl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Th:return(e.displayName||"Context")+".Consumer";case Nh:return(e._context.displayName||"Context")+".Provider";case Bu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Vu:return t=e.displayName||null,t!==null?t:xl(e.type)||"Memo";case hn:t=e._payload,e=e._init;try{return xl(e(t))}catch{}}return null}function Bg(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return xl(t);case 8:return t===Uu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Bn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Rh(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Vg(e){var t=Rh(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ui(e){e._valueTracker||(e._valueTracker=Vg(e))}function Ph(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Rh(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function _o(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function wl(e,t){var n=t.checked;return pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function nd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Bn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ih(e,t){t=t.checked,t!=null&&$u(e,"checked",t,!1)}function kl(e,t){Ih(e,t);var n=Bn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Sl(e,t.type,n):t.hasOwnProperty("defaultValue")&&Sl(e,t.type,Bn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function rd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Sl(e,t,n){(t!=="number"||_o(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Bs=Array.isArray;function Qr(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Bn(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Cl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function sd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(Bs(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Bn(n)}}function Oh(e,t){var n=Bn(t.value),r=Bn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function id(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Dh(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function _l(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Dh(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Bi,Mh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Bi=Bi||document.createElement("div"),Bi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Bi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ri(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Zs={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Qg=["Webkit","ms","Moz","O"];Object.keys(Zs).forEach(function(e){Qg.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Zs[t]=Zs[e]})});function Ah(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Zs.hasOwnProperty(e)&&Zs[e]?(""+t).trim():t+"px"}function Fh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Ah(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Wg=pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function bl(e,t){if(t){if(Wg[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function El(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Nl=null;function Qu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Tl=null,Wr=null,Hr=null;function od(e){if(e=Ii(e)){if(typeof Tl!="function")throw Error(T(280));var t=e.stateNode;t&&(t=ca(t),Tl(e.stateNode,e.type,t))}}function Lh(e){Wr?Hr?Hr.push(e):Hr=[e]:Wr=e}function zh(){if(Wr){var e=Wr,t=Hr;if(Hr=Wr=null,od(e),t)for(e=0;e<t.length;e++)od(t[e])}}function $h(e,t){return e(t)}function Uh(){}var Oa=!1;function Bh(e,t,n){if(Oa)return e(t,n);Oa=!0;try{return $h(e,t,n)}finally{Oa=!1,(Wr!==null||Hr!==null)&&(Uh(),zh())}}function si(e,t){var n=e.stateNode;if(n===null)return null;var r=ca(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var jl=!1;if(Jt)try{var Is={};Object.defineProperty(Is,"passive",{get:function(){jl=!0}}),window.addEventListener("test",Is,Is),window.removeEventListener("test",Is,Is)}catch{jl=!1}function Hg(e,t,n,r,s,i,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var Ks=!1,bo=null,Eo=!1,Rl=null,Zg={onError:function(e){Ks=!0,bo=e}};function Kg(e,t,n,r,s,i,o,a,l){Ks=!1,bo=null,Hg.apply(Zg,arguments)}function Gg(e,t,n,r,s,i,o,a,l){if(Kg.apply(this,arguments),Ks){if(Ks){var u=bo;Ks=!1,bo=null}else throw Error(T(198));Eo||(Eo=!0,Rl=u)}}function Er(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Vh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ad(e){if(Er(e)!==e)throw Error(T(188))}function qg(e){var t=e.alternate;if(!t){if(t=Er(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return ad(s),e;if(i===r)return ad(s),t;i=i.sibling}throw Error(T(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function Qh(e){return e=qg(e),e!==null?Wh(e):null}function Wh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Wh(e);if(t!==null)return t;e=e.sibling}return null}var Hh=rt.unstable_scheduleCallback,ld=rt.unstable_cancelCallback,Yg=rt.unstable_shouldYield,Xg=rt.unstable_requestPaint,ge=rt.unstable_now,Jg=rt.unstable_getCurrentPriorityLevel,Wu=rt.unstable_ImmediatePriority,Zh=rt.unstable_UserBlockingPriority,No=rt.unstable_NormalPriority,ev=rt.unstable_LowPriority,Kh=rt.unstable_IdlePriority,oa=null,Lt=null;function tv(e){if(Lt&&typeof Lt.onCommitFiberRoot=="function")try{Lt.onCommitFiberRoot(oa,e,void 0,(e.current.flags&128)===128)}catch{}}var Et=Math.clz32?Math.clz32:sv,nv=Math.log,rv=Math.LN2;function sv(e){return e>>>=0,e===0?32:31-(nv(e)/rv|0)|0}var Vi=64,Qi=4194304;function Vs(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function To(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=Vs(a):(i&=o,i!==0&&(r=Vs(i)))}else o=n&~s,o!==0?r=Vs(o):i!==0&&(r=Vs(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Et(t),s=1<<n,r|=e[n],t&=~s;return r}function iv(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ov(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-Et(i),a=1<<o,l=s[o];l===-1?(!(a&n)||a&r)&&(s[o]=iv(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Pl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Gh(){var e=Vi;return Vi<<=1,!(Vi&4194240)&&(Vi=64),e}function Da(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ri(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Et(t),e[t]=n}function av(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-Et(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function Hu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Et(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var oe=0;function qh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Yh,Zu,Xh,Jh,ep,Il=!1,Wi=[],Pn=null,In=null,On=null,ii=new Map,oi=new Map,yn=[],lv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ud(e,t){switch(e){case"focusin":case"focusout":Pn=null;break;case"dragenter":case"dragleave":In=null;break;case"mouseover":case"mouseout":On=null;break;case"pointerover":case"pointerout":ii.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":oi.delete(t.pointerId)}}function Os(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=Ii(t),t!==null&&Zu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function uv(e,t,n,r,s){switch(t){case"focusin":return Pn=Os(Pn,e,t,n,r,s),!0;case"dragenter":return In=Os(In,e,t,n,r,s),!0;case"mouseover":return On=Os(On,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return ii.set(i,Os(ii.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,oi.set(i,Os(oi.get(i)||null,e,t,n,r,s)),!0}return!1}function tp(e){var t=er(e.target);if(t!==null){var n=Er(t);if(n!==null){if(t=n.tag,t===13){if(t=Vh(n),t!==null){e.blockedOn=t,ep(e.priority,function(){Xh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function co(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ol(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Nl=r,n.target.dispatchEvent(r),Nl=null}else return t=Ii(n),t!==null&&Zu(t),e.blockedOn=n,!1;t.shift()}return!0}function cd(e,t,n){co(e)&&n.delete(t)}function cv(){Il=!1,Pn!==null&&co(Pn)&&(Pn=null),In!==null&&co(In)&&(In=null),On!==null&&co(On)&&(On=null),ii.forEach(cd),oi.forEach(cd)}function Ds(e,t){e.blockedOn===t&&(e.blockedOn=null,Il||(Il=!0,rt.unstable_scheduleCallback(rt.unstable_NormalPriority,cv)))}function ai(e){function t(s){return Ds(s,e)}if(0<Wi.length){Ds(Wi[0],e);for(var n=1;n<Wi.length;n++){var r=Wi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Pn!==null&&Ds(Pn,e),In!==null&&Ds(In,e),On!==null&&Ds(On,e),ii.forEach(t),oi.forEach(t),n=0;n<yn.length;n++)r=yn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<yn.length&&(n=yn[0],n.blockedOn===null);)tp(n),n.blockedOn===null&&yn.shift()}var Zr=sn.ReactCurrentBatchConfig,jo=!0;function dv(e,t,n,r){var s=oe,i=Zr.transition;Zr.transition=null;try{oe=1,Ku(e,t,n,r)}finally{oe=s,Zr.transition=i}}function fv(e,t,n,r){var s=oe,i=Zr.transition;Zr.transition=null;try{oe=4,Ku(e,t,n,r)}finally{oe=s,Zr.transition=i}}function Ku(e,t,n,r){if(jo){var s=Ol(e,t,n,r);if(s===null)Qa(e,t,r,Ro,n),ud(e,r);else if(uv(s,e,t,n,r))r.stopPropagation();else if(ud(e,r),t&4&&-1<lv.indexOf(e)){for(;s!==null;){var i=Ii(s);if(i!==null&&Yh(i),i=Ol(e,t,n,r),i===null&&Qa(e,t,r,Ro,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else Qa(e,t,r,null,n)}}var Ro=null;function Ol(e,t,n,r){if(Ro=null,e=Qu(r),e=er(e),e!==null)if(t=Er(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Vh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ro=e,null}function np(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Jg()){case Wu:return 1;case Zh:return 4;case No:case ev:return 16;case Kh:return 536870912;default:return 16}default:return 16}}var Tn=null,Gu=null,fo=null;function rp(){if(fo)return fo;var e,t=Gu,n=t.length,r,s="value"in Tn?Tn.value:Tn.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return fo=s.slice(e,1<r?1-r:void 0)}function ho(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Hi(){return!0}function dd(){return!1}function at(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Hi:dd,this.isPropagationStopped=dd,this}return pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Hi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Hi)},persist:function(){},isPersistent:Hi}),t}var bs={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},qu=at(bs),Pi=pe({},bs,{view:0,detail:0}),hv=at(Pi),Ma,Aa,Ms,aa=pe({},Pi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Yu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ms&&(Ms&&e.type==="mousemove"?(Ma=e.screenX-Ms.screenX,Aa=e.screenY-Ms.screenY):Aa=Ma=0,Ms=e),Ma)},movementY:function(e){return"movementY"in e?e.movementY:Aa}}),fd=at(aa),pv=pe({},aa,{dataTransfer:0}),mv=at(pv),yv=pe({},Pi,{relatedTarget:0}),Fa=at(yv),gv=pe({},bs,{animationName:0,elapsedTime:0,pseudoElement:0}),vv=at(gv),xv=pe({},bs,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),wv=at(xv),kv=pe({},bs,{data:0}),hd=at(kv),Sv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Cv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},_v={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=_v[e])?!!t[e]:!1}function Yu(){return bv}var Ev=pe({},Pi,{key:function(e){if(e.key){var t=Sv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ho(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Cv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Yu,charCode:function(e){return e.type==="keypress"?ho(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ho(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Nv=at(Ev),Tv=pe({},aa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pd=at(Tv),jv=pe({},Pi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Yu}),Rv=at(jv),Pv=pe({},bs,{propertyName:0,elapsedTime:0,pseudoElement:0}),Iv=at(Pv),Ov=pe({},aa,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Dv=at(Ov),Mv=[9,13,27,32],Xu=Jt&&"CompositionEvent"in window,Gs=null;Jt&&"documentMode"in document&&(Gs=document.documentMode);var Av=Jt&&"TextEvent"in window&&!Gs,sp=Jt&&(!Xu||Gs&&8<Gs&&11>=Gs),md=" ",yd=!1;function ip(e,t){switch(e){case"keyup":return Mv.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function op(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Dr=!1;function Fv(e,t){switch(e){case"compositionend":return op(t);case"keypress":return t.which!==32?null:(yd=!0,md);case"textInput":return e=t.data,e===md&&yd?null:e;default:return null}}function Lv(e,t){if(Dr)return e==="compositionend"||!Xu&&ip(e,t)?(e=rp(),fo=Gu=Tn=null,Dr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return sp&&t.locale!=="ko"?null:t.data;default:return null}}var zv={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!zv[e.type]:t==="textarea"}function ap(e,t,n,r){Lh(r),t=Po(t,"onChange"),0<t.length&&(n=new qu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qs=null,li=null;function $v(e){vp(e,0)}function la(e){var t=Fr(e);if(Ph(t))return e}function Uv(e,t){if(e==="change")return t}var lp=!1;if(Jt){var La;if(Jt){var za="oninput"in document;if(!za){var vd=document.createElement("div");vd.setAttribute("oninput","return;"),za=typeof vd.oninput=="function"}La=za}else La=!1;lp=La&&(!document.documentMode||9<document.documentMode)}function xd(){qs&&(qs.detachEvent("onpropertychange",up),li=qs=null)}function up(e){if(e.propertyName==="value"&&la(li)){var t=[];ap(t,li,e,Qu(e)),Bh($v,t)}}function Bv(e,t,n){e==="focusin"?(xd(),qs=t,li=n,qs.attachEvent("onpropertychange",up)):e==="focusout"&&xd()}function Vv(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return la(li)}function Qv(e,t){if(e==="click")return la(t)}function Wv(e,t){if(e==="input"||e==="change")return la(t)}function Hv(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:Hv;function ui(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!ml.call(t,s)||!Tt(e[s],t[s]))return!1}return!0}function wd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function kd(e,t){var n=wd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=wd(n)}}function cp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?cp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function dp(){for(var e=window,t=_o();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=_o(e.document)}return t}function Ju(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Zv(e){var t=dp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&cp(n.ownerDocument.documentElement,n)){if(r!==null&&Ju(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=kd(n,i);var o=kd(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Kv=Jt&&"documentMode"in document&&11>=document.documentMode,Mr=null,Dl=null,Ys=null,Ml=!1;function Sd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ml||Mr==null||Mr!==_o(r)||(r=Mr,"selectionStart"in r&&Ju(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Ys&&ui(Ys,r)||(Ys=r,r=Po(Dl,"onSelect"),0<r.length&&(t=new qu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Mr)))}function Zi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ar={animationend:Zi("Animation","AnimationEnd"),animationiteration:Zi("Animation","AnimationIteration"),animationstart:Zi("Animation","AnimationStart"),transitionend:Zi("Transition","TransitionEnd")},$a={},fp={};Jt&&(fp=document.createElement("div").style,"AnimationEvent"in window||(delete Ar.animationend.animation,delete Ar.animationiteration.animation,delete Ar.animationstart.animation),"TransitionEvent"in window||delete Ar.transitionend.transition);function ua(e){if($a[e])return $a[e];if(!Ar[e])return e;var t=Ar[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in fp)return $a[e]=t[n];return e}var hp=ua("animationend"),pp=ua("animationiteration"),mp=ua("animationstart"),yp=ua("transitionend"),gp=new Map,Cd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Hn(e,t){gp.set(e,t),br(t,[e])}for(var Ua=0;Ua<Cd.length;Ua++){var Ba=Cd[Ua],Gv=Ba.toLowerCase(),qv=Ba[0].toUpperCase()+Ba.slice(1);Hn(Gv,"on"+qv)}Hn(hp,"onAnimationEnd");Hn(pp,"onAnimationIteration");Hn(mp,"onAnimationStart");Hn("dblclick","onDoubleClick");Hn("focusin","onFocus");Hn("focusout","onBlur");Hn(yp,"onTransitionEnd");cs("onMouseEnter",["mouseout","mouseover"]);cs("onMouseLeave",["mouseout","mouseover"]);cs("onPointerEnter",["pointerout","pointerover"]);cs("onPointerLeave",["pointerout","pointerover"]);br("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));br("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));br("onBeforeInput",["compositionend","keypress","textInput","paste"]);br("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));br("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));br("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Qs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Yv=new Set("cancel close invalid load scroll toggle".split(" ").concat(Qs));function _d(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Gg(r,t,void 0,e),e.currentTarget=null}function vp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&s.isPropagationStopped())break e;_d(s,a,u),i=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&s.isPropagationStopped())break e;_d(s,a,u),i=l}}}if(Eo)throw e=Rl,Eo=!1,Rl=null,e}function ue(e,t){var n=t[$l];n===void 0&&(n=t[$l]=new Set);var r=e+"__bubble";n.has(r)||(xp(t,e,2,!1),n.add(r))}function Va(e,t,n){var r=0;t&&(r|=4),xp(n,e,r,t)}var Ki="_reactListening"+Math.random().toString(36).slice(2);function ci(e){if(!e[Ki]){e[Ki]=!0,Eh.forEach(function(n){n!=="selectionchange"&&(Yv.has(n)||Va(n,!1,e),Va(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ki]||(t[Ki]=!0,Va("selectionchange",!1,t))}}function xp(e,t,n,r){switch(np(t)){case 1:var s=dv;break;case 4:s=fv;break;default:s=Ku}n=s.bind(null,t,n,e),s=void 0,!jl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function Qa(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;o=o.return}for(;a!==null;){if(o=er(a),o===null)return;if(l=o.tag,l===5||l===6){r=i=o;continue e}a=a.parentNode}}r=r.return}Bh(function(){var u=i,f=Qu(n),h=[];e:{var m=gp.get(e);if(m!==void 0){var w=qu,S=e;switch(e){case"keypress":if(ho(n)===0)break e;case"keydown":case"keyup":w=Nv;break;case"focusin":S="focus",w=Fa;break;case"focusout":S="blur",w=Fa;break;case"beforeblur":case"afterblur":w=Fa;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=fd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=mv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=Rv;break;case hp:case pp:case mp:w=vv;break;case yp:w=Iv;break;case"scroll":w=hv;break;case"wheel":w=Dv;break;case"copy":case"cut":case"paste":w=wv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=pd}var y=(t&4)!==0,C=!y&&e==="scroll",p=y?m!==null?m+"Capture":null:m;y=[];for(var c=u,g;c!==null;){g=c;var x=g.stateNode;if(g.tag===5&&x!==null&&(g=x,p!==null&&(x=si(c,p),x!=null&&y.push(di(c,x,g)))),C)break;c=c.return}0<y.length&&(m=new w(m,S,null,n,f),h.push({event:m,listeners:y}))}}if(!(t&7)){e:{if(m=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",m&&n!==Nl&&(S=n.relatedTarget||n.fromElement)&&(er(S)||S[en]))break e;if((w||m)&&(m=f.window===f?f:(m=f.ownerDocument)?m.defaultView||m.parentWindow:window,w?(S=n.relatedTarget||n.toElement,w=u,S=S?er(S):null,S!==null&&(C=Er(S),S!==C||S.tag!==5&&S.tag!==6)&&(S=null)):(w=null,S=u),w!==S)){if(y=fd,x="onMouseLeave",p="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(y=pd,x="onPointerLeave",p="onPointerEnter",c="pointer"),C=w==null?m:Fr(w),g=S==null?m:Fr(S),m=new y(x,c+"leave",w,n,f),m.target=C,m.relatedTarget=g,x=null,er(f)===u&&(y=new y(p,c+"enter",S,n,f),y.target=g,y.relatedTarget=C,x=y),C=x,w&&S)t:{for(y=w,p=S,c=0,g=y;g;g=Nr(g))c++;for(g=0,x=p;x;x=Nr(x))g++;for(;0<c-g;)y=Nr(y),c--;for(;0<g-c;)p=Nr(p),g--;for(;c--;){if(y===p||p!==null&&y===p.alternate)break t;y=Nr(y),p=Nr(p)}y=null}else y=null;w!==null&&bd(h,m,w,y,!1),S!==null&&C!==null&&bd(h,C,S,y,!0)}}e:{if(m=u?Fr(u):window,w=m.nodeName&&m.nodeName.toLowerCase(),w==="select"||w==="input"&&m.type==="file")var _=Uv;else if(gd(m))if(lp)_=Wv;else{_=Vv;var b=Bv}else(w=m.nodeName)&&w.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(_=Qv);if(_&&(_=_(e,u))){ap(h,_,n,f);break e}b&&b(e,m,u),e==="focusout"&&(b=m._wrapperState)&&b.controlled&&m.type==="number"&&Sl(m,"number",m.value)}switch(b=u?Fr(u):window,e){case"focusin":(gd(b)||b.contentEditable==="true")&&(Mr=b,Dl=u,Ys=null);break;case"focusout":Ys=Dl=Mr=null;break;case"mousedown":Ml=!0;break;case"contextmenu":case"mouseup":case"dragend":Ml=!1,Sd(h,n,f);break;case"selectionchange":if(Kv)break;case"keydown":case"keyup":Sd(h,n,f)}var N;if(Xu)e:{switch(e){case"compositionstart":var E="onCompositionStart";break e;case"compositionend":E="onCompositionEnd";break e;case"compositionupdate":E="onCompositionUpdate";break e}E=void 0}else Dr?ip(e,n)&&(E="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(E="onCompositionStart");E&&(sp&&n.locale!=="ko"&&(Dr||E!=="onCompositionStart"?E==="onCompositionEnd"&&Dr&&(N=rp()):(Tn=f,Gu="value"in Tn?Tn.value:Tn.textContent,Dr=!0)),b=Po(u,E),0<b.length&&(E=new hd(E,e,null,n,f),h.push({event:E,listeners:b}),N?E.data=N:(N=op(n),N!==null&&(E.data=N)))),(N=Av?Fv(e,n):Lv(e,n))&&(u=Po(u,"onBeforeInput"),0<u.length&&(f=new hd("onBeforeInput","beforeinput",null,n,f),h.push({event:f,listeners:u}),f.data=N))}vp(h,t)})}function di(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Po(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=si(e,n),i!=null&&r.unshift(di(e,i,s)),i=si(e,t),i!=null&&r.push(di(e,i,s))),e=e.return}return r}function Nr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function bd(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,s?(l=si(n,i),l!=null&&o.unshift(di(n,l,a))):s||(l=si(n,i),l!=null&&o.push(di(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Xv=/\r\n?/g,Jv=/\u0000|\uFFFD/g;function Ed(e){return(typeof e=="string"?e:""+e).replace(Xv,`
`).replace(Jv,"")}function Gi(e,t,n){if(t=Ed(t),Ed(e)!==t&&n)throw Error(T(425))}function Io(){}var Al=null,Fl=null;function Ll(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var zl=typeof setTimeout=="function"?setTimeout:void 0,e0=typeof clearTimeout=="function"?clearTimeout:void 0,Nd=typeof Promise=="function"?Promise:void 0,t0=typeof queueMicrotask=="function"?queueMicrotask:typeof Nd<"u"?function(e){return Nd.resolve(null).then(e).catch(n0)}:zl;function n0(e){setTimeout(function(){throw e})}function Wa(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),ai(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);ai(t)}function Dn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Td(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Es=Math.random().toString(36).slice(2),At="__reactFiber$"+Es,fi="__reactProps$"+Es,en="__reactContainer$"+Es,$l="__reactEvents$"+Es,r0="__reactListeners$"+Es,s0="__reactHandles$"+Es;function er(e){var t=e[At];if(t)return t;for(var n=e.parentNode;n;){if(t=n[en]||n[At]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Td(e);e!==null;){if(n=e[At])return n;e=Td(e)}return t}e=n,n=e.parentNode}return null}function Ii(e){return e=e[At]||e[en],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Fr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function ca(e){return e[fi]||null}var Ul=[],Lr=-1;function Zn(e){return{current:e}}function ce(e){0>Lr||(e.current=Ul[Lr],Ul[Lr]=null,Lr--)}function ae(e,t){Lr++,Ul[Lr]=e.current,e.current=t}var Vn={},Fe=Zn(Vn),qe=Zn(!1),pr=Vn;function ds(e,t){var n=e.type.contextTypes;if(!n)return Vn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function Ye(e){return e=e.childContextTypes,e!=null}function Oo(){ce(qe),ce(Fe)}function jd(e,t,n){if(Fe.current!==Vn)throw Error(T(168));ae(Fe,t),ae(qe,n)}function wp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(T(108,Bg(e)||"Unknown",s));return pe({},n,r)}function Do(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Vn,pr=Fe.current,ae(Fe,e),ae(qe,qe.current),!0}function Rd(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=wp(e,t,pr),r.__reactInternalMemoizedMergedChildContext=e,ce(qe),ce(Fe),ae(Fe,e)):ce(qe),ae(qe,n)}var Ht=null,da=!1,Ha=!1;function kp(e){Ht===null?Ht=[e]:Ht.push(e)}function i0(e){da=!0,kp(e)}function Kn(){if(!Ha&&Ht!==null){Ha=!0;var e=0,t=oe;try{var n=Ht;for(oe=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ht=null,da=!1}catch(s){throw Ht!==null&&(Ht=Ht.slice(e+1)),Hh(Wu,Kn),s}finally{oe=t,Ha=!1}}return null}var zr=[],$r=0,Mo=null,Ao=0,ut=[],ct=0,mr=null,Gt=1,qt="";function Xn(e,t){zr[$r++]=Ao,zr[$r++]=Mo,Mo=e,Ao=t}function Sp(e,t,n){ut[ct++]=Gt,ut[ct++]=qt,ut[ct++]=mr,mr=e;var r=Gt;e=qt;var s=32-Et(r)-1;r&=~(1<<s),n+=1;var i=32-Et(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,Gt=1<<32-Et(t)+s|n<<s|r,qt=i+e}else Gt=1<<i|n<<s|r,qt=e}function ec(e){e.return!==null&&(Xn(e,1),Sp(e,1,0))}function tc(e){for(;e===Mo;)Mo=zr[--$r],zr[$r]=null,Ao=zr[--$r],zr[$r]=null;for(;e===mr;)mr=ut[--ct],ut[ct]=null,qt=ut[--ct],ut[ct]=null,Gt=ut[--ct],ut[ct]=null}var nt=null,tt=null,de=!1,_t=null;function Cp(e,t){var n=dt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Pd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,nt=e,tt=Dn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,nt=e,tt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=mr!==null?{id:Gt,overflow:qt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=dt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,nt=e,tt=null,!0):!1;default:return!1}}function Bl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Vl(e){if(de){var t=tt;if(t){var n=t;if(!Pd(e,t)){if(Bl(e))throw Error(T(418));t=Dn(n.nextSibling);var r=nt;t&&Pd(e,t)?Cp(r,n):(e.flags=e.flags&-4097|2,de=!1,nt=e)}}else{if(Bl(e))throw Error(T(418));e.flags=e.flags&-4097|2,de=!1,nt=e}}}function Id(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;nt=e}function qi(e){if(e!==nt)return!1;if(!de)return Id(e),de=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ll(e.type,e.memoizedProps)),t&&(t=tt)){if(Bl(e))throw _p(),Error(T(418));for(;t;)Cp(e,t),t=Dn(t.nextSibling)}if(Id(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){tt=Dn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}tt=null}}else tt=nt?Dn(e.stateNode.nextSibling):null;return!0}function _p(){for(var e=tt;e;)e=Dn(e.nextSibling)}function fs(){tt=nt=null,de=!1}function nc(e){_t===null?_t=[e]:_t.push(e)}var o0=sn.ReactCurrentBatchConfig;function As(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function Yi(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Od(e){var t=e._init;return t(e._payload)}function bp(e){function t(p,c){if(e){var g=p.deletions;g===null?(p.deletions=[c],p.flags|=16):g.push(c)}}function n(p,c){if(!e)return null;for(;c!==null;)t(p,c),c=c.sibling;return null}function r(p,c){for(p=new Map;c!==null;)c.key!==null?p.set(c.key,c):p.set(c.index,c),c=c.sibling;return p}function s(p,c){return p=Ln(p,c),p.index=0,p.sibling=null,p}function i(p,c,g){return p.index=g,e?(g=p.alternate,g!==null?(g=g.index,g<c?(p.flags|=2,c):g):(p.flags|=2,c)):(p.flags|=1048576,c)}function o(p){return e&&p.alternate===null&&(p.flags|=2),p}function a(p,c,g,x){return c===null||c.tag!==6?(c=Ja(g,p.mode,x),c.return=p,c):(c=s(c,g),c.return=p,c)}function l(p,c,g,x){var _=g.type;return _===Or?f(p,c,g.props.children,x,g.key):c!==null&&(c.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===hn&&Od(_)===c.type)?(x=s(c,g.props),x.ref=As(p,c,g),x.return=p,x):(x=wo(g.type,g.key,g.props,null,p.mode,x),x.ref=As(p,c,g),x.return=p,x)}function u(p,c,g,x){return c===null||c.tag!==4||c.stateNode.containerInfo!==g.containerInfo||c.stateNode.implementation!==g.implementation?(c=el(g,p.mode,x),c.return=p,c):(c=s(c,g.children||[]),c.return=p,c)}function f(p,c,g,x,_){return c===null||c.tag!==7?(c=hr(g,p.mode,x,_),c.return=p,c):(c=s(c,g),c.return=p,c)}function h(p,c,g){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Ja(""+c,p.mode,g),c.return=p,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case $i:return g=wo(c.type,c.key,c.props,null,p.mode,g),g.ref=As(p,null,c),g.return=p,g;case Ir:return c=el(c,p.mode,g),c.return=p,c;case hn:var x=c._init;return h(p,x(c._payload),g)}if(Bs(c)||Ps(c))return c=hr(c,p.mode,g,null),c.return=p,c;Yi(p,c)}return null}function m(p,c,g,x){var _=c!==null?c.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return _!==null?null:a(p,c,""+g,x);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case $i:return g.key===_?l(p,c,g,x):null;case Ir:return g.key===_?u(p,c,g,x):null;case hn:return _=g._init,m(p,c,_(g._payload),x)}if(Bs(g)||Ps(g))return _!==null?null:f(p,c,g,x,null);Yi(p,g)}return null}function w(p,c,g,x,_){if(typeof x=="string"&&x!==""||typeof x=="number")return p=p.get(g)||null,a(c,p,""+x,_);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case $i:return p=p.get(x.key===null?g:x.key)||null,l(c,p,x,_);case Ir:return p=p.get(x.key===null?g:x.key)||null,u(c,p,x,_);case hn:var b=x._init;return w(p,c,g,b(x._payload),_)}if(Bs(x)||Ps(x))return p=p.get(g)||null,f(c,p,x,_,null);Yi(c,x)}return null}function S(p,c,g,x){for(var _=null,b=null,N=c,E=c=0,L=null;N!==null&&E<g.length;E++){N.index>E?(L=N,N=null):L=N.sibling;var R=m(p,N,g[E],x);if(R===null){N===null&&(N=L);break}e&&N&&R.alternate===null&&t(p,N),c=i(R,c,E),b===null?_=R:b.sibling=R,b=R,N=L}if(E===g.length)return n(p,N),de&&Xn(p,E),_;if(N===null){for(;E<g.length;E++)N=h(p,g[E],x),N!==null&&(c=i(N,c,E),b===null?_=N:b.sibling=N,b=N);return de&&Xn(p,E),_}for(N=r(p,N);E<g.length;E++)L=w(N,p,E,g[E],x),L!==null&&(e&&L.alternate!==null&&N.delete(L.key===null?E:L.key),c=i(L,c,E),b===null?_=L:b.sibling=L,b=L);return e&&N.forEach(function(re){return t(p,re)}),de&&Xn(p,E),_}function y(p,c,g,x){var _=Ps(g);if(typeof _!="function")throw Error(T(150));if(g=_.call(g),g==null)throw Error(T(151));for(var b=_=null,N=c,E=c=0,L=null,R=g.next();N!==null&&!R.done;E++,R=g.next()){N.index>E?(L=N,N=null):L=N.sibling;var re=m(p,N,R.value,x);if(re===null){N===null&&(N=L);break}e&&N&&re.alternate===null&&t(p,N),c=i(re,c,E),b===null?_=re:b.sibling=re,b=re,N=L}if(R.done)return n(p,N),de&&Xn(p,E),_;if(N===null){for(;!R.done;E++,R=g.next())R=h(p,R.value,x),R!==null&&(c=i(R,c,E),b===null?_=R:b.sibling=R,b=R);return de&&Xn(p,E),_}for(N=r(p,N);!R.done;E++,R=g.next())R=w(N,p,E,R.value,x),R!==null&&(e&&R.alternate!==null&&N.delete(R.key===null?E:R.key),c=i(R,c,E),b===null?_=R:b.sibling=R,b=R);return e&&N.forEach(function(Q){return t(p,Q)}),de&&Xn(p,E),_}function C(p,c,g,x){if(typeof g=="object"&&g!==null&&g.type===Or&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case $i:e:{for(var _=g.key,b=c;b!==null;){if(b.key===_){if(_=g.type,_===Or){if(b.tag===7){n(p,b.sibling),c=s(b,g.props.children),c.return=p,p=c;break e}}else if(b.elementType===_||typeof _=="object"&&_!==null&&_.$$typeof===hn&&Od(_)===b.type){n(p,b.sibling),c=s(b,g.props),c.ref=As(p,b,g),c.return=p,p=c;break e}n(p,b);break}else t(p,b);b=b.sibling}g.type===Or?(c=hr(g.props.children,p.mode,x,g.key),c.return=p,p=c):(x=wo(g.type,g.key,g.props,null,p.mode,x),x.ref=As(p,c,g),x.return=p,p=x)}return o(p);case Ir:e:{for(b=g.key;c!==null;){if(c.key===b)if(c.tag===4&&c.stateNode.containerInfo===g.containerInfo&&c.stateNode.implementation===g.implementation){n(p,c.sibling),c=s(c,g.children||[]),c.return=p,p=c;break e}else{n(p,c);break}else t(p,c);c=c.sibling}c=el(g,p.mode,x),c.return=p,p=c}return o(p);case hn:return b=g._init,C(p,c,b(g._payload),x)}if(Bs(g))return S(p,c,g,x);if(Ps(g))return y(p,c,g,x);Yi(p,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,c!==null&&c.tag===6?(n(p,c.sibling),c=s(c,g),c.return=p,p=c):(n(p,c),c=Ja(g,p.mode,x),c.return=p,p=c),o(p)):n(p,c)}return C}var hs=bp(!0),Ep=bp(!1),Fo=Zn(null),Lo=null,Ur=null,rc=null;function sc(){rc=Ur=Lo=null}function ic(e){var t=Fo.current;ce(Fo),e._currentValue=t}function Ql(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Kr(e,t){Lo=e,rc=Ur=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ge=!0),e.firstContext=null)}function ht(e){var t=e._currentValue;if(rc!==e)if(e={context:e,memoizedValue:t,next:null},Ur===null){if(Lo===null)throw Error(T(308));Ur=e,Lo.dependencies={lanes:0,firstContext:e}}else Ur=Ur.next=e;return t}var tr=null;function oc(e){tr===null?tr=[e]:tr.push(e)}function Np(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,oc(t)):(n.next=s.next,s.next=n),t.interleaved=n,tn(e,r)}function tn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var pn=!1;function ac(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Tp(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Yt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Mn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,J&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,tn(e,n)}return s=r.interleaved,s===null?(t.next=t,oc(r)):(t.next=s.next,s.next=t),r.interleaved=t,tn(e,n)}function po(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Hu(e,n)}}function Dd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function zo(e,t,n,r){var s=e.updateQueue;pn=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?i=u:o.next=u,o=l;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==o&&(a===null?f.firstBaseUpdate=u:a.next=u,f.lastBaseUpdate=l))}if(i!==null){var h=s.baseState;o=0,f=u=l=null,a=i;do{var m=a.lane,w=a.eventTime;if((r&m)===m){f!==null&&(f=f.next={eventTime:w,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var S=e,y=a;switch(m=t,w=n,y.tag){case 1:if(S=y.payload,typeof S=="function"){h=S.call(w,h,m);break e}h=S;break e;case 3:S.flags=S.flags&-65537|128;case 0:if(S=y.payload,m=typeof S=="function"?S.call(w,h,m):S,m==null)break e;h=pe({},h,m);break e;case 2:pn=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,m=s.effects,m===null?s.effects=[a]:m.push(a))}else w={eventTime:w,lane:m,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(u=f=w,l=h):f=f.next=w,o|=m;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;m=a,a=m.next,m.next=null,s.lastBaseUpdate=m,s.shared.pending=null}}while(!0);if(f===null&&(l=h),s.baseState=l,s.firstBaseUpdate=u,s.lastBaseUpdate=f,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);gr|=o,e.lanes=o,e.memoizedState=h}}function Md(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(T(191,s));s.call(r)}}}var Oi={},zt=Zn(Oi),hi=Zn(Oi),pi=Zn(Oi);function nr(e){if(e===Oi)throw Error(T(174));return e}function lc(e,t){switch(ae(pi,t),ae(hi,e),ae(zt,Oi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:_l(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=_l(t,e)}ce(zt),ae(zt,t)}function ps(){ce(zt),ce(hi),ce(pi)}function jp(e){nr(pi.current);var t=nr(zt.current),n=_l(t,e.type);t!==n&&(ae(hi,e),ae(zt,n))}function uc(e){hi.current===e&&(ce(zt),ce(hi))}var fe=Zn(0);function $o(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Za=[];function cc(){for(var e=0;e<Za.length;e++)Za[e]._workInProgressVersionPrimary=null;Za.length=0}var mo=sn.ReactCurrentDispatcher,Ka=sn.ReactCurrentBatchConfig,yr=0,he=null,we=null,_e=null,Uo=!1,Xs=!1,mi=0,a0=0;function Pe(){throw Error(T(321))}function dc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Tt(e[n],t[n]))return!1;return!0}function fc(e,t,n,r,s,i){if(yr=i,he=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,mo.current=e===null||e.memoizedState===null?d0:f0,e=n(r,s),Xs){i=0;do{if(Xs=!1,mi=0,25<=i)throw Error(T(301));i+=1,_e=we=null,t.updateQueue=null,mo.current=h0,e=n(r,s)}while(Xs)}if(mo.current=Bo,t=we!==null&&we.next!==null,yr=0,_e=we=he=null,Uo=!1,t)throw Error(T(300));return e}function hc(){var e=mi!==0;return mi=0,e}function It(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return _e===null?he.memoizedState=_e=e:_e=_e.next=e,_e}function pt(){if(we===null){var e=he.alternate;e=e!==null?e.memoizedState:null}else e=we.next;var t=_e===null?he.memoizedState:_e.next;if(t!==null)_e=t,we=e;else{if(e===null)throw Error(T(310));we=e,e={memoizedState:we.memoizedState,baseState:we.baseState,baseQueue:we.baseQueue,queue:we.queue,next:null},_e===null?he.memoizedState=_e=e:_e=_e.next=e}return _e}function yi(e,t){return typeof t=="function"?t(e):t}function Ga(e){var t=pt(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=we,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,l=null,u=i;do{var f=u.lane;if((yr&f)===f)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var h={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=h,o=r):l=l.next=h,he.lanes|=f,gr|=f}u=u.next}while(u!==null&&u!==i);l===null?o=r:l.next=a,Tt(r,t.memoizedState)||(Ge=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,he.lanes|=i,gr|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function qa(e){var t=pt(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Tt(i,t.memoizedState)||(Ge=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Rp(){}function Pp(e,t){var n=he,r=pt(),s=t(),i=!Tt(r.memoizedState,s);if(i&&(r.memoizedState=s,Ge=!0),r=r.queue,pc(Dp.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||_e!==null&&_e.memoizedState.tag&1){if(n.flags|=2048,gi(9,Op.bind(null,n,r,s,t),void 0,null),be===null)throw Error(T(349));yr&30||Ip(n,t,s)}return s}function Ip(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Op(e,t,n,r){t.value=n,t.getSnapshot=r,Mp(t)&&Ap(e)}function Dp(e,t,n){return n(function(){Mp(t)&&Ap(e)})}function Mp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Tt(e,n)}catch{return!0}}function Ap(e){var t=tn(e,1);t!==null&&Nt(t,e,1,-1)}function Ad(e){var t=It();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:yi,lastRenderedState:e},t.queue=e,e=e.dispatch=c0.bind(null,he,e),[t.memoizedState,e]}function gi(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=he.updateQueue,t===null?(t={lastEffect:null,stores:null},he.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Fp(){return pt().memoizedState}function yo(e,t,n,r){var s=It();he.flags|=e,s.memoizedState=gi(1|t,n,void 0,r===void 0?null:r)}function fa(e,t,n,r){var s=pt();r=r===void 0?null:r;var i=void 0;if(we!==null){var o=we.memoizedState;if(i=o.destroy,r!==null&&dc(r,o.deps)){s.memoizedState=gi(t,n,i,r);return}}he.flags|=e,s.memoizedState=gi(1|t,n,i,r)}function Fd(e,t){return yo(8390656,8,e,t)}function pc(e,t){return fa(2048,8,e,t)}function Lp(e,t){return fa(4,2,e,t)}function zp(e,t){return fa(4,4,e,t)}function $p(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Up(e,t,n){return n=n!=null?n.concat([e]):null,fa(4,4,$p.bind(null,t,e),n)}function mc(){}function Bp(e,t){var n=pt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&dc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vp(e,t){var n=pt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&dc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Qp(e,t,n){return yr&21?(Tt(n,t)||(n=Gh(),he.lanes|=n,gr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ge=!0),e.memoizedState=n)}function l0(e,t){var n=oe;oe=n!==0&&4>n?n:4,e(!0);var r=Ka.transition;Ka.transition={};try{e(!1),t()}finally{oe=n,Ka.transition=r}}function Wp(){return pt().memoizedState}function u0(e,t,n){var r=Fn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Hp(e))Zp(t,n);else if(n=Np(e,t,n,r),n!==null){var s=Be();Nt(n,e,r,s),Kp(n,t,r)}}function c0(e,t,n){var r=Fn(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hp(e))Zp(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,Tt(a,o)){var l=t.interleaved;l===null?(s.next=s,oc(t)):(s.next=l.next,l.next=s),t.interleaved=s;return}}catch{}finally{}n=Np(e,t,s,r),n!==null&&(s=Be(),Nt(n,e,r,s),Kp(n,t,r))}}function Hp(e){var t=e.alternate;return e===he||t!==null&&t===he}function Zp(e,t){Xs=Uo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Kp(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Hu(e,n)}}var Bo={readContext:ht,useCallback:Pe,useContext:Pe,useEffect:Pe,useImperativeHandle:Pe,useInsertionEffect:Pe,useLayoutEffect:Pe,useMemo:Pe,useReducer:Pe,useRef:Pe,useState:Pe,useDebugValue:Pe,useDeferredValue:Pe,useTransition:Pe,useMutableSource:Pe,useSyncExternalStore:Pe,useId:Pe,unstable_isNewReconciler:!1},d0={readContext:ht,useCallback:function(e,t){return It().memoizedState=[e,t===void 0?null:t],e},useContext:ht,useEffect:Fd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,yo(4194308,4,$p.bind(null,t,e),n)},useLayoutEffect:function(e,t){return yo(4194308,4,e,t)},useInsertionEffect:function(e,t){return yo(4,2,e,t)},useMemo:function(e,t){var n=It();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=It();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=u0.bind(null,he,e),[r.memoizedState,e]},useRef:function(e){var t=It();return e={current:e},t.memoizedState=e},useState:Ad,useDebugValue:mc,useDeferredValue:function(e){return It().memoizedState=e},useTransition:function(){var e=Ad(!1),t=e[0];return e=l0.bind(null,e[1]),It().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=he,s=It();if(de){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),be===null)throw Error(T(349));yr&30||Ip(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,Fd(Dp.bind(null,r,i,e),[e]),r.flags|=2048,gi(9,Op.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=It(),t=be.identifierPrefix;if(de){var n=qt,r=Gt;n=(r&~(1<<32-Et(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=mi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=a0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},f0={readContext:ht,useCallback:Bp,useContext:ht,useEffect:pc,useImperativeHandle:Up,useInsertionEffect:Lp,useLayoutEffect:zp,useMemo:Vp,useReducer:Ga,useRef:Fp,useState:function(){return Ga(yi)},useDebugValue:mc,useDeferredValue:function(e){var t=pt();return Qp(t,we.memoizedState,e)},useTransition:function(){var e=Ga(yi)[0],t=pt().memoizedState;return[e,t]},useMutableSource:Rp,useSyncExternalStore:Pp,useId:Wp,unstable_isNewReconciler:!1},h0={readContext:ht,useCallback:Bp,useContext:ht,useEffect:pc,useImperativeHandle:Up,useInsertionEffect:Lp,useLayoutEffect:zp,useMemo:Vp,useReducer:qa,useRef:Fp,useState:function(){return qa(yi)},useDebugValue:mc,useDeferredValue:function(e){var t=pt();return we===null?t.memoizedState=e:Qp(t,we.memoizedState,e)},useTransition:function(){var e=qa(yi)[0],t=pt().memoizedState;return[e,t]},useMutableSource:Rp,useSyncExternalStore:Pp,useId:Wp,unstable_isNewReconciler:!1};function wt(e,t){if(e&&e.defaultProps){t=pe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Wl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:pe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ha={isMounted:function(e){return(e=e._reactInternals)?Er(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Be(),s=Fn(e),i=Yt(r,s);i.payload=t,n!=null&&(i.callback=n),t=Mn(e,i,s),t!==null&&(Nt(t,e,s,r),po(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Be(),s=Fn(e),i=Yt(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Mn(e,i,s),t!==null&&(Nt(t,e,s,r),po(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Be(),r=Fn(e),s=Yt(n,r);s.tag=2,t!=null&&(s.callback=t),t=Mn(e,s,r),t!==null&&(Nt(t,e,r,n),po(t,e,r))}};function Ld(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!ui(n,r)||!ui(s,i):!0}function Gp(e,t,n){var r=!1,s=Vn,i=t.contextType;return typeof i=="object"&&i!==null?i=ht(i):(s=Ye(t)?pr:Fe.current,r=t.contextTypes,i=(r=r!=null)?ds(e,s):Vn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ha,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function zd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ha.enqueueReplaceState(t,t.state,null)}function Hl(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},ac(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=ht(i):(i=Ye(t)?pr:Fe.current,s.context=ds(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Wl(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&ha.enqueueReplaceState(s,s.state,null),zo(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function ms(e,t){try{var n="",r=t;do n+=Ug(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function Ya(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Zl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var p0=typeof WeakMap=="function"?WeakMap:Map;function qp(e,t,n){n=Yt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Qo||(Qo=!0,ru=r),Zl(e,t)},n}function Yp(e,t,n){n=Yt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){Zl(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){Zl(e,t),typeof r!="function"&&(An===null?An=new Set([this]):An.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function $d(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new p0;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=T0.bind(null,e,t,n),t.then(e,e))}function Ud(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Bd(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Yt(-1,1),t.tag=2,Mn(n,t,1))),n.lanes|=1),e)}var m0=sn.ReactCurrentOwner,Ge=!1;function $e(e,t,n,r){t.child=e===null?Ep(t,null,n,r):hs(t,e.child,n,r)}function Vd(e,t,n,r,s){n=n.render;var i=t.ref;return Kr(t,s),r=fc(e,t,n,r,i,s),n=hc(),e!==null&&!Ge?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,nn(e,t,s)):(de&&n&&ec(t),t.flags|=1,$e(e,t,r,s),t.child)}function Qd(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!Cc(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Xp(e,t,i,r,s)):(e=wo(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:ui,n(o,r)&&e.ref===t.ref)return nn(e,t,s)}return t.flags|=1,e=Ln(i,r),e.ref=t.ref,e.return=t,t.child=e}function Xp(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(ui(i,r)&&e.ref===t.ref)if(Ge=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(Ge=!0);else return t.lanes=e.lanes,nn(e,t,s)}return Kl(e,t,n,r,s)}function Jp(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ae(Vr,et),et|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ae(Vr,et),et|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ae(Vr,et),et|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,ae(Vr,et),et|=r;return $e(e,t,s,n),t.child}function em(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Kl(e,t,n,r,s){var i=Ye(n)?pr:Fe.current;return i=ds(t,i),Kr(t,s),n=fc(e,t,n,r,i,s),r=hc(),e!==null&&!Ge?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,nn(e,t,s)):(de&&r&&ec(t),t.flags|=1,$e(e,t,n,s),t.child)}function Wd(e,t,n,r,s){if(Ye(n)){var i=!0;Do(t)}else i=!1;if(Kr(t,s),t.stateNode===null)go(e,t),Gp(t,n,r),Hl(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=ht(u):(u=Ye(n)?pr:Fe.current,u=ds(t,u));var f=n.getDerivedStateFromProps,h=typeof f=="function"||typeof o.getSnapshotBeforeUpdate=="function";h||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&zd(t,o,r,u),pn=!1;var m=t.memoizedState;o.state=m,zo(t,r,o,s),l=t.memoizedState,a!==r||m!==l||qe.current||pn?(typeof f=="function"&&(Wl(t,n,f,r),l=t.memoizedState),(a=pn||Ld(t,n,a,r,m,l,u))?(h||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Tp(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:wt(t.type,a),o.props=u,h=t.pendingProps,m=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=ht(l):(l=Ye(n)?pr:Fe.current,l=ds(t,l));var w=n.getDerivedStateFromProps;(f=typeof w=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==h||m!==l)&&zd(t,o,r,l),pn=!1,m=t.memoizedState,o.state=m,zo(t,r,o,s);var S=t.memoizedState;a!==h||m!==S||qe.current||pn?(typeof w=="function"&&(Wl(t,n,w,r),S=t.memoizedState),(u=pn||Ld(t,n,u,r,m,S,l)||!1)?(f||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,S,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,S,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=S),o.props=r,o.state=S,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return Gl(e,t,n,r,i,s)}function Gl(e,t,n,r,s,i){em(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&Rd(t,n,!1),nn(e,t,i);r=t.stateNode,m0.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=hs(t,e.child,null,i),t.child=hs(t,null,a,i)):$e(e,t,a,i),t.memoizedState=r.state,s&&Rd(t,n,!0),t.child}function tm(e){var t=e.stateNode;t.pendingContext?jd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&jd(e,t.context,!1),lc(e,t.containerInfo)}function Hd(e,t,n,r,s){return fs(),nc(s),t.flags|=256,$e(e,t,n,r),t.child}var ql={dehydrated:null,treeContext:null,retryLane:0};function Yl(e){return{baseLanes:e,cachePool:null,transitions:null}}function nm(e,t,n){var r=t.pendingProps,s=fe.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),ae(fe,s&1),e===null)return Vl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=ya(o,r,0,null),e=hr(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Yl(n),t.memoizedState=ql,e):yc(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return y0(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Ln(s,l),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=Ln(a,i):(i=hr(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?Yl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=ql,r}return i=e.child,e=i.sibling,r=Ln(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function yc(e,t){return t=ya({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Xi(e,t,n,r){return r!==null&&nc(r),hs(t,e.child,null,n),e=yc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function y0(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=Ya(Error(T(422))),Xi(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=ya({mode:"visible",children:r.children},s,0,null),i=hr(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&hs(t,e.child,null,o),t.child.memoizedState=Yl(o),t.memoizedState=ql,i);if(!(t.mode&1))return Xi(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(T(419)),r=Ya(i,r,void 0),Xi(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ge||a){if(r=be,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,tn(e,s),Nt(r,e,s,-1))}return Sc(),r=Ya(Error(T(421))),Xi(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=j0.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,tt=Dn(s.nextSibling),nt=t,de=!0,_t=null,e!==null&&(ut[ct++]=Gt,ut[ct++]=qt,ut[ct++]=mr,Gt=e.id,qt=e.overflow,mr=t),t=yc(t,r.children),t.flags|=4096,t)}function Zd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ql(e.return,t,n)}function Xa(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function rm(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if($e(e,t,r.children,n),r=fe.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zd(e,n,t);else if(e.tag===19)Zd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ae(fe,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&$o(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),Xa(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&$o(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}Xa(t,!0,n,null,i);break;case"together":Xa(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function go(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function nn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),gr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Ln(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ln(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function g0(e,t,n){switch(t.tag){case 3:tm(t),fs();break;case 5:jp(t);break;case 1:Ye(t.type)&&Do(t);break;case 4:lc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;ae(Fo,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ae(fe,fe.current&1),t.flags|=128,null):n&t.child.childLanes?nm(e,t,n):(ae(fe,fe.current&1),e=nn(e,t,n),e!==null?e.sibling:null);ae(fe,fe.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return rm(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),ae(fe,fe.current),r)break;return null;case 22:case 23:return t.lanes=0,Jp(e,t,n)}return nn(e,t,n)}var sm,Xl,im,om;sm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Xl=function(){};im=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,nr(zt.current);var i=null;switch(n){case"input":s=wl(e,s),r=wl(e,r),i=[];break;case"select":s=pe({},s,{value:void 0}),r=pe({},r,{value:void 0}),i=[];break;case"textarea":s=Cl(e,s),r=Cl(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Io)}bl(n,r);var o;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var a=s[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(ni.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(ni.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&ue("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};om=function(e,t,n,r){n!==r&&(t.flags|=4)};function Fs(e,t){if(!de)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ie(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function v0(e,t,n){var r=t.pendingProps;switch(tc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ie(t),null;case 1:return Ye(t.type)&&Oo(),Ie(t),null;case 3:return r=t.stateNode,ps(),ce(qe),ce(Fe),cc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(qi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,_t!==null&&(ou(_t),_t=null))),Xl(e,t),Ie(t),null;case 5:uc(t);var s=nr(pi.current);if(n=t.type,e!==null&&t.stateNode!=null)im(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return Ie(t),null}if(e=nr(zt.current),qi(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[At]=t,r[fi]=i,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(s=0;s<Qs.length;s++)ue(Qs[s],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":nd(r,i),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ue("invalid",r);break;case"textarea":sd(r,i),ue("invalid",r)}bl(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Gi(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Gi(r.textContent,a,e),s=["children",""+a]):ni.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&ue("scroll",r)}switch(n){case"input":Ui(r),rd(r,i,!0);break;case"textarea":Ui(r),id(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Io)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Dh(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[At]=t,e[fi]=r,sm(e,t,!1,!1),t.stateNode=e;e:{switch(o=El(n,r),n){case"dialog":ue("cancel",e),ue("close",e),s=r;break;case"iframe":case"object":case"embed":ue("load",e),s=r;break;case"video":case"audio":for(s=0;s<Qs.length;s++)ue(Qs[s],e);s=r;break;case"source":ue("error",e),s=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),s=r;break;case"details":ue("toggle",e),s=r;break;case"input":nd(e,r),s=wl(e,r),ue("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=pe({},r,{value:void 0}),ue("invalid",e);break;case"textarea":sd(e,r),s=Cl(e,r),ue("invalid",e);break;default:s=r}bl(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Fh(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Mh(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&ri(e,l):typeof l=="number"&&ri(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(ni.hasOwnProperty(i)?l!=null&&i==="onScroll"&&ue("scroll",e):l!=null&&$u(e,i,l,o))}switch(n){case"input":Ui(e),rd(e,r,!1);break;case"textarea":Ui(e),id(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Bn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Qr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Qr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=Io)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ie(t),null;case 6:if(e&&t.stateNode!=null)om(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=nr(pi.current),nr(zt.current),qi(t)){if(r=t.stateNode,n=t.memoizedProps,r[At]=t,(i=r.nodeValue!==n)&&(e=nt,e!==null))switch(e.tag){case 3:Gi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Gi(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[At]=t,t.stateNode=r}return Ie(t),null;case 13:if(ce(fe),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(de&&tt!==null&&t.mode&1&&!(t.flags&128))_p(),fs(),t.flags|=98560,i=!1;else if(i=qi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(T(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(T(317));i[At]=t}else fs(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ie(t),i=!1}else _t!==null&&(ou(_t),_t=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||fe.current&1?Se===0&&(Se=3):Sc())),t.updateQueue!==null&&(t.flags|=4),Ie(t),null);case 4:return ps(),Xl(e,t),e===null&&ci(t.stateNode.containerInfo),Ie(t),null;case 10:return ic(t.type._context),Ie(t),null;case 17:return Ye(t.type)&&Oo(),Ie(t),null;case 19:if(ce(fe),i=t.memoizedState,i===null)return Ie(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)Fs(i,!1);else{if(Se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=$o(e),o!==null){for(t.flags|=128,Fs(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ae(fe,fe.current&1|2),t.child}e=e.sibling}i.tail!==null&&ge()>ys&&(t.flags|=128,r=!0,Fs(i,!1),t.lanes=4194304)}else{if(!r)if(e=$o(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Fs(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!de)return Ie(t),null}else 2*ge()-i.renderingStartTime>ys&&n!==1073741824&&(t.flags|=128,r=!0,Fs(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ge(),t.sibling=null,n=fe.current,ae(fe,r?n&1|2:n&1),t):(Ie(t),null);case 22:case 23:return kc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?et&1073741824&&(Ie(t),t.subtreeFlags&6&&(t.flags|=8192)):Ie(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function x0(e,t){switch(tc(t),t.tag){case 1:return Ye(t.type)&&Oo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ps(),ce(qe),ce(Fe),cc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return uc(t),null;case 13:if(ce(fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));fs()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ce(fe),null;case 4:return ps(),null;case 10:return ic(t.type._context),null;case 22:case 23:return kc(),null;case 24:return null;default:return null}}var Ji=!1,Ae=!1,w0=typeof WeakSet=="function"?WeakSet:Set,M=null;function Br(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ye(e,t,r)}else n.current=null}function Jl(e,t,n){try{n()}catch(r){ye(e,t,r)}}var Kd=!1;function k0(e,t){if(Al=jo,e=dp(),Ju(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,f=0,h=e,m=null;t:for(;;){for(var w;h!==n||s!==0&&h.nodeType!==3||(a=o+s),h!==i||r!==0&&h.nodeType!==3||(l=o+r),h.nodeType===3&&(o+=h.nodeValue.length),(w=h.firstChild)!==null;)m=h,h=w;for(;;){if(h===e)break t;if(m===n&&++u===s&&(a=o),m===i&&++f===r&&(l=o),(w=h.nextSibling)!==null)break;h=m,m=h.parentNode}h=w}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Fl={focusedElem:e,selectionRange:n},jo=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var S=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(S!==null){var y=S.memoizedProps,C=S.memoizedState,p=t.stateNode,c=p.getSnapshotBeforeUpdate(t.elementType===t.type?y:wt(t.type,y),C);p.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(x){ye(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return S=Kd,Kd=!1,S}function Js(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Jl(t,n,i)}s=s.next}while(s!==r)}}function pa(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function eu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function am(e){var t=e.alternate;t!==null&&(e.alternate=null,am(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[At],delete t[fi],delete t[$l],delete t[r0],delete t[s0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function lm(e){return e.tag===5||e.tag===3||e.tag===4}function Gd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||lm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function tu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Io));else if(r!==4&&(e=e.child,e!==null))for(tu(e,t,n),e=e.sibling;e!==null;)tu(e,t,n),e=e.sibling}function nu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(nu(e,t,n),e=e.sibling;e!==null;)nu(e,t,n),e=e.sibling}var Te=null,Ct=!1;function ln(e,t,n){for(n=n.child;n!==null;)um(e,t,n),n=n.sibling}function um(e,t,n){if(Lt&&typeof Lt.onCommitFiberUnmount=="function")try{Lt.onCommitFiberUnmount(oa,n)}catch{}switch(n.tag){case 5:Ae||Br(n,t);case 6:var r=Te,s=Ct;Te=null,ln(e,t,n),Te=r,Ct=s,Te!==null&&(Ct?(e=Te,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Te.removeChild(n.stateNode));break;case 18:Te!==null&&(Ct?(e=Te,n=n.stateNode,e.nodeType===8?Wa(e.parentNode,n):e.nodeType===1&&Wa(e,n),ai(e)):Wa(Te,n.stateNode));break;case 4:r=Te,s=Ct,Te=n.stateNode.containerInfo,Ct=!0,ln(e,t,n),Te=r,Ct=s;break;case 0:case 11:case 14:case 15:if(!Ae&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&Jl(n,t,o),s=s.next}while(s!==r)}ln(e,t,n);break;case 1:if(!Ae&&(Br(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ye(n,t,a)}ln(e,t,n);break;case 21:ln(e,t,n);break;case 22:n.mode&1?(Ae=(r=Ae)||n.memoizedState!==null,ln(e,t,n),Ae=r):ln(e,t,n);break;default:ln(e,t,n)}}function qd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new w0),t.forEach(function(r){var s=R0.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function xt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:Te=a.stateNode,Ct=!1;break e;case 3:Te=a.stateNode.containerInfo,Ct=!0;break e;case 4:Te=a.stateNode.containerInfo,Ct=!0;break e}a=a.return}if(Te===null)throw Error(T(160));um(i,o,s),Te=null,Ct=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(u){ye(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)cm(t,e),t=t.sibling}function cm(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(xt(t,e),Pt(e),r&4){try{Js(3,e,e.return),pa(3,e)}catch(y){ye(e,e.return,y)}try{Js(5,e,e.return)}catch(y){ye(e,e.return,y)}}break;case 1:xt(t,e),Pt(e),r&512&&n!==null&&Br(n,n.return);break;case 5:if(xt(t,e),Pt(e),r&512&&n!==null&&Br(n,n.return),e.flags&32){var s=e.stateNode;try{ri(s,"")}catch(y){ye(e,e.return,y)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Ih(s,i),El(a,o);var u=El(a,i);for(o=0;o<l.length;o+=2){var f=l[o],h=l[o+1];f==="style"?Fh(s,h):f==="dangerouslySetInnerHTML"?Mh(s,h):f==="children"?ri(s,h):$u(s,f,h,u)}switch(a){case"input":kl(s,i);break;case"textarea":Oh(s,i);break;case"select":var m=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var w=i.value;w!=null?Qr(s,!!i.multiple,w,!1):m!==!!i.multiple&&(i.defaultValue!=null?Qr(s,!!i.multiple,i.defaultValue,!0):Qr(s,!!i.multiple,i.multiple?[]:"",!1))}s[fi]=i}catch(y){ye(e,e.return,y)}}break;case 6:if(xt(t,e),Pt(e),r&4){if(e.stateNode===null)throw Error(T(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(y){ye(e,e.return,y)}}break;case 3:if(xt(t,e),Pt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{ai(t.containerInfo)}catch(y){ye(e,e.return,y)}break;case 4:xt(t,e),Pt(e);break;case 13:xt(t,e),Pt(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(xc=ge())),r&4&&qd(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(Ae=(u=Ae)||f,xt(t,e),Ae=u):xt(t,e),Pt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(M=e,f=e.child;f!==null;){for(h=M=f;M!==null;){switch(m=M,w=m.child,m.tag){case 0:case 11:case 14:case 15:Js(4,m,m.return);break;case 1:Br(m,m.return);var S=m.stateNode;if(typeof S.componentWillUnmount=="function"){r=m,n=m.return;try{t=r,S.props=t.memoizedProps,S.state=t.memoizedState,S.componentWillUnmount()}catch(y){ye(r,n,y)}}break;case 5:Br(m,m.return);break;case 22:if(m.memoizedState!==null){Xd(h);continue}}w!==null?(w.return=m,M=w):Xd(h)}f=f.sibling}e:for(f=null,h=e;;){if(h.tag===5){if(f===null){f=h;try{s=h.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=h.stateNode,l=h.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Ah("display",o))}catch(y){ye(e,e.return,y)}}}else if(h.tag===6){if(f===null)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(y){ye(e,e.return,y)}}else if((h.tag!==22&&h.tag!==23||h.memoizedState===null||h===e)&&h.child!==null){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;h.sibling===null;){if(h.return===null||h.return===e)break e;f===h&&(f=null),h=h.return}f===h&&(f=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:xt(t,e),Pt(e),r&4&&qd(e);break;case 21:break;default:xt(t,e),Pt(e)}}function Pt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(lm(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(ri(s,""),r.flags&=-33);var i=Gd(e);nu(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Gd(e);tu(e,a,o);break;default:throw Error(T(161))}}catch(l){ye(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function S0(e,t,n){M=e,dm(e)}function dm(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var s=M,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Ji;if(!o){var a=s.alternate,l=a!==null&&a.memoizedState!==null||Ae;a=Ji;var u=Ae;if(Ji=o,(Ae=l)&&!u)for(M=s;M!==null;)o=M,l=o.child,o.tag===22&&o.memoizedState!==null?Jd(s):l!==null?(l.return=o,M=l):Jd(s);for(;i!==null;)M=i,dm(i),i=i.sibling;M=s,Ji=a,Ae=u}Yd(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,M=i):Yd(e)}}function Yd(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ae||pa(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ae)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:wt(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Md(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Md(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var h=f.dehydrated;h!==null&&ai(h)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}Ae||t.flags&512&&eu(t)}catch(m){ye(t,t.return,m)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Xd(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Jd(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{pa(4,t)}catch(l){ye(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(l){ye(t,s,l)}}var i=t.return;try{eu(t)}catch(l){ye(t,i,l)}break;case 5:var o=t.return;try{eu(t)}catch(l){ye(t,o,l)}}}catch(l){ye(t,t.return,l)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var C0=Math.ceil,Vo=sn.ReactCurrentDispatcher,gc=sn.ReactCurrentOwner,ft=sn.ReactCurrentBatchConfig,J=0,be=null,xe=null,je=0,et=0,Vr=Zn(0),Se=0,vi=null,gr=0,ma=0,vc=0,ei=null,Ke=null,xc=0,ys=1/0,Wt=null,Qo=!1,ru=null,An=null,eo=!1,jn=null,Wo=0,ti=0,su=null,vo=-1,xo=0;function Be(){return J&6?ge():vo!==-1?vo:vo=ge()}function Fn(e){return e.mode&1?J&2&&je!==0?je&-je:o0.transition!==null?(xo===0&&(xo=Gh()),xo):(e=oe,e!==0||(e=window.event,e=e===void 0?16:np(e.type)),e):1}function Nt(e,t,n,r){if(50<ti)throw ti=0,su=null,Error(T(185));Ri(e,n,r),(!(J&2)||e!==be)&&(e===be&&(!(J&2)&&(ma|=n),Se===4&&gn(e,je)),Xe(e,r),n===1&&J===0&&!(t.mode&1)&&(ys=ge()+500,da&&Kn()))}function Xe(e,t){var n=e.callbackNode;ov(e,t);var r=To(e,e===be?je:0);if(r===0)n!==null&&ld(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ld(n),t===1)e.tag===0?i0(ef.bind(null,e)):kp(ef.bind(null,e)),t0(function(){!(J&6)&&Kn()}),n=null;else{switch(qh(r)){case 1:n=Wu;break;case 4:n=Zh;break;case 16:n=No;break;case 536870912:n=Kh;break;default:n=No}n=xm(n,fm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function fm(e,t){if(vo=-1,xo=0,J&6)throw Error(T(327));var n=e.callbackNode;if(Gr()&&e.callbackNode!==n)return null;var r=To(e,e===be?je:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ho(e,r);else{t=r;var s=J;J|=2;var i=pm();(be!==e||je!==t)&&(Wt=null,ys=ge()+500,fr(e,t));do try{E0();break}catch(a){hm(e,a)}while(!0);sc(),Vo.current=i,J=s,xe!==null?t=0:(be=null,je=0,t=Se)}if(t!==0){if(t===2&&(s=Pl(e),s!==0&&(r=s,t=iu(e,s))),t===1)throw n=vi,fr(e,0),gn(e,r),Xe(e,ge()),n;if(t===6)gn(e,r);else{if(s=e.current.alternate,!(r&30)&&!_0(s)&&(t=Ho(e,r),t===2&&(i=Pl(e),i!==0&&(r=i,t=iu(e,i))),t===1))throw n=vi,fr(e,0),gn(e,r),Xe(e,ge()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:Jn(e,Ke,Wt);break;case 3:if(gn(e,r),(r&130023424)===r&&(t=xc+500-ge(),10<t)){if(To(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){Be(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=zl(Jn.bind(null,e,Ke,Wt),t);break}Jn(e,Ke,Wt);break;case 4:if(gn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-Et(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=ge()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*C0(r/1960))-r,10<r){e.timeoutHandle=zl(Jn.bind(null,e,Ke,Wt),r);break}Jn(e,Ke,Wt);break;case 5:Jn(e,Ke,Wt);break;default:throw Error(T(329))}}}return Xe(e,ge()),e.callbackNode===n?fm.bind(null,e):null}function iu(e,t){var n=ei;return e.current.memoizedState.isDehydrated&&(fr(e,t).flags|=256),e=Ho(e,t),e!==2&&(t=Ke,Ke=n,t!==null&&ou(t)),e}function ou(e){Ke===null?Ke=e:Ke.push.apply(Ke,e)}function _0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Tt(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gn(e,t){for(t&=~vc,t&=~ma,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Et(t),r=1<<n;e[n]=-1,t&=~r}}function ef(e){if(J&6)throw Error(T(327));Gr();var t=To(e,0);if(!(t&1))return Xe(e,ge()),null;var n=Ho(e,t);if(e.tag!==0&&n===2){var r=Pl(e);r!==0&&(t=r,n=iu(e,r))}if(n===1)throw n=vi,fr(e,0),gn(e,t),Xe(e,ge()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Jn(e,Ke,Wt),Xe(e,ge()),null}function wc(e,t){var n=J;J|=1;try{return e(t)}finally{J=n,J===0&&(ys=ge()+500,da&&Kn())}}function vr(e){jn!==null&&jn.tag===0&&!(J&6)&&Gr();var t=J;J|=1;var n=ft.transition,r=oe;try{if(ft.transition=null,oe=1,e)return e()}finally{oe=r,ft.transition=n,J=t,!(J&6)&&Kn()}}function kc(){et=Vr.current,ce(Vr)}function fr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,e0(n)),xe!==null)for(n=xe.return;n!==null;){var r=n;switch(tc(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Oo();break;case 3:ps(),ce(qe),ce(Fe),cc();break;case 5:uc(r);break;case 4:ps();break;case 13:ce(fe);break;case 19:ce(fe);break;case 10:ic(r.type._context);break;case 22:case 23:kc()}n=n.return}if(be=e,xe=e=Ln(e.current,null),je=et=t,Se=0,vi=null,vc=ma=gr=0,Ke=ei=null,tr!==null){for(t=0;t<tr.length;t++)if(n=tr[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}tr=null}return e}function hm(e,t){do{var n=xe;try{if(sc(),mo.current=Bo,Uo){for(var r=he.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}Uo=!1}if(yr=0,_e=we=he=null,Xs=!1,mi=0,gc.current=null,n===null||n.return===null){Se=1,vi=t,xe=null;break}e:{var i=e,o=n.return,a=n,l=t;if(t=je,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,f=a,h=f.tag;if(!(f.mode&1)&&(h===0||h===11||h===15)){var m=f.alternate;m?(f.updateQueue=m.updateQueue,f.memoizedState=m.memoizedState,f.lanes=m.lanes):(f.updateQueue=null,f.memoizedState=null)}var w=Ud(o);if(w!==null){w.flags&=-257,Bd(w,o,a,i,t),w.mode&1&&$d(i,u,t),t=w,l=u;var S=t.updateQueue;if(S===null){var y=new Set;y.add(l),t.updateQueue=y}else S.add(l);break e}else{if(!(t&1)){$d(i,u,t),Sc();break e}l=Error(T(426))}}else if(de&&a.mode&1){var C=Ud(o);if(C!==null){!(C.flags&65536)&&(C.flags|=256),Bd(C,o,a,i,t),nc(ms(l,a));break e}}i=l=ms(l,a),Se!==4&&(Se=2),ei===null?ei=[i]:ei.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var p=qp(i,l,t);Dd(i,p);break e;case 1:a=l;var c=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(An===null||!An.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Yp(i,a,t);Dd(i,x);break e}}i=i.return}while(i!==null)}ym(n)}catch(_){t=_,xe===n&&n!==null&&(xe=n=n.return);continue}break}while(!0)}function pm(){var e=Vo.current;return Vo.current=Bo,e===null?Bo:e}function Sc(){(Se===0||Se===3||Se===2)&&(Se=4),be===null||!(gr&268435455)&&!(ma&268435455)||gn(be,je)}function Ho(e,t){var n=J;J|=2;var r=pm();(be!==e||je!==t)&&(Wt=null,fr(e,t));do try{b0();break}catch(s){hm(e,s)}while(!0);if(sc(),J=n,Vo.current=r,xe!==null)throw Error(T(261));return be=null,je=0,Se}function b0(){for(;xe!==null;)mm(xe)}function E0(){for(;xe!==null&&!Yg();)mm(xe)}function mm(e){var t=vm(e.alternate,e,et);e.memoizedProps=e.pendingProps,t===null?ym(e):xe=t,gc.current=null}function ym(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=x0(n,t),n!==null){n.flags&=32767,xe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Se=6,xe=null;return}}else if(n=v0(n,t,et),n!==null){xe=n;return}if(t=t.sibling,t!==null){xe=t;return}xe=t=e}while(t!==null);Se===0&&(Se=5)}function Jn(e,t,n){var r=oe,s=ft.transition;try{ft.transition=null,oe=1,N0(e,t,n,r)}finally{ft.transition=s,oe=r}return null}function N0(e,t,n,r){do Gr();while(jn!==null);if(J&6)throw Error(T(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(av(e,i),e===be&&(xe=be=null,je=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||eo||(eo=!0,xm(No,function(){return Gr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=ft.transition,ft.transition=null;var o=oe;oe=1;var a=J;J|=4,gc.current=null,k0(e,n),cm(n,e),Zv(Fl),jo=!!Al,Fl=Al=null,e.current=n,S0(n),Xg(),J=a,oe=o,ft.transition=i}else e.current=n;if(eo&&(eo=!1,jn=e,Wo=s),i=e.pendingLanes,i===0&&(An=null),tv(n.stateNode),Xe(e,ge()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(Qo)throw Qo=!1,e=ru,ru=null,e;return Wo&1&&e.tag!==0&&Gr(),i=e.pendingLanes,i&1?e===su?ti++:(ti=0,su=e):ti=0,Kn(),null}function Gr(){if(jn!==null){var e=qh(Wo),t=ft.transition,n=oe;try{if(ft.transition=null,oe=16>e?16:e,jn===null)var r=!1;else{if(e=jn,jn=null,Wo=0,J&6)throw Error(T(331));var s=J;for(J|=4,M=e.current;M!==null;){var i=M,o=i.child;if(M.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(M=u;M!==null;){var f=M;switch(f.tag){case 0:case 11:case 15:Js(8,f,i)}var h=f.child;if(h!==null)h.return=f,M=h;else for(;M!==null;){f=M;var m=f.sibling,w=f.return;if(am(f),f===u){M=null;break}if(m!==null){m.return=w,M=m;break}M=w}}}var S=i.alternate;if(S!==null){var y=S.child;if(y!==null){S.child=null;do{var C=y.sibling;y.sibling=null,y=C}while(y!==null)}}M=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,M=o;else e:for(;M!==null;){if(i=M,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Js(9,i,i.return)}var p=i.sibling;if(p!==null){p.return=i.return,M=p;break e}M=i.return}}var c=e.current;for(M=c;M!==null;){o=M;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,M=g;else e:for(o=c;M!==null;){if(a=M,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:pa(9,a)}}catch(_){ye(a,a.return,_)}if(a===o){M=null;break e}var x=a.sibling;if(x!==null){x.return=a.return,M=x;break e}M=a.return}}if(J=s,Kn(),Lt&&typeof Lt.onPostCommitFiberRoot=="function")try{Lt.onPostCommitFiberRoot(oa,e)}catch{}r=!0}return r}finally{oe=n,ft.transition=t}}return!1}function tf(e,t,n){t=ms(n,t),t=qp(e,t,1),e=Mn(e,t,1),t=Be(),e!==null&&(Ri(e,1,t),Xe(e,t))}function ye(e,t,n){if(e.tag===3)tf(e,e,n);else for(;t!==null;){if(t.tag===3){tf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(An===null||!An.has(r))){e=ms(n,e),e=Yp(t,e,1),t=Mn(t,e,1),e=Be(),t!==null&&(Ri(t,1,e),Xe(t,e));break}}t=t.return}}function T0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Be(),e.pingedLanes|=e.suspendedLanes&n,be===e&&(je&n)===n&&(Se===4||Se===3&&(je&130023424)===je&&500>ge()-xc?fr(e,0):vc|=n),Xe(e,t)}function gm(e,t){t===0&&(e.mode&1?(t=Qi,Qi<<=1,!(Qi&130023424)&&(Qi=4194304)):t=1);var n=Be();e=tn(e,t),e!==null&&(Ri(e,t,n),Xe(e,n))}function j0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),gm(e,n)}function R0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),gm(e,n)}var vm;vm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||qe.current)Ge=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ge=!1,g0(e,t,n);Ge=!!(e.flags&131072)}else Ge=!1,de&&t.flags&1048576&&Sp(t,Ao,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;go(e,t),e=t.pendingProps;var s=ds(t,Fe.current);Kr(t,n),s=fc(null,t,r,e,s,n);var i=hc();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ye(r)?(i=!0,Do(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,ac(t),s.updater=ha,t.stateNode=s,s._reactInternals=t,Hl(t,r,e,n),t=Gl(null,t,r,!0,i,n)):(t.tag=0,de&&i&&ec(t),$e(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(go(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=I0(r),e=wt(r,e),s){case 0:t=Kl(null,t,r,e,n);break e;case 1:t=Wd(null,t,r,e,n);break e;case 11:t=Vd(null,t,r,e,n);break e;case 14:t=Qd(null,t,r,wt(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:wt(r,s),Kl(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:wt(r,s),Wd(e,t,r,s,n);case 3:e:{if(tm(t),e===null)throw Error(T(387));r=t.pendingProps,i=t.memoizedState,s=i.element,Tp(e,t),zo(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=ms(Error(T(423)),t),t=Hd(e,t,r,n,s);break e}else if(r!==s){s=ms(Error(T(424)),t),t=Hd(e,t,r,n,s);break e}else for(tt=Dn(t.stateNode.containerInfo.firstChild),nt=t,de=!0,_t=null,n=Ep(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(fs(),r===s){t=nn(e,t,n);break e}$e(e,t,r,n)}t=t.child}return t;case 5:return jp(t),e===null&&Vl(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,Ll(r,s)?o=null:i!==null&&Ll(r,i)&&(t.flags|=32),em(e,t),$e(e,t,o,n),t.child;case 6:return e===null&&Vl(t),null;case 13:return nm(e,t,n);case 4:return lc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=hs(t,null,r,n):$e(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:wt(r,s),Vd(e,t,r,s,n);case 7:return $e(e,t,t.pendingProps,n),t.child;case 8:return $e(e,t,t.pendingProps.children,n),t.child;case 12:return $e(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,ae(Fo,r._currentValue),r._currentValue=o,i!==null)if(Tt(i.value,o)){if(i.children===s.children&&!qe.current){t=nn(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=Yt(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?l.next=l:(l.next=f.next,f.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),Ql(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(T(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Ql(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}$e(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Kr(t,n),s=ht(s),r=r(s),t.flags|=1,$e(e,t,r,n),t.child;case 14:return r=t.type,s=wt(r,t.pendingProps),s=wt(r.type,s),Qd(e,t,r,s,n);case 15:return Xp(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:wt(r,s),go(e,t),t.tag=1,Ye(r)?(e=!0,Do(t)):e=!1,Kr(t,n),Gp(t,r,s),Hl(t,r,s,n),Gl(null,t,r,!0,e,n);case 19:return rm(e,t,n);case 22:return Jp(e,t,n)}throw Error(T(156,t.tag))};function xm(e,t){return Hh(e,t)}function P0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function dt(e,t,n,r){return new P0(e,t,n,r)}function Cc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function I0(e){if(typeof e=="function")return Cc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Bu)return 11;if(e===Vu)return 14}return 2}function Ln(e,t){var n=e.alternate;return n===null?(n=dt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function wo(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")Cc(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Or:return hr(n.children,s,i,t);case Uu:o=8,s|=8;break;case yl:return e=dt(12,n,t,s|2),e.elementType=yl,e.lanes=i,e;case gl:return e=dt(13,n,t,s),e.elementType=gl,e.lanes=i,e;case vl:return e=dt(19,n,t,s),e.elementType=vl,e.lanes=i,e;case jh:return ya(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Nh:o=10;break e;case Th:o=9;break e;case Bu:o=11;break e;case Vu:o=14;break e;case hn:o=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=dt(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function hr(e,t,n,r){return e=dt(7,e,r,t),e.lanes=n,e}function ya(e,t,n,r){return e=dt(22,e,r,t),e.elementType=jh,e.lanes=n,e.stateNode={isHidden:!1},e}function Ja(e,t,n){return e=dt(6,e,null,t),e.lanes=n,e}function el(e,t,n){return t=dt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function O0(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Da(0),this.expirationTimes=Da(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Da(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function _c(e,t,n,r,s,i,o,a,l){return e=new O0(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=dt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ac(i),e}function D0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Ir,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function wm(e){if(!e)return Vn;e=e._reactInternals;e:{if(Er(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ye(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Ye(n))return wp(e,n,t)}return t}function km(e,t,n,r,s,i,o,a,l){return e=_c(n,r,!0,e,s,i,o,a,l),e.context=wm(null),n=e.current,r=Be(),s=Fn(n),i=Yt(r,s),i.callback=t??null,Mn(n,i,s),e.current.lanes=s,Ri(e,s,r),Xe(e,r),e}function ga(e,t,n,r){var s=t.current,i=Be(),o=Fn(s);return n=wm(n),t.context===null?t.context=n:t.pendingContext=n,t=Yt(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Mn(s,t,o),e!==null&&(Nt(e,s,o,i),po(e,s,o)),o}function Zo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function nf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function bc(e,t){nf(e,t),(e=e.alternate)&&nf(e,t)}function M0(){return null}var Sm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ec(e){this._internalRoot=e}va.prototype.render=Ec.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));ga(e,t,null,null)};va.prototype.unmount=Ec.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;vr(function(){ga(null,e,null,null)}),t[en]=null}};function va(e){this._internalRoot=e}va.prototype.unstable_scheduleHydration=function(e){if(e){var t=Jh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yn.length&&t!==0&&t<yn[n].priority;n++);yn.splice(n,0,e),n===0&&tp(e)}};function Nc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function xa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rf(){}function A0(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=Zo(o);i.call(u)}}var o=km(t,r,e,0,null,!1,!1,"",rf);return e._reactRootContainer=o,e[en]=o.current,ci(e.nodeType===8?e.parentNode:e),vr(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var u=Zo(l);a.call(u)}}var l=_c(e,0,!1,null,null,!1,!1,"",rf);return e._reactRootContainer=l,e[en]=l.current,ci(e.nodeType===8?e.parentNode:e),vr(function(){ga(t,l,n,r)}),l}function wa(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var l=Zo(o);a.call(l)}}ga(t,o,e,s)}else o=A0(n,t,e,s,r);return Zo(o)}Yh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Vs(t.pendingLanes);n!==0&&(Hu(t,n|1),Xe(t,ge()),!(J&6)&&(ys=ge()+500,Kn()))}break;case 13:vr(function(){var r=tn(e,1);if(r!==null){var s=Be();Nt(r,e,1,s)}}),bc(e,1)}};Zu=function(e){if(e.tag===13){var t=tn(e,134217728);if(t!==null){var n=Be();Nt(t,e,134217728,n)}bc(e,134217728)}};Xh=function(e){if(e.tag===13){var t=Fn(e),n=tn(e,t);if(n!==null){var r=Be();Nt(n,e,t,r)}bc(e,t)}};Jh=function(){return oe};ep=function(e,t){var n=oe;try{return oe=e,t()}finally{oe=n}};Tl=function(e,t,n){switch(t){case"input":if(kl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=ca(r);if(!s)throw Error(T(90));Ph(r),kl(r,s)}}}break;case"textarea":Oh(e,n);break;case"select":t=n.value,t!=null&&Qr(e,!!n.multiple,t,!1)}};$h=wc;Uh=vr;var F0={usingClientEntryPoint:!1,Events:[Ii,Fr,ca,Lh,zh,wc]},Ls={findFiberByHostInstance:er,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},L0={bundleType:Ls.bundleType,version:Ls.version,rendererPackageName:Ls.rendererPackageName,rendererConfig:Ls.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:sn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Qh(e),e===null?null:e.stateNode},findFiberByHostInstance:Ls.findFiberByHostInstance||M0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var to=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!to.isDisabled&&to.supportsFiber)try{oa=to.inject(L0),Lt=to}catch{}}ot.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=F0;ot.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Nc(t))throw Error(T(200));return D0(e,t,null,n)};ot.createRoot=function(e,t){if(!Nc(e))throw Error(T(299));var n=!1,r="",s=Sm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=_c(e,1,!1,null,null,n,!1,r,s),e[en]=t.current,ci(e.nodeType===8?e.parentNode:e),new Ec(t)};ot.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=Qh(t),e=e===null?null:e.stateNode,e};ot.flushSync=function(e){return vr(e)};ot.hydrate=function(e,t,n){if(!xa(t))throw Error(T(200));return wa(null,e,t,!0,n)};ot.hydrateRoot=function(e,t,n){if(!Nc(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=Sm;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=km(t,null,e,1,n??null,s,!1,i,o),e[en]=t.current,ci(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new va(t)};ot.render=function(e,t,n){if(!xa(t))throw Error(T(200));return wa(null,e,t,!1,n)};ot.unmountComponentAtNode=function(e){if(!xa(e))throw Error(T(40));return e._reactRootContainer?(vr(function(){wa(null,null,e,!1,function(){e._reactRootContainer=null,e[en]=null})}),!0):!1};ot.unstable_batchedUpdates=wc;ot.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!xa(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return wa(e,t,n,!1,r)};ot.version="18.3.1-next-f1338f8080-20240426";function Cm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Cm)}catch(e){console.error(e)}}Cm(),Ch.exports=ot;var Tc=Ch.exports;const z0=Ou(Tc);var sf=Tc;pl.createRoot=sf.createRoot,pl.hydrateRoot=sf.hydrateRoot;var Ns=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},xr=typeof window>"u"||"Deno"in globalThis;function Ue(){}function $0(e,t){return typeof e=="function"?e(t):e}function au(e){return typeof e=="number"&&e>=0&&e!==1/0}function _m(e,t){return Math.max(e+(t||0)-Date.now(),0)}function zn(e,t){return typeof e=="function"?e(t):e}function bt(e,t){return typeof e=="function"?e(t):e}function of(e,t){const{type:n="all",exact:r,fetchStatus:s,predicate:i,queryKey:o,stale:a}=e;if(o){if(r){if(t.queryHash!==jc(o,t.options))return!1}else if(!xi(t.queryKey,o))return!1}if(n!=="all"){const l=t.isActive();if(n==="active"&&!l||n==="inactive"&&l)return!1}return!(typeof a=="boolean"&&t.isStale()!==a||s&&s!==t.state.fetchStatus||i&&!i(t))}function af(e,t){const{exact:n,status:r,predicate:s,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(wr(t.options.mutationKey)!==wr(i))return!1}else if(!xi(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||s&&!s(t))}function jc(e,t){return((t==null?void 0:t.queryKeyHashFn)||wr)(e)}function wr(e){return JSON.stringify(e,(t,n)=>lu(n)?Object.keys(n).sort().reduce((r,s)=>(r[s]=n[s],r),{}):n)}function xi(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?Object.keys(t).every(n=>xi(e[n],t[n])):!1}function bm(e,t){if(e===t)return e;const n=lf(e)&&lf(t);if(n||lu(e)&&lu(t)){const r=n?e:Object.keys(e),s=r.length,i=n?t:Object.keys(t),o=i.length,a=n?[]:{},l=new Set(r);let u=0;for(let f=0;f<o;f++){const h=n?f:i[f];(!n&&l.has(h)||n)&&e[h]===void 0&&t[h]===void 0?(a[h]=void 0,u++):(a[h]=bm(e[h],t[h]),a[h]===e[h]&&e[h]!==void 0&&u++)}return s===o&&u===s?e:a}return t}function Ko(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function lf(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function lu(e){if(!uf(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!uf(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function uf(e){return Object.prototype.toString.call(e)==="[object Object]"}function U0(e){return new Promise(t=>{setTimeout(t,e)})}function uu(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?bm(e,t):t}function B0(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function V0(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var Rc=Symbol();function Em(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===Rc?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}function Nm(e,t){return typeof e=="function"?e(...t):!!e}var rr,vn,Xr,th,Q0=(th=class extends Ns{constructor(){super();z(this,rr);z(this,vn);z(this,Xr);I(this,Xr,t=>{if(!xr&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){k(this,vn)||this.setEventListener(k(this,Xr))}onUnsubscribe(){var t;this.hasListeners()||((t=k(this,vn))==null||t.call(this),I(this,vn,void 0))}setEventListener(t){var n;I(this,Xr,t),(n=k(this,vn))==null||n.call(this),I(this,vn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){k(this,rr)!==t&&(I(this,rr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof k(this,rr)=="boolean"?k(this,rr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},rr=new WeakMap,vn=new WeakMap,Xr=new WeakMap,th),Pc=new Q0,Jr,xn,es,nh,W0=(nh=class extends Ns{constructor(){super();z(this,Jr,!0);z(this,xn);z(this,es);I(this,es,t=>{if(!xr&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){k(this,xn)||this.setEventListener(k(this,es))}onUnsubscribe(){var t;this.hasListeners()||((t=k(this,xn))==null||t.call(this),I(this,xn,void 0))}setEventListener(t){var n;I(this,es,t),(n=k(this,xn))==null||n.call(this),I(this,xn,t(this.setOnline.bind(this)))}setOnline(t){k(this,Jr)!==t&&(I(this,Jr,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return k(this,Jr)}},Jr=new WeakMap,xn=new WeakMap,es=new WeakMap,nh),Go=new W0;function cu(){let e,t;const n=new Promise((s,i)=>{e=s,t=i});n.status="pending",n.catch(()=>{});function r(s){Object.assign(n,s),delete n.resolve,delete n.reject}return n.resolve=s=>{r({status:"fulfilled",value:s}),e(s)},n.reject=s=>{r({status:"rejected",reason:s}),t(s)},n}function H0(e){return Math.min(1e3*2**e,3e4)}function Tm(e){return(e??"online")==="online"?Go.isOnline():!0}var jm=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function tl(e){return e instanceof jm}function Rm(e){let t=!1,n=0,r=!1,s;const i=cu(),o=y=>{var C;r||(m(new jm(y)),(C=e.abort)==null||C.call(e))},a=()=>{t=!0},l=()=>{t=!1},u=()=>Pc.isFocused()&&(e.networkMode==="always"||Go.isOnline())&&e.canRun(),f=()=>Tm(e.networkMode)&&e.canRun(),h=y=>{var C;r||(r=!0,(C=e.onSuccess)==null||C.call(e,y),s==null||s(),i.resolve(y))},m=y=>{var C;r||(r=!0,(C=e.onError)==null||C.call(e,y),s==null||s(),i.reject(y))},w=()=>new Promise(y=>{var C;s=p=>{(r||u())&&y(p)},(C=e.onPause)==null||C.call(e)}).then(()=>{var y;s=void 0,r||(y=e.onContinue)==null||y.call(e)}),S=()=>{if(r)return;let y;const C=n===0?e.initialPromise:void 0;try{y=C??e.fn()}catch(p){y=Promise.reject(p)}Promise.resolve(y).then(h).catch(p=>{var b;if(r)return;const c=e.retry??(xr?0:3),g=e.retryDelay??H0,x=typeof g=="function"?g(n,p):g,_=c===!0||typeof c=="number"&&n<c||typeof c=="function"&&c(n,p);if(t||!_){m(p);return}n++,(b=e.onFail)==null||b.call(e,n,p),U0(x).then(()=>u()?void 0:w()).then(()=>{t?m(p):S()})})};return{promise:i,cancel:o,continue:()=>(s==null||s(),i),cancelRetry:a,continueRetry:l,canStart:f,start:()=>(f()?S():w().then(S),i)}}var Z0=e=>setTimeout(e,0);function K0(){let e=[],t=0,n=a=>{a()},r=a=>{a()},s=Z0;const i=a=>{t?e.push(a):s(()=>{n(a)})},o=()=>{const a=e;e=[],a.length&&s(()=>{r(()=>{a.forEach(l=>{n(l)})})})};return{batch:a=>{let l;t++;try{l=a()}finally{t--,t||o()}return l},batchCalls:a=>(...l)=>{i(()=>{a(...l)})},schedule:i,setNotifyFunction:a=>{n=a},setBatchNotifyFunction:a=>{r=a},setScheduler:a=>{s=a}}}var ke=K0(),sr,rh,Pm=(rh=class{constructor(){z(this,sr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),au(this.gcTime)&&I(this,sr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(xr?1/0:5*60*1e3))}clearGcTimeout(){k(this,sr)&&(clearTimeout(k(this,sr)),I(this,sr,void 0))}},sr=new WeakMap,rh),ts,ir,lt,or,Oe,bi,ar,kt,Qt,sh,G0=(sh=class extends Pm{constructor(t){super();z(this,kt);z(this,ts);z(this,ir);z(this,lt);z(this,or);z(this,Oe);z(this,bi);z(this,ar);I(this,ar,!1),I(this,bi,t.defaultOptions),this.setOptions(t.options),this.observers=[],I(this,or,t.client),I(this,lt,k(this,or).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,I(this,ts,q0(this.options)),this.state=t.state??k(this,ts),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=k(this,Oe))==null?void 0:t.promise}setOptions(t){this.options={...k(this,bi),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&k(this,lt).remove(this)}setData(t,n){const r=uu(this.state.data,t,this.options);return H(this,kt,Qt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){H(this,kt,Qt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,s;const n=(r=k(this,Oe))==null?void 0:r.promise;return(s=k(this,Oe))==null||s.cancel(t),n?n.then(Ue).catch(Ue):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(k(this,ts))}isActive(){return this.observers.some(t=>bt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===Rc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>zn(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!_m(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=k(this,Oe))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=k(this,Oe))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),k(this,lt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(k(this,Oe)&&(k(this,ar)?k(this,Oe).cancel({revert:!0}):k(this,Oe).cancelRetry()),this.scheduleGc()),k(this,lt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||H(this,kt,Qt).call(this,{type:"invalidate"})}fetch(t,n){var u,f,h;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(k(this,Oe))return k(this,Oe).continueRetry(),k(this,Oe).promise}if(t&&this.setOptions(t),!this.options.queryFn){const m=this.observers.find(w=>w.options.queryFn);m&&this.setOptions(m.options)}const r=new AbortController,s=m=>{Object.defineProperty(m,"signal",{enumerable:!0,get:()=>(I(this,ar,!0),r.signal)})},i=()=>{const m=Em(this.options,n),S=(()=>{const y={client:k(this,or),queryKey:this.queryKey,meta:this.meta};return s(y),y})();return I(this,ar,!1),this.options.persister?this.options.persister(m,S,this):m(S)},a=(()=>{const m={fetchOptions:n,options:this.options,queryKey:this.queryKey,client:k(this,or),state:this.state,fetchFn:i};return s(m),m})();(u=this.options.behavior)==null||u.onFetch(a,this),I(this,ir,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((f=a.fetchOptions)==null?void 0:f.meta))&&H(this,kt,Qt).call(this,{type:"fetch",meta:(h=a.fetchOptions)==null?void 0:h.meta});const l=m=>{var w,S,y,C;tl(m)&&m.silent||H(this,kt,Qt).call(this,{type:"error",error:m}),tl(m)||((S=(w=k(this,lt).config).onError)==null||S.call(w,m,this),(C=(y=k(this,lt).config).onSettled)==null||C.call(y,this.state.data,m,this)),this.scheduleGc()};return I(this,Oe,Rm({initialPromise:n==null?void 0:n.initialPromise,fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:m=>{var w,S,y,C;if(m===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(m)}catch(p){l(p);return}(S=(w=k(this,lt).config).onSuccess)==null||S.call(w,m,this),(C=(y=k(this,lt).config).onSettled)==null||C.call(y,m,this.state.error,this),this.scheduleGc()},onError:l,onFail:(m,w)=>{H(this,kt,Qt).call(this,{type:"failed",failureCount:m,error:w})},onPause:()=>{H(this,kt,Qt).call(this,{type:"pause"})},onContinue:()=>{H(this,kt,Qt).call(this,{type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0})),k(this,Oe).start()}},ts=new WeakMap,ir=new WeakMap,lt=new WeakMap,or=new WeakMap,Oe=new WeakMap,bi=new WeakMap,ar=new WeakMap,kt=new WeakSet,Qt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Im(r.data,this.options),fetchMeta:t.meta??null};case"success":return I(this,ir,void 0),{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const s=t.error;return tl(s)&&s.revert&&k(this,ir)?{...k(this,ir),fetchStatus:"idle"}:{...r,error:s,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:s,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),ke.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),k(this,lt).notify({query:this,type:"updated",action:t})})},sh);function Im(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Tm(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function q0(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Ot,ih,Y0=(ih=class extends Ns{constructor(t={}){super();z(this,Ot);this.config=t,I(this,Ot,new Map)}build(t,n,r){const s=n.queryKey,i=n.queryHash??jc(s,n);let o=this.get(i);return o||(o=new G0({client:t,queryKey:s,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(s)}),this.add(o)),o}add(t){k(this,Ot).has(t.queryHash)||(k(this,Ot).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=k(this,Ot).get(t.queryHash);n&&(t.destroy(),n===t&&k(this,Ot).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){ke.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return k(this,Ot).get(t)}getAll(){return[...k(this,Ot).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>of(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>of(t,r)):n}notify(t){ke.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){ke.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){ke.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Ot=new WeakMap,ih),Dt,Le,lr,Mt,dn,oh,X0=(oh=class extends Pm{constructor(t){super();z(this,Mt);z(this,Dt);z(this,Le);z(this,lr);this.mutationId=t.mutationId,I(this,Le,t.mutationCache),I(this,Dt,[]),this.state=t.state||Om(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){k(this,Dt).includes(t)||(k(this,Dt).push(t),this.clearGcTimeout(),k(this,Le).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){I(this,Dt,k(this,Dt).filter(n=>n!==t)),this.scheduleGc(),k(this,Le).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){k(this,Dt).length||(this.state.status==="pending"?this.scheduleGc():k(this,Le).remove(this))}continue(){var t;return((t=k(this,lr))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var i,o,a,l,u,f,h,m,w,S,y,C,p,c,g,x,_,b,N,E;const n=()=>{H(this,Mt,dn).call(this,{type:"continue"})};I(this,lr,Rm({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(L,R)=>{H(this,Mt,dn).call(this,{type:"failed",failureCount:L,error:R})},onPause:()=>{H(this,Mt,dn).call(this,{type:"pause"})},onContinue:n,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>k(this,Le).canRun(this)}));const r=this.state.status==="pending",s=!k(this,lr).canStart();try{if(r)n();else{H(this,Mt,dn).call(this,{type:"pending",variables:t,isPaused:s}),await((o=(i=k(this,Le).config).onMutate)==null?void 0:o.call(i,t,this));const R=await((l=(a=this.options).onMutate)==null?void 0:l.call(a,t));R!==this.state.context&&H(this,Mt,dn).call(this,{type:"pending",context:R,variables:t,isPaused:s})}const L=await k(this,lr).start();return await((f=(u=k(this,Le).config).onSuccess)==null?void 0:f.call(u,L,t,this.state.context,this)),await((m=(h=this.options).onSuccess)==null?void 0:m.call(h,L,t,this.state.context)),await((S=(w=k(this,Le).config).onSettled)==null?void 0:S.call(w,L,null,this.state.variables,this.state.context,this)),await((C=(y=this.options).onSettled)==null?void 0:C.call(y,L,null,t,this.state.context)),H(this,Mt,dn).call(this,{type:"success",data:L}),L}catch(L){try{throw await((c=(p=k(this,Le).config).onError)==null?void 0:c.call(p,L,t,this.state.context,this)),await((x=(g=this.options).onError)==null?void 0:x.call(g,L,t,this.state.context)),await((b=(_=k(this,Le).config).onSettled)==null?void 0:b.call(_,void 0,L,this.state.variables,this.state.context,this)),await((E=(N=this.options).onSettled)==null?void 0:E.call(N,void 0,L,t,this.state.context)),L}finally{H(this,Mt,dn).call(this,{type:"error",error:L})}}finally{k(this,Le).runNext(this)}}},Dt=new WeakMap,Le=new WeakMap,lr=new WeakMap,Mt=new WeakSet,dn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),ke.batch(()=>{k(this,Dt).forEach(r=>{r.onMutationUpdate(t)}),k(this,Le).notify({mutation:this,type:"updated",action:t})})},oh);function Om(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Zt,St,Ei,ah,J0=(ah=class extends Ns{constructor(t={}){super();z(this,Zt);z(this,St);z(this,Ei);this.config=t,I(this,Zt,new Set),I(this,St,new Map),I(this,Ei,0)}build(t,n,r){const s=new X0({mutationCache:this,mutationId:++Li(this,Ei)._,options:t.defaultMutationOptions(n),state:r});return this.add(s),s}add(t){k(this,Zt).add(t);const n=no(t);if(typeof n=="string"){const r=k(this,St).get(n);r?r.push(t):k(this,St).set(n,[t])}this.notify({type:"added",mutation:t})}remove(t){if(k(this,Zt).delete(t)){const n=no(t);if(typeof n=="string"){const r=k(this,St).get(n);if(r)if(r.length>1){const s=r.indexOf(t);s!==-1&&r.splice(s,1)}else r[0]===t&&k(this,St).delete(n)}}this.notify({type:"removed",mutation:t})}canRun(t){const n=no(t);if(typeof n=="string"){const r=k(this,St).get(n),s=r==null?void 0:r.find(i=>i.state.status==="pending");return!s||s===t}else return!0}runNext(t){var r;const n=no(t);if(typeof n=="string"){const s=(r=k(this,St).get(n))==null?void 0:r.find(i=>i!==t&&i.state.isPaused);return(s==null?void 0:s.continue())??Promise.resolve()}else return Promise.resolve()}clear(){ke.batch(()=>{k(this,Zt).forEach(t=>{this.notify({type:"removed",mutation:t})}),k(this,Zt).clear(),k(this,St).clear()})}getAll(){return Array.from(k(this,Zt))}find(t){const n={exact:!0,...t};return this.getAll().find(r=>af(n,r))}findAll(t={}){return this.getAll().filter(n=>af(t,n))}notify(t){ke.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return ke.batch(()=>Promise.all(t.map(n=>n.continue().catch(Ue))))}},Zt=new WeakMap,St=new WeakMap,Ei=new WeakMap,ah);function no(e){var t;return(t=e.options.scope)==null?void 0:t.id}function cf(e){return{onFetch:(t,n)=>{var f,h,m,w,S;const r=t.options,s=(m=(h=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:h.fetchMore)==null?void 0:m.direction,i=((w=t.state.data)==null?void 0:w.pages)||[],o=((S=t.state.data)==null?void 0:S.pageParams)||[];let a={pages:[],pageParams:[]},l=0;const u=async()=>{let y=!1;const C=g=>{Object.defineProperty(g,"signal",{enumerable:!0,get:()=>(t.signal.aborted?y=!0:t.signal.addEventListener("abort",()=>{y=!0}),t.signal)})},p=Em(t.options,t.fetchOptions),c=async(g,x,_)=>{if(y)return Promise.reject();if(x==null&&g.pages.length)return Promise.resolve(g);const N=(()=>{const re={client:t.client,queryKey:t.queryKey,pageParam:x,direction:_?"backward":"forward",meta:t.options.meta};return C(re),re})(),E=await p(N),{maxPages:L}=t.options,R=_?V0:B0;return{pages:R(g.pages,E,L),pageParams:R(g.pageParams,x,L)}};if(s&&i.length){const g=s==="backward",x=g?ex:df,_={pages:i,pageParams:o},b=x(r,_);a=await c(_,b,g)}else{const g=e??i.length;do{const x=l===0?o[0]??r.initialPageParam:df(r,a);if(l>0&&x==null)break;a=await c(a,x),l++}while(l<g)}return a};t.options.persister?t.fetchFn=()=>{var y,C;return(C=(y=t.options).persister)==null?void 0:C.call(y,u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function df(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function ex(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var me,wn,kn,ns,rs,Sn,ss,is,lh,tx=(lh=class{constructor(e={}){z(this,me);z(this,wn);z(this,kn);z(this,ns);z(this,rs);z(this,Sn);z(this,ss);z(this,is);I(this,me,e.queryCache||new Y0),I(this,wn,e.mutationCache||new J0),I(this,kn,e.defaultOptions||{}),I(this,ns,new Map),I(this,rs,new Map),I(this,Sn,0)}mount(){Li(this,Sn)._++,k(this,Sn)===1&&(I(this,ss,Pc.subscribe(async e=>{e&&(await this.resumePausedMutations(),k(this,me).onFocus())})),I(this,is,Go.subscribe(async e=>{e&&(await this.resumePausedMutations(),k(this,me).onOnline())})))}unmount(){var e,t;Li(this,Sn)._--,k(this,Sn)===0&&((e=k(this,ss))==null||e.call(this),I(this,ss,void 0),(t=k(this,is))==null||t.call(this),I(this,is,void 0))}isFetching(e){return k(this,me).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return k(this,wn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=k(this,me).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),n=k(this,me).build(this,t),r=n.state.data;return r===void 0?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(zn(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return k(this,me).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),s=k(this,me).get(r.queryHash),i=s==null?void 0:s.state.data,o=$0(t,i);if(o!==void 0)return k(this,me).build(this,r).setData(o,{...n,manual:!0})}setQueriesData(e,t,n){return ke.batch(()=>k(this,me).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=k(this,me).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=k(this,me);ke.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=k(this,me);return ke.batch(()=>(n.findAll(e).forEach(r=>{r.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){const n={revert:!0,...t},r=ke.batch(()=>k(this,me).findAll(e).map(s=>s.cancel(n)));return Promise.all(r).then(Ue).catch(Ue)}invalidateQueries(e,t={}){return ke.batch(()=>(k(this,me).findAll(e).forEach(n=>{n.invalidate()}),(e==null?void 0:e.refetchType)==="none"?Promise.resolve():this.refetchQueries({...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"},t)))}refetchQueries(e,t={}){const n={...t,cancelRefetch:t.cancelRefetch??!0},r=ke.batch(()=>k(this,me).findAll(e).filter(s=>!s.isDisabled()&&!s.isStatic()).map(s=>{let i=s.fetch(void 0,n);return n.throwOnError||(i=i.catch(Ue)),s.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(Ue)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=k(this,me).build(this,t);return n.isStaleByTime(zn(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(Ue).catch(Ue)}fetchInfiniteQuery(e){return e.behavior=cf(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(Ue).catch(Ue)}ensureInfiniteQueryData(e){return e.behavior=cf(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Go.isOnline()?k(this,wn).resumePausedMutations():Promise.resolve()}getQueryCache(){return k(this,me)}getMutationCache(){return k(this,wn)}getDefaultOptions(){return k(this,kn)}setDefaultOptions(e){I(this,kn,e)}setQueryDefaults(e,t){k(this,ns).set(wr(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...k(this,ns).values()],n={};return t.forEach(r=>{xi(e,r.queryKey)&&Object.assign(n,r.defaultOptions)}),n}setMutationDefaults(e,t){k(this,rs).set(wr(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...k(this,rs).values()],n={};return t.forEach(r=>{xi(e,r.mutationKey)&&Object.assign(n,r.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...k(this,kn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=jc(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===Rc&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...k(this,kn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){k(this,me).clear(),k(this,wn).clear()}},me=new WeakMap,wn=new WeakMap,kn=new WeakMap,ns=new WeakMap,rs=new WeakMap,Sn=new WeakMap,ss=new WeakMap,is=new WeakMap,lh),We,q,Ni,ze,ur,os,Cn,_n,Ti,as,ls,cr,dr,bn,us,se,Ws,du,fu,hu,pu,mu,yu,gu,Dm,uh,nx=(uh=class extends Ns{constructor(t,n){super();z(this,se);z(this,We);z(this,q);z(this,Ni);z(this,ze);z(this,ur);z(this,os);z(this,Cn);z(this,_n);z(this,Ti);z(this,as);z(this,ls);z(this,cr);z(this,dr);z(this,bn);z(this,us,new Set);this.options=n,I(this,We,t),I(this,_n,null),I(this,Cn,cu()),this.options.experimental_prefetchInRender||k(this,Cn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(k(this,q).addObserver(this),ff(k(this,q),this.options)?H(this,se,Ws).call(this):this.updateResult(),H(this,se,pu).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return vu(k(this,q),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return vu(k(this,q),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,H(this,se,mu).call(this),H(this,se,yu).call(this),k(this,q).removeObserver(this)}setOptions(t){const n=this.options,r=k(this,q);if(this.options=k(this,We).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof bt(this.options.enabled,k(this,q))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");H(this,se,gu).call(this),k(this,q).setOptions(this.options),n._defaulted&&!Ko(this.options,n)&&k(this,We).getQueryCache().notify({type:"observerOptionsUpdated",query:k(this,q),observer:this});const s=this.hasListeners();s&&hf(k(this,q),r,this.options,n)&&H(this,se,Ws).call(this),this.updateResult(),s&&(k(this,q)!==r||bt(this.options.enabled,k(this,q))!==bt(n.enabled,k(this,q))||zn(this.options.staleTime,k(this,q))!==zn(n.staleTime,k(this,q)))&&H(this,se,du).call(this);const i=H(this,se,fu).call(this);s&&(k(this,q)!==r||bt(this.options.enabled,k(this,q))!==bt(n.enabled,k(this,q))||i!==k(this,bn))&&H(this,se,hu).call(this,i)}getOptimisticResult(t){const n=k(this,We).getQueryCache().build(k(this,We),t),r=this.createResult(n,t);return sx(this,r)&&(I(this,ze,r),I(this,os,this.options),I(this,ur,k(this,q).state)),r}getCurrentResult(){return k(this,ze)}trackResult(t,n){return new Proxy(t,{get:(r,s)=>(this.trackProp(s),n==null||n(s),Reflect.get(r,s))})}trackProp(t){k(this,us).add(t)}getCurrentQuery(){return k(this,q)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=k(this,We).defaultQueryOptions(t),r=k(this,We).getQueryCache().build(k(this,We),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){return H(this,se,Ws).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),k(this,ze)))}createResult(t,n){var L;const r=k(this,q),s=this.options,i=k(this,ze),o=k(this,ur),a=k(this,os),u=t!==r?t.state:k(this,Ni),{state:f}=t;let h={...f},m=!1,w;if(n._optimisticResults){const R=this.hasListeners(),re=!R&&ff(t,n),Q=R&&hf(t,r,n,s);(re||Q)&&(h={...h,...Im(f.data,t.options)}),n._optimisticResults==="isRestoring"&&(h.fetchStatus="idle")}let{error:S,errorUpdatedAt:y,status:C}=h;w=h.data;let p=!1;if(n.placeholderData!==void 0&&w===void 0&&C==="pending"){let R;i!=null&&i.isPlaceholderData&&n.placeholderData===(a==null?void 0:a.placeholderData)?(R=i.data,p=!0):R=typeof n.placeholderData=="function"?n.placeholderData((L=k(this,ls))==null?void 0:L.state.data,k(this,ls)):n.placeholderData,R!==void 0&&(C="success",w=uu(i==null?void 0:i.data,R,n),m=!0)}if(n.select&&w!==void 0&&!p)if(i&&w===(o==null?void 0:o.data)&&n.select===k(this,Ti))w=k(this,as);else try{I(this,Ti,n.select),w=n.select(w),w=uu(i==null?void 0:i.data,w,n),I(this,as,w),I(this,_n,null)}catch(R){I(this,_n,R)}k(this,_n)&&(S=k(this,_n),w=k(this,as),y=Date.now(),C="error");const c=h.fetchStatus==="fetching",g=C==="pending",x=C==="error",_=g&&c,b=w!==void 0,E={status:C,fetchStatus:h.fetchStatus,isPending:g,isSuccess:C==="success",isError:x,isInitialLoading:_,isLoading:_,data:w,dataUpdatedAt:h.dataUpdatedAt,error:S,errorUpdatedAt:y,failureCount:h.fetchFailureCount,failureReason:h.fetchFailureReason,errorUpdateCount:h.errorUpdateCount,isFetched:h.dataUpdateCount>0||h.errorUpdateCount>0,isFetchedAfterMount:h.dataUpdateCount>u.dataUpdateCount||h.errorUpdateCount>u.errorUpdateCount,isFetching:c,isRefetching:c&&!g,isLoadingError:x&&!b,isPaused:h.fetchStatus==="paused",isPlaceholderData:m,isRefetchError:x&&b,isStale:Ic(t,n),refetch:this.refetch,promise:k(this,Cn)};if(this.options.experimental_prefetchInRender){const R=Ee=>{E.status==="error"?Ee.reject(E.error):E.data!==void 0&&Ee.resolve(E.data)},re=()=>{const Ee=I(this,Cn,E.promise=cu());R(Ee)},Q=k(this,Cn);switch(Q.status){case"pending":t.queryHash===r.queryHash&&R(Q);break;case"fulfilled":(E.status==="error"||E.data!==Q.value)&&re();break;case"rejected":(E.status!=="error"||E.error!==Q.reason)&&re();break}}return E}updateResult(){const t=k(this,ze),n=this.createResult(k(this,q),this.options);if(I(this,ur,k(this,q).state),I(this,os,this.options),k(this,ur).data!==void 0&&I(this,ls,k(this,q)),Ko(n,t))return;I(this,ze,n);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:s}=this.options,i=typeof s=="function"?s():s;if(i==="all"||!i&&!k(this,us).size)return!0;const o=new Set(i??k(this,us));return this.options.throwOnError&&o.add("error"),Object.keys(k(this,ze)).some(a=>{const l=a;return k(this,ze)[l]!==t[l]&&o.has(l)})};H(this,se,Dm).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&H(this,se,pu).call(this)}},We=new WeakMap,q=new WeakMap,Ni=new WeakMap,ze=new WeakMap,ur=new WeakMap,os=new WeakMap,Cn=new WeakMap,_n=new WeakMap,Ti=new WeakMap,as=new WeakMap,ls=new WeakMap,cr=new WeakMap,dr=new WeakMap,bn=new WeakMap,us=new WeakMap,se=new WeakSet,Ws=function(t){H(this,se,gu).call(this);let n=k(this,q).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(Ue)),n},du=function(){H(this,se,mu).call(this);const t=zn(this.options.staleTime,k(this,q));if(xr||k(this,ze).isStale||!au(t))return;const r=_m(k(this,ze).dataUpdatedAt,t)+1;I(this,cr,setTimeout(()=>{k(this,ze).isStale||this.updateResult()},r))},fu=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(k(this,q)):this.options.refetchInterval)??!1},hu=function(t){H(this,se,yu).call(this),I(this,bn,t),!(xr||bt(this.options.enabled,k(this,q))===!1||!au(k(this,bn))||k(this,bn)===0)&&I(this,dr,setInterval(()=>{(this.options.refetchIntervalInBackground||Pc.isFocused())&&H(this,se,Ws).call(this)},k(this,bn)))},pu=function(){H(this,se,du).call(this),H(this,se,hu).call(this,H(this,se,fu).call(this))},mu=function(){k(this,cr)&&(clearTimeout(k(this,cr)),I(this,cr,void 0))},yu=function(){k(this,dr)&&(clearInterval(k(this,dr)),I(this,dr,void 0))},gu=function(){const t=k(this,We).getQueryCache().build(k(this,We),this.options);if(t===k(this,q))return;const n=k(this,q);I(this,q,t),I(this,Ni,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},Dm=function(t){ke.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(k(this,ze))}),k(this,We).getQueryCache().notify({query:k(this,q),type:"observerResultsUpdated"})})},uh);function rx(e,t){return bt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function ff(e,t){return rx(e,t)||e.state.data!==void 0&&vu(e,t,t.refetchOnMount)}function vu(e,t,n){if(bt(t.enabled,e)!==!1&&zn(t.staleTime,e)!=="static"){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&Ic(e,t)}return!1}function hf(e,t,n,r){return(e!==t||bt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&Ic(e,n)}function Ic(e,t){return bt(t.enabled,e)!==!1&&e.isStaleByTime(zn(t.staleTime,e))}function sx(e,t){return!Ko(e.getCurrentResult(),t)}var En,Nn,He,Kt,Xt,ko,xu,ch,ix=(ch=class extends Ns{constructor(n,r){super();z(this,Xt);z(this,En);z(this,Nn);z(this,He);z(this,Kt);I(this,En,n),this.setOptions(r),this.bindMethods(),H(this,Xt,ko).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(n){var s;const r=this.options;this.options=k(this,En).defaultMutationOptions(n),Ko(this.options,r)||k(this,En).getMutationCache().notify({type:"observerOptionsUpdated",mutation:k(this,He),observer:this}),r!=null&&r.mutationKey&&this.options.mutationKey&&wr(r.mutationKey)!==wr(this.options.mutationKey)?this.reset():((s=k(this,He))==null?void 0:s.state.status)==="pending"&&k(this,He).setOptions(this.options)}onUnsubscribe(){var n;this.hasListeners()||(n=k(this,He))==null||n.removeObserver(this)}onMutationUpdate(n){H(this,Xt,ko).call(this),H(this,Xt,xu).call(this,n)}getCurrentResult(){return k(this,Nn)}reset(){var n;(n=k(this,He))==null||n.removeObserver(this),I(this,He,void 0),H(this,Xt,ko).call(this),H(this,Xt,xu).call(this)}mutate(n,r){var s;return I(this,Kt,r),(s=k(this,He))==null||s.removeObserver(this),I(this,He,k(this,En).getMutationCache().build(k(this,En),this.options)),k(this,He).addObserver(this),k(this,He).execute(n)}},En=new WeakMap,Nn=new WeakMap,He=new WeakMap,Kt=new WeakMap,Xt=new WeakSet,ko=function(){var r;const n=((r=k(this,He))==null?void 0:r.state)??Om();I(this,Nn,{...n,isPending:n.status==="pending",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset})},xu=function(n){ke.batch(()=>{var r,s,i,o,a,l,u,f;if(k(this,Kt)&&this.hasListeners()){const h=k(this,Nn).variables,m=k(this,Nn).context;(n==null?void 0:n.type)==="success"?((s=(r=k(this,Kt)).onSuccess)==null||s.call(r,n.data,h,m),(o=(i=k(this,Kt)).onSettled)==null||o.call(i,n.data,null,h,m)):(n==null?void 0:n.type)==="error"&&((l=(a=k(this,Kt)).onError)==null||l.call(a,n.error,h,m),(f=(u=k(this,Kt)).onSettled)==null||f.call(u,void 0,n.error,h,m))}this.listeners.forEach(h=>{h(k(this,Nn))})})},ch),Mm=v.createContext(void 0),Ts=e=>{const t=v.useContext(Mm);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},ox=({client:e,children:t})=>(v.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),d.jsx(Mm.Provider,{value:e,children:t})),Am=v.createContext(!1),ax=()=>v.useContext(Am);Am.Provider;function lx(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var ux=v.createContext(lx()),cx=()=>v.useContext(ux),dx=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},fx=e=>{v.useEffect(()=>{e.clearReset()},[e])},hx=({result:e,errorResetBoundary:t,throwOnError:n,query:r,suspense:s})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(s&&e.data===void 0||Nm(n,[e.error,r])),px=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),n=e.staleTime;e.staleTime=typeof n=="function"?(...r)=>t(n(...r)):t(n),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},mx=(e,t)=>e.isLoading&&e.isFetching&&!t,yx=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,pf=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function gx(e,t,n){var h,m,w,S,y;const r=ax(),s=cx(),i=Ts(),o=i.defaultQueryOptions(e);(m=(h=i.getDefaultOptions().queries)==null?void 0:h._experimental_beforeQuery)==null||m.call(h,o),o._optimisticResults=r?"isRestoring":"optimistic",px(o),dx(o,s),fx(s);const a=!i.getQueryCache().get(o.queryHash),[l]=v.useState(()=>new t(i,o)),u=l.getOptimisticResult(o),f=!r&&e.subscribed!==!1;if(v.useSyncExternalStore(v.useCallback(C=>{const p=f?l.subscribe(ke.batchCalls(C)):Ue;return l.updateResult(),p},[l,f]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),v.useEffect(()=>{l.setOptions(o)},[o,l]),yx(o,u))throw pf(o,l,s);if(hx({result:u,errorResetBoundary:s,throwOnError:o.throwOnError,query:i.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw u.error;if((S=(w=i.getDefaultOptions().queries)==null?void 0:w._experimental_afterQuery)==null||S.call(w,o,u),o.experimental_prefetchInRender&&!xr&&mx(u,r)){const C=a?pf(o,l,s):(y=i.getQueryCache().get(o.queryHash))==null?void 0:y.promise;C==null||C.catch(Ue).finally(()=>{l.updateResult()})}return o.notifyOnChangeProps?u:l.trackResult(u)}function Fm(e,t){return gx(e,nx)}function ka(e,t){const n=Ts(),[r]=v.useState(()=>new ix(n,e));v.useEffect(()=>{r.setOptions(e)},[r,e]);const s=v.useSyncExternalStore(v.useCallback(o=>r.subscribe(ke.batchCalls(o)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),i=v.useCallback((o,a)=>{r.mutate(o,a).catch(Ue)},[r]);if(s.error&&Nm(r.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:i,mutateAsync:s.mutate}}var vx=function(){return null};const xx=new tx({defaultOptions:{queries:{staleTime:5*60*1e3,gcTime:10*60*1e3,retry:2,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}),Je={tasks:["tasks"],taskStats:["tasks","stats"]};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var wx={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kx=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),ne=(e,t)=>{const n=v.forwardRef(({color:r="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:a="",children:l,...u},f)=>v.createElement("svg",{ref:f,...wx,width:s,height:s,stroke:r,strokeWidth:o?Number(i)*24/Number(s):i,className:["lucide",`lucide-${kx(e)}`,a].join(" "),...u},[...t.map(([h,m])=>v.createElement(h,m)),...Array.isArray(l)?l:[l]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lm=ne("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zm=ne("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sx=ne("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oc=ne("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mf=ne("CheckCircle2",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $m=ne("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wu=ne("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cx=ne("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _x=ne("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bx=ne("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ku=ne("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Um=ne("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ex=ne("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nx=ne("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tx=ne("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bm=ne("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=ne("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=ne("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Px=ne("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ix=ne("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ox=ne("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qo=ne("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yf=ne("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dx=ne("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mx=ne("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ax=ne("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fx=ne("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dc=ne("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gf=ne("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wi=ne("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Su=ne("Undo2",[["path",{d:"M9 14 4 9l5-5",key:"102s5s"}],["path",{d:"M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5v0a5.5 5.5 0 0 1-5.5 5.5H11",key:"llx8ln"}]]);/**
 * @license lucide-react v0.303.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=ne("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function vf(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Vm(...e){return t=>{let n=!1;const r=e.map(s=>{const i=vf(s,t);return!n&&typeof i=="function"&&(n=!0),i});if(n)return()=>{for(let s=0;s<r.length;s++){const i=r[s];typeof i=="function"?i():vf(e[s],null)}}}}function on(...e){return v.useCallback(Vm(...e),e)}function Mc(e){const t=zx(e),n=v.forwardRef((r,s)=>{const{children:i,...o}=r,a=v.Children.toArray(i),l=a.find(Ux);if(l){const u=l.props.children,f=a.map(h=>h===l?v.Children.count(u)>1?v.Children.only(null):v.isValidElement(u)?u.props.children:null:h);return d.jsx(t,{...o,ref:s,children:v.isValidElement(u)?v.cloneElement(u,void 0,f):null})}return d.jsx(t,{...o,ref:s,children:i})});return n.displayName=`${e}.Slot`,n}var Lx=Mc("Slot");function zx(e){const t=v.forwardRef((n,r)=>{const{children:s,...i}=n;if(v.isValidElement(s)){const o=Vx(s),a=Bx(i,s.props);return s.type!==v.Fragment&&(a.ref=r?Vm(r,o):o),v.cloneElement(s,a)}return v.Children.count(s)>1?v.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var $x=Symbol("radix.slottable");function Ux(e){return v.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===$x}function Bx(e,t){const n={...t};for(const r in t){const s=e[r],i=t[r];/^on[A-Z]/.test(r)?s&&i?n[r]=(...a)=>{const l=i(...a);return s(...a),l}:s&&(n[r]=s):r==="style"?n[r]={...s,...i}:r==="className"&&(n[r]=[s,i].filter(Boolean).join(" "))}return{...e,...n}}function Vx(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Qm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(n=Qm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Wm(){for(var e,t,n=0,r="",s=arguments.length;n<s;n++)(e=arguments[n])&&(t=Qm(e))&&(r&&(r+=" "),r+=t);return r}const xf=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,wf=Wm,Qx=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return wf(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:s,defaultVariants:i}=t,o=Object.keys(s).map(u=>{const f=n==null?void 0:n[u],h=i==null?void 0:i[u];if(f===null)return null;const m=xf(f)||xf(h);return s[u][m]}),a=n&&Object.entries(n).reduce((u,f)=>{let[h,m]=f;return m===void 0||(u[h]=m),u},{}),l=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:h,className:m,...w}=f;return Object.entries(w).every(S=>{let[y,C]=S;return Array.isArray(C)?C.includes({...i,...a}[y]):{...i,...a}[y]===C})?[...u,h,m]:u},[]);return wf(e,o,l,n==null?void 0:n.class,n==null?void 0:n.className)},Ac="-",Wx=e=>{const t=Zx(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:o=>{const a=o.split(Ac);return a[0]===""&&a.length!==1&&a.shift(),Hm(a,t)||Hx(o)},getConflictingClassGroupIds:(o,a)=>{const l=n[o]||[];return a&&r[o]?[...l,...r[o]]:l}}},Hm=(e,t)=>{var o;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),s=r?Hm(e.slice(1),r):void 0;if(s)return s;if(t.validators.length===0)return;const i=e.join(Ac);return(o=t.validators.find(({validator:a})=>a(i)))==null?void 0:o.classGroupId},kf=/^\[(.+)\]$/,Hx=e=>{if(kf.test(e)){const t=kf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},Zx=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return Gx(Object.entries(e.classGroups),n).forEach(([i,o])=>{Cu(o,r,i,t)}),r},Cu=(e,t,n,r)=>{e.forEach(s=>{if(typeof s=="string"){const i=s===""?t:Sf(t,s);i.classGroupId=n;return}if(typeof s=="function"){if(Kx(s)){Cu(s(r),t,n,r);return}t.validators.push({validator:s,classGroupId:n});return}Object.entries(s).forEach(([i,o])=>{Cu(o,Sf(t,i),n,r)})})},Sf=(e,t)=>{let n=e;return t.split(Ac).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},Kx=e=>e.isThemeGetter,Gx=(e,t)=>t?e.map(([n,r])=>{const s=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([o,a])=>[t+o,a])):i);return[n,s]}):e,qx=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const s=(i,o)=>{n.set(i,o),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let o=n.get(i);if(o!==void 0)return o;if((o=r.get(i))!==void 0)return s(i,o),o},set(i,o){n.has(i)?n.set(i,o):s(i,o)}}},Zm="!",Yx=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,s=t[0],i=t.length,o=a=>{const l=[];let u=0,f=0,h;for(let C=0;C<a.length;C++){let p=a[C];if(u===0){if(p===s&&(r||a.slice(C,C+i)===t)){l.push(a.slice(f,C)),f=C+i;continue}if(p==="/"){h=C;continue}}p==="["?u++:p==="]"&&u--}const m=l.length===0?a:a.substring(f),w=m.startsWith(Zm),S=w?m.substring(1):m,y=h&&h>f?h-f:void 0;return{modifiers:l,hasImportantModifier:w,baseClassName:S,maybePostfixModifierPosition:y}};return n?a=>n({className:a,parseClassName:o}):o},Xx=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},Jx=e=>({cache:qx(e.cacheSize),parseClassName:Yx(e),...Wx(e)}),e1=/\s+/,t1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:s}=t,i=[],o=e.trim().split(e1);let a="";for(let l=o.length-1;l>=0;l-=1){const u=o[l],{modifiers:f,hasImportantModifier:h,baseClassName:m,maybePostfixModifierPosition:w}=n(u);let S=!!w,y=r(S?m.substring(0,w):m);if(!y){if(!S){a=u+(a.length>0?" "+a:a);continue}if(y=r(m),!y){a=u+(a.length>0?" "+a:a);continue}S=!1}const C=Xx(f).join(":"),p=h?C+Zm:C,c=p+y;if(i.includes(c))continue;i.push(c);const g=s(y,S);for(let x=0;x<g.length;++x){const _=g[x];i.push(p+_)}a=u+(a.length>0?" "+a:a)}return a};function n1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=Km(t))&&(r&&(r+=" "),r+=n);return r}const Km=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Km(e[r]))&&(n&&(n+=" "),n+=t);return n};function r1(e,...t){let n,r,s,i=o;function o(l){const u=t.reduce((f,h)=>h(f),e());return n=Jx(u),r=n.cache.get,s=n.cache.set,i=a,a(l)}function a(l){const u=r(l);if(u)return u;const f=t1(l,n);return s(l,f),f}return function(){return i(n1.apply(null,arguments))}}const le=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Gm=/^\[(?:([a-z-]+):)?(.+)\]$/i,s1=/^\d+\/\d+$/,i1=new Set(["px","full","screen"]),o1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,a1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,l1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,u1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,c1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Vt=e=>qr(e)||i1.has(e)||s1.test(e),un=e=>js(e,"length",v1),qr=e=>!!e&&!Number.isNaN(Number(e)),nl=e=>js(e,"number",qr),zs=e=>!!e&&Number.isInteger(Number(e)),d1=e=>e.endsWith("%")&&qr(e.slice(0,-1)),W=e=>Gm.test(e),cn=e=>o1.test(e),f1=new Set(["length","size","percentage"]),h1=e=>js(e,f1,qm),p1=e=>js(e,"position",qm),m1=new Set(["image","url"]),y1=e=>js(e,m1,w1),g1=e=>js(e,"",x1),$s=()=>!0,js=(e,t,n)=>{const r=Gm.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},v1=e=>a1.test(e)&&!l1.test(e),qm=()=>!1,x1=e=>u1.test(e),w1=e=>c1.test(e),k1=()=>{const e=le("colors"),t=le("spacing"),n=le("blur"),r=le("brightness"),s=le("borderColor"),i=le("borderRadius"),o=le("borderSpacing"),a=le("borderWidth"),l=le("contrast"),u=le("grayscale"),f=le("hueRotate"),h=le("invert"),m=le("gap"),w=le("gradientColorStops"),S=le("gradientColorStopPositions"),y=le("inset"),C=le("margin"),p=le("opacity"),c=le("padding"),g=le("saturate"),x=le("scale"),_=le("sepia"),b=le("skew"),N=le("space"),E=le("translate"),L=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],re=()=>["auto",W,t],Q=()=>[W,t],Ee=()=>["",Vt,un],gt=()=>["auto",qr,W],Gn=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Rt=()=>["solid","dashed","dotted","double","none"],an=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["start","end","center","between","around","evenly","stretch"],D=()=>["","0",W],U=()=>["auto","avoid","all","avoid-page","page","left","right","column"],G=()=>[qr,W];return{cacheSize:500,separator:":",theme:{colors:[$s],spacing:[Vt,un],blur:["none","",cn,W],brightness:G(),borderColor:[e],borderRadius:["none","","full",cn,W],borderSpacing:Q(),borderWidth:Ee(),contrast:G(),grayscale:D(),hueRotate:G(),invert:D(),gap:Q(),gradientColorStops:[e],gradientColorStopPositions:[d1,un],inset:re(),margin:re(),opacity:G(),padding:Q(),saturate:G(),scale:G(),sepia:D(),skew:G(),space:Q(),translate:Q()},classGroups:{aspect:[{aspect:["auto","square","video",W]}],container:["container"],columns:[{columns:[cn]}],"break-after":[{"break-after":U()}],"break-before":[{"break-before":U()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Gn(),W]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:L()}],"overscroll-x":[{"overscroll-x":L()}],"overscroll-y":[{"overscroll-y":L()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[y]}],"inset-x":[{"inset-x":[y]}],"inset-y":[{"inset-y":[y]}],start:[{start:[y]}],end:[{end:[y]}],top:[{top:[y]}],right:[{right:[y]}],bottom:[{bottom:[y]}],left:[{left:[y]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",zs,W]}],basis:[{basis:re()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",W]}],grow:[{grow:D()}],shrink:[{shrink:D()}],order:[{order:["first","last","none",zs,W]}],"grid-cols":[{"grid-cols":[$s]}],"col-start-end":[{col:["auto",{span:["full",zs,W]},W]}],"col-start":[{"col-start":gt()}],"col-end":[{"col-end":gt()}],"grid-rows":[{"grid-rows":[$s]}],"row-start-end":[{row:["auto",{span:[zs,W]},W]}],"row-start":[{"row-start":gt()}],"row-end":[{"row-end":gt()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",W]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",W]}],gap:[{gap:[m]}],"gap-x":[{"gap-x":[m]}],"gap-y":[{"gap-y":[m]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[c]}],px:[{px:[c]}],py:[{py:[c]}],ps:[{ps:[c]}],pe:[{pe:[c]}],pt:[{pt:[c]}],pr:[{pr:[c]}],pb:[{pb:[c]}],pl:[{pl:[c]}],m:[{m:[C]}],mx:[{mx:[C]}],my:[{my:[C]}],ms:[{ms:[C]}],me:[{me:[C]}],mt:[{mt:[C]}],mr:[{mr:[C]}],mb:[{mb:[C]}],ml:[{ml:[C]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",W,t]}],"min-w":[{"min-w":[W,t,"min","max","fit"]}],"max-w":[{"max-w":[W,t,"none","full","min","max","fit","prose",{screen:[cn]},cn]}],h:[{h:[W,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[W,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[W,t,"auto","min","max","fit"]}],"font-size":[{text:["base",cn,un]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",nl]}],"font-family":[{font:[$s]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",W]}],"line-clamp":[{"line-clamp":["none",qr,nl]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Vt,W]}],"list-image":[{"list-image":["none",W]}],"list-style-type":[{list:["none","disc","decimal",W]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[p]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[p]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Rt(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Vt,un]}],"underline-offset":[{"underline-offset":["auto",Vt,W]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:Q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",W]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",W]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[p]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Gn(),p1]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",h1]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},y1]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[S]}],"gradient-via-pos":[{via:[S]}],"gradient-to-pos":[{to:[S]}],"gradient-from":[{from:[w]}],"gradient-via":[{via:[w]}],"gradient-to":[{to:[w]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[p]}],"border-style":[{border:[...Rt(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[p]}],"divide-style":[{divide:Rt()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...Rt()]}],"outline-offset":[{"outline-offset":[Vt,W]}],"outline-w":[{outline:[Vt,un]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:Ee()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[p]}],"ring-offset-w":[{"ring-offset":[Vt,un]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",cn,g1]}],"shadow-color":[{shadow:[$s]}],opacity:[{opacity:[p]}],"mix-blend":[{"mix-blend":[...an(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":an()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",cn,W]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[h]}],saturate:[{saturate:[g]}],sepia:[{sepia:[_]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[h]}],"backdrop-opacity":[{"backdrop-opacity":[p]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[_]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",W]}],duration:[{duration:G()}],ease:[{ease:["linear","in","out","in-out",W]}],delay:[{delay:G()}],animate:[{animate:["none","spin","ping","pulse","bounce",W]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[zs,W]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[b]}],"skew-y":[{"skew-y":[b]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",W]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",W]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":Q()}],"scroll-mx":[{"scroll-mx":Q()}],"scroll-my":[{"scroll-my":Q()}],"scroll-ms":[{"scroll-ms":Q()}],"scroll-me":[{"scroll-me":Q()}],"scroll-mt":[{"scroll-mt":Q()}],"scroll-mr":[{"scroll-mr":Q()}],"scroll-mb":[{"scroll-mb":Q()}],"scroll-ml":[{"scroll-ml":Q()}],"scroll-p":[{"scroll-p":Q()}],"scroll-px":[{"scroll-px":Q()}],"scroll-py":[{"scroll-py":Q()}],"scroll-ps":[{"scroll-ps":Q()}],"scroll-pe":[{"scroll-pe":Q()}],"scroll-pt":[{"scroll-pt":Q()}],"scroll-pr":[{"scroll-pr":Q()}],"scroll-pb":[{"scroll-pb":Q()}],"scroll-pl":[{"scroll-pl":Q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",W]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Vt,un,nl]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},S1=r1(k1);function Y(...e){return S1(Wm(e))}function C1(e){const t=new Date(e),r=e-new Date().getTime(),s=Math.ceil(r/(1e3*60*60*24));return s<0?`逾期 ${Math.abs(s)} 天`:s===0?"今天到期":s===1?"明天到期":s<7?`${s}天后到期`:t.toLocaleDateString("zh-CN",{month:"short",day:"numeric"})}function _1(e){return e?e<Date.now():!1}const b1=Qx("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),X=v.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...s},i)=>{const o=r?Lx:"button";return d.jsx(o,{className:Y(b1({variant:t,size:n,className:e})),ref:i,...s})});X.displayName="Button";const Fc=v.forwardRef(({className:e,type:t,...n},r)=>d.jsx("input",{type:t,className:Y("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...n}));Fc.displayName="Input";var te;(function(e){e.assertEqual=s=>{};function t(s){}e.assertIs=t;function n(s){throw new Error}e.assertNever=n,e.arrayToEnum=s=>{const i={};for(const o of s)i[o]=o;return i},e.getValidEnumValues=s=>{const i=e.objectKeys(s).filter(a=>typeof s[s[a]]!="number"),o={};for(const a of i)o[a]=s[a];return e.objectValues(o)},e.objectValues=s=>e.objectKeys(s).map(function(i){return s[i]}),e.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{const i=[];for(const o in s)Object.prototype.hasOwnProperty.call(s,o)&&i.push(o);return i},e.find=(s,i)=>{for(const o of s)if(i(o))return o},e.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&Number.isFinite(s)&&Math.floor(s)===s;function r(s,i=" | "){return s.map(o=>typeof o=="string"?`'${o}'`:o).join(i)}e.joinValues=r,e.jsonStringifyReplacer=(s,i)=>typeof i=="bigint"?i.toString():i})(te||(te={}));var Cf;(function(e){e.mergeShapes=(t,n)=>({...t,...n})})(Cf||(Cf={}));const A=te.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),mn=e=>{switch(typeof e){case"undefined":return A.undefined;case"string":return A.string;case"number":return Number.isNaN(e)?A.nan:A.number;case"boolean":return A.boolean;case"function":return A.function;case"bigint":return A.bigint;case"symbol":return A.symbol;case"object":return Array.isArray(e)?A.array:e===null?A.null:e.then&&typeof e.then=="function"&&e.catch&&typeof e.catch=="function"?A.promise:typeof Map<"u"&&e instanceof Map?A.map:typeof Set<"u"&&e instanceof Set?A.set:typeof Date<"u"&&e instanceof Date?A.date:A.object;default:return A.unknown}},j=te.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class rn extends Error{get errors(){return this.issues}constructor(t){super(),this.issues=[],this.addIssue=r=>{this.issues=[...this.issues,r]},this.addIssues=(r=[])=>{this.issues=[...this.issues,...r]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=t}format(t){const n=t||function(i){return i.message},r={_errors:[]},s=i=>{for(const o of i.issues)if(o.code==="invalid_union")o.unionErrors.map(s);else if(o.code==="invalid_return_type")s(o.returnTypeError);else if(o.code==="invalid_arguments")s(o.argumentsError);else if(o.path.length===0)r._errors.push(n(o));else{let a=r,l=0;for(;l<o.path.length;){const u=o.path[l];l===o.path.length-1?(a[u]=a[u]||{_errors:[]},a[u]._errors.push(n(o))):a[u]=a[u]||{_errors:[]},a=a[u],l++}}};return s(this),r}static assert(t){if(!(t instanceof rn))throw new Error(`Not a ZodError: ${t}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,te.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(t=n=>n.message){const n={},r=[];for(const s of this.issues)s.path.length>0?(n[s.path[0]]=n[s.path[0]]||[],n[s.path[0]].push(t(s))):r.push(t(s));return{formErrors:r,fieldErrors:n}}get formErrors(){return this.flatten()}}rn.create=e=>new rn(e);const _u=(e,t)=>{let n;switch(e.code){case j.invalid_type:e.received===A.undefined?n="Required":n=`Expected ${e.expected}, received ${e.received}`;break;case j.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,te.jsonStringifyReplacer)}`;break;case j.unrecognized_keys:n=`Unrecognized key(s) in object: ${te.joinValues(e.keys,", ")}`;break;case j.invalid_union:n="Invalid input";break;case j.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${te.joinValues(e.options)}`;break;case j.invalid_enum_value:n=`Invalid enum value. Expected ${te.joinValues(e.options)}, received '${e.received}'`;break;case j.invalid_arguments:n="Invalid function arguments";break;case j.invalid_return_type:n="Invalid function return type";break;case j.invalid_date:n="Invalid date";break;case j.invalid_string:typeof e.validation=="object"?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,typeof e.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:te.assertNever(e.validation):e.validation!=="regex"?n=`Invalid ${e.validation}`:n="Invalid";break;case j.too_small:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:n="Invalid input";break;case j.too_big:e.type==="array"?n=`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:e.type==="string"?n=`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:e.type==="number"?n=`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="bigint"?n=`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:e.type==="date"?n=`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:n="Invalid input";break;case j.custom:n="Invalid input";break;case j.invalid_intersection_types:n="Intersection results could not be merged";break;case j.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case j.not_finite:n="Number must be finite";break;default:n=t.defaultError,te.assertNever(e)}return{message:n}};let E1=_u;function N1(){return E1}const T1=e=>{const{data:t,path:n,errorMaps:r,issueData:s}=e,i=[...n,...s.path||[]],o={...s,path:i};if(s.message!==void 0)return{...s,path:i,message:s.message};let a="";const l=r.filter(u=>!!u).slice().reverse();for(const u of l)a=u(o,{data:t,defaultError:a}).message;return{...s,path:i,message:a}};function O(e,t){const n=N1(),r=T1({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===_u?void 0:_u].filter(s=>!!s)});e.common.issues.push(r)}class st{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(t,n){const r=[];for(const s of n){if(s.status==="aborted")return B;s.status==="dirty"&&t.dirty(),r.push(s.value)}return{status:t.value,value:r}}static async mergeObjectAsync(t,n){const r=[];for(const s of n){const i=await s.key,o=await s.value;r.push({key:i,value:o})}return st.mergeObjectSync(t,r)}static mergeObjectSync(t,n){const r={};for(const s of n){const{key:i,value:o}=s;if(i.status==="aborted"||o.status==="aborted")return B;i.status==="dirty"&&t.dirty(),o.status==="dirty"&&t.dirty(),i.value!=="__proto__"&&(typeof o.value<"u"||s.alwaysSet)&&(r[i.value]=o.value)}return{status:t.value,value:r}}}const B=Object.freeze({status:"aborted"}),Hs=e=>({status:"dirty",value:e}),mt=e=>({status:"valid",value:e}),_f=e=>e.status==="aborted",bf=e=>e.status==="dirty",gs=e=>e.status==="valid",Yo=e=>typeof Promise<"u"&&e instanceof Promise;var F;(function(e){e.errToObj=t=>typeof t=="string"?{message:t}:t||{},e.toString=t=>typeof t=="string"?t:t==null?void 0:t.message})(F||(F={}));class Qn{constructor(t,n,r,s){this._cachedPath=[],this.parent=t,this.data=n,this._path=r,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ef=(e,t)=>{if(gs(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new rn(e.common.issues);return this._error=n,this._error}}};function Z(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:r,description:s}=e;if(t&&(n||r))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return t?{errorMap:t,description:s}:{errorMap:(o,a)=>{const{message:l}=e;return o.code==="invalid_enum_value"?{message:l??a.defaultError}:typeof a.data>"u"?{message:l??r??a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:l??n??a.defaultError}},description:s}}class ee{get description(){return this._def.description}_getType(t){return mn(t.data)}_getOrReturnCtx(t,n){return n||{common:t.parent.common,data:t.data,parsedType:mn(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}_processInputParams(t){return{status:new st,ctx:{common:t.parent.common,data:t.data,parsedType:mn(t.data),schemaErrorMap:this._def.errorMap,path:t.path,parent:t.parent}}}_parseSync(t){const n=this._parse(t);if(Yo(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(t){const n=this._parse(t);return Promise.resolve(n)}parse(t,n){const r=this.safeParse(t,n);if(r.success)return r.data;throw r.error}safeParse(t,n){const r={common:{issues:[],async:(n==null?void 0:n.async)??!1,contextualErrorMap:n==null?void 0:n.errorMap},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:mn(t)},s=this._parseSync({data:t,path:r.path,parent:r});return Ef(r,s)}"~validate"(t){var r,s;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:mn(t)};if(!this["~standard"].async)try{const i=this._parseSync({data:t,path:[],parent:n});return gs(i)?{value:i.value}:{issues:n.common.issues}}catch(i){(s=(r=i==null?void 0:i.message)==null?void 0:r.toLowerCase())!=null&&s.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:t,path:[],parent:n}).then(i=>gs(i)?{value:i.value}:{issues:n.common.issues})}async parseAsync(t,n){const r=await this.safeParseAsync(t,n);if(r.success)return r.data;throw r.error}async safeParseAsync(t,n){const r={common:{issues:[],contextualErrorMap:n==null?void 0:n.errorMap,async:!0},path:(n==null?void 0:n.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:t,parsedType:mn(t)},s=this._parse({data:t,path:r.path,parent:r}),i=await(Yo(s)?s:Promise.resolve(s));return Ef(r,i)}refine(t,n){const r=s=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(s):n;return this._refinement((s,i)=>{const o=t(s),a=()=>i.addIssue({code:j.custom,...r(s)});return typeof Promise<"u"&&o instanceof Promise?o.then(l=>l?!0:(a(),!1)):o?!0:(a(),!1)})}refinement(t,n){return this._refinement((r,s)=>t(r)?!0:(s.addIssue(typeof n=="function"?n(r,s):n),!1))}_refinement(t){return new ws({schema:this,typeName:V.ZodEffects,effect:{type:"refinement",refinement:t}})}superRefine(t){return this._refinement(t)}constructor(t){this.spa=this.safeParseAsync,this._def=t,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return $n.create(this,this._def)}nullable(){return ks.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return $t.create(this)}promise(){return ta.create(this,this._def)}or(t){return Jo.create([this,t],this._def)}and(t){return ea.create(this,t,this._def)}transform(t){return new ws({...Z(this._def),schema:this,typeName:V.ZodEffects,effect:{type:"transform",transform:t}})}default(t){const n=typeof t=="function"?t:()=>t;return new Tu({...Z(this._def),innerType:this,defaultValue:n,typeName:V.ZodDefault})}brand(){return new Y1({typeName:V.ZodBranded,type:this,...Z(this._def)})}catch(t){const n=typeof t=="function"?t:()=>t;return new ju({...Z(this._def),innerType:this,catchValue:n,typeName:V.ZodCatch})}describe(t){const n=this.constructor;return new n({...this._def,description:t})}pipe(t){return Lc.create(this,t)}readonly(){return Ru.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const j1=/^c[^\s-]{8,}$/i,R1=/^[0-9a-z]+$/,P1=/^[0-9A-HJKMNP-TV-Z]{26}$/i,I1=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,O1=/^[a-z0-9_-]{21}$/i,D1=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,M1=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,A1=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,F1="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let rl;const L1=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,z1=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,$1=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,U1=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,B1=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,V1=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ym="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Q1=new RegExp(`^${Ym}$`);function Xm(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:e.precision==null&&(t=`${t}(\\.\\d+)?`);const n=e.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${n}`}function W1(e){return new RegExp(`^${Xm(e)}$`)}function H1(e){let t=`${Ym}T${Xm(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function Z1(e,t){return!!((t==="v4"||!t)&&L1.test(e)||(t==="v6"||!t)&&$1.test(e))}function K1(e,t){if(!D1.test(e))return!1;try{const[n]=e.split("."),r=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),s=JSON.parse(atob(r));return!(typeof s!="object"||s===null||"typ"in s&&(s==null?void 0:s.typ)!=="JWT"||!s.alg||t&&s.alg!==t)}catch{return!1}}function G1(e,t){return!!((t==="v4"||!t)&&z1.test(e)||(t==="v6"||!t)&&U1.test(e))}class Rn extends ee{_parse(t){if(this._def.coerce&&(t.data=String(t.data)),this._getType(t)!==A.string){const i=this._getOrReturnCtx(t);return O(i,{code:j.invalid_type,expected:A.string,received:i.parsedType}),B}const r=new st;let s;for(const i of this._def.checks)if(i.kind==="min")t.data.length<i.value&&(s=this._getOrReturnCtx(t,s),O(s,{code:j.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="max")t.data.length>i.value&&(s=this._getOrReturnCtx(t,s),O(s,{code:j.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="length"){const o=t.data.length>i.value,a=t.data.length<i.value;(o||a)&&(s=this._getOrReturnCtx(t,s),o?O(s,{code:j.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):a&&O(s,{code:j.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),r.dirty())}else if(i.kind==="email")A1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"email",code:j.invalid_string,message:i.message}),r.dirty());else if(i.kind==="emoji")rl||(rl=new RegExp(F1,"u")),rl.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"emoji",code:j.invalid_string,message:i.message}),r.dirty());else if(i.kind==="uuid")I1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"uuid",code:j.invalid_string,message:i.message}),r.dirty());else if(i.kind==="nanoid")O1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"nanoid",code:j.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid")j1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"cuid",code:j.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid2")R1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"cuid2",code:j.invalid_string,message:i.message}),r.dirty());else if(i.kind==="ulid")P1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"ulid",code:j.invalid_string,message:i.message}),r.dirty());else if(i.kind==="url")try{new URL(t.data)}catch{s=this._getOrReturnCtx(t,s),O(s,{validation:"url",code:j.invalid_string,message:i.message}),r.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"regex",code:j.invalid_string,message:i.message}),r.dirty())):i.kind==="trim"?t.data=t.data.trim():i.kind==="includes"?t.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(t,s),O(s,{code:j.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),r.dirty()):i.kind==="toLowerCase"?t.data=t.data.toLowerCase():i.kind==="toUpperCase"?t.data=t.data.toUpperCase():i.kind==="startsWith"?t.data.startsWith(i.value)||(s=this._getOrReturnCtx(t,s),O(s,{code:j.invalid_string,validation:{startsWith:i.value},message:i.message}),r.dirty()):i.kind==="endsWith"?t.data.endsWith(i.value)||(s=this._getOrReturnCtx(t,s),O(s,{code:j.invalid_string,validation:{endsWith:i.value},message:i.message}),r.dirty()):i.kind==="datetime"?H1(i).test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{code:j.invalid_string,validation:"datetime",message:i.message}),r.dirty()):i.kind==="date"?Q1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{code:j.invalid_string,validation:"date",message:i.message}),r.dirty()):i.kind==="time"?W1(i).test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{code:j.invalid_string,validation:"time",message:i.message}),r.dirty()):i.kind==="duration"?M1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"duration",code:j.invalid_string,message:i.message}),r.dirty()):i.kind==="ip"?Z1(t.data,i.version)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"ip",code:j.invalid_string,message:i.message}),r.dirty()):i.kind==="jwt"?K1(t.data,i.alg)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"jwt",code:j.invalid_string,message:i.message}),r.dirty()):i.kind==="cidr"?G1(t.data,i.version)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"cidr",code:j.invalid_string,message:i.message}),r.dirty()):i.kind==="base64"?B1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"base64",code:j.invalid_string,message:i.message}),r.dirty()):i.kind==="base64url"?V1.test(t.data)||(s=this._getOrReturnCtx(t,s),O(s,{validation:"base64url",code:j.invalid_string,message:i.message}),r.dirty()):te.assertNever(i);return{status:r.value,value:t.data}}_regex(t,n,r){return this.refinement(s=>t.test(s),{validation:n,code:j.invalid_string,...F.errToObj(r)})}_addCheck(t){return new Rn({...this._def,checks:[...this._def.checks,t]})}email(t){return this._addCheck({kind:"email",...F.errToObj(t)})}url(t){return this._addCheck({kind:"url",...F.errToObj(t)})}emoji(t){return this._addCheck({kind:"emoji",...F.errToObj(t)})}uuid(t){return this._addCheck({kind:"uuid",...F.errToObj(t)})}nanoid(t){return this._addCheck({kind:"nanoid",...F.errToObj(t)})}cuid(t){return this._addCheck({kind:"cuid",...F.errToObj(t)})}cuid2(t){return this._addCheck({kind:"cuid2",...F.errToObj(t)})}ulid(t){return this._addCheck({kind:"ulid",...F.errToObj(t)})}base64(t){return this._addCheck({kind:"base64",...F.errToObj(t)})}base64url(t){return this._addCheck({kind:"base64url",...F.errToObj(t)})}jwt(t){return this._addCheck({kind:"jwt",...F.errToObj(t)})}ip(t){return this._addCheck({kind:"ip",...F.errToObj(t)})}cidr(t){return this._addCheck({kind:"cidr",...F.errToObj(t)})}datetime(t){return typeof t=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:t}):this._addCheck({kind:"datetime",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,offset:(t==null?void 0:t.offset)??!1,local:(t==null?void 0:t.local)??!1,...F.errToObj(t==null?void 0:t.message)})}date(t){return this._addCheck({kind:"date",message:t})}time(t){return typeof t=="string"?this._addCheck({kind:"time",precision:null,message:t}):this._addCheck({kind:"time",precision:typeof(t==null?void 0:t.precision)>"u"?null:t==null?void 0:t.precision,...F.errToObj(t==null?void 0:t.message)})}duration(t){return this._addCheck({kind:"duration",...F.errToObj(t)})}regex(t,n){return this._addCheck({kind:"regex",regex:t,...F.errToObj(n)})}includes(t,n){return this._addCheck({kind:"includes",value:t,position:n==null?void 0:n.position,...F.errToObj(n==null?void 0:n.message)})}startsWith(t,n){return this._addCheck({kind:"startsWith",value:t,...F.errToObj(n)})}endsWith(t,n){return this._addCheck({kind:"endsWith",value:t,...F.errToObj(n)})}min(t,n){return this._addCheck({kind:"min",value:t,...F.errToObj(n)})}max(t,n){return this._addCheck({kind:"max",value:t,...F.errToObj(n)})}length(t,n){return this._addCheck({kind:"length",value:t,...F.errToObj(n)})}nonempty(t){return this.min(1,F.errToObj(t))}trim(){return new Rn({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new Rn({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new Rn({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(t=>t.kind==="datetime")}get isDate(){return!!this._def.checks.find(t=>t.kind==="date")}get isTime(){return!!this._def.checks.find(t=>t.kind==="time")}get isDuration(){return!!this._def.checks.find(t=>t.kind==="duration")}get isEmail(){return!!this._def.checks.find(t=>t.kind==="email")}get isURL(){return!!this._def.checks.find(t=>t.kind==="url")}get isEmoji(){return!!this._def.checks.find(t=>t.kind==="emoji")}get isUUID(){return!!this._def.checks.find(t=>t.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(t=>t.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(t=>t.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(t=>t.kind==="cuid2")}get isULID(){return!!this._def.checks.find(t=>t.kind==="ulid")}get isIP(){return!!this._def.checks.find(t=>t.kind==="ip")}get isCIDR(){return!!this._def.checks.find(t=>t.kind==="cidr")}get isBase64(){return!!this._def.checks.find(t=>t.kind==="base64")}get isBase64url(){return!!this._def.checks.find(t=>t.kind==="base64url")}get minLength(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxLength(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}Rn.create=e=>new Rn({checks:[],typeName:V.ZodString,coerce:(e==null?void 0:e.coerce)??!1,...Z(e)});function q1(e,t){const n=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,s=n>r?n:r,i=Number.parseInt(e.toFixed(s).replace(".","")),o=Number.parseInt(t.toFixed(s).replace(".",""));return i%o/10**s}class vs extends ee{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(t){if(this._def.coerce&&(t.data=Number(t.data)),this._getType(t)!==A.number){const i=this._getOrReturnCtx(t);return O(i,{code:j.invalid_type,expected:A.number,received:i.parsedType}),B}let r;const s=new st;for(const i of this._def.checks)i.kind==="int"?te.isInteger(t.data)||(r=this._getOrReturnCtx(t,r),O(r,{code:j.invalid_type,expected:"integer",received:"float",message:i.message}),s.dirty()):i.kind==="min"?(i.inclusive?t.data<i.value:t.data<=i.value)&&(r=this._getOrReturnCtx(t,r),O(r,{code:j.too_small,minimum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?t.data>i.value:t.data>=i.value)&&(r=this._getOrReturnCtx(t,r),O(r,{code:j.too_big,maximum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="multipleOf"?q1(t.data,i.value)!==0&&(r=this._getOrReturnCtx(t,r),O(r,{code:j.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):i.kind==="finite"?Number.isFinite(t.data)||(r=this._getOrReturnCtx(t,r),O(r,{code:j.not_finite,message:i.message}),s.dirty()):te.assertNever(i);return{status:s.value,value:t.data}}gte(t,n){return this.setLimit("min",t,!0,F.toString(n))}gt(t,n){return this.setLimit("min",t,!1,F.toString(n))}lte(t,n){return this.setLimit("max",t,!0,F.toString(n))}lt(t,n){return this.setLimit("max",t,!1,F.toString(n))}setLimit(t,n,r,s){return new vs({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:F.toString(s)}]})}_addCheck(t){return new vs({...this._def,checks:[...this._def.checks,t]})}int(t){return this._addCheck({kind:"int",message:F.toString(t)})}positive(t){return this._addCheck({kind:"min",value:0,inclusive:!1,message:F.toString(t)})}negative(t){return this._addCheck({kind:"max",value:0,inclusive:!1,message:F.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:0,inclusive:!0,message:F.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:0,inclusive:!0,message:F.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:F.toString(n)})}finite(t){return this._addCheck({kind:"finite",message:F.toString(t)})}safe(t){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:F.toString(t)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:F.toString(t)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}get isInt(){return!!this._def.checks.find(t=>t.kind==="int"||t.kind==="multipleOf"&&te.isInteger(t.value))}get isFinite(){let t=null,n=null;for(const r of this._def.checks){if(r.kind==="finite"||r.kind==="int"||r.kind==="multipleOf")return!0;r.kind==="min"?(n===null||r.value>n)&&(n=r.value):r.kind==="max"&&(t===null||r.value<t)&&(t=r.value)}return Number.isFinite(n)&&Number.isFinite(t)}}vs.create=e=>new vs({checks:[],typeName:V.ZodNumber,coerce:(e==null?void 0:e.coerce)||!1,...Z(e)});class ki extends ee{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(t){if(this._def.coerce)try{t.data=BigInt(t.data)}catch{return this._getInvalidInput(t)}if(this._getType(t)!==A.bigint)return this._getInvalidInput(t);let r;const s=new st;for(const i of this._def.checks)i.kind==="min"?(i.inclusive?t.data<i.value:t.data<=i.value)&&(r=this._getOrReturnCtx(t,r),O(r,{code:j.too_small,type:"bigint",minimum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?t.data>i.value:t.data>=i.value)&&(r=this._getOrReturnCtx(t,r),O(r,{code:j.too_big,type:"bigint",maximum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="multipleOf"?t.data%i.value!==BigInt(0)&&(r=this._getOrReturnCtx(t,r),O(r,{code:j.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):te.assertNever(i);return{status:s.value,value:t.data}}_getInvalidInput(t){const n=this._getOrReturnCtx(t);return O(n,{code:j.invalid_type,expected:A.bigint,received:n.parsedType}),B}gte(t,n){return this.setLimit("min",t,!0,F.toString(n))}gt(t,n){return this.setLimit("min",t,!1,F.toString(n))}lte(t,n){return this.setLimit("max",t,!0,F.toString(n))}lt(t,n){return this.setLimit("max",t,!1,F.toString(n))}setLimit(t,n,r,s){return new ki({...this._def,checks:[...this._def.checks,{kind:t,value:n,inclusive:r,message:F.toString(s)}]})}_addCheck(t){return new ki({...this._def,checks:[...this._def.checks,t]})}positive(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:F.toString(t)})}negative(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:F.toString(t)})}nonpositive(t){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:F.toString(t)})}nonnegative(t){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:F.toString(t)})}multipleOf(t,n){return this._addCheck({kind:"multipleOf",value:t,message:F.toString(n)})}get minValue(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t}get maxValue(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t}}ki.create=e=>new ki({checks:[],typeName:V.ZodBigInt,coerce:(e==null?void 0:e.coerce)??!1,...Z(e)});class bu extends ee{_parse(t){if(this._def.coerce&&(t.data=!!t.data),this._getType(t)!==A.boolean){const r=this._getOrReturnCtx(t);return O(r,{code:j.invalid_type,expected:A.boolean,received:r.parsedType}),B}return mt(t.data)}}bu.create=e=>new bu({typeName:V.ZodBoolean,coerce:(e==null?void 0:e.coerce)||!1,...Z(e)});class Xo extends ee{_parse(t){if(this._def.coerce&&(t.data=new Date(t.data)),this._getType(t)!==A.date){const i=this._getOrReturnCtx(t);return O(i,{code:j.invalid_type,expected:A.date,received:i.parsedType}),B}if(Number.isNaN(t.data.getTime())){const i=this._getOrReturnCtx(t);return O(i,{code:j.invalid_date}),B}const r=new st;let s;for(const i of this._def.checks)i.kind==="min"?t.data.getTime()<i.value&&(s=this._getOrReturnCtx(t,s),O(s,{code:j.too_small,message:i.message,inclusive:!0,exact:!1,minimum:i.value,type:"date"}),r.dirty()):i.kind==="max"?t.data.getTime()>i.value&&(s=this._getOrReturnCtx(t,s),O(s,{code:j.too_big,message:i.message,inclusive:!0,exact:!1,maximum:i.value,type:"date"}),r.dirty()):te.assertNever(i);return{status:r.value,value:new Date(t.data.getTime())}}_addCheck(t){return new Xo({...this._def,checks:[...this._def.checks,t]})}min(t,n){return this._addCheck({kind:"min",value:t.getTime(),message:F.toString(n)})}max(t,n){return this._addCheck({kind:"max",value:t.getTime(),message:F.toString(n)})}get minDate(){let t=null;for(const n of this._def.checks)n.kind==="min"&&(t===null||n.value>t)&&(t=n.value);return t!=null?new Date(t):null}get maxDate(){let t=null;for(const n of this._def.checks)n.kind==="max"&&(t===null||n.value<t)&&(t=n.value);return t!=null?new Date(t):null}}Xo.create=e=>new Xo({checks:[],coerce:(e==null?void 0:e.coerce)||!1,typeName:V.ZodDate,...Z(e)});class Nf extends ee{_parse(t){if(this._getType(t)!==A.symbol){const r=this._getOrReturnCtx(t);return O(r,{code:j.invalid_type,expected:A.symbol,received:r.parsedType}),B}return mt(t.data)}}Nf.create=e=>new Nf({typeName:V.ZodSymbol,...Z(e)});class Tf extends ee{_parse(t){if(this._getType(t)!==A.undefined){const r=this._getOrReturnCtx(t);return O(r,{code:j.invalid_type,expected:A.undefined,received:r.parsedType}),B}return mt(t.data)}}Tf.create=e=>new Tf({typeName:V.ZodUndefined,...Z(e)});class jf extends ee{_parse(t){if(this._getType(t)!==A.null){const r=this._getOrReturnCtx(t);return O(r,{code:j.invalid_type,expected:A.null,received:r.parsedType}),B}return mt(t.data)}}jf.create=e=>new jf({typeName:V.ZodNull,...Z(e)});class Rf extends ee{constructor(){super(...arguments),this._any=!0}_parse(t){return mt(t.data)}}Rf.create=e=>new Rf({typeName:V.ZodAny,...Z(e)});class Pf extends ee{constructor(){super(...arguments),this._unknown=!0}_parse(t){return mt(t.data)}}Pf.create=e=>new Pf({typeName:V.ZodUnknown,...Z(e)});class Wn extends ee{_parse(t){const n=this._getOrReturnCtx(t);return O(n,{code:j.invalid_type,expected:A.never,received:n.parsedType}),B}}Wn.create=e=>new Wn({typeName:V.ZodNever,...Z(e)});class If extends ee{_parse(t){if(this._getType(t)!==A.undefined){const r=this._getOrReturnCtx(t);return O(r,{code:j.invalid_type,expected:A.void,received:r.parsedType}),B}return mt(t.data)}}If.create=e=>new If({typeName:V.ZodVoid,...Z(e)});class $t extends ee{_parse(t){const{ctx:n,status:r}=this._processInputParams(t),s=this._def;if(n.parsedType!==A.array)return O(n,{code:j.invalid_type,expected:A.array,received:n.parsedType}),B;if(s.exactLength!==null){const o=n.data.length>s.exactLength.value,a=n.data.length<s.exactLength.value;(o||a)&&(O(n,{code:o?j.too_big:j.too_small,minimum:a?s.exactLength.value:void 0,maximum:o?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),r.dirty())}if(s.minLength!==null&&n.data.length<s.minLength.value&&(O(n,{code:j.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),r.dirty()),s.maxLength!==null&&n.data.length>s.maxLength.value&&(O(n,{code:j.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),r.dirty()),n.common.async)return Promise.all([...n.data].map((o,a)=>s.type._parseAsync(new Qn(n,o,n.path,a)))).then(o=>st.mergeArray(r,o));const i=[...n.data].map((o,a)=>s.type._parseSync(new Qn(n,o,n.path,a)));return st.mergeArray(r,i)}get element(){return this._def.type}min(t,n){return new $t({...this._def,minLength:{value:t,message:F.toString(n)}})}max(t,n){return new $t({...this._def,maxLength:{value:t,message:F.toString(n)}})}length(t,n){return new $t({...this._def,exactLength:{value:t,message:F.toString(n)}})}nonempty(t){return this.min(1,t)}}$t.create=(e,t)=>new $t({type:e,minLength:null,maxLength:null,exactLength:null,typeName:V.ZodArray,...Z(t)});function Pr(e){if(e instanceof ve){const t={};for(const n in e.shape){const r=e.shape[n];t[n]=$n.create(Pr(r))}return new ve({...e._def,shape:()=>t})}else return e instanceof $t?new $t({...e._def,type:Pr(e.element)}):e instanceof $n?$n.create(Pr(e.unwrap())):e instanceof ks?ks.create(Pr(e.unwrap())):e instanceof Sr?Sr.create(e.items.map(t=>Pr(t))):e}class ve extends ee{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const t=this._def.shape(),n=te.objectKeys(t);return this._cached={shape:t,keys:n},this._cached}_parse(t){if(this._getType(t)!==A.object){const u=this._getOrReturnCtx(t);return O(u,{code:j.invalid_type,expected:A.object,received:u.parsedType}),B}const{status:r,ctx:s}=this._processInputParams(t),{shape:i,keys:o}=this._getCached(),a=[];if(!(this._def.catchall instanceof Wn&&this._def.unknownKeys==="strip"))for(const u in s.data)o.includes(u)||a.push(u);const l=[];for(const u of o){const f=i[u],h=s.data[u];l.push({key:{status:"valid",value:u},value:f._parse(new Qn(s,h,s.path,u)),alwaysSet:u in s.data})}if(this._def.catchall instanceof Wn){const u=this._def.unknownKeys;if(u==="passthrough")for(const f of a)l.push({key:{status:"valid",value:f},value:{status:"valid",value:s.data[f]}});else if(u==="strict")a.length>0&&(O(s,{code:j.unrecognized_keys,keys:a}),r.dirty());else if(u!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const u=this._def.catchall;for(const f of a){const h=s.data[f];l.push({key:{status:"valid",value:f},value:u._parse(new Qn(s,h,s.path,f)),alwaysSet:f in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const u=[];for(const f of l){const h=await f.key,m=await f.value;u.push({key:h,value:m,alwaysSet:f.alwaysSet})}return u}).then(u=>st.mergeObjectSync(r,u)):st.mergeObjectSync(r,l)}get shape(){return this._def.shape()}strict(t){return F.errToObj,new ve({...this._def,unknownKeys:"strict",...t!==void 0?{errorMap:(n,r)=>{var i,o;const s=((o=(i=this._def).errorMap)==null?void 0:o.call(i,n,r).message)??r.defaultError;return n.code==="unrecognized_keys"?{message:F.errToObj(t).message??s}:{message:s}}}:{}})}strip(){return new ve({...this._def,unknownKeys:"strip"})}passthrough(){return new ve({...this._def,unknownKeys:"passthrough"})}extend(t){return new ve({...this._def,shape:()=>({...this._def.shape(),...t})})}merge(t){return new ve({unknownKeys:t._def.unknownKeys,catchall:t._def.catchall,shape:()=>({...this._def.shape(),...t._def.shape()}),typeName:V.ZodObject})}setKey(t,n){return this.augment({[t]:n})}catchall(t){return new ve({...this._def,catchall:t})}pick(t){const n={};for(const r of te.objectKeys(t))t[r]&&this.shape[r]&&(n[r]=this.shape[r]);return new ve({...this._def,shape:()=>n})}omit(t){const n={};for(const r of te.objectKeys(this.shape))t[r]||(n[r]=this.shape[r]);return new ve({...this._def,shape:()=>n})}deepPartial(){return Pr(this)}partial(t){const n={};for(const r of te.objectKeys(this.shape)){const s=this.shape[r];t&&!t[r]?n[r]=s:n[r]=s.optional()}return new ve({...this._def,shape:()=>n})}required(t){const n={};for(const r of te.objectKeys(this.shape))if(t&&!t[r])n[r]=this.shape[r];else{let i=this.shape[r];for(;i instanceof $n;)i=i._def.innerType;n[r]=i}return new ve({...this._def,shape:()=>n})}keyof(){return Jm(te.objectKeys(this.shape))}}ve.create=(e,t)=>new ve({shape:()=>e,unknownKeys:"strip",catchall:Wn.create(),typeName:V.ZodObject,...Z(t)});ve.strictCreate=(e,t)=>new ve({shape:()=>e,unknownKeys:"strict",catchall:Wn.create(),typeName:V.ZodObject,...Z(t)});ve.lazycreate=(e,t)=>new ve({shape:e,unknownKeys:"strip",catchall:Wn.create(),typeName:V.ZodObject,...Z(t)});class Jo extends ee{_parse(t){const{ctx:n}=this._processInputParams(t),r=this._def.options;function s(i){for(const a of i)if(a.result.status==="valid")return a.result;for(const a of i)if(a.result.status==="dirty")return n.common.issues.push(...a.ctx.common.issues),a.result;const o=i.map(a=>new rn(a.ctx.common.issues));return O(n,{code:j.invalid_union,unionErrors:o}),B}if(n.common.async)return Promise.all(r.map(async i=>{const o={...n,common:{...n.common,issues:[]},parent:null};return{result:await i._parseAsync({data:n.data,path:n.path,parent:o}),ctx:o}})).then(s);{let i;const o=[];for(const l of r){const u={...n,common:{...n.common,issues:[]},parent:null},f=l._parseSync({data:n.data,path:n.path,parent:u});if(f.status==="valid")return f;f.status==="dirty"&&!i&&(i={result:f,ctx:u}),u.common.issues.length&&o.push(u.common.issues)}if(i)return n.common.issues.push(...i.ctx.common.issues),i.result;const a=o.map(l=>new rn(l));return O(n,{code:j.invalid_union,unionErrors:a}),B}}get options(){return this._def.options}}Jo.create=(e,t)=>new Jo({options:e,typeName:V.ZodUnion,...Z(t)});function Eu(e,t){const n=mn(e),r=mn(t);if(e===t)return{valid:!0,data:e};if(n===A.object&&r===A.object){const s=te.objectKeys(t),i=te.objectKeys(e).filter(a=>s.indexOf(a)!==-1),o={...e,...t};for(const a of i){const l=Eu(e[a],t[a]);if(!l.valid)return{valid:!1};o[a]=l.data}return{valid:!0,data:o}}else if(n===A.array&&r===A.array){if(e.length!==t.length)return{valid:!1};const s=[];for(let i=0;i<e.length;i++){const o=e[i],a=t[i],l=Eu(o,a);if(!l.valid)return{valid:!1};s.push(l.data)}return{valid:!0,data:s}}else return n===A.date&&r===A.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}class ea extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=(i,o)=>{if(_f(i)||_f(o))return B;const a=Eu(i.value,o.value);return a.valid?((bf(i)||bf(o))&&n.dirty(),{status:n.value,value:a.data}):(O(r,{code:j.invalid_intersection_types}),B)};return r.common.async?Promise.all([this._def.left._parseAsync({data:r.data,path:r.path,parent:r}),this._def.right._parseAsync({data:r.data,path:r.path,parent:r})]).then(([i,o])=>s(i,o)):s(this._def.left._parseSync({data:r.data,path:r.path,parent:r}),this._def.right._parseSync({data:r.data,path:r.path,parent:r}))}}ea.create=(e,t,n)=>new ea({left:e,right:t,typeName:V.ZodIntersection,...Z(n)});class Sr extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==A.array)return O(r,{code:j.invalid_type,expected:A.array,received:r.parsedType}),B;if(r.data.length<this._def.items.length)return O(r,{code:j.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),B;!this._def.rest&&r.data.length>this._def.items.length&&(O(r,{code:j.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const i=[...r.data].map((o,a)=>{const l=this._def.items[a]||this._def.rest;return l?l._parse(new Qn(r,o,r.path,a)):null}).filter(o=>!!o);return r.common.async?Promise.all(i).then(o=>st.mergeArray(n,o)):st.mergeArray(n,i)}get items(){return this._def.items}rest(t){return new Sr({...this._def,rest:t})}}Sr.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Sr({items:e,typeName:V.ZodTuple,rest:null,...Z(t)})};class Of extends ee{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==A.map)return O(r,{code:j.invalid_type,expected:A.map,received:r.parsedType}),B;const s=this._def.keyType,i=this._def.valueType,o=[...r.data.entries()].map(([a,l],u)=>({key:s._parse(new Qn(r,a,r.path,[u,"key"])),value:i._parse(new Qn(r,l,r.path,[u,"value"]))}));if(r.common.async){const a=new Map;return Promise.resolve().then(async()=>{for(const l of o){const u=await l.key,f=await l.value;if(u.status==="aborted"||f.status==="aborted")return B;(u.status==="dirty"||f.status==="dirty")&&n.dirty(),a.set(u.value,f.value)}return{status:n.value,value:a}})}else{const a=new Map;for(const l of o){const u=l.key,f=l.value;if(u.status==="aborted"||f.status==="aborted")return B;(u.status==="dirty"||f.status==="dirty")&&n.dirty(),a.set(u.value,f.value)}return{status:n.value,value:a}}}}Of.create=(e,t,n)=>new Of({valueType:t,keyType:e,typeName:V.ZodMap,...Z(n)});class Si extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.parsedType!==A.set)return O(r,{code:j.invalid_type,expected:A.set,received:r.parsedType}),B;const s=this._def;s.minSize!==null&&r.data.size<s.minSize.value&&(O(r,{code:j.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),n.dirty()),s.maxSize!==null&&r.data.size>s.maxSize.value&&(O(r,{code:j.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),n.dirty());const i=this._def.valueType;function o(l){const u=new Set;for(const f of l){if(f.status==="aborted")return B;f.status==="dirty"&&n.dirty(),u.add(f.value)}return{status:n.value,value:u}}const a=[...r.data.values()].map((l,u)=>i._parse(new Qn(r,l,r.path,u)));return r.common.async?Promise.all(a).then(l=>o(l)):o(a)}min(t,n){return new Si({...this._def,minSize:{value:t,message:F.toString(n)}})}max(t,n){return new Si({...this._def,maxSize:{value:t,message:F.toString(n)}})}size(t,n){return this.min(t,n).max(t,n)}nonempty(t){return this.min(1,t)}}Si.create=(e,t)=>new Si({valueType:e,minSize:null,maxSize:null,typeName:V.ZodSet,...Z(t)});class Nu extends ee{get schema(){return this._def.getter()}_parse(t){const{ctx:n}=this._processInputParams(t);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}Nu.create=(e,t)=>new Nu({getter:e,typeName:V.ZodLazy,...Z(t)});class Df extends ee{_parse(t){if(t.data!==this._def.value){const n=this._getOrReturnCtx(t);return O(n,{received:n.data,code:j.invalid_literal,expected:this._def.value}),B}return{status:"valid",value:t.data}}get value(){return this._def.value}}Df.create=(e,t)=>new Df({value:e,typeName:V.ZodLiteral,...Z(t)});function Jm(e,t){return new xs({values:e,typeName:V.ZodEnum,...Z(t)})}class xs extends ee{_parse(t){if(typeof t.data!="string"){const n=this._getOrReturnCtx(t),r=this._def.values;return O(n,{expected:te.joinValues(r),received:n.parsedType,code:j.invalid_type}),B}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(t.data)){const n=this._getOrReturnCtx(t),r=this._def.values;return O(n,{received:n.data,code:j.invalid_enum_value,options:r}),B}return mt(t.data)}get options(){return this._def.values}get enum(){const t={};for(const n of this._def.values)t[n]=n;return t}get Values(){const t={};for(const n of this._def.values)t[n]=n;return t}get Enum(){const t={};for(const n of this._def.values)t[n]=n;return t}extract(t,n=this._def){return xs.create(t,{...this._def,...n})}exclude(t,n=this._def){return xs.create(this.options.filter(r=>!t.includes(r)),{...this._def,...n})}}xs.create=Jm;class Mf extends ee{_parse(t){const n=te.getValidEnumValues(this._def.values),r=this._getOrReturnCtx(t);if(r.parsedType!==A.string&&r.parsedType!==A.number){const s=te.objectValues(n);return O(r,{expected:te.joinValues(s),received:r.parsedType,code:j.invalid_type}),B}if(this._cache||(this._cache=new Set(te.getValidEnumValues(this._def.values))),!this._cache.has(t.data)){const s=te.objectValues(n);return O(r,{received:r.data,code:j.invalid_enum_value,options:s}),B}return mt(t.data)}get enum(){return this._def.values}}Mf.create=(e,t)=>new Mf({values:e,typeName:V.ZodNativeEnum,...Z(t)});class ta extends ee{unwrap(){return this._def.type}_parse(t){const{ctx:n}=this._processInputParams(t);if(n.parsedType!==A.promise&&n.common.async===!1)return O(n,{code:j.invalid_type,expected:A.promise,received:n.parsedType}),B;const r=n.parsedType===A.promise?n.data:Promise.resolve(n.data);return mt(r.then(s=>this._def.type.parseAsync(s,{path:n.path,errorMap:n.common.contextualErrorMap})))}}ta.create=(e,t)=>new ta({type:e,typeName:V.ZodPromise,...Z(t)});class ws extends ee{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===V.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(t){const{status:n,ctx:r}=this._processInputParams(t),s=this._def.effect||null,i={addIssue:o=>{O(r,o),o.fatal?n.abort():n.dirty()},get path(){return r.path}};if(i.addIssue=i.addIssue.bind(i),s.type==="preprocess"){const o=s.transform(r.data,i);if(r.common.async)return Promise.resolve(o).then(async a=>{if(n.value==="aborted")return B;const l=await this._def.schema._parseAsync({data:a,path:r.path,parent:r});return l.status==="aborted"?B:l.status==="dirty"||n.value==="dirty"?Hs(l.value):l});{if(n.value==="aborted")return B;const a=this._def.schema._parseSync({data:o,path:r.path,parent:r});return a.status==="aborted"?B:a.status==="dirty"||n.value==="dirty"?Hs(a.value):a}}if(s.type==="refinement"){const o=a=>{const l=s.refinement(a,i);if(r.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(r.common.async===!1){const a=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});return a.status==="aborted"?B:(a.status==="dirty"&&n.dirty(),o(a.value),{status:n.value,value:a.value})}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(a=>a.status==="aborted"?B:(a.status==="dirty"&&n.dirty(),o(a.value).then(()=>({status:n.value,value:a.value}))))}if(s.type==="transform")if(r.common.async===!1){const o=this._def.schema._parseSync({data:r.data,path:r.path,parent:r});if(!gs(o))return B;const a=s.transform(o.value,i);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:a}}else return this._def.schema._parseAsync({data:r.data,path:r.path,parent:r}).then(o=>gs(o)?Promise.resolve(s.transform(o.value,i)).then(a=>({status:n.value,value:a})):B);te.assertNever(s)}}ws.create=(e,t,n)=>new ws({schema:e,typeName:V.ZodEffects,effect:t,...Z(n)});ws.createWithPreprocess=(e,t,n)=>new ws({schema:t,effect:{type:"preprocess",transform:e},typeName:V.ZodEffects,...Z(n)});class $n extends ee{_parse(t){return this._getType(t)===A.undefined?mt(void 0):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}$n.create=(e,t)=>new $n({innerType:e,typeName:V.ZodOptional,...Z(t)});class ks extends ee{_parse(t){return this._getType(t)===A.null?mt(null):this._def.innerType._parse(t)}unwrap(){return this._def.innerType}}ks.create=(e,t)=>new ks({innerType:e,typeName:V.ZodNullable,...Z(t)});class Tu extends ee{_parse(t){const{ctx:n}=this._processInputParams(t);let r=n.data;return n.parsedType===A.undefined&&(r=this._def.defaultValue()),this._def.innerType._parse({data:r,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}Tu.create=(e,t)=>new Tu({innerType:e,typeName:V.ZodDefault,defaultValue:typeof t.default=="function"?t.default:()=>t.default,...Z(t)});class ju extends ee{_parse(t){const{ctx:n}=this._processInputParams(t),r={...n,common:{...n.common,issues:[]}},s=this._def.innerType._parse({data:r.data,path:r.path,parent:{...r}});return Yo(s)?s.then(i=>({status:"valid",value:i.status==="valid"?i.value:this._def.catchValue({get error(){return new rn(r.common.issues)},input:r.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new rn(r.common.issues)},input:r.data})}}removeCatch(){return this._def.innerType}}ju.create=(e,t)=>new ju({innerType:e,typeName:V.ZodCatch,catchValue:typeof t.catch=="function"?t.catch:()=>t.catch,...Z(t)});class Af extends ee{_parse(t){if(this._getType(t)!==A.nan){const r=this._getOrReturnCtx(t);return O(r,{code:j.invalid_type,expected:A.nan,received:r.parsedType}),B}return{status:"valid",value:t.data}}}Af.create=e=>new Af({typeName:V.ZodNaN,...Z(e)});class Y1 extends ee{_parse(t){const{ctx:n}=this._processInputParams(t),r=n.data;return this._def.type._parse({data:r,path:n.path,parent:n})}unwrap(){return this._def.type}}class Lc extends ee{_parse(t){const{status:n,ctx:r}=this._processInputParams(t);if(r.common.async)return(async()=>{const i=await this._def.in._parseAsync({data:r.data,path:r.path,parent:r});return i.status==="aborted"?B:i.status==="dirty"?(n.dirty(),Hs(i.value)):this._def.out._parseAsync({data:i.value,path:r.path,parent:r})})();{const s=this._def.in._parseSync({data:r.data,path:r.path,parent:r});return s.status==="aborted"?B:s.status==="dirty"?(n.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:r.path,parent:r})}}static create(t,n){return new Lc({in:t,out:n,typeName:V.ZodPipeline})}}class Ru extends ee{_parse(t){const n=this._def.innerType._parse(t),r=s=>(gs(s)&&(s.value=Object.freeze(s.value)),s);return Yo(n)?n.then(s=>r(s)):r(n)}unwrap(){return this._def.innerType}}Ru.create=(e,t)=>new Ru({innerType:e,typeName:V.ZodReadonly,...Z(t)});var V;(function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"})(V||(V={}));const $=Rn.create,ie=vs.create,Cr=bu.create;Wn.create;const na=$t.create,it=ve.create;Jo.create;ea.create;Sr.create;const X1=Nu.create,Di=xs.create;ta.create;$n.create;ks.create;const J1=it({background:$(),foreground:$(),card:$(),cardForeground:$(),popover:$(),popoverForeground:$(),primary:$(),primaryForeground:$(),secondary:$(),secondaryForeground:$(),muted:$(),mutedForeground:$(),accent:$(),accentForeground:$(),destructive:$(),destructiveForeground:$(),border:$(),input:$(),ring:$()});it({id:$(),name:$(),mode:Di(["light","dark"]),colors:J1,isBuiltIn:Cr(),createdAt:ie()});it({mode:Di(["light","dark","system"]),activeThemeId:$(),autoSwitchEnabled:Cr(),autoSwitchTimes:it({lightModeStart:$(),darkModeStart:$()}),eyeCareMode:Cr(),transitionDuration:ie()});const ra=[{id:"light-default",name:"浅色默认",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"222.2 47.4% 11.2%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"210 40% 96%",accentForeground:"222.2 47.4% 11.2%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"222.2 84% 4.9%"},isBuiltIn:!0,createdAt:Date.now()},{id:"dark-default",name:"深色默认",mode:"dark",colors:{background:"222.2 84% 4.9%",foreground:"210 40% 98%",card:"222.2 84% 4.9%",cardForeground:"210 40% 98%",popover:"222.2 84% 4.9%",popoverForeground:"210 40% 98%",primary:"210 40% 98%",primaryForeground:"222.2 47.4% 11.2%",secondary:"217.2 32.6% 17.5%",secondaryForeground:"210 40% 98%",muted:"217.2 32.6% 17.5%",mutedForeground:"215 20.2% 65.1%",accent:"217.2 32.6% 17.5%",accentForeground:"210 40% 98%",destructive:"0 62.8% 30.6%",destructiveForeground:"210 40% 98%",border:"217.2 32.6% 17.5%",input:"217.2 32.6% 17.5%",ring:"212.7 26.8% 83.9%"},isBuiltIn:!0,createdAt:Date.now()},{id:"blue-theme",name:"蓝色主题",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"221.2 83.2% 53.3%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"221.2 83.2% 53.3%",accentForeground:"210 40% 98%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"221.2 83.2% 53.3%"},isBuiltIn:!0,createdAt:Date.now()},{id:"green-theme",name:"绿色主题",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"142.1 76.2% 36.3%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"142.1 76.2% 36.3%",accentForeground:"210 40% 98%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"142.1 76.2% 36.3%"},isBuiltIn:!0,createdAt:Date.now()},{id:"purple-theme",name:"紫色主题",mode:"light",colors:{background:"0 0% 100%",foreground:"222.2 84% 4.9%",card:"0 0% 100%",cardForeground:"222.2 84% 4.9%",popover:"0 0% 100%",popoverForeground:"222.2 84% 4.9%",primary:"262.1 83.3% 57.8%",primaryForeground:"210 40% 98%",secondary:"210 40% 96%",secondaryForeground:"222.2 47.4% 11.2%",muted:"210 40% 96%",mutedForeground:"215.4 16.3% 46.9%",accent:"262.1 83.3% 57.8%",accentForeground:"210 40% 98%",destructive:"0 84.2% 60.2%",destructiveForeground:"210 40% 98%",border:"214.3 31.8% 91.4%",input:"214.3 31.8% 91.4%",ring:"262.1 83.3% 57.8%"},isBuiltIn:!0,createdAt:Date.now()},{id:"eye-care",name:"护眼模式",mode:"light",colors:{background:"60 9.1% 97.8%",foreground:"24 9.8% 10%",card:"60 9.1% 97.8%",cardForeground:"24 9.8% 10%",popover:"60 9.1% 97.8%",popoverForeground:"24 9.8% 10%",primary:"25 95% 53%",primaryForeground:"60 9.1% 97.8%",secondary:"60 4.8% 95.9%",secondaryForeground:"24 9.8% 10%",muted:"60 4.8% 95.9%",mutedForeground:"25 5.3% 44.7%",accent:"60 4.8% 95.9%",accentForeground:"24 9.8% 10%",destructive:"0 84.2% 60.2%",destructiveForeground:"60 9.1% 97.8%",border:"20 5.9% 90%",input:"20 5.9% 90%",ring:"25 95% 53%"},isBuiltIn:!0,createdAt:Date.now()}],Ff={mode:"system",activeThemeId:"light-default",autoSwitchEnabled:!1,autoSwitchTimes:{lightModeStart:"06:00",darkModeStart:"18:00"},eyeCareMode:!1,transitionDuration:300},ew=e=>ra.find(t=>t.id===e),Lf=e=>ra.filter(t=>t.mode===e),Ne=window.electronAPI,Rs={getAll:()=>Ne.task.getAll(),getHierarchical:()=>Ne.task.getHierarchical(),create:e=>Ne.task.create(e),update:(e,t)=>Ne.task.update(e,t),delete:e=>Ne.task.delete(e),reorder:e=>Ne.task.reorder(e),getStats:()=>Ne.task.getStats(),softDelete:e=>Ne.task.softDelete(e),restore:e=>Ne.task.restore(e),getById:e=>Ne.task.getById(e),batchSoftDelete:e=>Ne.task.batchSoftDelete(e),batchRestore:e=>Ne.task.batchRestore(e),cleanupDeleted:e=>Ne.task.cleanupDeleted(e),getDeletedStats:()=>Ne.task.getDeletedStats(),getUndoable:()=>Ne.task.getUndoable()},zf={get:e=>Ne.settings.get(e),set:(e,t)=>Ne.settings.set(e,t)},ey=v.createContext(void 0),tw=()=>{const[e,t]=v.useState("light");return v.useEffect(()=>{const n=window.matchMedia("(prefers-color-scheme: dark)"),r=s=>{t(s.matches?"dark":"light")};return t(n.matches?"dark":"light"),n.addEventListener("change",r),()=>n.removeEventListener("change",r)},[]),e},nw=(e,t=300)=>{const n=document.documentElement;n.style.setProperty("--theme-transition-duration",`${t}ms`),Object.entries(e.colors).forEach(([r,s])=>{const i=`--${r.replace(/([A-Z])/g,"-$1").toLowerCase()}`;n.style.setProperty(i,s)}),e.mode==="dark"?n.classList.add("dark"):n.classList.remove("dark"),n.setAttribute("data-theme",e.id),n.setAttribute("data-theme-mode",e.mode)},rw=({children:e})=>{const[t,n]=v.useState(Ff),[r,s]=v.useState([]),[i,o]=v.useState(!0),a=tw(),l=[...ra,...r],u=v.useCallback(()=>{const _=ew(t.activeThemeId);return _||ra[0]},[t.activeThemeId]),f=v.useCallback(()=>{if(t.mode==="system"){const _=Lf(a),b=u();return b.mode===a?b:_.find(N=>N.id.includes("default"))||_[0]||b}return u()},[t.mode,a,u]),h=f(),m=v.useCallback(_=>{nw(_,t.transitionDuration)},[t.transitionDuration]),w=v.useCallback(async _=>{try{await zf.set("theme_config",JSON.stringify(_)),n(_)}catch(b){throw console.error("Failed to save theme config:",b),b}},[]),S=v.useCallback(async _=>{const b={...t,mode:_};await w(b)},[t,w]),y=v.useCallback(async _=>{if(!l.find(E=>E.id===_))throw new Error(`Theme with id "${_}" not found`);const N={...t,activeThemeId:_};await w(N)},[t,l,w]),C=v.useCallback(async _=>{const b={...t,..._};await w(b)},[t,w]),p=v.useCallback(async()=>{const _=["light","dark","system"],b=_.indexOf(t.mode),N=_[(b+1)%_.length];await S(N)},[t.mode,S]),c=v.useCallback(async _=>{console.log("Creating custom theme:",_)},[]),g=v.useCallback(async _=>{console.log("Deleting custom theme:",_)},[]);v.useEffect(()=>{(async()=>{try{o(!0);const b=await zf.get("theme_config");if(b){const N=JSON.parse(b);n(N)}}catch(b){console.error("Failed to load theme config:",b),n(Ff)}finally{o(!1)}})()},[]),v.useEffect(()=>{i||m(h)},[h,i,m]),v.useEffect(()=>{if(!t.autoSwitchEnabled)return;const _=()=>{const N=new Date,E=`${N.getHours().toString().padStart(2,"0")}:${N.getMinutes().toString().padStart(2,"0")}`,{lightModeStart:L,darkModeStart:R}=t.autoSwitchTimes;let re;if(E>=L&&E<R?re="light":re="dark",h.mode!==re){const Q=Lf(re),Ee=Q.find(gt=>gt.id.includes("default"))||Q[0];Ee&&y(Ee.id)}};_();const b=setInterval(_,6e4);return()=>clearInterval(b)},[t.autoSwitchEnabled,t.autoSwitchTimes,h.mode,y]);const x={config:t,currentTheme:h,availableThemes:l,systemTheme:a,isLoading:i,setMode:S,setTheme:y,updateConfig:C,toggleMode:p,createCustomTheme:c,deleteCustomTheme:g,getEffectiveTheme:f,applyTheme:m};return d.jsx(ey.Provider,{value:x,children:e})},sw=()=>{const e=v.useContext(ey);if(e===void 0)throw new Error("useTheme must be used within a ThemeProvider");return e},iw={},$f=e=>{let t;const n=new Set,r=(f,h)=>{const m=typeof f=="function"?f(t):f;if(!Object.is(m,t)){const w=t;t=h??(typeof m!="object"||m===null)?m:Object.assign({},t,m),n.forEach(S=>S(t,w))}},s=()=>t,l={setState:r,getState:s,getInitialState:()=>u,subscribe:f=>(n.add(f),()=>n.delete(f)),destroy:()=>{(iw?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},u=t=e(r,s,l);return l},ow=e=>e?$f(e):$f;var ty={exports:{}},ny={},ry={exports:{}},sy={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ss=v;function aw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var lw=typeof Object.is=="function"?Object.is:aw,uw=Ss.useState,cw=Ss.useEffect,dw=Ss.useLayoutEffect,fw=Ss.useDebugValue;function hw(e,t){var n=t(),r=uw({inst:{value:n,getSnapshot:t}}),s=r[0].inst,i=r[1];return dw(function(){s.value=n,s.getSnapshot=t,sl(s)&&i({inst:s})},[e,n,t]),cw(function(){return sl(s)&&i({inst:s}),e(function(){sl(s)&&i({inst:s})})},[e]),fw(n),n}function sl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!lw(e,n)}catch{return!0}}function pw(e,t){return t()}var mw=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?pw:hw;sy.useSyncExternalStore=Ss.useSyncExternalStore!==void 0?Ss.useSyncExternalStore:mw;ry.exports=sy;var yw=ry.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sa=v,gw=yw;function vw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xw=typeof Object.is=="function"?Object.is:vw,ww=gw.useSyncExternalStore,kw=Sa.useRef,Sw=Sa.useEffect,Cw=Sa.useMemo,_w=Sa.useDebugValue;ny.useSyncExternalStoreWithSelector=function(e,t,n,r,s){var i=kw(null);if(i.current===null){var o={hasValue:!1,value:null};i.current=o}else o=i.current;i=Cw(function(){function l(w){if(!u){if(u=!0,f=w,w=r(w),s!==void 0&&o.hasValue){var S=o.value;if(s(S,w))return h=S}return h=w}if(S=h,xw(f,w))return S;var y=r(w);return s!==void 0&&s(S,y)?(f=w,S):(f=w,h=y)}var u=!1,f,h,m=n===void 0?null:n;return[function(){return l(t())},m===null?void 0:function(){return l(m())}]},[t,n,r,s]);var a=ww(e,i[0],i[1]);return Sw(function(){o.hasValue=!0,o.value=a},[a]),_w(a),a};ty.exports=ny;var bw=ty.exports;const Ew=Ou(bw),iy={},{useDebugValue:Nw}=Bt,{useSyncExternalStoreWithSelector:Tw}=Ew;let Uf=!1;const jw=e=>e;function Rw(e,t=jw,n){(iy?"production":void 0)!=="production"&&n&&!Uf&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Uf=!0);const r=Tw(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return Nw(r),r}const Bf=e=>{(iy?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?ow(e):e,n=(r,s)=>Rw(t,r,s);return Object.assign(n,t),n},oy=e=>e?Bf(e):Bf,Pw={};function Iw(e,t){let n;try{n=e()}catch{return}return{getItem:s=>{var i;const o=l=>l===null?null:JSON.parse(l,void 0),a=(i=n.getItem(s))!=null?i:null;return a instanceof Promise?a.then(o):o(a)},setItem:(s,i)=>n.setItem(s,JSON.stringify(i,void 0)),removeItem:s=>n.removeItem(s)}}const Ci=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then(r){return Ci(r)(n)},catch(r){return this}}}catch(n){return{then(r){return this},catch(r){return Ci(r)(n)}}}},Ow=(e,t)=>(n,r,s)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:C=>C,version:0,merge:(C,p)=>({...p,...C}),...t},o=!1;const a=new Set,l=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return e((...C)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...C)},r,s);const f=Ci(i.serialize),h=()=>{const C=i.partialize({...r()});let p;const c=f({state:C,version:i.version}).then(g=>u.setItem(i.name,g)).catch(g=>{p=g});if(p)throw p;return c},m=s.setState;s.setState=(C,p)=>{m(C,p),h()};const w=e((...C)=>{n(...C),h()},r,s);let S;const y=()=>{var C;if(!u)return;o=!1,a.forEach(c=>c(r()));const p=((C=i.onRehydrateStorage)==null?void 0:C.call(i,r()))||void 0;return Ci(u.getItem.bind(u))(i.name).then(c=>{if(c)return i.deserialize(c)}).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==i.version){if(i.migrate)return i.migrate(c.state,c.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return c.state}).then(c=>{var g;return S=i.merge(c,(g=r())!=null?g:w),n(S,!0),h()}).then(()=>{p==null||p(S,void 0),o=!0,l.forEach(c=>c(S))}).catch(c=>{p==null||p(void 0,c)})};return s.persist={setOptions:C=>{i={...i,...C},C.getStorage&&(u=C.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>y(),hasHydrated:()=>o,onHydrate:C=>(a.add(C),()=>{a.delete(C)}),onFinishHydration:C=>(l.add(C),()=>{l.delete(C)})},y(),S||w},Dw=(e,t)=>(n,r,s)=>{let i={storage:Iw(()=>localStorage),partialize:y=>y,version:0,merge:(y,C)=>({...C,...y}),...t},o=!1;const a=new Set,l=new Set;let u=i.storage;if(!u)return e((...y)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),n(...y)},r,s);const f=()=>{const y=i.partialize({...r()});return u.setItem(i.name,{state:y,version:i.version})},h=s.setState;s.setState=(y,C)=>{h(y,C),f()};const m=e((...y)=>{n(...y),f()},r,s);s.getInitialState=()=>m;let w;const S=()=>{var y,C;if(!u)return;o=!1,a.forEach(c=>{var g;return c((g=r())!=null?g:m)});const p=((C=i.onRehydrateStorage)==null?void 0:C.call(i,(y=r())!=null?y:m))||void 0;return Ci(u.getItem.bind(u))(i.name).then(c=>{if(c)if(typeof c.version=="number"&&c.version!==i.version){if(i.migrate)return[!0,i.migrate(c.state,c.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,c.state];return[!1,void 0]}).then(c=>{var g;const[x,_]=c;if(w=i.merge(_,(g=r())!=null?g:m),n(w,!0),x)return f()}).then(()=>{p==null||p(w,void 0),w=r(),o=!0,l.forEach(c=>c(w))}).catch(c=>{p==null||p(void 0,c)})};return s.persist={setOptions:y=>{i={...i,...y},y.storage&&(u=y.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>S(),hasHydrated:()=>o,onHydrate:y=>(a.add(y),()=>{a.delete(y)}),onFinishHydration:y=>(l.add(y),()=>{l.delete(y)})},i.skipHydration||S(),w||m},Mw=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((Pw?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),Ow(e,t)):Dw(e,t),Aw=Mw,Ca=oy()(Aw(e=>({activeView:"today",setActiveView:t=>e({activeView:t}),searchQuery:"",setSearchQuery:t=>e({searchQuery:t}),sortBy:"custom",setSortBy:t=>e({sortBy:t}),showCompleted:!0,setShowCompleted:t=>e({showCompleted:t}),isTaskInputFocused:!1,setTaskInputFocused:t=>e({isTaskInputFocused:t})}),{name:"lingan-view-store",partialize:e=>({activeView:e.activeView,sortBy:e.sortBy,showCompleted:e.showCompleted})}));function zc(){return Fm({queryKey:Je.tasks,queryFn:Rs.getAll})}function Fw(){return Fm({queryKey:Je.taskStats,queryFn:Rs.getStats})}function ay(){const e=Ts();return ka({mutationFn:t=>Rs.create(t),onSuccess:t=>{e.setQueryData(Je.tasks,n=>n?[...n,t]:[t]),e.invalidateQueries({queryKey:Je.taskStats})},onError:()=>{e.invalidateQueries({queryKey:Je.tasks})}})}function ly(){const e=Ts();return ka({mutationFn:({id:t,input:n})=>Rs.update(t,n),onSuccess:t=>{e.setQueryData(Je.tasks,n=>n?n.map(r=>r.id===t.id?t:r):[t]),e.invalidateQueries({queryKey:Je.taskStats})},onError:()=>{e.invalidateQueries({queryKey:Je.tasks})}})}function Lw(){const e=Ts();return ka({mutationFn:t=>Rs.delete(t),onSuccess:(t,n)=>{e.setQueryData(Je.tasks,r=>r?r.filter(s=>s.id!==n):[]),e.invalidateQueries({queryKey:Je.taskStats})},onError:()=>{e.invalidateQueries({queryKey:Je.tasks})}})}function zw(){const e=Ts();return ka({mutationFn:t=>Rs.softDelete(t),onSuccess:(t,n)=>{e.setQueryData(Je.tasks,r=>r?r.filter(s=>s.id!==n):[]),e.invalidateQueries({queryKey:Je.taskStats})},onError:()=>{e.invalidateQueries({queryKey:Je.tasks})}})}function $w({children:e,onAddTaskClick:t}){var g;const[n,r]=v.useState(!1),{currentTheme:s,toggleMode:i}=sw(),{data:o=[]}=zc(),{activeView:a,setActiveView:l,searchQuery:u,setSearchQuery:f,sortBy:h,setSortBy:m,showCompleted:w,setShowCompleted:S,setTaskInputFocused:y}=Ca(),C=v.useMemo(()=>{const x=Date.now(),_=new Date;_.setHours(0,0,0,0);const b=_.getTime(),N=b+24*60*60*1e3;return{today:o.filter(E=>!E.isCompleted&&E.dueDate&&E.dueDate>=b&&E.dueDate<N).length,important:o.filter(E=>!E.isCompleted&&(E.priority===3||E.isImportant)).length,planned:o.filter(E=>!E.isCompleted&&E.dueDate&&E.dueDate>x).length,all:o.filter(E=>!E.isCompleted).length,completed:o.filter(E=>E.isCompleted).length}},[o]),p=[{id:"today",label:"我的一天",icon:gf,count:C.today,active:a==="today"},{id:"important",label:"重要",icon:Dc,count:C.important,active:a==="important"},{id:"planned",label:"已计划",icon:Oc,count:C.planned,active:a==="planned"},{id:"all",label:"全部",icon:Bm,count:C.all,active:a==="all"},{id:"completed",label:"已完成",icon:wu,count:C.completed,active:a==="completed"}],c=()=>{y(!0),t==null||t()};return d.jsxs("div",{className:"flex h-screen bg-background",children:[d.jsxs("div",{className:Y("flex flex-col bg-card border-r border-border transition-all duration-300",n?"w-16":"w-80"),children:[d.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-border",children:[d.jsxs("div",{className:Y("flex items-center gap-3",n&&"justify-center"),children:[d.jsx(X,{variant:"ghost",size:"sm",onClick:()=>r(!n),className:"p-2",children:d.jsx(Rx,{className:"h-5 w-5"})}),!n&&d.jsx("h1",{className:"text-xl font-semibold text-foreground",children:"灵感 APP"})]}),!n&&d.jsx(X,{variant:"ghost",size:"sm",onClick:i,className:"p-2",title:`当前主题: ${s.name} (${s.mode==="dark"?"深色":"浅色"})`,children:s.mode==="dark"?d.jsx(gf,{className:"h-4 w-4"}):d.jsx(Px,{className:"h-4 w-4"})})]}),!n&&d.jsx("div",{className:"p-4",children:d.jsxs("div",{className:"relative",children:[d.jsx(Dx,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),d.jsx(Fc,{value:u,onChange:x=>f(x.target.value),placeholder:"搜索任务...",className:"pl-10 bg-muted/50 border-muted focus:bg-background"})]})}),d.jsx("div",{className:"flex-1 px-2",children:d.jsx("nav",{className:"space-y-1",children:p.map(x=>d.jsxs(X,{variant:x.active?"secondary":"ghost",className:Y("w-full justify-start gap-3 h-12 px-3",n&&"justify-center px-2",x.active&&"bg-primary/10 text-primary border-r-2 border-primary"),onClick:()=>l(x.id),children:[d.jsx(x.icon,{className:"h-5 w-5 flex-shrink-0"}),!n&&d.jsxs(d.Fragment,{children:[d.jsx("span",{className:"flex-1 text-left",children:x.label}),x.count!==void 0&&d.jsx("span",{className:"text-xs bg-muted text-muted-foreground px-2 py-1 rounded-full min-w-[20px] text-center",children:x.count})]})]},x.id))})}),d.jsx("div",{className:"p-2 border-t border-border",children:d.jsxs(X,{variant:"ghost",className:Y("w-full justify-start gap-3 h-12 px-3",n&&"justify-center px-2"),children:[d.jsx(Ax,{className:"h-5 w-5 flex-shrink-0"}),!n&&d.jsx("span",{children:"设置"})]})})]}),d.jsxs("div",{className:"flex-1 flex flex-col overflow-hidden",children:[d.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border bg-card/50",children:[d.jsxs("div",{className:"flex items-center gap-4",children:[d.jsx("h2",{className:"text-2xl font-semibold text-foreground",children:((g=p.find(x=>x.id===a))==null?void 0:g.label)||"我的一天"}),d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(X,{variant:"ghost",size:"sm",onClick:()=>{const x=["custom","priority","dueDate","created","alphabetical"],b=(x.indexOf(h)+1)%x.length;m(x[b])},title:`当前排序: ${h==="custom"?"自定义":h==="priority"?"优先级":h==="dueDate"?"截止日期":h==="created"?"创建时间":"字母顺序"}`,children:d.jsx(Sx,{className:"h-4 w-4"})}),d.jsx(X,{variant:"ghost",size:"sm",onClick:()=>S(!w),title:w?"隐藏已完成任务":"显示已完成任务",children:w?d.jsx(Nx,{className:"h-4 w-4"}):d.jsx(Ex,{className:"h-4 w-4"})}),d.jsx(X,{variant:"ghost",size:"sm",title:"更多选项",children:d.jsx(Ix,{className:"h-4 w-4"})})]})]}),d.jsxs(X,{onClick:c,className:"gap-2",children:[d.jsx(qo,{className:"h-4 w-4"}),"添加任务"]})]}),d.jsx("div",{className:"flex-1 overflow-auto",children:d.jsx("div",{className:"max-w-4xl mx-auto p-6",children:e})})]})]})}const De={HIGH:1,MEDIUM:2,LOW:3},Uw=it({id:$(),content:$().min(1,"任务内容不能为空"),isCompleted:Cr(),priority:ie().int().min(1).max(3),dueDate:ie().int().nullable(),orderIndex:ie().int(),createdAt:ie().int(),deletedAt:ie().int().nullable().optional(),parentTaskId:$().nullable().optional(),taskType:Di(["task","subtask","template"]).default("task"),description:$().nullable().optional(),estimatedDuration:ie().int().nullable().optional(),actualDuration:ie().int().nullable().optional(),progress:ie().int().min(0).max(100).default(0)});it({content:$().min(1,"任务内容不能为空"),priority:ie().int().min(1).max(3).default(De.MEDIUM),dueDate:ie().int().nullable().optional(),parentTaskId:$().nullable().optional(),taskType:Di(["task","subtask","template"]).default("task"),description:$().nullable().optional(),estimatedDuration:ie().int().nullable().optional(),progress:ie().int().min(0).max(100).default(0)});it({content:$().min(1,"任务内容不能为空").optional(),isCompleted:Cr().optional(),priority:ie().int().min(1).max(3).optional(),dueDate:ie().int().nullable().optional(),parentTaskId:$().nullable().optional(),taskType:Di(["task","subtask","template"]).optional(),description:$().nullable().optional(),estimatedDuration:ie().int().nullable().optional(),actualDuration:ie().int().nullable().optional(),progress:ie().int().min(0).max(100).optional()});it({id:$(),orderIndex:ie().int()});const Bw=X1(()=>it({task:Uw,children:na(Bw),depth:ie().int(),path:na($())}));it({id:$(),name:$().min(1,"标签名称不能为空"),color:$().regex(/^#[0-9A-Fa-f]{6}$/,"颜色格式不正确"),description:$().nullable().optional(),createdAt:ie().int(),updatedAt:ie().int()});it({name:$().min(1,"标签名称不能为空"),color:$().regex(/^#[0-9A-Fa-f]{6}$/,"颜色格式不正确").default("#3b82f6"),description:$().nullable().optional()});it({id:$(),name:$().min(1,"模板名称不能为空"),content:$().min(1,"模板内容不能为空"),description:$().nullable().optional(),priority:ie().int().min(1).max(3),estimatedDuration:ie().int().nullable().optional(),tags:na($()),isPublic:Cr(),createdBy:$().nullable().optional(),createdAt:ie().int(),updatedAt:ie().int()});it({name:$().min(1,"模板名称不能为空"),content:$().min(1,"模板内容不能为空"),description:$().nullable().optional(),priority:ie().int().min(1).max(3).default(De.MEDIUM),estimatedDuration:ie().int().nullable().optional(),tags:na($()).default([]),isPublic:Cr().default(!1)});const Vw={[De.HIGH]:"text-red-600 bg-red-50 border-red-200",[De.MEDIUM]:"text-yellow-600 bg-yellow-50 border-yellow-200",[De.LOW]:"text-green-600 bg-green-50 border-green-200"};function Qw(){const[e,t]=v.useState(""),[n,r]=v.useState(!1),[s,i]=v.useState(De.MEDIUM),[o,a]=v.useState(""),[l,u]=v.useState(!1),f=v.useRef(null),h=ay(),{isTaskInputFocused:m,setTaskInputFocused:w,activeView:S}=Ca();v.useEffect(()=>{var x;m&&((x=f.current)==null||x.focus(),r(!0),w(!1))},[m,w]);const y=async x=>{var _;if(x.preventDefault(),!!e.trim())try{let b=o?new Date(o).getTime():void 0,N=l;if(S==="today"&&!o){const E=new Date;E.setHours(23,59,59,999),b=E.getTime()}S==="important"&&(N=!0),await h.mutateAsync({content:e.trim(),priority:s,dueDate:b,isImportant:N}),t(""),a(""),u(!1),i(De.MEDIUM),r(!1),(_=f.current)==null||_.focus()}catch(b){console.error("Failed to create task:",b)}},C=()=>{r(!0)},p=()=>{var x;t(""),a(""),u(!1),i(De.MEDIUM),r(!1),(x=f.current)==null||x.blur()},c=x=>{x.key==="Escape"?p():x.key==="Enter"&&(x.ctrlKey||x.metaKey)&&y(x)},g=x=>{switch(x){case De.HIGH:return"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-950/30";case De.LOW:return"text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-950/30";default:return"text-muted-foreground bg-muted"}};return d.jsx("div",{className:"bg-card rounded-xl border border-border/50 transition-all duration-200 focus-within:border-primary/50 focus-within:shadow-sm",children:d.jsxs("form",{onSubmit:y,children:[d.jsxs("div",{className:"flex items-center gap-3 p-4",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("div",{className:"w-6 h-6 rounded-full border-2 border-muted-foreground/30 flex items-center justify-center",children:d.jsx(qo,{className:"h-4 w-4 text-muted-foreground"})})}),d.jsx(Fc,{ref:f,value:e,onChange:x=>t(x.target.value),onFocus:C,onKeyDown:c,placeholder:`添加任务${S==="today"?"到我的一天":S==="important"?"（重要）":""}`,className:"border-0 bg-transparent text-base placeholder:text-muted-foreground focus-visible:ring-0 focus-visible:ring-offset-0 px-0"}),e.trim()&&d.jsxs("div",{className:"flex items-center gap-2 flex-shrink-0",children:[n&&d.jsx(X,{type:"button",variant:"ghost",size:"sm",onClick:p,className:"p-2",children:d.jsx(kr,{className:"h-4 w-4"})}),d.jsxs(X,{type:"submit",size:"sm",disabled:h.isPending,className:"gap-2",children:[h.isPending?d.jsx("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent"}):d.jsx(Mx,{className:"h-4 w-4"}),n&&(h.isPending?"添加中...":"添加")]})]})]}),n&&d.jsxs("div",{className:"border-t border-border/30 p-4 space-y-4",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(Oc,{className:"h-4 w-4 text-muted-foreground"}),d.jsx("input",{type:"date",value:o,onChange:x=>a(x.target.value),className:"text-sm border border-border rounded-md px-2 py-1 bg-background"})]}),d.jsxs(X,{type:"button",variant:"ghost",size:"sm",onClick:()=>u(!l),className:Y("gap-2",l&&"text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-950/30"),children:[d.jsx(Dc,{className:Y("h-4 w-4",l&&"fill-current")}),"重要"]}),d.jsxs("div",{className:"flex items-center gap-1",children:[d.jsx(Tx,{className:"h-4 w-4 text-muted-foreground"}),d.jsx("div",{className:"flex gap-1",children:[De.LOW,De.MEDIUM,De.HIGH].map(x=>d.jsx(X,{type:"button",variant:"ghost",size:"sm",onClick:()=>i(x),className:Y("w-8 h-8 p-0 rounded-full",s===x&&g(x)),children:x},x))})]})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"text-xs text-muted-foreground space-y-1",children:[d.jsxs("div",{children:[o&&`截止日期: ${new Date(o).toLocaleDateString()}`,l&&(o?" • ":"")+"重要",s!==De.MEDIUM&&(o||l?" • ":"")+`优先级: ${s===De.HIGH?"高":"低"}`]}),d.jsx("div",{className:"text-muted-foreground/70",children:"Ctrl+Enter 提交 • Esc 取消"})]}),d.jsxs("div",{className:"flex gap-2",children:[d.jsx(X,{type:"button",variant:"ghost",size:"sm",onClick:p,children:"取消"}),d.jsx(X,{type:"submit",size:"sm",disabled:!e.trim()||h.isPending,children:h.isPending?"添加中...":"添加任务"})]})]})]})]})})}const Ww=oy((e,t)=>({selectedTaskIds:new Set,isSelectionMode:!1,recentlyDeletedTasks:new Map,undoTimeouts:new Map,toggleTaskSelection:n=>{e(r=>{const s=new Set(r.selectedTaskIds);return s.has(n)?s.delete(n):s.add(n),{selectedTaskIds:s}})},selectAllTasks:n=>{e({selectedTaskIds:new Set(n)})},clearSelection:()=>{e({selectedTaskIds:new Set})},enterSelectionMode:()=>{e({isSelectionMode:!0})},exitSelectionMode:()=>{e({isSelectionMode:!1,selectedTaskIds:new Set})},toggleSelectionMode:()=>{const{isSelectionMode:n}=t();e(n?{isSelectionMode:!1,selectedTaskIds:new Set}:{isSelectionMode:!0})},addDeletedTask:n=>{e(r=>{const s=new Map(r.recentlyDeletedTasks);s.set(n.id,n);const i=r.undoTimeouts.get(n.id);i&&clearTimeout(i);const o=new Map(r.undoTimeouts),a=setTimeout(()=>{e(l=>{const u=new Map(l.recentlyDeletedTasks),f=new Map(l.undoTimeouts);return u.delete(n.id),f.delete(n.id),{recentlyDeletedTasks:u,undoTimeouts:f}})},3e4);return o.set(n.id,a),{recentlyDeletedTasks:s,undoTimeouts:o}})},removeDeletedTask:n=>{e(r=>{const s=new Map(r.recentlyDeletedTasks),i=new Map(r.undoTimeouts);s.delete(n);const o=i.get(n);return o&&(clearTimeout(o),i.delete(n)),{recentlyDeletedTasks:s,undoTimeouts:i}})},clearDeletedTasks:()=>{const{undoTimeouts:n}=t();n.forEach(r=>clearTimeout(r)),e({recentlyDeletedTasks:new Map,undoTimeouts:new Map})},getUndoableTask:n=>{const{recentlyDeletedTasks:r}=t();return r.get(n)}}));function $c(){const e=Ww();return{selectedTaskIds:Array.from(e.selectedTaskIds),selectedCount:e.selectedTaskIds.size,isSelectionMode:e.isSelectionMode,isTaskSelected:t=>e.selectedTaskIds.has(t),recentlyDeletedTasks:Array.from(e.recentlyDeletedTasks.values()),hasUndoableTasks:e.recentlyDeletedTasks.size>0,toggleTaskSelection:e.toggleTaskSelection,selectAllTasks:e.selectAllTasks,clearSelection:e.clearSelection,enterSelectionMode:e.enterSelectionMode,exitSelectionMode:e.exitSelectionMode,toggleSelectionMode:e.toggleSelectionMode,addDeletedTask:e.addDeletedTask,removeDeletedTask:e.removeDeletedTask,clearDeletedTasks:e.clearDeletedTasks,getUndoableTask:e.getUndoableTask}}function Hw({onLongPress:e,onClick:t,delay:n=500}){const r=v.useRef(null),s=v.useRef(!1),i=v.useCallback(()=>{s.current=!1,r.current=setTimeout(()=>{s.current=!0,e()},n)},[e,n]),o=v.useCallback(()=>{r.current&&(clearTimeout(r.current),r.current=null)},[]),a=v.useCallback(()=>{!s.current&&t&&t(),s.current=!1},[t]);return{onMouseDown:i,onMouseUp:o,onMouseLeave:o,onTouchStart:i,onTouchEnd:o,onClick:a}}const Me={fast:150,normal:300,slow:500},Ze={easeOut:"cubic-bezier(0.25, 0.46, 0.45, 0.94)",easeIn:"cubic-bezier(0.55, 0.055, 0.675, 0.19)",bounce:"cubic-bezier(0.68, -0.55, 0.265, 1.55)"},Zw=(e,t)=>{e.style.transition=`all ${Me.fast}ms ${Ze.easeOut}`,t?(e.style.transform="scale(0.98)",e.style.boxShadow="0 0 0 2px rgb(59 130 246 / 0.5)"):(e.style.transform="scale(1)",e.style.boxShadow="")},Kw=e=>{e.style.transform="translateY(100%)",e.style.opacity="0",e.offsetHeight,e.style.transition=`all ${Me.normal}ms ${Ze.easeOut}`,e.style.transform="translateY(0)",e.style.opacity="1"},Gw=e=>new Promise(t=>{e.style.transition=`all ${Me.normal}ms ${Ze.easeIn}`,e.style.transform="translateY(100%)",e.style.opacity="0",setTimeout(()=>{t()},Me.normal)}),qw=()=>{if(document.getElementById("custom-animations"))return;const e=document.createElement("style");e.id="custom-animations",e.textContent=`
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
    
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
      20%, 40%, 60%, 80% { transform: translateX(2px); }
    }
    
    @keyframes bounce {
      0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
      40%, 43% { transform: translate3d(0, -8px, 0); }
      70% { transform: translate3d(0, -4px, 0); }
      90% { transform: translate3d(0, -2px, 0); }
    }
    
    @keyframes slideInFromRight {
      from { transform: translateX(100%); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideInFromBottom {
      from { transform: translateY(100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes slideInFromTop {
      from { transform: translateY(-100%); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    @keyframes scaleIn {
      from { transform: scale(0.9); opacity: 0; }
      to { transform: scale(1); opacity: 1; }
    }
    
    .animate-slide-in-right {
      animation: slideInFromRight ${Me.normal}ms ${Ze.easeOut};
    }
    
    .animate-slide-in-bottom {
      animation: slideInFromBottom ${Me.normal}ms ${Ze.easeOut};
    }
    
    .animate-slide-in-top {
      animation: slideInFromTop ${Me.normal}ms ${Ze.bounce};
    }
    
    .animate-fade-in {
      animation: fadeIn ${Me.normal}ms ${Ze.easeOut};
    }
    
    .animate-scale-in {
      animation: scaleIn ${Me.normal}ms ${Ze.bounce};
    }
    
    .transition-all-fast {
      transition: all ${Me.fast}ms ${Ze.easeOut};
    }
    
    .transition-all-normal {
      transition: all ${Me.normal}ms ${Ze.easeOut};
    }
    
    .transition-all-slow {
      transition: all ${Me.slow}ms ${Ze.easeOut};
    }
  `,document.head.appendChild(e)},Yw=()=>{qw()};class il{static buttonClick(t){t.style.transform="scale(0.95)",t.style.transition=`transform ${Me.fast}ms ${Ze.easeOut}`,setTimeout(()=>{t.style.transform="scale(1)"},Me.fast)}static hover(t,n){t.style.transition=`transform ${Me.fast}ms ${Ze.easeOut}`,t.style.transform=n?"translateY(-1px)":"translateY(0)"}static focus(t,n){t.style.transition=`box-shadow ${Me.fast}ms ${Ze.easeOut}`,t.style.boxShadow=n?"0 0 0 2px rgb(59 130 246 / 0.5)":""}}const uy=v.createContext(void 0);function Xw({children:e}){const[t,n]=v.useState([]),r=v.useCallback(i=>{const o=Math.random().toString(36).substr(2,9),a={...i,id:o};n(u=>[...u,a]);const l=i.duration||5e3;setTimeout(()=>{n(u=>u.filter(f=>f.id!==o))},l)},[]),s=v.useCallback(i=>{n(o=>o.filter(a=>a.id!==i))},[]);return d.jsxs(uy.Provider,{value:{toasts:t,addToast:r,removeToast:s},children:[e,d.jsx(Jw,{})]})}function Uc(){const e=v.useContext(uy);if(!e)throw new Error("useToast must be used within a ToastProvider");return e}function Jw(){const{toasts:e}=Uc();return d.jsx("div",{className:"fixed bottom-4 right-4 z-50 flex flex-col gap-2 max-w-sm",children:e.map(t=>d.jsx(ek,{toast:t},t.id))})}function ek({toast:e}){const{removeToast:t}=Uc(),n={success:$m,error:Lm,warning:zm,info:jx},r={success:"bg-green-50 border-green-200 text-green-800",error:"bg-red-50 border-red-200 text-red-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",info:"bg-blue-50 border-blue-200 text-blue-800"},s={success:"text-green-600",error:"text-red-600",warning:"text-yellow-600",info:"text-blue-600"},i=n[e.type];return d.jsxs("div",{className:Y("flex items-start gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm","animate-in slide-in-from-right-full duration-300",r[e.type]),children:[d.jsx(i,{className:Y("h-5 w-5 mt-0.5 flex-shrink-0",s[e.type])}),d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsx("div",{className:"font-medium text-sm",children:e.title}),e.description&&d.jsx("div",{className:"text-sm opacity-90 mt-1",children:e.description}),e.action&&d.jsxs("button",{onClick:e.action.onClick,className:"inline-flex items-center gap-1 mt-2 text-sm font-medium underline hover:no-underline",children:[d.jsx(Su,{className:"h-3 w-3"}),e.action.label]})]}),d.jsx("button",{onClick:()=>t(e.id),className:"flex-shrink-0 opacity-70 hover:opacity-100 transition-opacity",children:d.jsx(kr,{className:"h-4 w-4"})})]})}function Bc(){const{addToast:e}=Uc(),t=v.useCallback((u,f,h)=>{e({type:"success",title:u,description:f,action:h})},[e]),n=v.useCallback((u,f)=>{e({type:"error",title:u,description:f})},[e]),r=v.useCallback((u,f)=>{e({type:"warning",title:u,description:f})},[e]),s=v.useCallback((u,f)=>{e({type:"info",title:u,description:f})},[e]),i=v.useCallback((u,f)=>{e({type:"success",title:"删除成功",description:`"${u}" 已被删除`,action:{label:"撤销",onClick:f},duration:8e3})},[e]),o=v.useCallback((u,f)=>{e({type:"success",title:"批量删除成功",description:`已删除 ${u} 个任务`,action:{label:"撤销",onClick:f},duration:8e3})},[e]),a=v.useCallback(u=>{e({type:"success",title:"批量恢复成功",description:`已恢复 ${u} 个任务`,duration:5e3})},[e]),l=v.useCallback(u=>{e({type:"info",title:"清理完成",description:`已清理 ${u} 个过期的已删除任务`,duration:5e3})},[e]);return{showSuccess:t,showError:n,showWarning:r,showInfo:s,showDeleteSuccess:i,showBatchDeleteSuccess:o,showBatchRestoreSuccess:a,showCleanupSuccess:l}}function tk(e,t){const n=v.createContext(t),r=i=>{const{children:o,...a}=i,l=v.useMemo(()=>a,Object.values(a));return d.jsx(n.Provider,{value:l,children:o})};r.displayName=e+"Provider";function s(i){const o=v.useContext(n);if(o)return o;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,s]}function cy(e,t=[]){let n=[];function r(i,o){const a=v.createContext(o),l=n.length;n=[...n,o];const u=h=>{var p;const{scope:m,children:w,...S}=h,y=((p=m==null?void 0:m[e])==null?void 0:p[l])||a,C=v.useMemo(()=>S,Object.values(S));return d.jsx(y.Provider,{value:C,children:w})};u.displayName=i+"Provider";function f(h,m){var y;const w=((y=m==null?void 0:m[e])==null?void 0:y[l])||a,S=v.useContext(w);if(S)return S;if(o!==void 0)return o;throw new Error(`\`${h}\` must be used within \`${i}\``)}return[u,f]}const s=()=>{const i=n.map(o=>v.createContext(o));return function(a){const l=(a==null?void 0:a[e])||i;return v.useMemo(()=>({[`__scope${e}`]:{...a,[e]:l}}),[a,l])}};return s.scopeName=e,[r,nk(s,...t)]}function nk(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(i){const o=r.reduce((a,{useScope:l,scopeName:u})=>{const h=l(i)[`__scope${u}`];return{...a,...h}},{});return v.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}function Ut(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e==null||e(s),n===!1||!s.defaultPrevented)return t==null?void 0:t(s)}}var Cs=globalThis!=null&&globalThis.document?v.useLayoutEffect:()=>{},rk=kh[" useInsertionEffect ".trim().toString()]||Cs;function dy({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[s,i,o]=sk({defaultProp:t,onChange:n}),a=e!==void 0,l=a?e:s;{const f=v.useRef(e!==void 0);v.useEffect(()=>{const h=f.current;h!==a&&console.warn(`${r} is changing from ${h?"controlled":"uncontrolled"} to ${a?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),f.current=a},[a,r])}const u=v.useCallback(f=>{var h;if(a){const m=ik(f)?f(e):f;m!==e&&((h=o.current)==null||h.call(o,m))}else i(f)},[a,e,i,o]);return[l,u]}function sk({defaultProp:e,onChange:t}){const[n,r]=v.useState(e),s=v.useRef(n),i=v.useRef(t);return rk(()=>{i.current=t},[t]),v.useEffect(()=>{var o;s.current!==n&&((o=i.current)==null||o.call(i,n),s.current=n)},[n,s]),[n,r,i]}function ik(e){return typeof e=="function"}function ok(e){const t=v.useRef({value:e,previous:e});return v.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}function ak(e){const[t,n]=v.useState(void 0);return Cs(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const i=s[0];let o,a;if("borderBoxSize"in i){const l=i.borderBoxSize,u=Array.isArray(l)?l[0]:l;o=u.inlineSize,a=u.blockSize}else o=e.offsetWidth,a=e.offsetHeight;n({width:o,height:a})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}function lk(e,t){return v.useReducer((n,r)=>t[n][r]??n,e)}var Mi=e=>{const{present:t,children:n}=e,r=uk(t),s=typeof n=="function"?n({present:r.isPresent}):v.Children.only(n),i=on(r.ref,ck(s));return typeof n=="function"||r.isPresent?v.cloneElement(s,{ref:i}):null};Mi.displayName="Presence";function uk(e){const[t,n]=v.useState(),r=v.useRef(null),s=v.useRef(e),i=v.useRef("none"),o=e?"mounted":"unmounted",[a,l]=lk(o,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return v.useEffect(()=>{const u=ro(r.current);i.current=a==="mounted"?u:"none"},[a]),Cs(()=>{const u=r.current,f=s.current;if(f!==e){const m=i.current,w=ro(u);e?l("MOUNT"):w==="none"||(u==null?void 0:u.display)==="none"?l("UNMOUNT"):l(f&&m!==w?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,l]),Cs(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,h=w=>{const y=ro(r.current).includes(w.animationName);if(w.target===t&&y&&(l("ANIMATION_END"),!s.current)){const C=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=C)})}},m=w=>{w.target===t&&(i.current=ro(r.current))};return t.addEventListener("animationstart",m),t.addEventListener("animationcancel",h),t.addEventListener("animationend",h),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",m),t.removeEventListener("animationcancel",h),t.removeEventListener("animationend",h)}}else l("ANIMATION_END")},[t,l]),{isPresent:["mounted","unmountSuspended"].includes(a),ref:v.useCallback(u=>{r.current=u?getComputedStyle(u):null,n(u)},[])}}function ro(e){return(e==null?void 0:e.animationName)||"none"}function ck(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var dk=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],yt=dk.reduce((e,t)=>{const n=Mc(`Primitive.${t}`),r=v.forwardRef((s,i)=>{const{asChild:o,...a}=s,l=o?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),d.jsx(l,{...a,ref:i})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function fk(e,t){e&&Tc.flushSync(()=>e.dispatchEvent(t))}var _a="Checkbox",[hk,YS]=cy(_a),[pk,Vc]=hk(_a);function mk(e){const{__scopeCheckbox:t,checked:n,children:r,defaultChecked:s,disabled:i,form:o,name:a,onCheckedChange:l,required:u,value:f="on",internal_do_not_use_render:h}=e,[m,w]=dy({prop:n,defaultProp:s??!1,onChange:l,caller:_a}),[S,y]=v.useState(null),[C,p]=v.useState(null),c=v.useRef(!1),g=S?!!o||!!S.closest("form"):!0,x={checked:m,disabled:i,setChecked:w,control:S,setControl:y,name:a,form:o,value:f,hasConsumerStoppedPropagationRef:c,required:u,defaultChecked:Un(s)?!1:s,isFormControl:g,bubbleInput:C,setBubbleInput:p};return d.jsx(pk,{scope:t,...x,children:yk(h)?h(x):r})}var fy="CheckboxTrigger",hy=v.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...r},s)=>{const{control:i,value:o,disabled:a,checked:l,required:u,setControl:f,setChecked:h,hasConsumerStoppedPropagationRef:m,isFormControl:w,bubbleInput:S}=Vc(fy,e),y=on(s,f),C=v.useRef(l);return v.useEffect(()=>{const p=i==null?void 0:i.form;if(p){const c=()=>h(C.current);return p.addEventListener("reset",c),()=>p.removeEventListener("reset",c)}},[i,h]),d.jsx(yt.button,{type:"button",role:"checkbox","aria-checked":Un(l)?"mixed":l,"aria-required":u,"data-state":vy(l),"data-disabled":a?"":void 0,disabled:a,value:o,...r,ref:y,onKeyDown:Ut(t,p=>{p.key==="Enter"&&p.preventDefault()}),onClick:Ut(n,p=>{h(c=>Un(c)?!0:!c),S&&w&&(m.current=p.isPropagationStopped(),m.current||p.stopPropagation())})})});hy.displayName=fy;var Qc=v.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:r,checked:s,defaultChecked:i,required:o,disabled:a,value:l,onCheckedChange:u,form:f,...h}=e;return d.jsx(mk,{__scopeCheckbox:n,checked:s,defaultChecked:i,disabled:a,required:o,onCheckedChange:u,name:r,form:f,value:l,internal_do_not_use_render:({isFormControl:m})=>d.jsxs(d.Fragment,{children:[d.jsx(hy,{...h,ref:t,__scopeCheckbox:n}),m&&d.jsx(gy,{__scopeCheckbox:n})]})})});Qc.displayName=_a;var py="CheckboxIndicator",my=v.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...s}=e,i=Vc(py,n);return d.jsx(Mi,{present:r||Un(i.checked)||i.checked===!0,children:d.jsx(yt.span,{"data-state":vy(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});my.displayName=py;var yy="CheckboxBubbleInput",gy=v.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:r,hasConsumerStoppedPropagationRef:s,checked:i,defaultChecked:o,required:a,disabled:l,name:u,value:f,form:h,bubbleInput:m,setBubbleInput:w}=Vc(yy,e),S=on(n,w),y=ok(i),C=ak(r);v.useEffect(()=>{const c=m;if(!c)return;const g=window.HTMLInputElement.prototype,_=Object.getOwnPropertyDescriptor(g,"checked").set,b=!s.current;if(y!==i&&_){const N=new Event("click",{bubbles:b});c.indeterminate=Un(i),_.call(c,Un(i)?!1:i),c.dispatchEvent(N)}},[m,y,i,s]);const p=v.useRef(Un(i)?!1:i);return d.jsx(yt.input,{type:"checkbox","aria-hidden":!0,defaultChecked:o??p.current,required:a,disabled:l,name:u,value:f,form:h,...t,tabIndex:-1,ref:S,style:{...t.style,...C,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});gy.displayName=yy;function yk(e){return typeof e=="function"}function Un(e){return e==="indeterminate"}function vy(e){return Un(e)?"indeterminate":e?"checked":"unchecked"}const xy=v.forwardRef(({className:e,...t},n)=>d.jsx(Qc,{ref:n,className:Y("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:d.jsx(my,{className:Y("flex items-center justify-center text-current"),children:d.jsx(Cx,{className:"h-4 w-4"})})}));xy.displayName=Qc.displayName;function gk({task:e,subtasks:t=[],onToggleComplete:n,onAddSubtask:r,onEdit:s,className:i}){const[o,a]=v.useState(!0),[l,u]=v.useState(!1),[f,h]=v.useState(!1),m=Bt.useRef(null),w=ly();Lw();const S=zw(),{isSelectionMode:y,isTaskSelected:C,toggleTaskSelection:p,enterSelectionMode:c,addDeletedTask:g}=$c(),{showDeleteSuccess:x,showError:_}=Bc(),b=_1(e.dueDate),N=t.length>0,E=t.filter(D=>D.isCompleted).length,L=N?E/t.length*100:0,R=C(e.id),re=()=>{y?p(e.id):n==null||n(e.id)},gt=Hw({onLongPress:()=>{y||(c(),p(e.id))},onClick:()=>{y&&(p(e.id),m.current&&Zw(m.current,!R))},delay:500}),Gn=()=>{r==null||r(e.id)},Rt=()=>{w.mutate({id:e.id,input:{isImportant:!e.isImportant}})},an=async()=>{if(!f){h(!0);try{m.current&&il.buttonClick(m.current),await S.mutateAsync(e.id),g({id:e.id,content:e.content,deletedAt:Date.now()}),x(e.content,async()=>{try{await window.electronAPI.task.restore(e.id),x("撤销成功",()=>{})}catch(D){console.error("Failed to restore task:",D),_("撤销失败","无法恢复已删除的任务")}})}catch(D){console.error("Failed to soft delete task:",D);let U="删除失败",G="删除任务时发生错误";D instanceof Error&&(D.message.includes("任务已经被删除")?(U="任务已删除",G="该任务已经被删除，请刷新页面查看最新状态"):D.message.includes("任务不存在")?(U="任务不存在",G="该任务可能已被删除或不存在，请刷新页面"):G=D.message),_(U,G)}finally{h(!1)}}},P=D=>Vw[D]||"bg-gray-100";return d.jsxs("div",{ref:m,...gt,className:Y("group bg-card rounded-xl border border-border/50 transition-all duration-200 hover:border-border hover:shadow-sm",e.isCompleted&&"opacity-75",b&&!e.isCompleted&&"border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-950/20",y&&"cursor-pointer",R&&"border-primary bg-primary/5 shadow-md transform scale-[0.98]",f&&"opacity-50 pointer-events-none",i),onMouseEnter:()=>{u(!0),m.current&&!y&&il.hover(m.current,!0)},onMouseLeave:()=>{u(!1),m.current&&!y&&il.hover(m.current,!1)},children:[d.jsxs("div",{className:"flex items-start gap-4 p-4",children:[y?d.jsx(xy,{checked:R,onCheckedChange:()=>p(e.id),className:"mt-1"}):d.jsx(X,{variant:"ghost",size:"sm",onClick:re,className:"p-0 h-6 w-6 rounded-full hover:bg-transparent",children:e.isCompleted?d.jsx(mf,{className:"h-6 w-6 text-green-600 fill-green-100"}):d.jsx(ku,{className:"h-6 w-6 text-muted-foreground hover:text-primary transition-colors"})}),d.jsx("div",{className:"flex-1 min-w-0",children:d.jsxs("div",{className:"flex items-start justify-between gap-2",children:[d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsx("h3",{className:Y("text-base font-medium text-foreground cursor-pointer hover:text-primary transition-colors",e.isCompleted&&"line-through text-muted-foreground"),onClick:()=>s==null?void 0:s(e),children:e.content}),d.jsxs("div",{className:"flex items-center gap-3 mt-2",children:[e.priority!==De.MEDIUM&&d.jsx("div",{className:Y("w-2 h-2 rounded-full",P(e.priority))}),e.dueDate&&d.jsxs("div",{className:Y("flex items-center gap-1 text-xs px-2 py-1 rounded-md",b&&!e.isCompleted?"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-950/30":"text-muted-foreground bg-muted/50"),children:[d.jsx(Oc,{className:"h-3 w-3"}),d.jsx("span",{children:C1(e.dueDate)})]}),N&&d.jsx("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:d.jsxs("div",{className:"flex items-center gap-1",children:[d.jsx("div",{className:"w-12 h-1.5 bg-muted rounded-full overflow-hidden",children:d.jsx("div",{className:"h-full bg-primary transition-all duration-300",style:{width:`${L}%`}})}),d.jsxs("span",{children:[E,"/",t.length]})]})})]})]}),d.jsxs("div",{className:Y("flex items-center gap-1 transition-opacity",l?"opacity-100":"opacity-0"),children:[d.jsx(X,{variant:"ghost",size:"sm",className:Y("p-1 h-8 w-8",e.isImportant&&"text-yellow-600"),onClick:Rt,title:e.isImportant?"取消重要标记":"标记为重要",children:d.jsx(Dc,{className:Y("h-4 w-4",e.isImportant&&"fill-current")})}),d.jsx(X,{variant:"ghost",size:"sm",className:"p-1 h-8 w-8",onClick:()=>s==null?void 0:s(e),title:"编辑任务",children:d.jsx(Ox,{className:"h-4 w-4"})}),d.jsx(X,{variant:"ghost",size:"sm",className:"p-1 h-8 w-8 text-red-600 hover:text-red-700",onClick:an,title:"删除任务",children:d.jsx(wi,{className:"h-4 w-4"})})]})]})})]}),N&&d.jsxs("div",{className:"border-t border-border/30",children:[d.jsxs("div",{className:"flex items-center justify-between px-4 py-2 bg-muted/20",children:[d.jsxs(X,{variant:"ghost",size:"sm",onClick:()=>a(!o),className:"flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground p-1 h-auto",children:[o?d.jsx(_x,{className:"h-4 w-4"}):d.jsx(bx,{className:"h-4 w-4"}),d.jsxs("span",{children:["子任务 (",E,"/",t.length,")"]})]}),r&&d.jsx(X,{variant:"ghost",size:"sm",onClick:Gn,className:"p-1 h-auto text-muted-foreground hover:text-foreground",children:d.jsx(qo,{className:"h-4 w-4"})})]}),o&&d.jsx("div",{className:"px-4 pb-3 space-y-2",children:t.map(D=>d.jsxs("div",{className:"flex items-center gap-3 py-2",children:[d.jsx(X,{variant:"ghost",size:"sm",onClick:()=>n==null?void 0:n(D.id),className:"p-0 h-5 w-5 rounded-full hover:bg-transparent",children:D.isCompleted?d.jsx(mf,{className:"h-5 w-5 text-green-600 fill-green-100"}):d.jsx(ku,{className:"h-5 w-5 text-muted-foreground hover:text-primary transition-colors"})}),d.jsx("span",{className:Y("text-sm text-foreground cursor-pointer hover:text-primary transition-colors flex-1",D.isCompleted&&"line-through text-muted-foreground"),onClick:()=>s==null?void 0:s(D),children:D.content})]},D.id))})]}),!N&&r&&l&&d.jsx("div",{className:"border-t border-border/30 px-4 py-3",children:d.jsxs(X,{variant:"ghost",size:"sm",onClick:Gn,className:"text-muted-foreground hover:text-foreground text-sm gap-2 h-auto p-1",children:[d.jsx(qo,{className:"h-4 w-4"}),"添加子任务"]})})]})}var vk=kh[" useId ".trim().toString()]||(()=>{}),xk=0;function ol(e){const[t,n]=v.useState(vk());return Cs(()=>{n(r=>r??String(xk++))},[e]),e||(t?`radix-${t}`:"")}function _i(e){const t=v.useRef(e);return v.useEffect(()=>{t.current=e}),v.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function wk(e,t=globalThis==null?void 0:globalThis.document){const n=_i(e);v.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var kk="DismissableLayer",Pu="dismissableLayer.update",Sk="dismissableLayer.pointerDownOutside",Ck="dismissableLayer.focusOutside",Vf,wy=v.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ky=v.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:i,onInteractOutside:o,onDismiss:a,...l}=e,u=v.useContext(wy),[f,h]=v.useState(null),m=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,w]=v.useState({}),S=on(t,N=>h(N)),y=Array.from(u.layers),[C]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),p=y.indexOf(C),c=f?y.indexOf(f):-1,g=u.layersWithOutsidePointerEventsDisabled.size>0,x=c>=p,_=Ek(N=>{const E=N.target,L=[...u.branches].some(R=>R.contains(E));!x||L||(s==null||s(N),o==null||o(N),N.defaultPrevented||a==null||a())},m),b=Nk(N=>{const E=N.target;[...u.branches].some(R=>R.contains(E))||(i==null||i(N),o==null||o(N),N.defaultPrevented||a==null||a())},m);return wk(N=>{c===u.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&a&&(N.preventDefault(),a()))},m),v.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(Vf=m.body.style.pointerEvents,m.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),Qf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(m.body.style.pointerEvents=Vf)}},[f,m,n,u]),v.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),Qf())},[f,u]),v.useEffect(()=>{const N=()=>w({});return document.addEventListener(Pu,N),()=>document.removeEventListener(Pu,N)},[]),d.jsx(yt.div,{...l,ref:S,style:{pointerEvents:g?x?"auto":"none":void 0,...e.style},onFocusCapture:Ut(e.onFocusCapture,b.onFocusCapture),onBlurCapture:Ut(e.onBlurCapture,b.onBlurCapture),onPointerDownCapture:Ut(e.onPointerDownCapture,_.onPointerDownCapture)})});ky.displayName=kk;var _k="DismissableLayerBranch",bk=v.forwardRef((e,t)=>{const n=v.useContext(wy),r=v.useRef(null),s=on(t,r);return v.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),d.jsx(yt.div,{...e,ref:s})});bk.displayName=_k;function Ek(e,t=globalThis==null?void 0:globalThis.document){const n=_i(e),r=v.useRef(!1),s=v.useRef(()=>{});return v.useEffect(()=>{const i=a=>{if(a.target&&!r.current){let l=function(){Sy(Sk,n,u,{discrete:!0})};const u={originalEvent:a};a.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=l,t.addEventListener("click",s.current,{once:!0})):l()}else t.removeEventListener("click",s.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",i),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Nk(e,t=globalThis==null?void 0:globalThis.document){const n=_i(e),r=v.useRef(!1);return v.useEffect(()=>{const s=i=>{i.target&&!r.current&&Sy(Ck,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Qf(){const e=new CustomEvent(Pu);document.dispatchEvent(e)}function Sy(e,t,n,{discrete:r}){const s=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?fk(s,i):s.dispatchEvent(i)}var al="focusScope.autoFocusOnMount",ll="focusScope.autoFocusOnUnmount",Wf={bubbles:!1,cancelable:!0},Tk="FocusScope",Cy=v.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:i,...o}=e,[a,l]=v.useState(null),u=_i(s),f=_i(i),h=v.useRef(null),m=on(t,y=>l(y)),w=v.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;v.useEffect(()=>{if(r){let y=function(g){if(w.paused||!a)return;const x=g.target;a.contains(x)?h.current=x:fn(h.current,{select:!0})},C=function(g){if(w.paused||!a)return;const x=g.relatedTarget;x!==null&&(a.contains(x)||fn(h.current,{select:!0}))},p=function(g){if(document.activeElement===document.body)for(const _ of g)_.removedNodes.length>0&&fn(a)};document.addEventListener("focusin",y),document.addEventListener("focusout",C);const c=new MutationObserver(p);return a&&c.observe(a,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",y),document.removeEventListener("focusout",C),c.disconnect()}}},[r,a,w.paused]),v.useEffect(()=>{if(a){Zf.add(w);const y=document.activeElement;if(!a.contains(y)){const p=new CustomEvent(al,Wf);a.addEventListener(al,u),a.dispatchEvent(p),p.defaultPrevented||(jk(Dk(_y(a)),{select:!0}),document.activeElement===y&&fn(a))}return()=>{a.removeEventListener(al,u),setTimeout(()=>{const p=new CustomEvent(ll,Wf);a.addEventListener(ll,f),a.dispatchEvent(p),p.defaultPrevented||fn(y??document.body,{select:!0}),a.removeEventListener(ll,f),Zf.remove(w)},0)}}},[a,u,f,w]);const S=v.useCallback(y=>{if(!n&&!r||w.paused)return;const C=y.key==="Tab"&&!y.altKey&&!y.ctrlKey&&!y.metaKey,p=document.activeElement;if(C&&p){const c=y.currentTarget,[g,x]=Rk(c);g&&x?!y.shiftKey&&p===x?(y.preventDefault(),n&&fn(g,{select:!0})):y.shiftKey&&p===g&&(y.preventDefault(),n&&fn(x,{select:!0})):p===c&&y.preventDefault()}},[n,r,w.paused]);return d.jsx(yt.div,{tabIndex:-1,...o,ref:m,onKeyDown:S})});Cy.displayName=Tk;function jk(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(fn(r,{select:t}),document.activeElement!==n)return}function Rk(e){const t=_y(e),n=Hf(t,e),r=Hf(t.reverse(),e);return[n,r]}function _y(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Hf(e,t){for(const n of e)if(!Pk(n,{upTo:t}))return n}function Pk(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Ik(e){return e instanceof HTMLInputElement&&"select"in e}function fn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Ik(e)&&t&&e.select()}}var Zf=Ok();function Ok(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Kf(e,t),e.unshift(t)},remove(t){var n;e=Kf(e,t),(n=e[0])==null||n.resume()}}}function Kf(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Dk(e){return e.filter(t=>t.tagName!=="A")}var Mk="Portal",by=v.forwardRef((e,t)=>{var a;const{container:n,...r}=e,[s,i]=v.useState(!1);Cs(()=>i(!0),[]);const o=n||s&&((a=globalThis==null?void 0:globalThis.document)==null?void 0:a.body);return o?z0.createPortal(d.jsx(yt.div,{...r,ref:t}),o):null});by.displayName=Mk;var ul=0;function Ak(){v.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Gf()),document.body.insertAdjacentElement("beforeend",e[1]??Gf()),ul++,()=>{ul===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),ul--}},[])}function Gf(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Ft=function(){return Ft=Object.assign||function(t){for(var n,r=1,s=arguments.length;r<s;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Ft.apply(this,arguments)};function Ey(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}function Fk(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,i;r<s;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var So="right-scroll-bar-position",Co="width-before-scroll-bar",Lk="with-scroll-bars-hidden",zk="--removed-body-scroll-bar-size";function cl(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function $k(e,t){var n=v.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var s=n.value;s!==r&&(n.value=r,n.callback(r,s))}}}})[0];return n.callback=t,n.facade}var Uk=typeof window<"u"?v.useLayoutEffect:v.useEffect,qf=new WeakMap;function Bk(e,t){var n=$k(null,function(r){return e.forEach(function(s){return cl(s,r)})});return Uk(function(){var r=qf.get(n);if(r){var s=new Set(r),i=new Set(e),o=n.current;s.forEach(function(a){i.has(a)||cl(a,null)}),i.forEach(function(a){s.has(a)||cl(a,o)})}qf.set(n,e)},[e]),n}function Vk(e){return e}function Qk(e,t){t===void 0&&(t=Vk);var n=[],r=!1,s={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var o=t(i,r);return n.push(o),function(){n=n.filter(function(a){return a!==o})}},assignSyncMedium:function(i){for(r=!0;n.length;){var o=n;n=[],o.forEach(i)}n={push:function(a){return i(a)},filter:function(){return n}}},assignMedium:function(i){r=!0;var o=[];if(n.length){var a=n;n=[],a.forEach(i),o=n}var l=function(){var f=o;o=[],f.forEach(i)},u=function(){return Promise.resolve().then(l)};u(),n={push:function(f){o.push(f),u()},filter:function(f){return o=o.filter(f),n}}}};return s}function Wk(e){e===void 0&&(e={});var t=Qk(null);return t.options=Ft({async:!0,ssr:!1},e),t}var Ny=function(e){var t=e.sideCar,n=Ey(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return v.createElement(r,Ft({},n))};Ny.isSideCarExport=!0;function Hk(e,t){return e.useMedium(t),Ny}var Ty=Wk(),dl=function(){},ba=v.forwardRef(function(e,t){var n=v.useRef(null),r=v.useState({onScrollCapture:dl,onWheelCapture:dl,onTouchMoveCapture:dl}),s=r[0],i=r[1],o=e.forwardProps,a=e.children,l=e.className,u=e.removeScrollBar,f=e.enabled,h=e.shards,m=e.sideCar,w=e.noRelative,S=e.noIsolation,y=e.inert,C=e.allowPinchZoom,p=e.as,c=p===void 0?"div":p,g=e.gapMode,x=Ey(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),_=m,b=Bk([n,t]),N=Ft(Ft({},x),s);return v.createElement(v.Fragment,null,f&&v.createElement(_,{sideCar:Ty,removeScrollBar:u,shards:h,noRelative:w,noIsolation:S,inert:y,setCallbacks:i,allowPinchZoom:!!C,lockRef:n,gapMode:g}),o?v.cloneElement(v.Children.only(a),Ft(Ft({},N),{ref:b})):v.createElement(c,Ft({},N,{className:l,ref:b}),a))});ba.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ba.classNames={fullWidth:Co,zeroRight:So};var Zk=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Kk(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Zk();return t&&e.setAttribute("nonce",t),e}function Gk(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function qk(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Yk=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Kk())&&(Gk(t,n),qk(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Xk=function(){var e=Yk();return function(t,n){v.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},jy=function(){var e=Xk(),t=function(n){var r=n.styles,s=n.dynamic;return e(r,s),null};return t},Jk={left:0,top:0,right:0,gap:0},fl=function(e){return parseInt(e||"",10)||0},eS=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],s=t[e==="padding"?"paddingRight":"marginRight"];return[fl(n),fl(r),fl(s)]},tS=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Jk;var t=eS(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},nS=jy(),Yr="data-scroll-locked",rS=function(e,t,n,r){var s=e.left,i=e.top,o=e.right,a=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(Lk,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(a,"px ").concat(r,`;
  }
  body[`).concat(Yr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(o,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(a,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(a,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(So,` {
    right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(Co,` {
    margin-right: `).concat(a,"px ").concat(r,`;
  }
  
  .`).concat(So," .").concat(So,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Co," .").concat(Co,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Yr,`] {
    `).concat(zk,": ").concat(a,`px;
  }
`)},Yf=function(){var e=parseInt(document.body.getAttribute(Yr)||"0",10);return isFinite(e)?e:0},sS=function(){v.useEffect(function(){return document.body.setAttribute(Yr,(Yf()+1).toString()),function(){var e=Yf()-1;e<=0?document.body.removeAttribute(Yr):document.body.setAttribute(Yr,e.toString())}},[])},iS=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,s=r===void 0?"margin":r;sS();var i=v.useMemo(function(){return tS(s)},[s]);return v.createElement(nS,{styles:rS(i,!t,s,n?"":"!important")})},Iu=!1;if(typeof window<"u")try{var so=Object.defineProperty({},"passive",{get:function(){return Iu=!0,!0}});window.addEventListener("test",so,so),window.removeEventListener("test",so,so)}catch{Iu=!1}var Tr=Iu?{passive:!1}:!1,oS=function(e){return e.tagName==="TEXTAREA"},Ry=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!oS(e)&&n[t]==="visible")},aS=function(e){return Ry(e,"overflowY")},lS=function(e){return Ry(e,"overflowX")},Xf=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var s=Py(e,r);if(s){var i=Iy(e,r),o=i[1],a=i[2];if(o>a)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},uS=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},cS=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Py=function(e,t){return e==="v"?aS(t):lS(t)},Iy=function(e,t){return e==="v"?uS(t):cS(t)},dS=function(e,t){return e==="h"&&t==="rtl"?-1:1},fS=function(e,t,n,r,s){var i=dS(e,window.getComputedStyle(t).direction),o=i*r,a=n.target,l=t.contains(a),u=!1,f=o>0,h=0,m=0;do{if(!a)break;var w=Iy(e,a),S=w[0],y=w[1],C=w[2],p=y-C-i*S;(S||p)&&Py(e,a)&&(h+=p,m+=S);var c=a.parentNode;a=c&&c.nodeType===Node.DOCUMENT_FRAGMENT_NODE?c.host:c}while(!l&&a!==document.body||l&&(t.contains(a)||t===a));return(f&&Math.abs(h)<1||!f&&Math.abs(m)<1)&&(u=!0),u},io=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Jf=function(e){return[e.deltaX,e.deltaY]},eh=function(e){return e&&"current"in e?e.current:e},hS=function(e,t){return e[0]===t[0]&&e[1]===t[1]},pS=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},mS=0,jr=[];function yS(e){var t=v.useRef([]),n=v.useRef([0,0]),r=v.useRef(),s=v.useState(mS++)[0],i=v.useState(jy)[0],o=v.useRef(e);v.useEffect(function(){o.current=e},[e]),v.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var y=Fk([e.lockRef.current],(e.shards||[]).map(eh),!0).filter(Boolean);return y.forEach(function(C){return C.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),y.forEach(function(C){return C.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var a=v.useCallback(function(y,C){if("touches"in y&&y.touches.length===2||y.type==="wheel"&&y.ctrlKey)return!o.current.allowPinchZoom;var p=io(y),c=n.current,g="deltaX"in y?y.deltaX:c[0]-p[0],x="deltaY"in y?y.deltaY:c[1]-p[1],_,b=y.target,N=Math.abs(g)>Math.abs(x)?"h":"v";if("touches"in y&&N==="h"&&b.type==="range")return!1;var E=Xf(N,b);if(!E)return!0;if(E?_=N:(_=N==="v"?"h":"v",E=Xf(N,b)),!E)return!1;if(!r.current&&"changedTouches"in y&&(g||x)&&(r.current=_),!_)return!0;var L=r.current||_;return fS(L,C,y,L==="h"?g:x)},[]),l=v.useCallback(function(y){var C=y;if(!(!jr.length||jr[jr.length-1]!==i)){var p="deltaY"in C?Jf(C):io(C),c=t.current.filter(function(_){return _.name===C.type&&(_.target===C.target||C.target===_.shadowParent)&&hS(_.delta,p)})[0];if(c&&c.should){C.cancelable&&C.preventDefault();return}if(!c){var g=(o.current.shards||[]).map(eh).filter(Boolean).filter(function(_){return _.contains(C.target)}),x=g.length>0?a(C,g[0]):!o.current.noIsolation;x&&C.cancelable&&C.preventDefault()}}},[]),u=v.useCallback(function(y,C,p,c){var g={name:y,delta:C,target:p,should:c,shadowParent:gS(p)};t.current.push(g),setTimeout(function(){t.current=t.current.filter(function(x){return x!==g})},1)},[]),f=v.useCallback(function(y){n.current=io(y),r.current=void 0},[]),h=v.useCallback(function(y){u(y.type,Jf(y),y.target,a(y,e.lockRef.current))},[]),m=v.useCallback(function(y){u(y.type,io(y),y.target,a(y,e.lockRef.current))},[]);v.useEffect(function(){return jr.push(i),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:m}),document.addEventListener("wheel",l,Tr),document.addEventListener("touchmove",l,Tr),document.addEventListener("touchstart",f,Tr),function(){jr=jr.filter(function(y){return y!==i}),document.removeEventListener("wheel",l,Tr),document.removeEventListener("touchmove",l,Tr),document.removeEventListener("touchstart",f,Tr)}},[]);var w=e.removeScrollBar,S=e.inert;return v.createElement(v.Fragment,null,S?v.createElement(i,{styles:pS(s)}):null,w?v.createElement(iS,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function gS(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const vS=Hk(Ty,yS);var Oy=v.forwardRef(function(e,t){return v.createElement(ba,Ft({},e,{ref:t,sideCar:vS}))});Oy.classNames=ba.classNames;var xS=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Rr=new WeakMap,oo=new WeakMap,ao={},hl=0,Dy=function(e){return e&&(e.host||Dy(e.parentNode))},wS=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Dy(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},kS=function(e,t,n,r){var s=wS(t,Array.isArray(e)?e:[e]);ao[n]||(ao[n]=new WeakMap);var i=ao[n],o=[],a=new Set,l=new Set(s),u=function(h){!h||a.has(h)||(a.add(h),u(h.parentNode))};s.forEach(u);var f=function(h){!h||l.has(h)||Array.prototype.forEach.call(h.children,function(m){if(a.has(m))f(m);else try{var w=m.getAttribute(r),S=w!==null&&w!=="false",y=(Rr.get(m)||0)+1,C=(i.get(m)||0)+1;Rr.set(m,y),i.set(m,C),o.push(m),y===1&&S&&oo.set(m,!0),C===1&&m.setAttribute(n,"true"),S||m.setAttribute(r,"true")}catch(p){console.error("aria-hidden: cannot operate on ",m,p)}})};return f(t),a.clear(),hl++,function(){o.forEach(function(h){var m=Rr.get(h)-1,w=i.get(h)-1;Rr.set(h,m),i.set(h,w),m||(oo.has(h)||h.removeAttribute(r),oo.delete(h)),w||h.removeAttribute(n)}),hl--,hl||(Rr=new WeakMap,Rr=new WeakMap,oo=new WeakMap,ao={})}},SS=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),s=xS(e);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live], script"))),kS(r,s,n,"aria-hidden")):function(){return null}},Ea="Dialog",[My,XS]=cy(Ea),[CS,jt]=My(Ea),Ay=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:s,onOpenChange:i,modal:o=!0}=e,a=v.useRef(null),l=v.useRef(null),[u,f]=dy({prop:r,defaultProp:s??!1,onChange:i,caller:Ea});return d.jsx(CS,{scope:t,triggerRef:a,contentRef:l,contentId:ol(),titleId:ol(),descriptionId:ol(),open:u,onOpenChange:f,onOpenToggle:v.useCallback(()=>f(h=>!h),[f]),modal:o,children:n})};Ay.displayName=Ea;var Fy="DialogTrigger",_S=v.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=jt(Fy,n),i=on(t,s.triggerRef);return d.jsx(yt.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Zc(s.open),...r,ref:i,onClick:Ut(e.onClick,s.onOpenToggle)})});_S.displayName=Fy;var Wc="DialogPortal",[bS,Ly]=My(Wc,{forceMount:void 0}),zy=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:s}=e,i=jt(Wc,t);return d.jsx(bS,{scope:t,forceMount:n,children:v.Children.map(r,o=>d.jsx(Mi,{present:n||i.open,children:d.jsx(by,{asChild:!0,container:s,children:o})}))})};zy.displayName=Wc;var sa="DialogOverlay",$y=v.forwardRef((e,t)=>{const n=Ly(sa,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,i=jt(sa,e.__scopeDialog);return i.modal?d.jsx(Mi,{present:r||i.open,children:d.jsx(NS,{...s,ref:t})}):null});$y.displayName=sa;var ES=Mc("DialogOverlay.RemoveScroll"),NS=v.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=jt(sa,n);return d.jsx(Oy,{as:ES,allowPinchZoom:!0,shards:[s.contentRef],children:d.jsx(yt.div,{"data-state":Zc(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),_r="DialogContent",Uy=v.forwardRef((e,t)=>{const n=Ly(_r,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,i=jt(_r,e.__scopeDialog);return d.jsx(Mi,{present:r||i.open,children:i.modal?d.jsx(TS,{...s,ref:t}):d.jsx(jS,{...s,ref:t})})});Uy.displayName=_r;var TS=v.forwardRef((e,t)=>{const n=jt(_r,e.__scopeDialog),r=v.useRef(null),s=on(t,n.contentRef,r);return v.useEffect(()=>{const i=r.current;if(i)return SS(i)},[]),d.jsx(By,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Ut(e.onCloseAutoFocus,i=>{var o;i.preventDefault(),(o=n.triggerRef.current)==null||o.focus()}),onPointerDownOutside:Ut(e.onPointerDownOutside,i=>{const o=i.detail.originalEvent,a=o.button===0&&o.ctrlKey===!0;(o.button===2||a)&&i.preventDefault()}),onFocusOutside:Ut(e.onFocusOutside,i=>i.preventDefault())})}),jS=v.forwardRef((e,t)=>{const n=jt(_r,e.__scopeDialog),r=v.useRef(!1),s=v.useRef(!1);return d.jsx(By,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var o,a;(o=e.onCloseAutoFocus)==null||o.call(e,i),i.defaultPrevented||(r.current||(a=n.triggerRef.current)==null||a.focus(),i.preventDefault()),r.current=!1,s.current=!1},onInteractOutside:i=>{var l,u;(l=e.onInteractOutside)==null||l.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const o=i.target;((u=n.triggerRef.current)==null?void 0:u.contains(o))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&s.current&&i.preventDefault()}})}),By=v.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:i,...o}=e,a=jt(_r,n),l=v.useRef(null),u=on(t,l);return Ak(),d.jsxs(d.Fragment,{children:[d.jsx(Cy,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:i,children:d.jsx(ky,{role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":Zc(a.open),...o,ref:u,onDismiss:()=>a.onOpenChange(!1)})}),d.jsxs(d.Fragment,{children:[d.jsx(RS,{titleId:a.titleId}),d.jsx(IS,{contentRef:l,descriptionId:a.descriptionId})]})]})}),Hc="DialogTitle",Vy=v.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=jt(Hc,n);return d.jsx(yt.h2,{id:s.titleId,...r,ref:t})});Vy.displayName=Hc;var Qy="DialogDescription",Wy=v.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=jt(Qy,n);return d.jsx(yt.p,{id:s.descriptionId,...r,ref:t})});Wy.displayName=Qy;var Hy="DialogClose",Zy=v.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=jt(Hy,n);return d.jsx(yt.button,{type:"button",...r,ref:t,onClick:Ut(e.onClick,()=>s.onOpenChange(!1))})});Zy.displayName=Hy;function Zc(e){return e?"open":"closed"}var Ky="DialogTitleWarning",[JS,Gy]=tk(Ky,{contentName:_r,titleName:Hc,docsSlug:"dialog"}),RS=({titleId:e})=>{const t=Gy(Ky),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return v.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},PS="DialogDescriptionWarning",IS=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Gy(PS).contentName}}.`;return v.useEffect(()=>{var i;const s=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},OS=Ay,DS=zy,qy=$y,Yy=Uy,Xy=Vy,Jy=Wy,MS=Zy;const AS=OS,FS=DS,eg=v.forwardRef(({className:e,...t},n)=>d.jsx(qy,{ref:n,className:Y("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));eg.displayName=qy.displayName;const tg=v.forwardRef(({className:e,children:t,...n},r)=>d.jsxs(FS,{children:[d.jsx(eg,{}),d.jsxs(Yy,{ref:r,className:Y("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,d.jsxs(MS,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[d.jsx(kr,{className:"h-4 w-4"}),d.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));tg.displayName=Yy.displayName;const ng=({className:e,...t})=>d.jsx("div",{className:Y("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ng.displayName="DialogHeader";const rg=({className:e,...t})=>d.jsx("div",{className:Y("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});rg.displayName="DialogFooter";const sg=v.forwardRef(({className:e,...t},n)=>d.jsx(Xy,{ref:n,className:Y("text-lg font-semibold leading-none tracking-tight",e),...t}));sg.displayName=Xy.displayName;const ig=v.forwardRef(({className:e,...t},n)=>d.jsx(Jy,{ref:n,className:Y("text-sm text-muted-foreground",e),...t}));ig.displayName=Jy.displayName;function LS({open:e,onOpenChange:t,onConfirm:n,count:r,isLoading:s=!1}){const i=()=>{n(),t(!1)},o=()=>{t(!1)};return d.jsx(AS,{open:e,onOpenChange:t,children:d.jsxs(tg,{className:"sm:max-w-[425px]",children:[d.jsxs(ng,{className:"text-center",children:[d.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:d.jsx(wi,{className:"h-6 w-6 text-red-600"})}),d.jsx(sg,{className:"text-lg font-semibold text-gray-900",children:"批量删除确认"}),d.jsxs(ig,{className:"text-sm text-gray-500 mt-2",children:["确定要删除选中的 ",d.jsx("span",{className:"font-medium text-red-600",children:r})," 个任务吗？",d.jsx("br",{}),"此操作无法撤销。"]})]}),d.jsxs(rg,{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3 mt-6",children:[d.jsxs(X,{variant:"outline",onClick:o,disabled:s,className:"sm:w-auto w-full",children:[d.jsx(kr,{className:"h-4 w-4 mr-2"}),"取消"]}),d.jsxs(X,{variant:"destructive",onClick:i,disabled:s,className:"sm:w-auto w-full",children:[d.jsx(wi,{className:"h-4 w-4 mr-2"}),s?"删除中...":`删除 ${r} 个任务`]})]})]})})}function zS(){const[e,t]=v.useState(!1),[n,r]=v.useState(!1),[s,i]=v.useState(!1),o=v.useRef(null),{selectedCount:a,isSelectionMode:l,exitSelectionMode:u,clearSelection:f,selectedTaskIds:h,selectAllTasks:m,addDeletedTask:w}=$c(),{data:S=[]}=zc(),{showBatchDeleteSuccess:y,showBatchRestoreSuccess:C,showError:p}=Bc();if(v.useEffect(()=>{const b=l&&a>0;b&&!s?(i(!0),setTimeout(()=>{o.current&&Kw(o.current)},10)):!b&&s&&(o.current?Gw(o.current).then(()=>{i(!1)}):i(!1))},[l,a,s]),!s)return null;const c=S.filter(b=>!b.deletedAt),g=a===c.length,x=()=>{g?f():m(c.map(b=>b.id))},_=async()=>{if(!n){r(!0);try{if(h.length===0){p("批量删除失败","没有选中任何任务");return}const b=S.filter(E=>h.includes(E.id)&&!E.deletedAt);if(b.length===0){p("批量删除失败","选中的任务已被删除或不存在");return}console.log("Attempting to batch delete tasks:",b.map(E=>E.id));const N=await window.electronAPI.task.batchSoftDelete(b.map(E=>E.id));console.log("Batch delete result:",N),N&&N.deletedCount>0?(b.forEach(E=>{N.deletedTaskIds.includes(E.id)&&w({id:E.id,content:E.content,deletedAt:Date.now()})}),y(N.deletedCount,async()=>{try{const E=await window.electronAPI.task.batchRestore(N.deletedTaskIds);C(E.restoredCount||N.deletedTaskIds.length)}catch(E){console.error("Failed to restore tasks:",E);const L=E instanceof Error?E.message:"未知错误";p("撤销失败",`无法恢复已删除的任务: ${L}`)}}),u()):p("批量删除失败","没有任务被成功删除")}catch(b){console.error("Batch delete failed:",b);const N=b instanceof Error?b.message:"未知错误";p("批量删除失败",`删除操作失败: ${N}`)}finally{r(!1)}}};return d.jsxs(d.Fragment,{children:[d.jsxs("div",{ref:o,className:Y("fixed bottom-4 left-1/2 transform -translate-x-1/2 z-40","bg-white border border-gray-200 rounded-lg shadow-lg p-3","flex items-center gap-3 min-w-[300px]","transition-all duration-300"),children:[d.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[d.jsx(wu,{className:"h-4 w-4"}),"已选择 ",a," 个任务"]}),d.jsxs("div",{className:"flex items-center gap-2 ml-auto",children:[d.jsx(X,{size:"sm",variant:"outline",onClick:x,children:g?d.jsxs(d.Fragment,{children:[d.jsx(Fx,{className:"h-4 w-4 mr-1"}),"取消全选"]}):d.jsxs(d.Fragment,{children:[d.jsx(wu,{className:"h-4 w-4 mr-1"}),"全选"]})}),d.jsxs(X,{size:"sm",variant:"outline",onClick:f,children:[d.jsx(kr,{className:"h-4 w-4 mr-1"}),"取消选择"]}),d.jsxs(X,{size:"sm",variant:"destructive",onClick:()=>t(!0),disabled:n,children:[d.jsx(wi,{className:"h-4 w-4 mr-1"}),n?"删除中...":"删除选中"]}),d.jsx(X,{size:"sm",variant:"ghost",onClick:u,children:d.jsx(kr,{className:"h-4 w-4"})})]})]}),d.jsx(LS,{open:e,onOpenChange:t,onConfirm:_,count:a,isLoading:n})]})}function $S(){const{data:e=[],isLoading:t,error:n}=zc(),r=ly(),s=ay(),{activeView:i,searchQuery:o,sortBy:a,showCompleted:l}=Ca(),{parentTasks:u,taskMap:f}=v.useMemo(()=>{const S=Date.now(),y=new Date;y.setHours(0,0,0,0);const C=y.getTime(),p=C+24*60*60*1e3;let c=e.filter(x=>i==="completed"||!x.parentTaskId);switch(i){case"today":c=c.filter(x=>!x.isCompleted&&x.dueDate&&x.dueDate>=C&&x.dueDate<p);break;case"important":c=c.filter(x=>!x.isCompleted&&(x.priority===3||x.isImportant));break;case"planned":c=c.filter(x=>!x.isCompleted&&x.dueDate&&x.dueDate>S);break;case"completed":c=c.filter(x=>x.isCompleted);break;case"all":default:l||(c=c.filter(x=>!x.isCompleted));break}if(o.trim()){const x=o.toLowerCase();c=c.filter(_=>_.content.toLowerCase().includes(x))}c.sort((x,_)=>{switch(a){case"priority":return _.priority-x.priority;case"dueDate":return!x.dueDate&&!_.dueDate?0:x.dueDate?_.dueDate?x.dueDate-_.dueDate:-1:1;case"created":return _.createdAt-x.createdAt;case"alphabetical":return x.content.localeCompare(_.content);case"custom":default:return x.orderIndex-_.orderIndex}});const g=new Map;return e.forEach(x=>{x.parentTaskId&&(g.has(x.parentTaskId)||g.set(x.parentTaskId,[]),g.get(x.parentTaskId).push(x))}),g.forEach(x=>{x.sort((_,b)=>_.orderIndex-b.orderIndex)}),{parentTasks:c,taskMap:g}},[e,i,o,a,l]),h=S=>{const y=e.find(C=>C.id===S);y&&r.mutate({id:S,input:{isCompleted:!y.isCompleted}})},m=async S=>{try{await s.mutateAsync({content:"新子任务",priority:2,parentTaskId:S,taskType:"subtask"})}catch(y){console.error("Failed to create subtask:",y)}},w=S=>{console.log("Edit task:",S)};if(t)return d.jsx("div",{className:"space-y-4",children:Array.from({length:3}).map((S,y)=>d.jsx("div",{className:"h-20 bg-muted/50 rounded-xl animate-pulse"},y))});if(n)return d.jsxs("div",{className:"text-center py-12",children:[d.jsx("p",{className:"text-red-500 mb-4",children:"加载任务失败"}),d.jsx("p",{className:"text-muted-foreground text-sm",children:"请刷新页面重试"})]});if(u.length===0){const S=(()=>{if(o.trim())return`没有找到包含 "${o}" 的任务`;switch(i){case"today":return"今天没有安排任务";case"important":return"没有重要任务";case"planned":return"没有已计划的任务";case"completed":return"没有已完成的任务";case"all":default:return l?"还没有任务，创建第一个任务吧！":"没有待完成的任务"}})();return d.jsxs("div",{className:"text-center py-16",children:[d.jsx("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:d.jsx("span",{className:"text-2xl",children:"📝"})}),d.jsx("p",{className:"text-muted-foreground text-lg mb-2",children:S}),d.jsx("p",{className:"text-muted-foreground text-sm",children:!o.trim()&&i==="all"&&l&&'点击上方的"添加任务"按钮开始'})]})}return d.jsxs(d.Fragment,{children:[d.jsx("div",{className:"space-y-4",children:u.map(S=>d.jsx(gk,{task:S,subtasks:f.get(S.id)||[],onToggleComplete:h,onAddSubtask:m,onEdit:w},S.id))}),d.jsx(zS,{})]})}function US({className:e}){const[t,n]=v.useState(!1),{recentlyDeletedTasks:r,hasUndoableTasks:s,removeDeletedTask:i,clearDeletedTasks:o}=$c(),{showSuccess:a,showError:l}=Bc();v.useEffect(()=>{n(s)},[s]);const u=async w=>{try{await window.electronAPI.task.restore(w.id),i(w.id),a("撤销成功",`"${w.content}" 已恢复`)}catch{l("撤销失败","无法恢复已删除的任务")}},f=async()=>{try{const w=r.map(S=>S.id);await window.electronAPI.task.batchRestore(w),o(),a("批量撤销成功",`已恢复 ${w.length} 个任务`)}catch{l("批量撤销失败","无法恢复部分任务")}},h=()=>{o()},m=w=>{const S=3e4-(Date.now()-w);return`${Math.max(0,Math.ceil(S/1e3))}s`};return!t||r.length===0?null:d.jsx("div",{className:Y("fixed top-4 right-4 z-50 max-w-sm","animate-in slide-in-from-top-full duration-300",e),children:d.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 space-y-3",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center gap-2",children:[d.jsx(wi,{className:"h-4 w-4 text-red-600"}),d.jsxs("span",{className:"text-sm font-medium text-gray-900",children:["最近删除 (",r.length,")"]})]}),d.jsx(X,{variant:"ghost",size:"sm",onClick:h,className:"p-1 h-6 w-6",children:d.jsx(kr,{className:"h-4 w-4"})})]}),d.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:r.map(w=>d.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded-md",children:[d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsx("p",{className:"text-sm text-gray-900 truncate",children:w.content}),d.jsxs("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[d.jsx(Um,{className:"h-3 w-3"}),m(w.deletedAt)]})]}),d.jsxs(X,{variant:"outline",size:"sm",onClick:()=>u(w),className:"ml-2 h-7 px-2 text-xs",children:[d.jsx(Su,{className:"h-3 w-3 mr-1"}),"撤销"]})]},w.id))}),r.length>1&&d.jsx("div",{className:"pt-2 border-t border-gray-200",children:d.jsxs(X,{variant:"outline",size:"sm",onClick:f,className:"w-full",children:[d.jsx(Su,{className:"h-4 w-4 mr-2"}),"撤销全部 (",r.length,")"]})})]})})}function BS(){const{data:e,isLoading:t}=Fw();if(t||!e)return d.jsx("div",{className:"grid grid-cols-4 gap-4",children:Array.from({length:4}).map((s,i)=>d.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[d.jsx("div",{className:"h-4 bg-gray-200 rounded animate-pulse mb-2"}),d.jsx("div",{className:"h-6 bg-gray-200 rounded animate-pulse"})]},i))});const n=e.total>0?Math.round(e.completed/e.total*100):0,r=[{label:"总任务",value:e.total,icon:ku,color:"text-gray-600",bgColor:"bg-gray-50"},{label:"已完成",value:e.completed,icon:$m,color:"text-green-600",bgColor:"bg-green-50"},{label:"待完成",value:e.pending,icon:Um,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"已逾期",value:e.overdue,icon:Lm,color:"text-red-600",bgColor:"bg-red-50"}];return d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{className:"bg-white p-4 rounded-lg border border-gray-200",children:[d.jsxs("div",{className:"flex items-center justify-between mb-2",children:[d.jsx("span",{className:"text-sm font-medium text-gray-700",children:"完成率"}),d.jsxs("span",{className:"text-lg font-bold text-gray-900",children:[n,"%"]})]}),d.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:d.jsx("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:`${n}%`}})})]}),d.jsx("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:r.map(s=>{const i=s.icon;return d.jsx("div",{className:`p-4 rounded-lg border border-gray-200 ${s.bgColor}`,children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{children:[d.jsx("p",{className:"text-sm font-medium text-gray-600",children:s.label}),d.jsx("p",{className:`text-2xl font-bold ${s.color}`,children:s.value})]}),d.jsx(i,{className:`h-8 w-8 ${s.color}`})]})},s.label)})})]})}function VS(){const e=v.useRef(null),{setTaskInputFocused:t}=Ca(),n=()=>{var r;t(!0),(r=e.current)==null||r.scrollIntoView({behavior:"smooth",block:"center"})};return d.jsxs($w,{onAddTaskClick:n,children:[d.jsxs("div",{className:"space-y-6",children:[d.jsx("div",{className:"bg-gradient-to-r from-primary/10 to-primary/5 rounded-xl p-6 border border-primary/20",children:d.jsx(BS,{})}),d.jsx("div",{ref:e,children:d.jsx(Qw,{})}),d.jsx($S,{})]}),d.jsx(US,{})]})}const og=Bt.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Y("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));og.displayName="Card";const ag=Bt.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Y("flex flex-col space-y-1.5 p-6",e),...t}));ag.displayName="CardHeader";const lg=Bt.forwardRef(({className:e,...t},n)=>d.jsx("h3",{ref:n,className:Y("text-2xl font-semibold leading-none tracking-tight",e),...t}));lg.displayName="CardTitle";const ug=Bt.forwardRef(({className:e,...t},n)=>d.jsx("p",{ref:n,className:Y("text-sm text-muted-foreground",e),...t}));ug.displayName="CardDescription";const cg=Bt.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Y("p-6 pt-0",e),...t}));cg.displayName="CardContent";const QS=Bt.forwardRef(({className:e,...t},n)=>d.jsx("div",{ref:n,className:Y("flex items-center p-6 pt-0",e),...t}));QS.displayName="CardFooter";class Kc extends v.Component{constructor(n){super(n);vt(this,"retryCount",0);vt(this,"maxRetries",3);vt(this,"reportError",(n,r)=>{var i;const s={id:this.state.errorId,message:n.message,stack:n.stack,componentStack:r.componentStack,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href,retryCount:this.retryCount};try{console.error("Error Report:",s),(i=window.electronAPI)!=null&&i.app&&console.log("Error logged to main process")}catch(o){console.error("Failed to report error:",o)}});vt(this,"handleReset",()=>{this.retryCount++,this.setState({hasError:!1,error:void 0,errorInfo:void 0,errorId:this.generateErrorId()})});vt(this,"handleRestart",()=>{var n,r;(r=(n=window.electronAPI)==null?void 0:n.app)!=null&&r.quit?window.electronAPI.app.quit():window.location.reload()});vt(this,"handleGoHome",()=>{this.handleReset()});vt(this,"copyErrorDetails",()=>{var r,s,i;const n={id:this.state.errorId,message:(r=this.state.error)==null?void 0:r.message,stack:(s=this.state.error)==null?void 0:s.stack,componentStack:(i=this.state.errorInfo)==null?void 0:i.componentStack,timestamp:new Date().toISOString()};navigator.clipboard.writeText(JSON.stringify(n,null,2)).then(()=>{console.log("Error details copied to clipboard")}).catch(o=>{console.error("Failed to copy error details:",o)})});this.state={hasError:!1,errorId:this.generateErrorId()}}static getDerivedStateFromError(n){return{hasError:!0,error:n,errorId:Kc.prototype.generateErrorId()}}componentDidCatch(n,r){var s,i;console.error("ErrorBoundary caught an error:",n,r),this.setState({errorInfo:r}),(i=(s=this.props).onError)==null||i.call(s,n,r),this.reportError(n,r)}generateErrorId(){return`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}render(){var n,r;if(this.state.hasError){if(this.props.fallback)return this.props.fallback;const s=this.retryCount<this.maxRetries,i=((n=this.state.error)==null?void 0:n.name)!=="ChunkLoadError";return d.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background p-4",children:d.jsxs(og,{className:"max-w-lg w-full",children:[d.jsxs(ag,{className:"text-center",children:[d.jsx("div",{className:"mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center",children:d.jsx(zm,{className:"h-6 w-6 text-destructive"})}),d.jsx(lg,{className:"text-xl font-semibold",children:"应用出现错误"}),d.jsx(ug,{children:"很抱歉，应用遇到了意外错误。您可以尝试以下操作来恢复。"})]}),d.jsxs(cg,{className:"space-y-4",children:[d.jsxs("div",{className:"bg-muted p-3 rounded-md",children:[d.jsx("p",{className:"text-sm font-medium text-muted-foreground mb-1",children:"错误信息:"}),d.jsx("p",{className:"text-sm text-foreground",children:((r=this.state.error)==null?void 0:r.message)||"未知错误"}),d.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["错误ID: ",this.state.errorId]})]}),d.jsxs("div",{className:"space-y-2",children:[s&&i&&d.jsxs(X,{onClick:this.handleReset,className:"w-full",variant:"default",children:[d.jsx(yf,{className:"mr-2 h-4 w-4"}),"重试 (",this.maxRetries-this.retryCount," 次机会)"]}),d.jsxs(X,{onClick:this.handleGoHome,variant:"outline",className:"w-full",children:[d.jsx(Bm,{className:"mr-2 h-4 w-4"}),"返回主页"]}),d.jsxs(X,{onClick:this.handleRestart,variant:"outline",className:"w-full",children:[d.jsx(yf,{className:"mr-2 h-4 w-4"}),"重启应用"]})]}),!1,d.jsx("div",{className:"text-center text-xs text-muted-foreground",children:"如果问题持续存在，请联系技术支持"})]})]})})}return this.props.children}}class WS{constructor(t={}){vt(this,"config");vt(this,"sessionId");vt(this,"reportQueue",[]);this.config={enableConsoleLogging:!0,enableLocalStorage:!0,enableRemoteReporting:!1,maxLocalReports:100,...t},this.sessionId=this.generateSessionId(),this.setupGlobalErrorHandlers(),this.loadStoredReports()}generateSessionId(){return`session_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}generateErrorId(){return`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}setupGlobalErrorHandlers(){window.addEventListener("error",t=>{var n;this.reportError({type:"javascript",message:t.message,stack:(n=t.error)==null?void 0:n.stack,severity:"high",context:{filename:t.filename,lineno:t.lineno,colno:t.colno}})}),window.addEventListener("unhandledrejection",t=>{var n,r;this.reportError({type:"promise",message:((n=t.reason)==null?void 0:n.message)||"Unhandled Promise Rejection",stack:(r=t.reason)==null?void 0:r.stack,severity:"medium",context:{reason:t.reason}})})}loadStoredReports(){if(this.config.enableLocalStorage)try{const t=localStorage.getItem("errorReports");t&&(this.reportQueue=JSON.parse(t))}catch(t){console.warn("Failed to load stored error reports:",t)}}saveReportsToStorage(){if(this.config.enableLocalStorage)try{const t=this.reportQueue.slice(-this.config.maxLocalReports);localStorage.setItem("errorReports",JSON.stringify(t))}catch(t){console.warn("Failed to save error reports to storage:",t)}}reportError(t){var r,s;const n={id:this.generateErrorId(),type:"javascript",message:"Unknown error",timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href,sessionId:this.sessionId,environment:"production",severity:"medium",...t};return(s=(r=window.electronAPI)==null?void 0:r.app)!=null&&s.getVersion&&window.electronAPI.app.getVersion().then(i=>{n.buildVersion=i}).catch(()=>{}),this.reportQueue.push(n),this.config.enableConsoleLogging&&console.error("Error reported:",n),this.saveReportsToStorage(),this.config.enableRemoteReporting&&this.sendToRemoteService(n),this.sendToMainProcess(n),n.id}reportReactError(t,n,r){return this.reportError({type:"react",message:t.message,stack:t.stack,componentStack:n.componentStack,severity:"high",context:r})}async sendToRemoteService(t){if(this.config.reportingEndpoint)try{const n=await fetch(this.config.reportingEndpoint,{method:"POST",headers:{"Content-Type":"application/json",...this.config.apiKey&&{Authorization:`Bearer ${this.config.apiKey}`}},body:JSON.stringify(t)});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);console.log("Error report sent successfully:",t.id)}catch(n){console.warn("Failed to send error report to remote service:",n)}}sendToMainProcess(t){var n;try{(n=window.electronAPI)!=null&&n.invoke&&window.electronAPI.invoke("error:report",t).catch(r=>{console.warn("Failed to send error report to main process:",r)})}catch(r){console.warn("Failed to communicate with main process:",r)}}getReports(){return[...this.reportQueue]}getReportById(t){return this.reportQueue.find(n=>n.id===t)}clearReports(){this.reportQueue=[],this.saveReportsToStorage()}getReportsByType(t){return this.reportQueue.filter(n=>n.type===t)}getReportsBySeverity(t){return this.reportQueue.filter(n=>n.severity===t)}exportReports(){return JSON.stringify(this.reportQueue,null,2)}updateConfig(t){this.config={...this.config,...t}}getStats(){const t=this.reportQueue,n=Date.now(),r=60*60*1e3,s=24*r;return{total:t.length,lastHour:t.filter(i=>n-new Date(i.timestamp).getTime()<r).length,lastDay:t.filter(i=>n-new Date(i.timestamp).getTime()<s).length,byType:{react:t.filter(i=>i.type==="react").length,javascript:t.filter(i=>i.type==="javascript").length,promise:t.filter(i=>i.type==="promise").length,network:t.filter(i=>i.type==="network").length},bySeverity:{low:t.filter(i=>i.severity==="low").length,medium:t.filter(i=>i.severity==="medium").length,high:t.filter(i=>i.severity==="high").length,critical:t.filter(i=>i.severity==="critical").length}}}}const HS=new WS({enableConsoleLogging:!0,enableLocalStorage:!0,enableRemoteReporting:!0,maxLocalReports:100}),ZS=(e,t,n)=>HS.reportReactError(e,t,n);function KS(){return v.useEffect(()=>{Yw()},[]),d.jsx(Kc,{onError:ZS,children:d.jsx(ox,{client:xx,children:d.jsx(rw,{children:d.jsxs(Xw,{children:[d.jsx("div",{className:"min-h-screen bg-background text-foreground theme-transition",children:d.jsx(VS,{})}),d.jsx(vx,{initialIsOpen:!1})]})})})})}pl.createRoot(document.getElementById("root")).render(d.jsx(Bt.StrictMode,{children:d.jsx(KS,{})}));
