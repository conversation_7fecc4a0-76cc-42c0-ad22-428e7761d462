/**
 * UI Store 单元测试
 * 测试 Zustand store 的状态管理功能
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useUIStore } from '../uiStore'

describe('useUIStore', () => {
  beforeEach(() => {
    // 重置 store 状态
    useUIStore.setState({
      taskFilter: 'all',
      sidebarOpen: true,
      theme: 'system',
      isAddingTask: false,
      editingTaskId: null,
      searchQuery: '',
      sortBy: 'custom',
    })
  })

  describe('Task Filter', () => {
    it('should have default task filter as "all"', () => {
      const { result } = renderHook(() => useUIStore())
      expect(result.current.taskFilter).toBe('all')
    })

    it('should update task filter', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setTaskFilter('completed')
      })

      expect(result.current.taskFilter).toBe('completed')
    })

    it('should update task filter to pending', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setTaskFilter('pending')
      })

      expect(result.current.taskFilter).toBe('pending')
    })
  })

  describe('Sidebar State', () => {
    it('should have default sidebar open as true', () => {
      const { result } = renderHook(() => useUIStore())
      expect(result.current.sidebarOpen).toBe(true)
    })

    it('should set sidebar open state', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setSidebarOpen(false)
      })

      expect(result.current.sidebarOpen).toBe(false)
    })

    it('should toggle sidebar state', () => {
      const { result } = renderHook(() => useUIStore())

      // 初始状态是 true
      expect(result.current.sidebarOpen).toBe(true)

      act(() => {
        result.current.toggleSidebar()
      })

      expect(result.current.sidebarOpen).toBe(false)

      act(() => {
        result.current.toggleSidebar()
      })

      expect(result.current.sidebarOpen).toBe(true)
    })
  })

  describe('Theme State', () => {
    it('should have default theme as "system"', () => {
      const { result } = renderHook(() => useUIStore())
      expect(result.current.theme).toBe('system')
    })

    it('should update theme', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setTheme('dark')
      })

      expect(result.current.theme).toBe('dark')
    })

    it('should update theme to light', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setTheme('light')
      })

      expect(result.current.theme).toBe('light')
    })
  })

  describe('Task Input State', () => {
    it('should have default isAddingTask as false', () => {
      const { result } = renderHook(() => useUIStore())
      expect(result.current.isAddingTask).toBe(false)
    })

    it('should update isAddingTask state', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setIsAddingTask(true)
      })

      expect(result.current.isAddingTask).toBe(true)

      act(() => {
        result.current.setIsAddingTask(false)
      })

      expect(result.current.isAddingTask).toBe(false)
    })
  })

  describe('Editing Task State', () => {
    it('should have default editingTaskId as null', () => {
      const { result } = renderHook(() => useUIStore())
      expect(result.current.editingTaskId).toBeNull()
    })

    it('should update editingTaskId', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setEditingTaskId('task-1')
      })

      expect(result.current.editingTaskId).toBe('task-1')

      act(() => {
        result.current.setEditingTaskId(null)
      })

      expect(result.current.editingTaskId).toBeNull()
    })
  })

  describe('Search Query State', () => {
    it('should have default searchQuery as empty string', () => {
      const { result } = renderHook(() => useUIStore())
      expect(result.current.searchQuery).toBe('')
    })

    it('should update search query', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setSearchQuery('test query')
      })

      expect(result.current.searchQuery).toBe('test query')
    })

    it('should clear search query', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setSearchQuery('test query')
      })

      expect(result.current.searchQuery).toBe('test query')

      act(() => {
        result.current.setSearchQuery('')
      })

      expect(result.current.searchQuery).toBe('')
    })
  })

  describe('Sort By State', () => {
    it('should have default sortBy as "custom"', () => {
      const { result } = renderHook(() => useUIStore())
      expect(result.current.sortBy).toBe('custom')
    })

    it('should update sortBy', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setSortBy('priority')
      })

      expect(result.current.sortBy).toBe('priority')
    })

    it('should update sortBy to different values', () => {
      const { result } = renderHook(() => useUIStore())

      const sortOptions = ['custom', 'priority', 'dueDate', 'createdAt']

      sortOptions.forEach(option => {
        act(() => {
          result.current.setSortBy(option as any)
        })

        expect(result.current.sortBy).toBe(option)
      })
    })
  })

  describe('Multiple State Updates', () => {
    it('should handle multiple state updates correctly', () => {
      const { result } = renderHook(() => useUIStore())

      act(() => {
        result.current.setTaskFilter('completed')
        result.current.setSidebarOpen(false)
        result.current.setTheme('dark')
        result.current.setIsAddingTask(true)
        result.current.setEditingTaskId('task-1')
        result.current.setSearchQuery('test')
        result.current.setSortBy('priority')
      })

      expect(result.current.taskFilter).toBe('completed')
      expect(result.current.sidebarOpen).toBe(false)
      expect(result.current.theme).toBe('dark')
      expect(result.current.isAddingTask).toBe(true)
      expect(result.current.editingTaskId).toBe('task-1')
      expect(result.current.searchQuery).toBe('test')
      expect(result.current.sortBy).toBe('priority')
    })

    it('should maintain state independence', () => {
      const { result } = renderHook(() => useUIStore())

      // 更新一个状态不应该影响其他状态
      act(() => {
        result.current.setTaskFilter('completed')
      })

      expect(result.current.taskFilter).toBe('completed')
      expect(result.current.sidebarOpen).toBe(true) // 应该保持默认值
      expect(result.current.theme).toBe('system') // 应该保持默认值
      expect(result.current.isAddingTask).toBe(false) // 应该保持默认值
    })
  })

  describe('Store Persistence', () => {
    it('should maintain state across multiple hook instances', () => {
      const { result: result1 } = renderHook(() => useUIStore())
      const { result: result2 } = renderHook(() => useUIStore())

      act(() => {
        result1.current.setTaskFilter('completed')
      })

      expect(result1.current.taskFilter).toBe('completed')
      expect(result2.current.taskFilter).toBe('completed')
    })

    it('should update all hook instances when state changes', () => {
      const { result: result1 } = renderHook(() => useUIStore())
      const { result: result2 } = renderHook(() => useUIStore())

      act(() => {
        result1.current.setSearchQuery('test query')
      })

      expect(result1.current.searchQuery).toBe('test query')
      expect(result2.current.searchQuery).toBe('test query')

      act(() => {
        result2.current.setSearchQuery('updated query')
      })

      expect(result1.current.searchQuery).toBe('updated query')
      expect(result2.current.searchQuery).toBe('updated query')
    })
  })
})
