{"version": 3, "sources": ["../../../../src/pg-core/columns/vector_extension/bit.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { PgColumn, PgColumnBuilder } from '../common.ts';\n\nexport type PgBinaryVectorBuilderInitial<TName extends string, TDimensions extends number> = PgBinaryVectorBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'PgBinaryVector';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tdimensions: TDimensions;\n}>;\n\nexport class PgBinaryVectorBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'PgBinaryVector'> & { dimensions: number },\n> extends PgColumnBuilder<\n\tT,\n\t{ dimensions: T['dimensions'] }\n> {\n\tstatic override readonly [entityKind]: string = 'PgBinaryVectorBuilder';\n\n\tconstructor(name: string, config: PgBinaryVectorConfig<T['dimensions']>) {\n\t\tsuper(name, 'string', 'PgBinaryVector');\n\t\tthis.config.dimensions = config.dimensions;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgBinaryVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }> {\n\t\treturn new PgBinaryVector<MakeColumnConfig<T, TTableName> & { dimensions: T['dimensions'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgBinaryVector<T extends ColumnBaseConfig<'string', 'PgBinaryVector'> & { dimensions: number }>\n\textends PgColumn<T, { dimensions: T['dimensions'] }, { dimensions: T['dimensions'] }>\n{\n\tstatic override readonly [entityKind]: string = 'PgBinaryVector';\n\n\treadonly dimensions = this.config.dimensions;\n\n\tgetSQLType(): string {\n\t\treturn `bit(${this.dimensions})`;\n\t}\n}\n\nexport interface PgBinaryVectorConfig<TDimensions extends number = number> {\n\tdimensions: TDimensions;\n}\n\nexport function bit<D extends number>(\n\tconfig: PgBinaryVectorConfig<D>,\n): PgBinaryVectorBuilderInitial<'', D>;\nexport function bit<TName extends string, D extends number>(\n\tname: TName,\n\tconfig: PgBinaryVectorConfig<D>,\n): PgBinaryVectorBuilderInitial<TName, D>;\nexport function bit(a: string | PgBinaryVectorConfig, b?: PgBinaryVectorConfig) {\n\tconst { name, config } = getColumnNameAndConfig<PgBinaryVectorConfig>(a, b);\n\treturn new PgBinaryVectorBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA0C;AAYnC,MAAM,8BAEH,8BAGR;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAc,QAA+C;AACxE,UAAM,MAAM,UAAU,gBAAgB;AACtC,SAAK,OAAO,aAAa,OAAO;AAAA,EACjC;AAAA;AAAA,EAGS,MACR,OACoF;AACpF,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,uBACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEvC,aAAa,KAAK,OAAO;AAAA,EAElC,aAAqB;AACpB,WAAO,OAAO,KAAK,UAAU;AAAA,EAC9B;AACD;AAaO,SAAS,IAAI,GAAkC,GAA0B;AAC/E,QAAM,EAAE,MAAM,OAAO,QAAI,qCAA6C,GAAG,CAAC;AAC1E,SAAO,IAAI,sBAAsB,MAAM,MAAM;AAC9C;", "names": []}