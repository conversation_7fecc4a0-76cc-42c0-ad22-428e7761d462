/**
 * TaskList 组件集成测试
 * 测试任务列表组件的渲染、过滤、排序和拖拽功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import { TaskList } from '../TaskList'
import { 
  mockTasks, 
  mockTask,
  mockCompletedTask,
  createMockTasks,
  mockElectronAPI,
  resetAllMocks,
  mockHelpers 
} from '../../../__tests__/mocks'
import { renderWithProviders } from '../../../__tests__/test-utils'

// Mock UI Store
const mockUIStore = {
  taskFilter: 'all',
  searchQuery: '',
  sortBy: 'custom',
}

vi.mock('../../../stores/uiStore', () => ({
  useTaskFilter: () => mockUIStore.taskFilter,
  useSearch: () => ({ query: mockUIStore.searchQuery }),
  useSort: () => ({ sortBy: mockUIStore.sortBy }),
}))

describe('TaskList', () => {
  beforeEach(() => {
    resetAllMocks()
    mockUIStore.taskFilter = 'all'
    mockUIStore.searchQuery = ''
    mockUIStore.sortBy = 'custom'
  })

  describe('Rendering', () => {
    it('should render loading state initially', () => {
      mockHelpers.setTasksResponse([])
      
      renderWithProviders(<TaskList />)
      
      expect(screen.getByTestId('task-list-loading')).toBeInTheDocument()
    })

    it('should render task list when data loaded', async () => {
      mockHelpers.setTasksResponse(mockTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByTestId('task-list')).toBeInTheDocument()
      })
      
      // 检查是否渲染了任务项
      expect(screen.getByText(mockTask.content)).toBeInTheDocument()
      expect(screen.getByText(mockCompletedTask.content)).toBeInTheDocument()
    })

    it('should render empty state when no tasks', async () => {
      mockHelpers.setTasksResponse([])
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByText('暂无任务')).toBeInTheDocument()
      })
    })

    it('should render error state on fetch failure', async () => {
      const error = new Error('Failed to fetch tasks')
      mockHelpers.makeTasksThrow(error)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByText('加载任务失败')).toBeInTheDocument()
      })
    })
  })

  describe('Task Filtering', () => {
    it('should show all tasks when filter is "all"', async () => {
      mockUIStore.taskFilter = 'all'
      mockHelpers.setTasksResponse(mockTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByText(mockTask.content)).toBeInTheDocument()
        expect(screen.getByText(mockCompletedTask.content)).toBeInTheDocument()
      })
    })

    it('should show only completed tasks when filter is "completed"', async () => {
      mockUIStore.taskFilter = 'completed'
      mockHelpers.setTasksResponse(mockTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.queryByText(mockTask.content)).not.toBeInTheDocument()
        expect(screen.getByText(mockCompletedTask.content)).toBeInTheDocument()
      })
    })

    it('should show only pending tasks when filter is "pending"', async () => {
      mockUIStore.taskFilter = 'pending'
      mockHelpers.setTasksResponse(mockTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByText(mockTask.content)).toBeInTheDocument()
        expect(screen.queryByText(mockCompletedTask.content)).not.toBeInTheDocument()
      })
    })
  })

  describe('Task Search', () => {
    it('should filter tasks by search query', async () => {
      mockUIStore.searchQuery = '测试'
      const tasks = [
        createMockTasks(1, { content: '测试任务' })[0],
        createMockTasks(1, { content: '其他任务' })[0],
      ]
      mockHelpers.setTasksResponse(tasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByText('测试任务')).toBeInTheDocument()
        expect(screen.queryByText('其他任务')).not.toBeInTheDocument()
      })
    })

    it('should show no results when search has no matches', async () => {
      mockUIStore.searchQuery = '不存在的任务'
      mockHelpers.setTasksResponse(mockTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByText('未找到匹配的任务')).toBeInTheDocument()
      })
    })
  })

  describe('Task Sorting', () => {
    it('should sort by custom order (orderIndex)', async () => {
      mockUIStore.sortBy = 'custom'
      const tasks = [
        createMockTasks(1, { content: '任务2', orderIndex: 2000 })[0],
        createMockTasks(1, { content: '任务1', orderIndex: 1000 })[0],
      ]
      mockHelpers.setTasksResponse(tasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        const taskElements = screen.getAllByTestId(/task-item-/)
        expect(taskElements[0]).toHaveTextContent('任务1')
        expect(taskElements[1]).toHaveTextContent('任务2')
      })
    })

    it('should sort by priority', async () => {
      mockUIStore.sortBy = 'priority'
      const tasks = [
        createMockTasks(1, { content: '低优先级', priority: 3 })[0],
        createMockTasks(1, { content: '高优先级', priority: 1 })[0],
      ]
      mockHelpers.setTasksResponse(tasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        const taskElements = screen.getAllByTestId(/task-item-/)
        expect(taskElements[0]).toHaveTextContent('高优先级')
        expect(taskElements[1]).toHaveTextContent('低优先级')
      })
    })
  })

  describe('Drag and Drop', () => {
    it('should handle drag end event', async () => {
      mockHelpers.setTasksResponse(mockTasks)
      mockHelpers.setReorderTasksResponse(true)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByTestId('task-list')).toBeInTheDocument()
      })
      
      // 模拟拖拽结束事件
      const dragEndEvent = {
        active: { id: 'task-1' },
        over: { id: 'task-2' },
      }
      
      // 这里需要根据实际的拖拽实现来调整测试
      // 由于 dnd-kit 的复杂性，这里只是示例
    })
  })

  describe('Task Interactions', () => {
    it('should toggle task completion', async () => {
      mockHelpers.setTasksResponse(mockTasks)
      mockHelpers.setUpdateTaskResponse({ ...mockTask, isCompleted: true })
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByTestId('task-list')).toBeInTheDocument()
      })
      
      const checkbox = screen.getByLabelText(`标记任务"${mockTask.content}"为已完成`)
      fireEvent.click(checkbox)
      
      await waitFor(() => {
        expect(mockElectronAPI.task.update).toHaveBeenCalledWith(
          mockTask.id,
          { isCompleted: true }
        )
      })
    })

    it('should delete task', async () => {
      mockHelpers.setTasksResponse(mockTasks)
      mockHelpers.setDeleteTaskResponse(true)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByTestId('task-list')).toBeInTheDocument()
      })
      
      const deleteButton = screen.getByLabelText('删除任务')
      fireEvent.click(deleteButton)
      
      await waitFor(() => {
        expect(mockElectronAPI.task.delete).toHaveBeenCalledWith(mockTask.id)
      })
    })
  })

  describe('Performance', () => {
    it('should handle large number of tasks', async () => {
      const largeTasks = createMockTasks(1000)
      mockHelpers.setTasksResponse(largeTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        expect(screen.getByTestId('task-list')).toBeInTheDocument()
      })
      
      // 检查是否正确渲染了任务
      expect(screen.getByText('任务 1')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      mockHelpers.setTasksResponse(mockTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        const taskList = screen.getByTestId('task-list')
        expect(taskList).toHaveAttribute('role', 'list')
      })
    })

    it('should be keyboard navigable', async () => {
      mockHelpers.setTasksResponse(mockTasks)
      
      renderWithProviders(<TaskList />)
      
      await waitFor(() => {
        const firstTask = screen.getByTestId(`task-item-${mockTask.id}`)
        expect(firstTask).toHaveAttribute('tabIndex', '0')
      })
    })
  })
})
