"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidThemeId = exports.getThemesByMode = exports.getThemeById = exports.DEFAULT_THEME_CONFIG = exports.BUILT_IN_THEMES = exports.ThemeConfigSchema = exports.ThemeSchema = exports.ThemeColorsSchema = void 0;
const zod_1 = require("zod");
// Zod 验证schemas
exports.ThemeColorsSchema = zod_1.z.object({
    background: zod_1.z.string(),
    foreground: zod_1.z.string(),
    card: zod_1.z.string(),
    cardForeground: zod_1.z.string(),
    popover: zod_1.z.string(),
    popoverForeground: zod_1.z.string(),
    primary: zod_1.z.string(),
    primaryForeground: zod_1.z.string(),
    secondary: zod_1.z.string(),
    secondaryForeground: zod_1.z.string(),
    muted: zod_1.z.string(),
    mutedForeground: zod_1.z.string(),
    accent: zod_1.z.string(),
    accentForeground: zod_1.z.string(),
    destructive: zod_1.z.string(),
    destructiveForeground: zod_1.z.string(),
    border: zod_1.z.string(),
    input: zod_1.z.string(),
    ring: zod_1.z.string(),
});
exports.ThemeSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    mode: zod_1.z.enum(['light', 'dark']),
    colors: exports.ThemeColorsSchema,
    isBuiltIn: zod_1.z.boolean(),
    createdAt: zod_1.z.number(),
});
exports.ThemeConfigSchema = zod_1.z.object({
    mode: zod_1.z.enum(['light', 'dark', 'system']),
    activeThemeId: zod_1.z.string(),
    autoSwitchEnabled: zod_1.z.boolean(),
    autoSwitchTimes: zod_1.z.object({
        lightModeStart: zod_1.z.string(),
        darkModeStart: zod_1.z.string(),
    }),
    eyeCareMode: zod_1.z.boolean(),
    transitionDuration: zod_1.z.number(),
});
// 内置主题数据
exports.BUILT_IN_THEMES = [
    {
        id: 'light-default',
        name: '浅色默认',
        mode: 'light',
        colors: {
            background: '0 0% 100%',
            foreground: '222.2 84% 4.9%',
            card: '0 0% 100%',
            cardForeground: '222.2 84% 4.9%',
            popover: '0 0% 100%',
            popoverForeground: '222.2 84% 4.9%',
            primary: '222.2 47.4% 11.2%',
            primaryForeground: '210 40% 98%',
            secondary: '210 40% 96%',
            secondaryForeground: '222.2 47.4% 11.2%',
            muted: '210 40% 96%',
            mutedForeground: '215.4 16.3% 46.9%',
            accent: '210 40% 96%',
            accentForeground: '222.2 47.4% 11.2%',
            destructive: '0 84.2% 60.2%',
            destructiveForeground: '210 40% 98%',
            border: '214.3 31.8% 91.4%',
            input: '214.3 31.8% 91.4%',
            ring: '222.2 84% 4.9%',
        },
        isBuiltIn: true,
        createdAt: Date.now(),
    },
    {
        id: 'dark-default',
        name: '深色默认',
        mode: 'dark',
        colors: {
            background: '222.2 84% 4.9%',
            foreground: '210 40% 98%',
            card: '222.2 84% 4.9%',
            cardForeground: '210 40% 98%',
            popover: '222.2 84% 4.9%',
            popoverForeground: '210 40% 98%',
            primary: '210 40% 98%',
            primaryForeground: '222.2 47.4% 11.2%',
            secondary: '217.2 32.6% 17.5%',
            secondaryForeground: '210 40% 98%',
            muted: '217.2 32.6% 17.5%',
            mutedForeground: '215 20.2% 65.1%',
            accent: '217.2 32.6% 17.5%',
            accentForeground: '210 40% 98%',
            destructive: '0 62.8% 30.6%',
            destructiveForeground: '210 40% 98%',
            border: '217.2 32.6% 17.5%',
            input: '217.2 32.6% 17.5%',
            ring: '212.7 26.8% 83.9%',
        },
        isBuiltIn: true,
        createdAt: Date.now(),
    },
    {
        id: 'blue-theme',
        name: '蓝色主题',
        mode: 'light',
        colors: {
            background: '0 0% 100%',
            foreground: '222.2 84% 4.9%',
            card: '0 0% 100%',
            cardForeground: '222.2 84% 4.9%',
            popover: '0 0% 100%',
            popoverForeground: '222.2 84% 4.9%',
            primary: '221.2 83.2% 53.3%', // 蓝色主色
            primaryForeground: '210 40% 98%',
            secondary: '210 40% 96%',
            secondaryForeground: '222.2 47.4% 11.2%',
            muted: '210 40% 96%',
            mutedForeground: '215.4 16.3% 46.9%',
            accent: '221.2 83.2% 53.3%', // 蓝色强调色
            accentForeground: '210 40% 98%',
            destructive: '0 84.2% 60.2%',
            destructiveForeground: '210 40% 98%',
            border: '214.3 31.8% 91.4%',
            input: '214.3 31.8% 91.4%',
            ring: '221.2 83.2% 53.3%',
        },
        isBuiltIn: true,
        createdAt: Date.now(),
    },
    {
        id: 'green-theme',
        name: '绿色主题',
        mode: 'light',
        colors: {
            background: '0 0% 100%',
            foreground: '222.2 84% 4.9%',
            card: '0 0% 100%',
            cardForeground: '222.2 84% 4.9%',
            popover: '0 0% 100%',
            popoverForeground: '222.2 84% 4.9%',
            primary: '142.1 76.2% 36.3%', // 绿色主色
            primaryForeground: '210 40% 98%',
            secondary: '210 40% 96%',
            secondaryForeground: '222.2 47.4% 11.2%',
            muted: '210 40% 96%',
            mutedForeground: '215.4 16.3% 46.9%',
            accent: '142.1 76.2% 36.3%', // 绿色强调色
            accentForeground: '210 40% 98%',
            destructive: '0 84.2% 60.2%',
            destructiveForeground: '210 40% 98%',
            border: '214.3 31.8% 91.4%',
            input: '214.3 31.8% 91.4%',
            ring: '142.1 76.2% 36.3%',
        },
        isBuiltIn: true,
        createdAt: Date.now(),
    },
    {
        id: 'purple-theme',
        name: '紫色主题',
        mode: 'light',
        colors: {
            background: '0 0% 100%',
            foreground: '222.2 84% 4.9%',
            card: '0 0% 100%',
            cardForeground: '222.2 84% 4.9%',
            popover: '0 0% 100%',
            popoverForeground: '222.2 84% 4.9%',
            primary: '262.1 83.3% 57.8%', // 紫色主色
            primaryForeground: '210 40% 98%',
            secondary: '210 40% 96%',
            secondaryForeground: '222.2 47.4% 11.2%',
            muted: '210 40% 96%',
            mutedForeground: '215.4 16.3% 46.9%',
            accent: '262.1 83.3% 57.8%', // 紫色强调色
            accentForeground: '210 40% 98%',
            destructive: '0 84.2% 60.2%',
            destructiveForeground: '210 40% 98%',
            border: '214.3 31.8% 91.4%',
            input: '214.3 31.8% 91.4%',
            ring: '262.1 83.3% 57.8%',
        },
        isBuiltIn: true,
        createdAt: Date.now(),
    },
    {
        id: 'eye-care',
        name: '护眼模式',
        mode: 'light',
        colors: {
            background: '60 9.1% 97.8%', // 暖白色背景
            foreground: '24 9.8% 10%',
            card: '60 9.1% 97.8%',
            cardForeground: '24 9.8% 10%',
            popover: '60 9.1% 97.8%',
            popoverForeground: '24 9.8% 10%',
            primary: '25 95% 53%', // 暖橙色主色
            primaryForeground: '60 9.1% 97.8%',
            secondary: '60 4.8% 95.9%',
            secondaryForeground: '24 9.8% 10%',
            muted: '60 4.8% 95.9%',
            mutedForeground: '25 5.3% 44.7%',
            accent: '60 4.8% 95.9%',
            accentForeground: '24 9.8% 10%',
            destructive: '0 84.2% 60.2%',
            destructiveForeground: '60 9.1% 97.8%',
            border: '20 5.9% 90%',
            input: '20 5.9% 90%',
            ring: '25 95% 53%',
        },
        isBuiltIn: true,
        createdAt: Date.now(),
    },
];
// 默认主题配置
exports.DEFAULT_THEME_CONFIG = {
    mode: 'system',
    activeThemeId: 'light-default',
    autoSwitchEnabled: false,
    autoSwitchTimes: {
        lightModeStart: '06:00',
        darkModeStart: '18:00',
    },
    eyeCareMode: false,
    transitionDuration: 300,
};
// 主题工具函数
const getThemeById = (id) => {
    return exports.BUILT_IN_THEMES.find(theme => theme.id === id);
};
exports.getThemeById = getThemeById;
const getThemesByMode = (mode) => {
    return exports.BUILT_IN_THEMES.filter(theme => theme.mode === mode);
};
exports.getThemesByMode = getThemesByMode;
const isValidThemeId = (id) => {
    return exports.BUILT_IN_THEMES.some(theme => theme.id === id);
};
exports.isValidThemeId = isValidThemeId;
